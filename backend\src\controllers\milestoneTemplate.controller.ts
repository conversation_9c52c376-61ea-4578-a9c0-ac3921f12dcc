import { Request, Response } from 'express';
import { prisma } from '../index';
import { v4 as uuidv4 } from 'uuid';

// @desc    Get all milestone templates
// @route   GET /api/milestone-templates
// @access  Private
export const getMilestoneTemplates = async (req: Request, res: Response): Promise<void> => {
  try {
    const templates = await prisma.milestone_template.findMany({
      orderBy: { sequence: 'asc' },
    });

    res.status(200).json({
      success: true,
      data: templates,
    });
  } catch (error) {
    console.error('Error fetching milestone templates:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get a single milestone template
// @route   GET /api/milestone-templates/:id
// @access  Private
export const getMilestoneTemplate = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const template = await prisma.milestone_template.findUnique({
      where: { id },
    });

    if (!template) {
      res.status(404).json({
        success: false,
        message: 'Milestone template not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: template,
    });
  } catch (error) {
    console.error('Error fetching milestone template:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create a new milestone template
// @route   POST /api/milestone-templates
// @access  Private (DIRECTOR only)
export const createMilestoneTemplate = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, description } = req.body;

    // Validate required fields
    if (!name) {
      res.status(400).json({
        success: false,
        message: 'Milestone template name is required',
      });
      return;
    }

    // Check if template name already exists
    const existingTemplate = await prisma.milestone_template.findFirst({
      where: { name },
    });

    if (existingTemplate) {
      res.status(400).json({
        success: false,
        message: 'Milestone template with this name already exists',
      });
      return;
    }

    // Get next sequence number
    const lastTemplate = await prisma.milestone_template.findFirst({
      orderBy: { sequence: 'desc' },
    });

    const nextSequence = lastTemplate ? lastTemplate.sequence + 1 : 1;

    // Create template
    const template = await prisma.milestone_template.create({
      data: {
        id: uuidv4(),
        name,
        description: description || '',
        sequence: nextSequence,
        isActive: true, // Always active since we removed the UI control
      },
    });

    // Apply this new milestone to all existing projects
    await applyMilestoneToAllProjects(template);

    res.status(201).json({
      success: true,
      data: template,
      message: 'Milestone template created and applied to all projects',
    });
  } catch (error) {
    console.error('Error creating milestone template:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update a milestone template
// @route   PUT /api/milestone-templates/:id
// @access  Private (DIRECTOR only)
export const updateMilestoneTemplate = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;

    // Check if template exists
    const templateExists = await prisma.milestone_template.findUnique({
      where: { id },
    });

    if (!templateExists) {
      res.status(404).json({
        success: false,
        message: 'Milestone template not found',
      });
      return;
    }

    // If name is being updated, check for duplicates
    if (name && name !== templateExists.name) {
      const existingTemplate = await prisma.milestone_template.findFirst({
        where: {
          name,
          id: { not: id },
        },
      });

      if (existingTemplate) {
        res.status(400).json({
          success: false,
          message: 'Milestone template with this name already exists',
        });
        return;
      }
    }

    // Update template
    const updatedTemplate = await prisma.milestone_template.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
        // isActive is always true since we removed the UI control
      },
    });

    // If name changed, update all project sections with this milestone
    if (name && name !== templateExists.name) {
      await updateMilestoneInAllProjects(templateExists.name, name);
    }

    // Note: All milestones are now always active since we removed the UI control

    res.status(200).json({
      success: true,
      data: updatedTemplate,
      message: 'Milestone template updated and changes applied to all projects',
    });
  } catch (error) {
    console.error('Error updating milestone template:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete a milestone template
// @route   DELETE /api/milestone-templates/:id
// @access  Private (DIRECTOR only)
export const deleteMilestoneTemplate = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // Check if template exists
    const templateExists = await prisma.milestone_template.findUnique({
      where: { id },
    });

    if (!templateExists) {
      res.status(404).json({
        success: false,
        message: 'Milestone template not found',
      });
      return;
    }

    // Check if any project sections are using this milestone
    const sectionsUsingMilestone = await prisma.section.findMany({
      where: { name: templateExists.name },
      include: { task: true },
    });

    if (sectionsUsingMilestone.length > 0) {
      const sectionsWithTasks = sectionsUsingMilestone.filter(section => section.task.length > 0);

      if (sectionsWithTasks.length > 0) {
        res.status(400).json({
          success: false,
          message: `Cannot delete milestone template "${templateExists.name}" because it contains tasks in ${sectionsWithTasks.length} project(s). Please move or delete all tasks first.`,
        });
        return;
      }
    }

    // Delete all project sections with this milestone name (only if they have no tasks)
    await prisma.section.deleteMany({
      where: {
        name: templateExists.name,
        task: { none: {} } // Only delete sections with no tasks
      },
    });

    // Delete template
    await prisma.milestone_template.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: `Milestone template "${templateExists.name}" deleted and removed from all projects`,
    });
  } catch (error) {
    console.error('Error deleting milestone template:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// Helper function to apply a new milestone to all existing projects
async function applyMilestoneToAllProjects(template: any): Promise<void> {
  try {
    const projects = await prisma.project.findMany({
      select: { id: true },
    });

    for (const project of projects) {
      // Check if this milestone already exists in the project
      const existingSection = await prisma.section.findFirst({
        where: {
          projectId: project.id,
          name: template.name,
        },
      });

      if (!existingSection) {
        await prisma.section.create({
          data: {
            id: uuidv4(),
            projectId: project.id,
            name: template.name,
            description: template.description || '',
            sequence: template.sequence,
          },
        });
      }
    }
  } catch (error) {
    console.error('Error applying milestone to all projects:', error);
  }
}

// Helper function to update milestone name in all projects
async function updateMilestoneInAllProjects(oldName: string, newName: string): Promise<void> {
  try {
    await prisma.section.updateMany({
      where: { name: oldName },
      data: { name: newName },
    });
  } catch (error) {
    console.error('Error updating milestone in all projects:', error);
  }
}
