import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { useNavigate } from 'react-router-dom';
import { momsAtom, currentUserAtom, projectsAtom, customersAtom, usersAtom } from '../store';
import { Calendar, User, Clock, CheckCircle, AlertCircle, Filter, Search, MoreVertical } from 'lucide-react';
import { MOMPointStatus, UserRole, User as UserType } from '../types';

// Utility function to format discussion points as bullet points
const formatDiscussionPoints = (discussion: string) => {
  if (!discussion) return [];

  // Split by newlines and filter out empty lines
  const lines = discussion.split('\n').filter(line => line.trim());

  return lines.map(line => {
    // Remove bullet point if it exists at the beginning
    const cleanLine = line.replace(/^[•\-\*]\s*/, '').trim();
    return cleanLine;
  }).filter(line => line.length > 0);
};

interface MOMAssignedTask {
  id: string;
  momId: string;
  momDate: string;
  projectName: string;
  projectCode: string;
  customerName: string;
  discussion: string;
  actionPlan?: string;
  responsibility: string;
  station?: string;
  plannedDate?: string;
  completionDate?: string;
  status: MOMPointStatus;
  remarks?: string;
  slNo: number;
}

const MOMAssignedPage: React.FC = () => {
  const navigate = useNavigate();
  const [moms] = useAtom(momsAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [projects] = useAtom(projectsAtom);
  const [customers] = useAtom(customersAtom);
  const [users] = useAtom(usersAtom);

  const [assignedTasks, setAssignedTasks] = useState<MOMAssignedTask[]>([]);
  const [subordinateTasks, setSubordinateTasks] = useState<MOMAssignedTask[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<MOMAssignedTask[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [projectFilter, setProjectFilter] = useState<string>('');
  const [viewFilter, setViewFilter] = useState<'MY_TASKS' | 'SUBORDINATE_TASKS' | 'ALL'>('ALL');
  const [isLoading, setIsLoading] = useState(true);

  // Function to get subordinate users based on organizational hierarchy
  const getSubordinateUsers = () => {
    if (!currentUser || !users.length) return [];

    const subordinates: UserType[] = [];

    switch (currentUser.role) {
      case UserRole.DIRECTOR:
        // Directors oversee all Project Managers
        subordinates.push(...users.filter(user => user.role === UserRole.PROJECT_MANAGER));
        break;

      case UserRole.PROJECT_MANAGER:
        // Project Managers oversee Team Leads and Engineers in their department
        subordinates.push(...users.filter(user =>
          user.department === currentUser.department &&
          (user.role === UserRole.TEAM_LEAD || user.role === UserRole.ENGINEER)
        ));
        break;

      case UserRole.TEAM_LEAD:
        // Team Leads oversee Engineers in their department
        subordinates.push(...users.filter(user =>
          user.role === UserRole.ENGINEER &&
          user.department === currentUser.department
        ));
        break;

      case UserRole.ENGINEER:
        // Engineers have no subordinates
        break;
    }

    return subordinates;
  };

  useEffect(() => {
    if (currentUser && moms.length > 0 && projects.length > 0 && customers.length > 0 && users.length > 0) {
      const myTasks: MOMAssignedTask[] = [];
      const subTasks: MOMAssignedTask[] = [];
      const subordinateUsers = getSubordinateUsers();
      const subordinateNames = subordinateUsers.map(user => user.name);

      moms.forEach(mom => {
        const project = projects.find(p => p.id === mom.projectId);
        const customer = customers.find(c => c.id === project?.customerId);

        if (mom.mompoint) {
          mom.mompoint.forEach(point => {
            const taskData = {
              id: point.id,
              momId: mom.id,
              momDate: mom.date,
              projectName: project?.name || 'Unknown Project',
              projectCode: project?.code || 'N/A',
              customerName: customer?.name || 'Unknown Customer',
              discussion: point.discussion,
              actionPlan: point.actionPlan || undefined,
              responsibility: point.responsibility || '',
              station: point.station || undefined,
              plannedDate: point.plannedDate || undefined,
              completionDate: point.completionDate || undefined,
              status: point.status,
              remarks: point.remarks || undefined,
              slNo: point.slNo
            };

            // Check if the current user is assigned to this point
            if (point.responsibility === currentUser.name) {
              myTasks.push(taskData);
            }
            // Check if any subordinate is assigned to this point
            else if (subordinateNames.includes(point.responsibility || '')) {
              subTasks.push(taskData);
            }
          });
        }
      });

      // Sort function
      const sortTasks = (tasks: MOMAssignedTask[]) => {
        return tasks.sort((a, b) => {
          if (a.plannedDate && b.plannedDate) {
            return new Date(a.plannedDate).getTime() - new Date(b.plannedDate).getTime();
          }
          if (a.plannedDate && !b.plannedDate) return -1;
          if (!a.plannedDate && b.plannedDate) return 1;
          return new Date(b.momDate).getTime() - new Date(a.momDate).getTime();
        });
      };

      setAssignedTasks(sortTasks(myTasks));
      setSubordinateTasks(sortTasks(subTasks));
      setIsLoading(false);
    }
  }, [moms, currentUser, projects, customers, users]);

  useEffect(() => {
    // Combine tasks based on view filter
    let allTasks: MOMAssignedTask[] = [];

    switch (viewFilter) {
      case 'MY_TASKS':
        allTasks = assignedTasks;
        break;
      case 'SUBORDINATE_TASKS':
        allTasks = subordinateTasks;
        break;
      case 'ALL':
      default:
        allTasks = [...assignedTasks, ...subordinateTasks];
        break;
    }

    let filtered = allTasks;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(task =>
        task.discussion.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.actionPlan?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.responsibility.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(task => task.status === statusFilter);
    }

    // Apply project filter
    if (projectFilter) {
      filtered = filtered.filter(task => task.projectCode === projectFilter);
    }

    setFilteredTasks(filtered);
  }, [assignedTasks, subordinateTasks, searchTerm, statusFilter, projectFilter, viewFilter]);

  const getStatusIcon = (status: MOMPointStatus) => {
    switch (status) {
      case MOMPointStatus.COMPLETED:
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case MOMPointStatus.IN_PROGRESS:
        return <Clock className="h-4 w-4 text-blue-600" />;
      case MOMPointStatus.ON_HOLD:
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: MOMPointStatus) => {
    switch (status) {
      case MOMPointStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case MOMPointStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case MOMPointStatus.ON_HOLD:
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  const isOverdue = (plannedDate?: string) => {
    if (!plannedDate) return false;
    return new Date(plannedDate) < new Date() && assignedTasks.find(t => t.plannedDate === plannedDate)?.status !== MOMPointStatus.COMPLETED;
  };



  const allTasks = [...assignedTasks, ...subordinateTasks];
  const uniqueProjects = Array.from(new Set(allTasks.map(task => task.projectCode)))
    .map(code => allTasks.find(task => task.projectCode === code))
    .filter(Boolean);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your assigned tasks...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <User className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">MOM Assigned Tasks</h1>
                <p className="text-sm text-gray-600">
                  Tasks assigned to you and your subordinates from Minutes of Meeting discussions
                </p>
              </div>
            </div>
            <div className="text-sm text-gray-500">
              {filteredTasks.length} of {assignedTasks.length + subordinateTasks.length} tasks
              <div className="text-xs text-gray-400 mt-1">
                My Tasks: {assignedTasks.length} | Subordinate Tasks: {subordinateTasks.length}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* View Filter */}
            <select
              value={viewFilter}
              onChange={(e) => setViewFilter(e.target.value as 'MY_TASKS' | 'SUBORDINATE_TASKS' | 'ALL')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="ALL">All Tasks ({assignedTasks.length + subordinateTasks.length})</option>
              <option value="MY_TASKS">My Tasks ({assignedTasks.length})</option>
              <option value="SUBORDINATE_TASKS">Subordinate Tasks ({subordinateTasks.length})</option>
            </select>

            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Statuses</option>
              {Object.values(MOMPointStatus).map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>

            {/* Project Filter */}
            <select
              value={projectFilter}
              onChange={(e) => setProjectFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Projects</option>
              {uniqueProjects.map(task => (
                <option key={task?.projectCode} value={task?.projectCode}>
                  {task?.projectName} ({task?.projectCode})
                </option>
              ))}
            </select>

            {/* Clear Filters */}
            <button
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('');
                setProjectFilter('');
                setViewFilter('ALL');
              }}
              className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        </div>

        {/* MOM-style Tasks Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto" style={{overflowX: 'auto', overflowY: 'visible'}}>
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px]">
                    Task #
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                    MOM Date
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px]">
                    Project
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                    Customer
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[250px]">
                    Discussion Point
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px]">
                    Action Plan
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px]">
                    Assigned To
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                    Station
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                    Planned Date
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                    Completion Date
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px]">
                    Remarks
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTasks.length === 0 ? (
                  <tr>
                    <td colSpan={12} className="px-4 py-8 text-center text-gray-500">
                      {allTasks.length === 0
                        ? "You don't have any tasks assigned from MOM discussions yet, and your subordinates don't have any tasks either."
                        : "No tasks match your current filters. Try adjusting your search criteria."
                      }
                    </td>
                  </tr>
                ) : (
                  filteredTasks.map((task) => (
                    <tr key={task.id} className="hover:bg-gray-50">
                      {/* Task # */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex flex-col">
                          <div className="font-medium">{task.slNo}</div>
                          <div className="flex space-x-1 mt-1">
                            {task.responsibility === currentUser?.name ? (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                My Task
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                Subordinate
                              </span>
                            )}
                            {isOverdue(task.plannedDate) && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Overdue
                              </span>
                            )}
                          </div>
                        </div>
                      </td>

                      {/* MOM Date */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="font-medium">{formatDate(task.momDate)}</div>
                      </td>

                      {/* Project */}
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className="font-medium">{task.projectName}</div>
                        <div className="text-xs text-gray-500">{task.projectCode}</div>
                      </td>

                      {/* Customer */}
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className="max-w-xs">
                          {task.customerName}
                        </div>
                      </td>

                      {/* Discussion Point */}
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className="max-w-xs">
                          <div className="truncate" title={task.discussion}>
                            {task.discussion}
                          </div>
                        </div>
                      </td>

                      {/* Action Plan */}
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className="max-w-xs">
                          <div className="truncate" title={task.actionPlan || ''}>
                            {task.actionPlan || 'N/A'}
                          </div>
                        </div>
                      </td>

                      {/* Assigned To */}
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className="max-w-xs">
                          {task.responsibility}
                        </div>
                      </td>

                      {/* Station */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>{task.station || 'N/A'}</div>
                      </td>

                      {/* Planned Date */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>{task.plannedDate ? formatDate(task.plannedDate) : 'N/A'}</div>
                      </td>

                      {/* Completion Date */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>{task.completionDate ? formatDate(task.completionDate) : 'N/A'}</div>
                      </td>

                      {/* Status */}
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                          {task.status.replace('_', ' ')}
                        </span>
                      </td>

                      {/* Remarks */}
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className="max-w-xs">
                          <div className="truncate" title={task.remarks || ''}>
                            {task.remarks || 'N/A'}
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Summary Footer */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>
                Total Tasks: {filteredTasks.length}
                {viewFilter === 'MY_TASKS' && ' (My Tasks)'}
                {viewFilter === 'SUBORDINATE_TASKS' && ' (Subordinate Tasks)'}
              </span>
              <span>Last updated: {filteredTasks.length > 0 ? formatDate(new Date().toISOString()) : 'N/A'}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MOMAssignedPage;
