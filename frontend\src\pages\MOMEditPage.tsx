import React, { useState, useEffect, useRef } from 'react';
import { useAtom } from 'jotai';
import { useNavigate, useParams } from 'react-router-dom';
import { projectsAtom, customersAtom, momsAtom, usersAtom } from '../store';
import { ArrowLeft, Plus, Calendar, Save, Trash2, ChevronDown, Search, MoreVertical } from 'lucide-react';
import { MOM, MOMPoint, MOMDiscussionType, MOMPointStatus } from '../types';
import { formatDateForInput } from '../utils/dateFormatter';
import { momsAPI } from '../services/api';
import { useNotification } from '../contexts/NotificationContext';
import { dataService } from '../services/dataServiceSingleton';

interface MOMPointFormData {
  id?: string;
  date?: string;
  discussionType?: string; // Changed from MOMDiscussionType to string
  station?: string;
  discussion: string;
  actionPlan?: string;
  responsibility?: string;
  plannedDate?: string;
  completionDate?: string;
  status: MOMPointStatus;
  remarks?: string;
  isNew?: boolean;
  shouldCreateTask?: boolean;
}

interface SearchableSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: { id: string; name: string }[];
  placeholder?: string;
  className?: string;
}

const SearchableSelect: React.FC<SearchableSelectProps> = ({
  value,
  onChange,
  options,
  placeholder = "Select option",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [displayValue, setDisplayValue] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const selectedOption = options.find(option => option.name === value);
    setDisplayValue(selectedOption ? selectedOption.name : '');
  }, [value, options]);

  const filteredOptions = options.filter(option =>
    option.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setDisplayValue(e.target.value);
    if (!isOpen) setIsOpen(true);
  };

  const handleInputFocus = () => {
    setIsOpen(true);
    setSearchTerm(displayValue);
  };

  const handleInputBlur = () => {
    setTimeout(() => {
      setIsOpen(false);
      setSearchTerm('');
      // Reset display value if no valid selection
      const selectedOption = options.find(option => option.name === value);
      setDisplayValue(selectedOption ? selectedOption.name : '');
    }, 200);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
        // Reset display value if no valid selection
        const selectedOption = options.find(option => option.name === value);
        setDisplayValue(selectedOption ? selectedOption.name : '');
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, value, options]);

  return (
    <div className="relative" ref={containerRef}>
      <div className="relative">
        <input
          type="text"
          value={isOpen ? searchTerm : displayValue}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          placeholder={placeholder}
          className={`w-full px-2 py-1 pr-8 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${className}`}
        />
        <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
      </div>

      {isOpen && (
        <div
          className="fixed bg-white border border-gray-300 rounded-md shadow-xl max-h-48 overflow-y-auto"
          style={{
            zIndex: 99999,
            top: containerRef.current ? containerRef.current.getBoundingClientRect().bottom + window.scrollY + 4 : 0,
            left: containerRef.current ? containerRef.current.getBoundingClientRect().left + window.scrollX : 0,
            width: containerRef.current ? containerRef.current.getBoundingClientRect().width : 'auto',
            minWidth: '200px'
          }}
        >
          {filteredOptions.length > 0 ? (
            <>
              <div className="px-3 py-2 text-xs font-medium text-white bg-blue-600 border-b border-blue-700 sticky top-0">
                Select Assignee
              </div>
              <div className="max-h-40 overflow-y-auto">
                {filteredOptions.map((option) => (
                  <div
                    key={option.id}
                    onMouseDown={(e) => e.preventDefault()}
                    onClick={() => handleSelect(option.name)}
                    className="px-3 py-2 text-sm cursor-pointer hover:bg-blue-50 hover:text-blue-700 border-b border-gray-100 last:border-b-0 transition-colors"
                  >
                    {option.name}
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="px-3 py-2 text-sm text-gray-500">No users found</div>
          )}
        </div>
      )}
    </div>
  );
};

const MOMEditPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [projects] = useAtom(projectsAtom);
  const [customers] = useAtom(customersAtom);
  const [moms] = useAtom(momsAtom);
  const [users] = useAtom(usersAtom);
  const { showSuccess, showError } = useNotification();

  // Form state
  const [meetingDate, setMeetingDate] = useState<string>('');
  const [meetingAgenda, setMeetingAgenda] = useState<string>('');
  const [mekhosAttendees, setMekhosAttendees] = useState<string[]>(['']);
  const [customerAttendees, setCustomerAttendees] = useState<string[]>(['']);
  const [discussionPoints, setDiscussionPoints] = useState<MOMPointFormData[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [currentPointIndex, setCurrentPointIndex] = useState<number | null>(null);

  // Refs for focus management
  const mekhosInputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const customerInputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Ensure refs arrays are properly sized
  useEffect(() => {
    mekhosInputRefs.current = mekhosInputRefs.current.slice(0, mekhosAttendees.length);
  }, [mekhosAttendees.length]);

  useEffect(() => {
    customerInputRefs.current = customerInputRefs.current.slice(0, customerAttendees.length);
  }, [customerAttendees.length]);

  // Get MOM data
  const mom = moms.find(m => m.id === id);
  const project = projects.find(p => p.id === mom?.projectId);
  const customer = customers.find(c => c.id === project?.customerId);

  // Load users data when component mounts
  useEffect(() => {
    const loadUsers = async () => {
      try {
        await dataService.loadUsers();
      } catch (error) {
        console.error('Error loading users:', error);
      }
    };
    loadUsers();
  }, []);

  // Use ref to track initialization to prevent resetting user changes
  const isFormInitializedRef = useRef(false);

  useEffect(() => {
    if (mom && projects.length > 0 && !isFormInitializedRef.current) {
      // Initialize form with MOM data only once
      setMeetingDate(mom.date ? formatDateForInput(new Date(mom.date)) : '');
      setMeetingAgenda(mom.agenda || '');

      // Initialize attendees (using company field to distinguish between Mekhos and customer attendees)
      setMekhosAttendees(mom.attendees?.filter(a => a.company === 'Mekhos Technology').map(a => a.name) || ['']);
      setCustomerAttendees(mom.attendees?.filter(a => a.company !== 'Mekhos Technology').map(a => a.name) || ['']);

      // Initialize discussion points
      const points: MOMPointFormData[] = mom.mompoint?.map(point => ({
        id: point.id,
        date: point.date ? formatDateForInput(new Date(point.date)) : '',
        discussionType: point.discussionType,
        station: point.station || '',
        discussion: point.discussion,
        actionPlan: point.actionPlan || '',
        responsibility: point.responsibility || '',
        plannedDate: point.plannedDate ? formatDateForInput(new Date(point.plannedDate)) : '',
        completionDate: point.completionDate ? formatDateForInput(new Date(point.completionDate)) : '',
        status: point.status,
        remarks: point.remarks || '',
      })) || [];

      // Add empty row if no points exist
      if (points.length === 0) {
        points.push({
          discussion: '',
          status: MOMPointStatus.PENDING,
          isNew: true
        });
      }

      setDiscussionPoints(points);
      isFormInitializedRef.current = true;
      setIsLoading(false);
    }
  }, [mom, projects]);

  const addDiscussionPoint = () => {
    const newIndex = discussionPoints.length;
    setDiscussionPoints([...discussionPoints, {
      discussion: '• ',
      status: MOMPointStatus.PENDING,
      isNew: true
    }]);

    // Show task creation modal for the new point
    setCurrentPointIndex(newIndex);
    setShowTaskModal(true);
  };

  const removeDiscussionPoint = (index: number) => {
    setDiscussionPoints(discussionPoints.filter((_, i) => i !== index));
  };

  const updateDiscussionPoint = (index: number, field: keyof MOMPointFormData, value: any) => {
    const updated = [...discussionPoints];
    updated[index] = { ...updated[index], [field]: value };
    setDiscussionPoints(updated);
  };

  const handleTaskDecision = (shouldCreateTask: boolean) => {
    if (currentPointIndex !== null) {
      updateDiscussionPoint(currentPointIndex, 'shouldCreateTask', shouldCreateTask);
    }
    setShowTaskModal(false);
    setCurrentPointIndex(null);
  };

  const openTaskModal = (index: number) => {
    setCurrentPointIndex(index);
    setShowTaskModal(true);
  };

  const addAttendee = (type: 'mekhos' | 'customer', shouldFocus: boolean = false) => {
    if (type === 'mekhos') {
      const newAttendees = [...mekhosAttendees, ''];
      setMekhosAttendees(newAttendees);
      if (shouldFocus) {
        setTimeout(() => {
          const newIndex = newAttendees.length - 1; // Focus the last (newly added) field
          if (mekhosInputRefs.current[newIndex]) {
            mekhosInputRefs.current[newIndex]?.focus();
          }
        }, 100); // Increased timeout to ensure DOM update
      }
    } else {
      const newAttendees = [...customerAttendees, ''];
      setCustomerAttendees(newAttendees);
      if (shouldFocus) {
        setTimeout(() => {
          const newIndex = newAttendees.length - 1; // Focus the last (newly added) field
          if (customerInputRefs.current[newIndex]) {
            customerInputRefs.current[newIndex]?.focus();
          }
        }, 100); // Increased timeout to ensure DOM update
      }
    }
  };

  const removeAttendee = (type: 'mekhos' | 'customer', index: number) => {
    if (type === 'mekhos') {
      setMekhosAttendees(mekhosAttendees.filter((_, i) => i !== index));
    } else {
      setCustomerAttendees(customerAttendees.filter((_, i) => i !== index));
    }
  };

  const updateAttendee = (type: 'mekhos' | 'customer', index: number, value: string) => {
    if (type === 'mekhos') {
      const updated = [...mekhosAttendees];
      updated[index] = value;
      setMekhosAttendees(updated);
    } else {
      const updated = [...customerAttendees];
      updated[index] = value;
      setCustomerAttendees(updated);
    }
  };

  const handleSave = async () => {
    if (!mom) return;

    setIsSubmitting(true);
    try {
      console.log('Updating MOM...');

      // Update MOM basic info
      await momsAPI.updateMOM(mom.id, {
        date: new Date(meetingDate).toISOString(),
        agenda: meetingAgenda || 'Meeting discussion points',
      });

      // Handle discussion points
      for (const point of discussionPoints.filter(p => p.discussion.trim())) {
        if (point.id && !point.isNew) {
          // Update existing point
          await momsAPI.updateMOMPoint(mom.id, point.id, {
            date: point.date ? new Date(point.date).toISOString() : null,
            discussionType: point.discussionType,
            station: point.station,
            discussion: point.discussion,
            actionPlan: point.actionPlan,
            responsibility: point.responsibility,
            plannedDate: point.plannedDate ? new Date(point.plannedDate).toISOString() : null,
            completionDate: point.completionDate ? new Date(point.completionDate).toISOString() : null,
            status: point.status,
            remarks: point.remarks,
          });
        } else if (point.isNew) {
          // Add new point
          await momsAPI.addMOMPoint(mom.id, {
            date: point.date ? new Date(point.date).toISOString() : null,
            discussionType: point.discussionType,
            station: point.station,
            discussion: point.discussion,
            actionPlan: point.actionPlan,
            responsibility: point.responsibility,
            plannedDate: point.plannedDate ? new Date(point.plannedDate).toISOString() : null,
            completionDate: point.completionDate ? new Date(point.completionDate).toISOString() : null,
            status: point.status,
            remarks: point.remarks,
          });
        }
      }

      // Handle deleted points (points that existed but are no longer in the list)
      const currentPointIds = discussionPoints.filter(p => p.id && !p.isNew).map(p => p.id);
      const originalPointIds = mom.mompoint?.map(p => p.id) || [];
      const deletedPointIds = originalPointIds.filter(id => !currentPointIds.includes(id));

      for (const pointId of deletedPointIds) {
        await momsAPI.deleteMOMPoint(mom.id, pointId);
      }

      // Refresh MOMs data
      await dataService.loadMOMs();

      showSuccess('MOM Updated', 'Minutes of Meeting updated successfully!');
      navigate('/mom');
    } catch (error) {
      console.error('Error updating MOM:', error);
      showError('Update Failed', 'Failed to update MOM. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/mom');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading MOM data...</p>
        </div>
      </div>
    );
  }

  if (!mom) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">MOM Not Found</h2>
          <p className="text-gray-600 mb-6">The requested Minutes of Meeting could not be found.</p>
          <button
            onClick={handleCancel}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Back to MOMs
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleCancel}
                className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to MOMs
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Calendar className="h-5 w-5 text-blue-600" />
                </div>
                <h1 className="text-xl font-semibold text-gray-900">Edit Minutes of Meeting</h1>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSubmitting}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors"
              >
                <Save className="h-4 w-4 mr-2" />
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6">
            {/* Basic Information Row */}
            <div className="mb-4">
              <div className="grid grid-cols-3 gap-4">
                {/* Meeting Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Meeting Date
                  </label>
                  <input
                    type="date"
                    value={meetingDate}
                    onChange={(e) => setMeetingDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Customer */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Customer
                  </label>
                  <input
                    type="text"
                    value={customer?.name || ''}
                    placeholder="Select Customer"
                    disabled
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                  />
                  <div className="flex items-center mt-1">
                    <button className="text-xs text-blue-600 hover:text-blue-800">+ New</button>
                    <span className="text-xs text-gray-500 ml-2">Auto-filled from project</span>
                  </div>
                </div>

                {/* Project */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Project
                  </label>
                  <input
                    type="text"
                    value={project ? `${project.name} (${project.code})` : ''}
                    placeholder="Select Project"
                    disabled
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                  />
                </div>
              </div>
            </div>

            {/* Meeting Agenda */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Meeting Agenda
              </label>
              <textarea
                value={meetingAgenda}
                onChange={(e) => setMeetingAgenda(e.target.value)}
                placeholder="Enter the meeting agenda..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={3}
              />
            </div>

            {/* Attendees */}
            <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Mekhos Attendees */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700">Mekhos Attendees</label>
                  <button
                    onClick={() => addAttendee('mekhos')}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    <Plus className="h-4 w-4 inline mr-1" />
                    Add
                  </button>
                </div>
                {mekhosAttendees.map((attendee, index) => (
                  <div key={index} className="flex items-center space-x-2 mb-2">
                    <input
                      ref={(el) => mekhosInputRefs.current[index] = el}
                      type="text"
                      value={attendee}
                      onChange={(e) => updateAttendee('mekhos', index, e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addAttendee('mekhos', true);
                        }
                      }}
                      placeholder="Attendee name (Press Enter to add new)"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    {mekhosAttendees.length > 1 && (
                      <button
                        onClick={() => removeAttendee('mekhos', index)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>

              {/* Customer Attendees */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700">Customer Attendees</label>
                  <button
                    onClick={() => addAttendee('customer')}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    <Plus className="h-4 w-4 inline mr-1" />
                    Add
                  </button>
                </div>
                {customerAttendees.map((attendee, index) => (
                  <div key={index} className="flex items-center space-x-2 mb-2">
                    <input
                      ref={(el) => customerInputRefs.current[index] = el}
                      type="text"
                      value={attendee}
                      onChange={(e) => updateAttendee('customer', index, e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addAttendee('customer', true);
                        }
                      }}
                      placeholder="Attendee name (Press Enter to add new)"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    {customerAttendees.length > 1 && (
                      <button
                        onClick={() => removeAttendee('customer', index)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Discussion Points */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Discussion Points</h3>
                <button
                  onClick={addDiscussionPoint}
                  className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Point
                </button>
              </div>

              {/* Scrollable Table Container */}
              <div className="rounded-lg overflow-hidden border border-gray-200">
                <div className="overflow-x-auto overflow-y-auto max-h-96 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
                  <table className="w-full table-fixed min-w-[1600px]">
                  <colgroup>
                    <col style={{width: '9%'}} /> {/* Date */}
                    <col style={{width: '10%'}} /> {/* Type */}
                    <col style={{width: '8%'}} /> {/* Station */}
                    <col style={{width: '16%'}} /> {/* Discussion */}
                    <col style={{width: '16%'}} /> {/* Action Plan */}
                    <col style={{width: '12%'}} /> {/* Responsibility */}
                    <col style={{width: '9%'}} /> {/* Planned Date */}
                    <col style={{width: '9%'}} /> {/* Completion Date */}
                    <col style={{width: '8%'}} /> {/* Status */}
                    <col style={{width: '15%'}} /> {/* Result/Comments */}
                    <col style={{width: '8%'}} /> {/* Actions */}
                  </colgroup>
                  <thead className="bg-gray-50 sticky top-0 z-10">
                    <tr>
                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Station</th>
                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discussion</th>
                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action Plan</th>
                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responsibility</th>
                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Planned Date</th>
                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Date</th>
                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Result/Comments</th>
                      <th className="px-2 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {discussionPoints.map((point, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-2 py-2">
                          <input
                            type="date"
                            value={point.date || ''}
                            onChange={(e) => updateDiscussionPoint(index, 'date', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                        </td>
                        <td className="px-2 py-2">
                          <input
                            type="text"
                            value={point.discussionType || ''}
                            onChange={(e) => updateDiscussionPoint(index, 'discussionType', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                            placeholder="Discussion Type"
                          />
                        </td>
                        <td className="px-2 py-2">
                          <input
                            type="text"
                            value={point.station || ''}
                            onChange={(e) => updateDiscussionPoint(index, 'station', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                            placeholder="Station"
                          />
                        </td>
                        <td className="px-2 py-2">
                          <textarea
                            value={point.discussion}
                            onChange={(e) => updateDiscussionPoint(index, 'discussion', e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                const currentValue = point.discussion;
                                const newValue = currentValue + '\n• ';
                                updateDiscussionPoint(index, 'discussion', newValue);
                              }
                            }}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                            rows={4}
                            placeholder="Enter discussion points (Press Enter for new point, Shift+Enter for new line)"
                          />
                        </td>
                        <td className="px-2 py-2">
                          <textarea
                            value={point.actionPlan || ''}
                            onChange={(e) => updateDiscussionPoint(index, 'actionPlan', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                            rows={2}
                            placeholder="Action plan"
                          />
                        </td>
                        <td className="px-2 py-2">
                          <SearchableSelect
                            value={point.responsibility || ''}
                            onChange={(value) => updateDiscussionPoint(index, 'responsibility', value)}
                            options={users || []}
                            placeholder="Select Assignee"
                          />
                        </td>
                        <td className="px-2 py-2">
                          <input
                            type="date"
                            value={point.plannedDate || ''}
                            onChange={(e) => updateDiscussionPoint(index, 'plannedDate', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                        </td>
                        <td className="px-2 py-2">
                          <input
                            type="date"
                            value={point.completionDate || ''}
                            onChange={(e) => updateDiscussionPoint(index, 'completionDate', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                        </td>
                        <td className="px-2 py-2">
                          <select
                            value={point.status}
                            onChange={(e) => updateDiscussionPoint(index, 'status', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          >
                            {Object.values(MOMPointStatus).map(status => (
                              <option key={status} value={status}>{status}</option>
                            ))}
                          </select>
                        </td>
                        <td className="px-2 py-2">
                          <textarea
                            value={point.remarks || ''}
                            onChange={(e) => updateDiscussionPoint(index, 'remarks', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                            rows={2}
                            placeholder="Result/comments"
                          />
                        </td>
                        <td className="px-2 py-2 text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <button
                              onClick={() => openTaskModal(index)}
                              className={`p-1 rounded transition-colors ${
                                point.shouldCreateTask
                                  ? 'text-green-600 bg-green-50 hover:bg-green-100'
                                  : 'text-blue-600 hover:bg-blue-50'
                              }`}
                              title={point.shouldCreateTask ? "Will create task" : "Create task?"}
                            >
                              <Plus className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => removeDiscussionPoint(index)}
                              className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors"
                              title="Remove point"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Task Creation Modal */}
      {showTaskModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Create Task from Discussion Point?
            </h3>
            <p className="text-sm text-gray-600 mb-6">
              Would you like to create a task from this discussion point? This will help track the action item in your project management system.
            </p>
            <div className="flex items-center justify-end space-x-3">
              <button
                onClick={() => handleTaskDecision(false)}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                No, Skip
              </button>
              <button
                onClick={() => handleTaskDecision(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Yes, Create Task
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MOMEditPage;
