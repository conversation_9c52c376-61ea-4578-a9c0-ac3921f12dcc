import React, { useState, useEffect } from 'react';
import { X, DollarSign, Calendar, CreditCard, FileText, Hash, Save } from 'lucide-react';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (paymentData: PaymentFormData) => void;
  projectId: string;
  projectName: string;
  poValue: number;
  currentPaid: number;
  payment?: PaymentData | null; // For editing existing payment
}

interface PaymentFormData {
  amount: number;
  paymentDate: string;
  paymentMethod: string;
  referenceNumber: string;
  description: string;
}

interface PaymentData extends PaymentFormData {
  id: string;
  projectId: string;
  createdAt: string;
  updatedAt: string;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  onSave,
  projectId,
  projectName,
  poValue,
  currentPaid,
  payment = null
}) => {
  const [formData, setFormData] = useState<PaymentFormData>({
    amount: 0,
    paymentDate: new Date().toISOString().split('T')[0],
    paymentMethod: 'BANK_TRANSFER',
    referenceNumber: '',
    description: ''
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form when modal opens/closes or payment changes
  useEffect(() => {
    if (isOpen) {
      if (payment) {
        // Editing existing payment
        setFormData({
          amount: payment.amount,
          paymentDate: payment.paymentDate.split('T')[0],
          paymentMethod: payment.paymentMethod,
          referenceNumber: payment.referenceNumber || '',
          description: payment.description || ''
        });
      } else {
        // Creating new payment
        setFormData({
          amount: 0,
          paymentDate: new Date().toISOString().split('T')[0],
          paymentMethod: 'BANK_TRANSFER',
          referenceNumber: '',
          description: ''
        });
      }
      setErrors({});
    }
  }, [isOpen, payment]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'amount' ? parseFloat(value) || 0 : value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'Amount must be greater than 0';
    }

    if (!formData.paymentDate) {
      newErrors.paymentDate = 'Payment date is required';
    }

    if (!formData.paymentMethod) {
      newErrors.paymentMethod = 'Payment method is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Error saving payment:', error);
    } finally {
      setLoading(false);
    }
  };

  const pendingAmount = poValue - currentPaid;
  const remainingAfterPayment = pendingAmount - formData.amount;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity duration-300"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl transform transition-all duration-300">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {payment ? 'Edit Payment' : 'Add Payment'}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                Project: {projectName}
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              <X size={24} className="text-gray-500" />
            </button>
          </div>

          {/* Payment Summary */}
          <div className="p-6 bg-gray-50 border-b border-gray-200">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-sm text-gray-600">PO Value</p>
                <p className="text-lg font-semibold text-blue-600">
                  ₹{poValue.toLocaleString('en-IN')}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Already Paid</p>
                <p className="text-lg font-semibold text-green-600">
                  ₹{currentPaid.toLocaleString('en-IN')}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Pending</p>
                <p className="text-lg font-semibold text-orange-600">
                  ₹{pendingAmount.toLocaleString('en-IN')}
                </p>
              </div>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6">
            <div className="space-y-6">
              {/* Amount */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <DollarSign className="mr-2 text-green-500" size={16} />
                  Payment Amount (₹) *
                </label>
                <input
                  type="number"
                  name="amount"
                  value={formData.amount || ''}
                  onChange={handleInputChange}
                  step="0.01"
                  min="0"
                  max={pendingAmount}
                  className={`
                    w-full h-12 px-4 bg-white border-2 rounded-lg
                    transition-all duration-200 text-gray-900
                    ${errors.amount
                      ? 'border-red-300 focus:border-red-500'
                      : 'border-gray-200 hover:border-gray-300 focus:border-blue-500'
                    }
                    focus:outline-none focus:ring-0
                  `}
                  placeholder="Enter payment amount"
                />
                {errors.amount && (
                  <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
                )}
                {formData.amount > 0 && (
                  <p className="mt-1 text-sm text-gray-600">
                    Remaining after this payment: ₹{remainingAfterPayment.toLocaleString('en-IN')}
                  </p>
                )}
              </div>

              {/* Payment Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <Calendar className="mr-2 text-blue-500" size={16} />
                  Payment Date *
                </label>
                <input
                  type="date"
                  name="paymentDate"
                  value={formData.paymentDate}
                  onChange={handleInputChange}
                  className={`
                    w-full h-12 px-4 bg-white border-2 rounded-lg
                    transition-all duration-200 text-gray-900
                    ${errors.paymentDate
                      ? 'border-red-300 focus:border-red-500'
                      : 'border-gray-200 hover:border-gray-300 focus:border-blue-500'
                    }
                    focus:outline-none focus:ring-0
                  `}
                />
                {errors.paymentDate && (
                  <p className="mt-1 text-sm text-red-600">{errors.paymentDate}</p>
                )}
              </div>

              {/* Payment Method */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <CreditCard className="mr-2 text-purple-500" size={16} />
                  Payment Method *
                </label>
                <select
                  name="paymentMethod"
                  value={formData.paymentMethod}
                  onChange={handleInputChange}
                  className={`
                    w-full h-12 px-4 bg-white border-2 rounded-lg
                    transition-all duration-200 text-gray-900
                    ${errors.paymentMethod
                      ? 'border-red-300 focus:border-red-500'
                      : 'border-gray-200 hover:border-gray-300 focus:border-blue-500'
                    }
                    focus:outline-none focus:ring-0
                  `}
                >
                  <option value="BANK_TRANSFER">Bank Transfer</option>
                  <option value="CASH">Cash</option>
                  <option value="CHEQUE">Cheque</option>
                  <option value="CREDIT_CARD">Credit Card</option>
                  <option value="UPI">UPI</option>
                  <option value="NEFT">NEFT</option>
                  <option value="RTGS">RTGS</option>
                  <option value="OTHER">Other</option>
                </select>
                {errors.paymentMethod && (
                  <p className="mt-1 text-sm text-red-600">{errors.paymentMethod}</p>
                )}
              </div>

              {/* Reference Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <Hash className="mr-2 text-indigo-500" size={16} />
                  Reference Number
                </label>
                <input
                  type="text"
                  name="referenceNumber"
                  value={formData.referenceNumber}
                  onChange={handleInputChange}
                  className="w-full h-12 px-4 bg-white border-2 border-gray-200 rounded-lg
                    transition-all duration-200 text-gray-900
                    hover:border-gray-300 focus:border-blue-500 focus:outline-none focus:ring-0"
                  placeholder="Transaction ID, Cheque number, etc."
                />
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <FileText className="mr-2 text-gray-500" size={16} />
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-lg
                    transition-all duration-200 text-gray-900 resize-none
                    hover:border-gray-300 focus:border-blue-500 focus:outline-none focus:ring-0"
                  placeholder="Additional notes about this payment..."
                />
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 
                  transition-colors duration-200 font-medium"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 
                  transition-colors duration-200 font-medium flex items-center disabled:opacity-50"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save size={16} className="mr-2" />
                    {payment ? 'Update Payment' : 'Add Payment'}
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
