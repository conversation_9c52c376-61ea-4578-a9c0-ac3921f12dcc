import { atom } from 'jotai';
import {
  User,
  User<PERSON><PERSON>,
  Department,
  Engineer,
  Project,
  Task,
  Subtask,
  MOM,
  Alert,
  Customer,
  ProjectCategoryItem,
  MilestoneTemplate
} from '../types';

// Get stored user data and token from localStorage
const storedUser = localStorage.getItem('user');
const hasToken = !!localStorage.getItem('token');

// Parse stored user data if available
const initialUser = storedUser ? JSON.parse(storedUser) : null;

// Authentication state
export const currentUserAtom = atom<User | null>(initialUser);
export const isAuthenticatedAtom = atom<boolean>(hasToken);
export const isAuthLoadingAtom = atom<boolean>(false); // Initialize as false to show content immediately
export const isDataLoadingAtom = atom<boolean>(false); // Track data loading state after login

// Data atoms
export const usersAtom = atom<User[]>([]);

export const departmentsAtom = atom<Department[]>([]); // Legacy - keeping for backward compatibility
export const engineersAtom = atom<Engineer[]>([]);
export const projectsAtom = atom<Project[]>([]);
export const momsAtom = atom<MOM[]>([]);
export const alertsAtom = atom<Alert[]>([]);
export const customersAtom = atom<Customer[]>([]);
export const projectCategoriesAtom = atom<ProjectCategoryItem[]>([]);
export const milestoneTemplatesAtom = atom<MilestoneTemplate[]>([]);

// Normalize projects data to handle task vs tasks mismatch
export const normalizedProjectsAtom = atom(
  (get) => {
    const projects = get(projectsAtom);
    return projects.map(project => {
      const normalizedProject = { ...project };
      // Handle potential data structure mismatch (task vs tasks)
      if (!normalizedProject.tasks && (normalizedProject as any).task) {
        normalizedProject.tasks = (normalizedProject as any).task;
      }
      return normalizedProject;
    });
  }
);

// UI state
export const sidebarExpandedAtom = atom<boolean>(true);
export const currentViewAtom = atom<string>('dashboard');

// Derived atoms for filtered data
export const teamLeadsAtom = atom((get) => {
  const engineers = get(engineersAtom);
  return engineers.filter(engineer => engineer.role === UserRole.TEAM_LEAD);
});

export const userProjectsAtom = atom((get) => {
  const currentUser = get(currentUserAtom);
  const projects = get(normalizedProjectsAtom);

  if (!currentUser) return [];

  // Directors can see all projects
  if (currentUser.role === UserRole.DIRECTOR) {
    return projects;
  }

  // Project Managers can see projects they manage
  if (currentUser.role === UserRole.PROJECT_MANAGER) {
    return projects.filter(project => project.projectManagerId === currentUser.id);
  }

  // Team Leads can see projects where they are assigned tasks or projects in their department
  if (currentUser.role === UserRole.TEAM_LEAD) {
    return projects.filter(project =>
      project.department === currentUser.department ||
      project.tasks?.some(task =>
        task.assigneeId === currentUser.id ||
        task.subtasks?.some(subtask => subtask.assigneeId === currentUser.id)
      )
    );
  }

  // Engineers only see projects where they are assigned tasks
  return projects.filter(project =>
    project.tasks?.some(task =>
      task.assigneeId === currentUser.id ||
      task.subtasks?.some(subtask => subtask.assigneeId === currentUser.id)
    )
  );
});

export const userTasksAtom = atom((get) => {
  const currentUser = get(currentUserAtom);
  const projects = get(normalizedProjectsAtom);

  if (!currentUser) return [];

  const allTasks: (Task & { projectName: string })[] = [];

  projects.forEach(project => {
    if (!project.tasks) return;

    project.tasks.forEach(task => {
      let canViewTask = false;

      if (currentUser.role === UserRole.DIRECTOR) {
        // Directors can see all tasks
        canViewTask = true;
      } else if (currentUser.role === UserRole.PROJECT_MANAGER) {
        // Project managers can see tasks in projects they manage
        canViewTask = project.projectManagerId === currentUser.id;
      } else if (currentUser.role === UserRole.TEAM_LEAD) {
        // Team leads can see:
        // 1. Tasks assigned to them
        // 2. Tasks in their department
        // 3. Tasks that have subtasks assigned to engineers in their department
        canViewTask = task.assigneeId === currentUser.id ||
                     project.department === currentUser.department ||
                     task.subtasks?.some(subtask =>
                       subtask.assigneeId &&
                       // Check if subtask assignee is an engineer in their department
                       // (This would require engineer data, so we'll use a simpler check)
                       project.department === currentUser.department
                     );
      } else if (currentUser.role === UserRole.ENGINEER) {
        // Engineers can see tasks assigned to them OR tasks that have subtasks assigned to them
        canViewTask = task.assigneeId === currentUser.id ||
                     task.subtasks?.some(subtask => subtask.assigneeId === currentUser.id);
      }

      if (canViewTask) {
        allTasks.push({
          ...task,
          projectName: project.name
        });
      }
    });
  });

  return allTasks;
});

export const userSubtasksAtom = atom((get) => {
  const currentUser = get(currentUserAtom);
  const projects = get(normalizedProjectsAtom);

  if (!currentUser) return [];

  const allSubtasks: (Subtask & { taskName: string, projectName: string })[] = [];

  projects.forEach(project => {
    if (!project.tasks) return;

    project.tasks.forEach(task => {
      if (!task.subtasks) return;

      task.subtasks.forEach(subtask => {
        let canViewSubtask = false;

        if (currentUser.role === UserRole.DIRECTOR) {
          // Directors can see all subtasks
          canViewSubtask = true;
        } else if (currentUser.role === UserRole.PROJECT_MANAGER) {
          // Project managers can see subtasks in projects they manage
          canViewSubtask = project.projectManagerId === currentUser.id;
        } else if (currentUser.role === UserRole.TEAM_LEAD) {
          // Team leads can see subtasks in their department or tasks assigned to them
          canViewSubtask = project.department === currentUser.department ||
                          task.assigneeId === currentUser.id;
        } else if (currentUser.role === UserRole.ENGINEER) {
          // Engineers can only see subtasks assigned to them
          canViewSubtask = subtask.assigneeId === currentUser.id;
        }

        if (canViewSubtask) {
          allSubtasks.push({
            ...subtask,
            taskName: task.name,
            projectName: project.name
          });
        }
      });
    });
  });

  return allSubtasks;
});

// Standalone unread alerts count atom (optimized - doesn't require full alerts data)
export const unreadAlertsCountStandaloneAtom = atom<number>(0);