import { useState, useCallback } from 'react';
import { ConfirmationType } from '../components/common/ConfirmationDialog';
import { ToastType } from '../components/common/Toast';

interface ConfirmationState {
  isOpen: boolean;
  title: string;
  message: string;
  type: ConfirmationType;
  confirmText?: string;
  cancelText?: string;
  details?: string[];
  onConfirm: () => void;
  onCancel: () => void;
}

interface ToastState {
  id: string;
  type: ToastType;
  title: string;
  message?: string;
  duration?: number;
}

export const useConfirmation = () => {
  const [confirmation, setConfirmation] = useState<ConfirmationState | null>(null);
  const [toasts, setToasts] = useState<ToastState[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Confirmation methods
  const showConfirmation = useCallback((config: {
    title: string;
    message: string;
    type?: ConfirmationType;
    confirmText?: string;
    cancelText?: string;
    details?: string[];
    onConfirm: () => void | Promise<void>;
    onCancel?: () => void;
  }) => {
    setConfirmation({
      isOpen: true,
      title: config.title,
      message: config.message,
      type: config.type || 'info',
      confirmText: config.confirmText,
      cancelText: config.cancelText,
      details: config.details,
      onConfirm: async () => {
        setIsLoading(true);
        try {
          await config.onConfirm();
          hideConfirmation();
        } catch (error) {
          console.error('Confirmation action failed:', error);
        } finally {
          setIsLoading(false);
        }
      },
      onCancel: () => {
        config.onCancel?.();
        hideConfirmation();
      }
    });
  }, []);

  const hideConfirmation = useCallback(() => {
    setConfirmation(null);
    setIsLoading(false);
  }, []);

  // Toast methods
  const showToast = useCallback((config: {
    type: ToastType;
    title: string;
    message?: string;
    duration?: number;
  }) => {
    const id = Date.now().toString();
    const newToast: ToastState = {
      id,
      type: config.type,
      title: config.title,
      message: config.message,
      duration: config.duration
    };

    setToasts(prev => [...prev, newToast]);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  // Convenience methods for different CRUD operations
  const confirmCreate = useCallback((itemName: string, onConfirm: () => void | Promise<void>, details?: string[]) => {
    showConfirmation({
      title: `Create ${itemName}`,
      message: `Are you sure you want to create this ${itemName.toLowerCase()}?`,
      type: 'create',
      confirmText: 'Create',
      details,
      onConfirm: async () => {
        await onConfirm();
        showToast({
          type: 'success',
          title: 'Created Successfully',
          message: `${itemName} has been created successfully.`
        });
      }
    });
  }, [showConfirmation, showToast]);

  const confirmUpdate = useCallback((itemName: string, onConfirm: () => void | Promise<void>, details?: string[]) => {
    showConfirmation({
      title: `Update ${itemName}`,
      message: `Are you sure you want to update this ${itemName.toLowerCase()}?`,
      type: 'update',
      confirmText: 'Update',
      details,
      onConfirm: async () => {
        await onConfirm();
        showToast({
          type: 'success',
          title: 'Updated Successfully',
          message: `${itemName} has been updated successfully.`
        });
      }
    });
  }, [showConfirmation, showToast]);

  const confirmDelete = useCallback((itemName: string, onConfirm: () => void | Promise<void>, details?: string[]) => {
    showConfirmation({
      title: `Delete ${itemName}`,
      message: `Are you sure you want to delete this ${itemName.toLowerCase()}? This action cannot be undone.`,
      type: 'delete',
      confirmText: 'Delete',
      details: details || ['This action is permanent and cannot be undone'],
      onConfirm: async () => {
        await onConfirm();
        // Success notification removed as requested by user
      }
    });
  }, [showConfirmation, showToast]);

  const confirmAction = useCallback((config: {
    title: string;
    message: string;
    type?: ConfirmationType;
    confirmText?: string;
    successTitle?: string;
    successMessage?: string;
    onConfirm: () => void | Promise<void>;
    details?: string[];
  }) => {
    showConfirmation({
      title: config.title,
      message: config.message,
      type: config.type || 'info',
      confirmText: config.confirmText,
      details: config.details,
      onConfirm: async () => {
        await config.onConfirm();
        if (config.successTitle) {
          showToast({
            type: 'success',
            title: config.successTitle,
            message: config.successMessage
          });
        }
      }
    });
  }, [showConfirmation, showToast]);

  // Error handling
  const showError = useCallback((title: string, message?: string) => {
    showToast({
      type: 'error',
      title,
      message,
      duration: 7000 // Longer duration for errors
    });
  }, [showToast]);

  const showSuccess = useCallback((title: string, message?: string) => {
    showToast({
      type: 'success',
      title,
      message
    });
  }, [showToast]);

  const showWarning = useCallback((title: string, message?: string) => {
    showToast({
      type: 'warning',
      title,
      message
    });
  }, [showToast]);

  const showInfo = useCallback((title: string, message?: string) => {
    showToast({
      type: 'info',
      title,
      message
    });
  }, [showToast]);

  return {
    // Confirmation state
    confirmation,
    isLoading,
    hideConfirmation,

    // Toast state
    toasts,
    removeToast,

    // CRUD confirmations
    confirmCreate,
    confirmUpdate,
    confirmDelete,
    confirmAction,

    // Toast shortcuts
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showToast
  };
};

export default useConfirmation;
