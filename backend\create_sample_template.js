const XLSX = require('xlsx');
const path = require('path');

// Create a sample Excel template
const templateData = [
  {
    'TaskID': '',
    'TaskName': 'Sample Task 1',
    'ParentTask': '',
    'MilestoneCategory': 'Design',
    'AssignedTo': '',
    'Status': '',
    'StartDate': '',
    'EndDate': '',
    'Priority': ''
  },
  {
    'TaskID': '',
    'TaskName': 'Sample Subtask 1.1',
    'ParentTask': 'Sample Task 1',
    'MilestoneCategory': 'Design',
    'AssignedTo': '',
    'Status': '',
    'StartDate': '',
    'EndDate': '',
    'Priority': ''
  },
  {
    'TaskID': '',
    'TaskName': 'Sample Task 2',
    'ParentTask': '',
    'MilestoneCategory': 'Procurement',
    'AssignedTo': '',
    'Status': '',
    'StartDate': '',
    'EndDate': '',
    'Priority': ''
  }
];

// Create workbook and worksheet
const workbook = XLSX.utils.book_new();
const worksheet = XLSX.utils.json_to_sheet(templateData);

// Set column widths
worksheet['!cols'] = [
  { wch: 10 }, // TaskID
  { wch: 25 }, // TaskName
  { wch: 20 }, // ParentTask
  { wch: 15 }, // MilestoneCategory
  { wch: 15 }, // AssignedTo
  { wch: 12 }, // Status
  { wch: 12 }, // StartDate
  { wch: 12 }, // EndDate
  { wch: 10 }  // Priority
];

// Add worksheet to workbook
XLSX.utils.book_append_sheet(workbook, worksheet, 'Tasks');

// Add instructions sheet
const instructions = [
  ['Project Excel Import Template Instructions'],
  [''],
  ['1. Fill in your project tasks and subtasks in the Tasks sheet'],
  ['2. Required fields: TaskName, MilestoneCategory'],
  ['3. Optional fields: All others'],
  [''],
  ['Field Descriptions:'],
  ['• TaskID: Leave empty (auto-generated)'],
  ['• TaskName: Name of the task or subtask (REQUIRED)'],
  ['• ParentTask: For subtasks, enter the parent task name'],
  ['• MilestoneCategory: Must match project milestones (REQUIRED)'],
  ['• AssignedTo: Engineer name (optional)'],
  ['• Status: NOT_STARTED, IN_PROGRESS, COMPLETED, DELAYED, ON_HOLD'],
  ['• StartDate: YYYY-MM-DD format (e.g., 2024-01-15)'],
  ['• EndDate: YYYY-MM-DD format (e.g., 2024-01-30)'],
  ['• Priority: Low, Medium, High'],
  [''],
  ['Common Milestones:'],
  ['• Design'],
  ['• Procurement'],
  ['• Assembly'],
  ['• Testing'],
  ['• MQI']
];

const instructionsSheet = XLSX.utils.aoa_to_sheet(instructions);
instructionsSheet['!cols'] = [{ wch: 60 }];
XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');

// Write the file
const filePath = path.join(__dirname, 'Sample_Template.xlsx');
XLSX.writeFile(workbook, filePath);

console.log(`✅ Sample template created at: ${filePath}`);
