/*
  Warnings:

  - You are about to drop the column `agenda` on the `mom` table. All the data in the column will be lost.
  - You are about to drop the column `customerId` on the `mom` table. All the data in the column will be lost.
  - You are about to drop the column `engineerId` on the `mom` table. All the data in the column will be lost.
  - You are about to drop the column `points` on the `mom` table. All the data in the column will be lost.
  - You are about to drop the column `momId` on the `momactionitem` table. All the data in the column will be lost.
  - You are about to drop the `momattendee` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[projectId]` on the table `mom` will be added. If there are existing duplicate values, this will fail.
  - Made the column `date` on table `mom` required. This step will fail if there are existing NULL values in that column.
  - Made the column `projectId` on table `mom` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE `mom` DROP FOREIGN KEY `MOM_customerId_fkey`;

-- DropForeignKey
ALTER TABLE `mom` DROP FOREIGN KEY `MOM_engineerId_fkey`;

-- DropForeignKey
ALTER TABLE `mom` DROP FOREIGN KEY `MOM_projectId_fkey`;

-- DropForeignKey
ALTER TABLE `momactionitem` DROP FOREIGN KEY `MOMActionItem_momId_fkey`;

-- DropForeignKey
ALTER TABLE `momattendee` DROP FOREIGN KEY `MOMAttendee_momId_fkey`;

-- DropIndex
DROP INDEX `MOM_customerId_fkey` ON `mom`;

-- DropIndex
DROP INDEX `MOM_engineerId_fkey` ON `mom`;

-- DropIndex
DROP INDEX `MOMActionItem_momId_fkey` ON `momactionitem`;

-- AlterTable
ALTER TABLE `mom` DROP COLUMN `agenda`,
    DROP COLUMN `customerId`,
    DROP COLUMN `engineerId`,
    DROP COLUMN `points`,
    ADD COLUMN `updatedBy` VARCHAR(191) NULL,
    MODIFY `date` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    MODIFY `projectId` VARCHAR(191) NOT NULL;

-- AlterTable
ALTER TABLE `momactionitem` DROP COLUMN `momId`,
    ADD COLUMN `projectId` VARCHAR(191) NULL;

-- DropTable
DROP TABLE `momattendee`;

-- CreateTable
CREATE TABLE `mompoint` (
    `id` VARCHAR(191) NOT NULL,
    `momId` VARCHAR(191) NOT NULL,
    `slNo` INTEGER NOT NULL,
    `date` DATETIME(3) NULL,
    `discussionType` ENUM('DESIGN', 'PROCUREMENT', 'ASSEMBLY', 'TESTING', 'QUALITY', 'DELIVERY', 'OTHER') NULL,
    `station` VARCHAR(191) NULL,
    `discussion` TEXT NOT NULL,
    `actionPlan` TEXT NULL,
    `responsibility` VARCHAR(191) NULL,
    `plannedDate` DATETIME(3) NULL,
    `completionDate` DATETIME(3) NULL,
    `status` ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'DELAYED', 'ON_HOLD') NOT NULL DEFAULT 'PENDING',
    `imageAttachment` VARCHAR(191) NULL,
    `remarks` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `MOMPoint_momId_fkey`(`momId`),
    UNIQUE INDEX `mompoint_momId_slNo_key`(`momId`, `slNo`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `mom_projectId_key` ON `mom`(`projectId`);

-- CreateIndex
CREATE INDEX `MOM_updatedBy_fkey` ON `mom`(`updatedBy`);

-- CreateIndex
CREATE INDEX `MOMActionItem_projectId_fkey` ON `momactionitem`(`projectId`);

-- AddForeignKey
ALTER TABLE `mom` ADD CONSTRAINT `MOM_updatedBy_fkey` FOREIGN KEY (`updatedBy`) REFERENCES `user`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `mom` ADD CONSTRAINT `MOM_projectId_fkey` FOREIGN KEY (`projectId`) REFERENCES `project`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `mompoint` ADD CONSTRAINT `MOMPoint_momId_fkey` FOREIGN KEY (`momId`) REFERENCES `mom`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `momactionitem` ADD CONSTRAINT `MOMActionItem_projectId_fkey` FOREIGN KEY (`projectId`) REFERENCES `project`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
