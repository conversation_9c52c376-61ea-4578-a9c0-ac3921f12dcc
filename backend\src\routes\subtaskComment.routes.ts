import express from 'express';
import {
  getSubtaskComments,
  createSubtaskComment,
  updateSubtaskComment,
  deleteSubtaskComment,
} from '../controllers/subtaskComment.controller';
import { protect } from '../middleware/auth.middleware';

const router = express.Router({ mergeParams: true });

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(getSubtaskComments)
  .post(createSubtaskComment);

router.route('/:commentId')
  .put(updateSubtaskComment)
  .delete(deleteSubtaskComment);

export default router;
