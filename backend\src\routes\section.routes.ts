import express from 'express';
import {
  getSectionsByProject,
  createSection,
  updateSection,
  deleteSection,
} from '../controllers/section.controller';
import { protect, authorize } from '../middleware/auth.middleware';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes for sections within a project
router.route('/projects/:projectId/sections')
  .get(getSectionsByProject)
  .post(
    authorize('DIRECTOR', 'PROJECT_MANAGER', 'TEAM_LEAD'),
    createSection
  );

// Routes for individual sections
router.route('/:id')
  .put(
    authorize('DIRECTOR', 'PROJECT_MANAGER', 'TEAM_LEAD'),
    updateSection
  )
  .delete(
    authorize('DIRECTOR', 'PROJECT_MANAGER'),
    deleteSection
  );

export default router;
