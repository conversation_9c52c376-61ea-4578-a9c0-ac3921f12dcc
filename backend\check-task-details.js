const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkTaskDetails() {
  try {
    const projectId = 'fe5a0b2b-1944-4cd2-b4f4-6bae179a1a21';
    
    console.log('=== DETAILED TASK INFORMATION ===');
    const tasks = await prisma.task.findMany({
      where: { projectId },
      orderBy: { sequence: 'asc' }
    });
    
    tasks.forEach(task => {
      console.log(`Task ID: ${task.id}`);
      console.log(`  Display ID: ${task.displayId}`);
      console.log(`  Sequence: ${task.sequence}`);
      console.log(`  Name: ${task.name}`);
      console.log(`  Section ID: ${task.sectionId}`);
      console.log(`  Created At: ${task.createdAt}`);
      console.log('---');
    });
    
    console.log('\n=== SECTIONS WITH TASKS (ORDERED) ===');
    const sections = await prisma.section.findMany({
      where: { projectId },
      include: {
        task: {
          orderBy: { sequence: 'asc' }
        }
      },
      orderBy: { sequence: 'asc' }
    });
    
    sections.forEach(section => {
      console.log(`\n${section.name} Section (sequence: ${section.sequence}):`);
      if (section.task.length === 0) {
        console.log('  No tasks');
      } else {
        section.task.forEach(task => {
          console.log(`  - ${task.displayId || `T${task.sequence}`}: ${task.name} (seq: ${task.sequence})`);
        });
      }
    });
    
  } catch (error) {
    console.error('Error checking task details:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTaskDetails();
