import { prisma } from '../index';
import { v4 as uuidv4 } from 'uuid';

export interface CreateAlertData {
  type: 'SYSTEM' | 'PROJECT' | 'TASK' | 'SUBTASK' | 'DEADLINE' | 'MOM' | 'USER' | 'SECURITY' | 'MAINTENANCE';
  priority: 'MEDIUM';
  title: string;
  message: string;
  relatedTo?: string;
  relatedType?: string;
  assigneeId?: string;
  department?: string;
  dueDate?: Date;
  createdBy?: string;
}

export class AlertService {
  // Helper function to find a user's immediate superior
  static async findUserSuperior(userId: string): Promise<string | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { role: true, department: true }
      });

      if (!user) return null;

      switch (user.role) {
        case 'ENGINEER':
          // Engineers report to Team Leads in the same department
          const teamLead = await prisma.user.findFirst({
            where: {
              role: 'TEAM_LEAD',
              department: user.department
            },
            select: { id: true }
          });
          return teamLead?.id || null;

        case 'TEAM_LEAD':
          // Team Leads report to Project Managers in the same department
          const projectManager = await prisma.user.findFirst({
            where: {
              role: 'PROJECT_MANAGER',
              department: user.department
            },
            select: { id: true }
          });
          return projectManager?.id || null;

        case 'PROJECT_MANAGER':
          // Project Managers report to Directors
          const director = await prisma.user.findFirst({
            where: { role: 'DIRECTOR' },
            select: { id: true }
          });
          return director?.id || null;

        case 'DIRECTOR':
          // Directors have no superior
          return null;

        default:
          return null;
      }
    } catch (error) {
      console.error('Error finding user superior:', error);
      return null;
    }
  }

  // Helper function to find a user's subordinates
  static async findUserSubordinates(userId: string): Promise<string[]> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { role: true, department: true }
      });

      if (!user) return [];

      const subordinates: string[] = [];

      switch (user.role) {
        case 'DIRECTOR':
          // Directors oversee all Project Managers
          const projectManagers = await prisma.user.findMany({
            where: { role: 'PROJECT_MANAGER' },
            select: { id: true }
          });
          subordinates.push(...projectManagers.map(pm => pm.id));
          break;

        case 'PROJECT_MANAGER':
          // Project Managers oversee Team Leads and Engineers in their department
          const teamLeadsAndEngineers = await prisma.user.findMany({
            where: {
              department: user.department,
              role: { in: ['TEAM_LEAD', 'ENGINEER'] }
            },
            select: { id: true }
          });
          subordinates.push(...teamLeadsAndEngineers.map(user => user.id));
          break;

        case 'TEAM_LEAD':
          // Team Leads oversee Engineers in their department
          const engineers = await prisma.user.findMany({
            where: {
              role: 'ENGINEER',
              department: user.department
            },
            select: { id: true }
          });
          subordinates.push(...engineers.map(eng => eng.id));
          break;

        case 'ENGINEER':
          // Engineers have no subordinates
          break;
      }

      return subordinates;
    } catch (error) {
      console.error('Error finding user subordinates:', error);
      return [];
    }
  }

  // Create a new alert with enhanced deduplication
  static async createAlert(data: CreateAlertData) {
    try {
      // Enhanced deduplication - check for duplicates within the last 30 minutes
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

      // For task/subtask alerts, check for similar alerts for the same entity and user
      let existingAlert = null;

      if (data.relatedType === 'TASK' || data.relatedType === 'SUBTASK') {
        // Check for any recent alert for the same task/subtask and user with similar content
        existingAlert = await prisma.alert.findFirst({
          where: {
            relatedTo: data.relatedTo,
            relatedType: data.relatedType,
            assigneeId: data.assigneeId,
            OR: [
              // Exact title match
              { title: data.title },
              // Similar content (for updates, assignments, etc.)
              {
                AND: [
                  { type: data.type },
                  {
                    OR: [
                      { title: { contains: data.relatedTo || '' } },
                      { message: { contains: data.relatedTo || '' } }
                    ]
                  }
                ]
              }
            ],
            createdAt: {
              gte: thirtyMinutesAgo
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        });
      } else if (data.relatedType === 'MOM') {
        // For MOM alerts, check for exact matches with same MOM and user
        existingAlert = await prisma.alert.findFirst({
          where: {
            type: data.type,
            relatedTo: data.relatedTo,
            relatedType: data.relatedType,
            assigneeId: data.assigneeId,
            title: data.title,
            createdAt: {
              gte: thirtyMinutesAgo
            }
          }
        });
      } else {
        // For other types, check for exact matches
        existingAlert = await prisma.alert.findFirst({
          where: {
            type: data.type,
            title: data.title,
            relatedTo: data.relatedTo,
            relatedType: data.relatedType,
            assigneeId: data.assigneeId,
            createdAt: {
              gte: thirtyMinutesAgo
            }
          }
        });
      }

      if (existingAlert) {
        console.log(`🚫 Duplicate alert prevented: ${data.type} - ${data.title} for user ${data.assigneeId} (existing: ${existingAlert.type} - ${existingAlert.title})`);
        return existingAlert;
      }

      const alert = await prisma.alert.create({
        data: {
          id: uuidv4(),
          type: data.type,
          priority: data.priority,
          title: data.title,
          message: data.message,
          relatedTo: data.relatedTo,
          relatedType: data.relatedType,
          assigneeId: data.assigneeId,
          department: data.department,
          dueDate: data.dueDate,
          createdBy: data.createdBy,
        },
      });

      console.log(`Alert created: ${alert.type} - ${alert.title} for user ${alert.assigneeId}`);
      return alert;
    } catch (error) {
      console.error('Error creating alert:', error);
      throw error;
    }
  }

  // Project-related alerts
  static async createProjectAlert(projectId: string, type: 'CREATED' | 'UPDATED' | 'DEADLINE' | 'DELAYED' | 'COMPLETED', userId?: string) {
    try {
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          user_project_projectManagerIdTouser: true,
          customer: true,
        },
      });

      if (!project) return;

      let title = '';
      let message = '';
      let priority: 'MEDIUM' = 'MEDIUM';
      let alertType: 'PROJECT' | 'DEADLINE' = 'PROJECT';

      switch (type) {
        case 'CREATED':
          title = 'New Project Created';
          message = `Project "${project.name}" has been created for ${project.customer.name}`;
          priority = 'MEDIUM';
          break;
        case 'UPDATED':
          title = 'Project Updated';
          message = `Project "${project.name}" has been updated`;
          priority = 'MEDIUM';
          break;
        case 'DEADLINE':
          title = 'Project Deadline Approaching';
          message = `Project "${project.name}" deadline is approaching${project.endDate ? ` (${project.endDate.toDateString()})` : ''}`;
          priority = 'MEDIUM';
          alertType = 'DEADLINE';
          break;
        case 'DELAYED':
          title = 'Project Delayed';
          message = `Project "${project.name}" is delayed and requires attention`;
          priority = 'MEDIUM';
          break;
        case 'COMPLETED':
          title = 'Project Completed';
          message = `Project "${project.name}" has been completed successfully`;
          priority = 'MEDIUM';
          break;
      }

      // Implement hierarchical notification system for projects
      const alertRecipients = new Set<string>();

      if (type === 'CREATED') {
        // CREATED notifications: Only to subordinates when something is created for them
        if (userId) {
          const subordinates = await this.findUserSubordinates(userId);
          subordinates.forEach(subordinateId => alertRecipients.add(subordinateId));
        }
      } else {
        // For other types, use existing logic
        // Add project manager
        if (project.projectManagerId) {
          alertRecipients.add(project.projectManagerId);
        }

        // Add director (if different from project manager)
        const director = await prisma.user.findFirst({
          where: { role: 'DIRECTOR' },
        });

        if (director && director.id !== project.projectManagerId) {
          alertRecipients.add(director.id);
        }
      }

      // Create alerts for all unique recipients
      for (const recipientId of alertRecipients) {
        await this.createAlert({
          type: alertType,
          priority,
          title,
          message,
          relatedTo: projectId,
          relatedType: 'PROJECT',
          assigneeId: recipientId,
          department: project.department,
          dueDate: type === 'DEADLINE' && project.endDate ? project.endDate : undefined,
          createdBy: userId,
        });
      }
    } catch (error) {
      console.error('Error creating project alert:', error);
    }
  }

  // Task-related alerts
  static async createTaskAlert(taskId: string, type: 'CREATED' | 'UPDATED' | 'DEADLINE' | 'DELAYED' | 'COMPLETED' | 'ASSIGNED', userId?: string) {
    try {
      const task = await prisma.task.findUnique({
        where: { id: taskId },
        include: {
          project: {
            include: {
              user_project_projectManagerIdTouser: true,
            },
          },
          user: true,
        },
      });

      if (!task) return;

      let title = '';
      let message = '';
      let priority: 'MEDIUM' = 'MEDIUM';
      let alertType: 'TASK' | 'DEADLINE' = 'TASK';

      const taskDisplayId = `T${task.sequence}`;
      const dueDateStr = task.endDate ? task.endDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }) : '';

      switch (type) {
        case 'CREATED':
          title = `Task Created - ${taskDisplayId}: ${task.name}`;
          message = `Task "${task.name}" (${taskDisplayId}) has been created in project "${task.project.name}"${task.endDate ? ` with due date ${dueDateStr}` : ''}`;
          priority = 'MEDIUM';
          break;
        case 'ASSIGNED':
          title = `Task Assigned - ${taskDisplayId}: ${task.name}`;
          message = `You have been assigned task "${task.name}" (${taskDisplayId}) in project "${task.project.name}"${task.endDate ? ` - Due: ${dueDateStr}` : ''}`;
          priority = 'MEDIUM';
          break;
        case 'UPDATED':
          title = `Task Updated - ${taskDisplayId}: ${task.name}`;
          message = `Task "${task.name}" (${taskDisplayId}) has been updated`;
          priority = 'MEDIUM';
          break;
        case 'DEADLINE':
          const daysUntilDue = task.endDate ? Math.ceil((task.endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 0;
          title = `Task Deadline Alert - ${taskDisplayId}: ${task.name}`;
          message = `Task "${task.name}" (${taskDisplayId}) deadline is ${daysUntilDue <= 0 ? 'overdue' : `in ${daysUntilDue} day${daysUntilDue > 1 ? 's' : ''}`} - Due: ${dueDateStr}`;
          priority = 'MEDIUM';
          alertType = 'DEADLINE';
          break;
        case 'DELAYED':
          const delayDays = task.endDate ? Math.ceil((new Date().getTime() - task.endDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;
          title = `Task Delayed - ${taskDisplayId}: ${task.name}`;
          message = `Task "${task.name}" (${taskDisplayId}) is ${delayDays} day${delayDays > 1 ? 's' : ''} overdue (Due: ${dueDateStr}) and requires immediate attention`;
          priority = 'MEDIUM';
          break;
        case 'COMPLETED':
          title = `Task Completed - ${taskDisplayId}: ${task.name}`;
          message = `Task "${task.name}" (${taskDisplayId}) has been completed successfully${task.user ? ` by ${task.user.name}` : ''}`;
          priority = 'MEDIUM';
          break;
      }

      // Implement hierarchical notification system
      const alertRecipients = new Set<string>();

      if (type === 'ASSIGNED') {
        // ASSIGNED notifications: Only to assignee + their immediate superior
        console.log(`[HIERARCHICAL] Task ASSIGNED alert for task ${taskDisplayId}`);
        if (task.assigneeId) {
          alertRecipients.add(task.assigneeId);
          console.log(`[HIERARCHICAL] Added assignee: ${task.assigneeId}`);

          // Add assignee's immediate superior
          const superior = await this.findUserSuperior(task.assigneeId);
          if (superior) {
            alertRecipients.add(superior);
            console.log(`[HIERARCHICAL] Added superior: ${superior}`);
          } else {
            console.log(`[HIERARCHICAL] No superior found for assignee: ${task.assigneeId}`);
          }
        }
      } else if (type === 'CREATED') {
        // CREATED notifications: Only to subordinates when something is created for them
        console.log(`[HIERARCHICAL] Task CREATED alert for task ${taskDisplayId}`);
        if (userId) {
          const subordinates = await this.findUserSubordinates(userId);
          subordinates.forEach(subordinateId => alertRecipients.add(subordinateId));
          console.log(`[HIERARCHICAL] Added subordinates: ${subordinates.join(', ')}`);
        }
      } else {
        // For other types (UPDATED, COMPLETED, DEADLINE, DELAYED), use existing logic
        // Add task assignee
        if (task.assigneeId) {
          alertRecipients.add(task.assigneeId);
        }

        // Add project manager (if different from task assignee)
        if (task.project.projectManagerId && task.project.projectManagerId !== task.assigneeId) {
          alertRecipients.add(task.project.projectManagerId);
        }
      }

      // Create alerts for all unique recipients
      for (const recipientId of alertRecipients) {
        await this.createAlert({
          type: alertType,
          priority,
          title,
          message,
          relatedTo: taskId,
          relatedType: 'TASK',
          assigneeId: recipientId,
          department: task.department,
          dueDate: type === 'DEADLINE' ? task.endDate : undefined,
          createdBy: userId,
        });
      }


    } catch (error) {
      console.error('Error creating task alert:', error);
    }
  }

  // Subtask-related alerts
  static async createSubtaskAlert(subtaskId: string, type: 'CREATED' | 'UPDATED' | 'DEADLINE' | 'DELAYED' | 'COMPLETED' | 'ASSIGNED', userId?: string) {
    try {
      const subtask = await prisma.subtask.findUnique({
        where: { id: subtaskId },
        include: {
          task: {
            include: {
              project: {
                include: {
                  user_project_projectManagerIdTouser: true,
                },
              },
            },
          },
          user: true,
        },
      });

      if (!subtask) return;

      let title = '';
      let message = '';
      let priority: 'MEDIUM' = 'MEDIUM';
      let alertType: 'SUBTASK' | 'DEADLINE' = 'SUBTASK';

      const subtaskDisplayId = `T${subtask.task.sequence}S${subtask.sequence}`;
      const dueDateStr = subtask.endDate ? subtask.endDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }) : '';

      switch (type) {
        case 'CREATED':
          title = `Subtask Created - ${subtaskDisplayId}: ${subtask.name}`;
          message = `Subtask "${subtask.name}" (${subtaskDisplayId}) has been created under task "${subtask.task.name}" in project "${subtask.task.project.name}"${subtask.endDate ? ` with due date ${dueDateStr}` : ''}`;
          priority = 'MEDIUM';
          break;
        case 'ASSIGNED':
          title = `Subtask Assigned - ${subtaskDisplayId}: ${subtask.name}`;
          message = `You have been assigned subtask "${subtask.name}" (${subtaskDisplayId}) under task "${subtask.task.name}"${subtask.endDate ? ` - Due: ${dueDateStr}` : ''}`;
          priority = 'MEDIUM';
          break;
        case 'UPDATED':
          title = `Subtask Updated - ${subtaskDisplayId}: ${subtask.name}`;
          message = `Subtask "${subtask.name}" (${subtaskDisplayId}) has been updated`;
          priority = 'MEDIUM';
          break;
        case 'DEADLINE':
          const daysUntilDue = subtask.endDate ? Math.ceil((subtask.endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 0;
          title = `Subtask Deadline Alert - ${subtaskDisplayId}: ${subtask.name}`;
          message = `Subtask "${subtask.name}" (${subtaskDisplayId}) deadline is ${daysUntilDue <= 0 ? 'overdue' : `in ${daysUntilDue} day${daysUntilDue > 1 ? 's' : ''}`} - Due: ${dueDateStr}`;
          priority = 'MEDIUM';
          alertType = 'DEADLINE';
          break;
        case 'DELAYED':
          const delayDays = subtask.endDate ? Math.ceil((new Date().getTime() - subtask.endDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;
          title = `Subtask Delayed - ${subtaskDisplayId}: ${subtask.name}`;
          message = `Subtask "${subtask.name}" (${subtaskDisplayId}) is ${delayDays} day${delayDays > 1 ? 's' : ''} overdue (Due: ${dueDateStr}) and requires immediate attention`;
          priority = 'MEDIUM';
          break;
        case 'COMPLETED':
          title = `Subtask Completed - ${subtaskDisplayId}: ${subtask.name}`;
          message = `Subtask "${subtask.name}" (${subtaskDisplayId}) has been completed successfully${subtask.user ? ` by ${subtask.user.name}` : ''}`;
          priority = 'MEDIUM';
          break;
      }

      // Implement hierarchical notification system for subtasks
      const alertRecipients = new Set<string>();

      if (type === 'ASSIGNED') {
        // ASSIGNED notifications: Only to assignee + their immediate superior
        if (subtask.assigneeId) {
          alertRecipients.add(subtask.assigneeId);

          // Add assignee's immediate superior
          const superior = await this.findUserSuperior(subtask.assigneeId);
          if (superior) {
            alertRecipients.add(superior);
          }
        }
      } else if (type === 'CREATED') {
        // CREATED notifications: Only to subordinates when something is created for them
        if (userId) {
          const subordinates = await this.findUserSubordinates(userId);
          subordinates.forEach(subordinateId => alertRecipients.add(subordinateId));
        }
      } else {
        // For other types (UPDATED, COMPLETED, DEADLINE, DELAYED), use existing logic
        // Add subtask assignee
        if (subtask.assigneeId) {
          alertRecipients.add(subtask.assigneeId);
        }

        // Add task assignee (if different from subtask assignee)
        if (subtask.task.assigneeId && subtask.task.assigneeId !== subtask.assigneeId) {
          alertRecipients.add(subtask.task.assigneeId);
        }

        // Add project manager (if different from both assignees)
        if (subtask.task.project.projectManagerId &&
            subtask.task.project.projectManagerId !== subtask.assigneeId &&
            subtask.task.project.projectManagerId !== subtask.task.assigneeId) {
          alertRecipients.add(subtask.task.project.projectManagerId);
        }
      }

      // Create alerts for all unique recipients
      for (const recipientId of alertRecipients) {
        await this.createAlert({
          type: alertType,
          priority,
          title,
          message,
          relatedTo: subtaskId,
          relatedType: 'SUBTASK',
          assigneeId: recipientId,
          department: subtask.task.project.department,
          dueDate: type === 'DEADLINE' ? subtask.endDate : undefined,
          createdBy: userId,
        });
      }


    } catch (error) {
      console.error('Error creating subtask alert:', error);
    }
  }

  // MOM-related alerts
  static async createMOMAlert(momId: string, type: 'CREATED' | 'ACTION_ITEM_ASSIGNED', userId?: string) {
    try {
      const mom = await prisma.mom.findUnique({
        where: { id: momId },
        include: {
          project: {
            include: {
              user_project_projectManagerIdTouser: true,
              customer: true,
            },
          },
          user_mom_createdByTouser: true,
          mompoint: true,
        },
      });

      if (!mom) return;

      let title = '';
      let message = '';
      let priority: 'MEDIUM' = 'MEDIUM';

      switch (type) {
        case 'CREATED':
          title = `Minutes of Meeting created for ${mom.project?.customer?.name || 'Unknown Customer'}${mom.project ? ` - ${mom.project.name}` : ''}`;
          message = `Meeting document created for project ${mom.project?.code || 'Unknown Project'}`;
          if (mom.mompoint && mom.mompoint.length > 0) {
            message += `. ${mom.mompoint.length} discussion point${mom.mompoint.length > 1 ? 's' : ''} recorded`;
          }
          priority = 'MEDIUM';
          break;
        case 'ACTION_ITEM_ASSIGNED':
          title = 'MOM Action Items Assigned';
          message = `Action items from MOM have been assigned and require attention`;
          priority = 'MEDIUM';
          break;
      }

      // Create alert for project manager if project exists
      if (mom.project) {
        await this.createAlert({
          type: 'MOM',
          priority,
          title,
          message,
          relatedTo: momId,
          relatedType: 'MOM',
          assigneeId: mom.project.projectManagerId,
          department: mom.project.department,
          createdBy: userId,
        });
      }

      // Note: Action item alerts are now handled separately since action items
      // are no longer directly part of MOM but are separate entities
    } catch (error) {
      console.error('Error creating MOM alert:', error);
    }
  }

  // System alerts
  static async createSystemAlert(title: string, message: string, priority: 'MEDIUM' = 'MEDIUM', department?: string) {
    try {
      // Create system alert for all users or specific department
      const users = await prisma.user.findMany({
        where: department ? { department } : {},
      });

      for (const user of users) {
        await this.createAlert({
          type: 'SYSTEM',
          priority,
          title,
          message,
          assigneeId: user.id,
          department: user.department,
        });
      }
    } catch (error) {
      console.error('Error creating system alert:', error);
    }
  }

  // Check for deadline alerts (to be run periodically)
  static async checkDeadlineAlerts() {
    try {
      const now = new Date();
      const threeDaysFromNow = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000);
      const oneDayFromNow = new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000);

      // Check project deadlines (only for projects with endDate)
      const upcomingProjects = await prisma.project.findMany({
        where: {
          endDate: {
            not: null,
            gte: now,
            lte: threeDaysFromNow,
          },
          status: {
            in: ['NOT_STARTED', 'IN_PROGRESS'],
          },
        },
      });

      for (const project of upcomingProjects) {
        await this.createProjectAlert(project.id, 'DEADLINE');
      }

      // Check task deadlines
      const upcomingTasks = await prisma.task.findMany({
        where: {
          endDate: {
            gte: now,
            lte: oneDayFromNow,
          },
          status: {
            in: ['NOT_STARTED', 'IN_PROGRESS'],
          },
        },
      });

      for (const task of upcomingTasks) {
        await this.createTaskAlert(task.id, 'DEADLINE');
      }

      // Check subtask deadlines
      const upcomingSubtasks = await prisma.subtask.findMany({
        where: {
          endDate: {
            gte: now,
            lte: oneDayFromNow,
          },
          status: {
            in: ['NOT_STARTED', 'IN_PROGRESS'],
          },
        },
      });

      for (const subtask of upcomingSubtasks) {
        await this.createSubtaskAlert(subtask.id, 'DEADLINE');
      }

      // Check for overdue tasks and subtasks (exclude COMPLETED and ON_HOLD)
      const overdueTasks = await prisma.task.findMany({
        where: {
          endDate: {
            lt: now,
          },
          status: {
            in: ['NOT_STARTED', 'IN_PROGRESS'],
          },
        },
      });

      for (const task of overdueTasks) {
        await this.createTaskAlert(task.id, 'DELAYED');
      }

      const overdueSubtasks = await prisma.subtask.findMany({
        where: {
          endDate: {
            lt: now,
          },
          status: {
            in: ['NOT_STARTED', 'IN_PROGRESS'],
          },
        },
      });

      for (const subtask of overdueSubtasks) {
        await this.createSubtaskAlert(subtask.id, 'DELAYED');
      }

      console.log(`Checked deadlines: ${upcomingProjects.length} projects, ${upcomingTasks.length} tasks, ${upcomingSubtasks.length} subtasks`);
      console.log(`Found overdue: ${overdueTasks.length} tasks, ${overdueSubtasks.length} subtasks`);
    } catch (error) {
      console.error('Error checking deadline alerts:', error);
    }
  }

  // Mark alert as read
  static async markAsRead(alertId: string) {
    try {
      return await prisma.alert.update({
        where: { id: alertId },
        data: { read: true },
      });
    } catch (error) {
      console.error('Error marking alert as read:', error);
      throw error;
    }
  }

  // Mark alert as resolved
  static async markAsResolved(alertId: string) {
    try {
      return await prisma.alert.update({
        where: { id: alertId },
        data: { resolved: true },
      });
    } catch (error) {
      console.error('Error marking alert as resolved:', error);
      throw error;
    }
  }
}