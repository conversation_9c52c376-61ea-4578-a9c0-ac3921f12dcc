import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { currentUserAtom } from '../store';
import { settingsAPI, authAPI } from '../services/api';
import {
  User,
  Bell,
  Key,
  Save,
  CheckCircle,
  AlertCircle,
  Settings
} from 'lucide-react';

const SettingsPage = () => {
  console.log('🎯 SettingsPage component loaded');

  // Get current user from store
  const [currentUser, setCurrentUser] = useAtom(currentUserAtom);
  console.log('🎯 SettingsPage currentUser from store:', currentUser);

  // State for different settings sections
  const [activeTab, setActiveTab] = useState('profile');

  // Profile settings
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');

  // Password settings
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');

  // Notification settings
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(true);
  const [taskReminders, setTaskReminders] = useState(true);
  const [projectUpdates, setProjectUpdates] = useState(true);
  const [securityAlerts, setSecurityAlerts] = useState(true);

  // UI state
  const [showSuccess, setShowSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);

  // Load user profile data on component mount
  useEffect(() => {
    const loadUserProfile = async () => {
      console.log('🚀 SettingsPage useEffect triggered, currentUser:', currentUser);
      console.log('🔍 localStorage user:', localStorage.getItem('user'));
      console.log('🔍 localStorage token:', localStorage.getItem('token'));

      try {
        setIsLoadingProfile(true);

        // Try to load user data from API first
        console.log('📡 Loading user profile from API...');
        const profileResponse = await settingsAPI.getProfile();
        console.log('✅ Profile response:', profileResponse);

        if (profileResponse.success && profileResponse.data) {
          const userData = profileResponse.data;
          setName(userData.name || '');
          setEmail(userData.email || '');
          console.log('📝 Set profile data from API:', { name: userData.name, email: userData.email });
        } else if (currentUser) {
          // Fallback to currentUser data
          console.log('📋 Falling back to currentUser data:', currentUser);
          setName(currentUser.name || '');
          setEmail(currentUser.email || '');
        }

        // Load user settings (notification preferences)
        console.log('📡 Loading user notification settings...');
        const settingsResponse = await settingsAPI.getNotificationSettings();
        console.log('⚙️ Settings response:', settingsResponse);
        if (settingsResponse.success && settingsResponse.data) {
          const settings = settingsResponse.data;
          setEmailNotifications(settings.emailNotifications ?? true);
          setPushNotifications(settings.pushNotifications ?? true);
          setTaskReminders(settings.taskReminders ?? true);
          setProjectUpdates(settings.projectUpdates ?? true);
          setSecurityAlerts(settings.securityAlerts ?? true);
        }
      } catch (error) {
        console.error('❌ Error loading user profile/settings:', error);
        console.error('Error details:', error.response?.data || error.message);

        // Fallback to currentUser data if API fails
        if (currentUser) {
          console.log('🔄 Using currentUser data due to API error:', currentUser);
          setName(currentUser.name || '');
          setEmail(currentUser.email || '');
        }
        setError(`Failed to load profile data: ${error.response?.data?.message || error.message}`);
      } finally {
        setIsLoadingProfile(false);
        console.log('✅ Settings loading completed');
      }
    };

    loadUserProfile();
  }, []); // Remove dependency on currentUser to force API call

  // Handle profile update
  const handleProfileUpdate = async (e) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      // Update profile via API
      const response = await settingsAPI.updateProfile({
        name: name.trim(),
        email: email.trim().toLowerCase()
      });

      // Update current user state with new data
      setCurrentUser(response.data);

      // Update localStorage
      localStorage.setItem('user', JSON.stringify(response.data));

      // Show success message
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 3000);
    } catch (error) {
      console.error('Error updating profile:', error);
      setError(error.response?.data?.message || 'Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle password change
  const handlePasswordChange = async (e) => {
    e.preventDefault();
    setPasswordError('');
    setError(null);
    setIsLoading(true);

    if (newPassword !== confirmPassword) {
      setPasswordError('New passwords do not match');
      setIsLoading(false);
      return;
    }

    if (newPassword.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      setIsLoading(false);
      return;
    }

    try {
      // Change password via API
      await settingsAPI.changePassword(currentPassword, newPassword);

      // Clear form
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');

      // Show success message
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 3000);
    } catch (error) {
      console.error('Error changing password:', error);
      setPasswordError(error.response?.data?.message || 'Failed to change password');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle notification settings update
  const handleNotificationUpdate = async (e) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      // Update notification settings via API
      const settingsData = {
        emailNotifications,
        pushNotifications,
        taskReminders,
        projectUpdates,
        securityAlerts
      };

      await settingsAPI.updateNotificationSettings(settingsData);

      // Show success message
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 3000);
    } catch (error) {
      console.error('Error updating notification settings:', error);
      setError(error.response?.data?.message || 'Failed to update notification settings');
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading or login prompt if no user (temporarily disabled for debugging)
  // if (!currentUser) {
  //   return (
  //     <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
  //       <div className="text-center">
  //         <div className="p-4 bg-white rounded-lg shadow-lg">
  //           <AlertCircle className="mx-auto h-12 w-12 text-amber-500 mb-4" />
  //           <h2 className="text-lg font-semibold text-slate-800 mb-2">Loading...</h2>
  //           <p className="text-slate-600 text-sm">Please wait while we load your settings.</p>
  //         </div>
  //       </div>
  //     </div>
  //   );
  // }

  // Debug information
  console.log('🔍 SettingsPage render - currentUser:', currentUser);
  console.log('🔍 SettingsPage render - name state:', name);
  console.log('🔍 SettingsPage render - email state:', email);
  console.log('🔍 SettingsPage render - localStorage user:', localStorage.getItem('user'));

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="w-full space-y-6 pt-4 pb-8">
        {/* Professional Header - UNCHANGED */}
        <div className="relative mx-6">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-indigo-600/10 rounded-xl blur-xl transform -rotate-1"></div>
          <div className="relative bg-white/95 backdrop-blur-sm rounded-xl p-6 shadow-[0_8px_32px_rgba(31,_38,_135,_0.15)] border border-white/20">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-[0_8px_24px_rgba(59,_130,_246,_0.4)]">
                <Settings className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                  Account Settings
                </h1>
                <p className="text-slate-600 text-sm">
                  Manage your project management preferences
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Professional Success/Error Messages */}
        {showSuccess && (
          <div className="mx-6">
            <div className="relative group">
              <div className="absolute inset-0 bg-emerald-100 rounded-lg blur-sm transform translate-y-1"></div>
              <div className="relative bg-gradient-to-r from-emerald-500 to-green-600 text-white p-4 rounded-lg flex items-center shadow-[0_6px_20px_rgba(16,_185,_129,_0.25)] transform hover:scale-[1.01] transition-all duration-200">
                <div className="p-1.5 bg-white/20 rounded-md mr-3">
                  <CheckCircle size={18} />
                </div>
                <span className="font-medium text-sm">Settings updated successfully!</span>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mx-6">
            <div className="relative group">
              <div className="absolute inset-0 bg-red-100 rounded-lg blur-sm transform translate-y-1"></div>
              <div className="relative bg-gradient-to-r from-red-500 to-rose-600 text-white p-4 rounded-lg flex items-center shadow-[0_6px_20px_rgba(239,_68,_68,_0.25)] transform hover:scale-[1.01] transition-all duration-200">
                <div className="p-1.5 bg-white/20 rounded-md mr-3">
                  <AlertCircle size={18} />
                </div>
                <span className="font-medium text-sm">{error}</span>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced 3D Main Content */}
        <div className="relative mx-6">
          <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-slate-100/40 rounded-2xl blur-lg transform translate-y-2 scale-[1.02]"></div>
          <div className="relative bg-white/95 backdrop-blur-sm rounded-2xl shadow-[0_20px_40px_rgba(0,_0,_0,_0.08),_0_1px_2px_rgba(0,_0,_0,_0.05)] border border-white/60 overflow-hidden transform-gpu">
            
            {/* Subtle top accent */}
            <div className="h-0.5 bg-gradient-to-r from-blue-400 via-purple-400 to-indigo-400"></div>
            
            <div className="flex flex-col lg:flex-row">
              
              {/* Professional Light Sidebar with 3D Effects */}
              <div className="w-full lg:w-72 bg-gradient-to-b from-slate-100 to-slate-200 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50"></div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-100/30 to-transparent rounded-full blur-2xl"></div>
                
                <nav className="relative p-6 space-y-2">
                  <div className="mb-6">
                    <h3 className="text-slate-600 text-xs font-semibold uppercase tracking-wider">Settings Panel</h3>
                    <div className="w-8 h-0.5 bg-gradient-to-r from-blue-400 to-indigo-400 mt-1.5 rounded-full"></div>
                  </div>
                  
                  {[
                    { id: 'profile', icon: User, label: 'Profile', color: 'from-blue-500 to-blue-600', bgColor: 'bg-blue-50', hoverBg: 'hover:bg-blue-100/70' },
                    { id: 'password', icon: Key, label: 'Security', color: 'from-purple-500 to-purple-600', bgColor: 'bg-purple-50', hoverBg: 'hover:bg-purple-100/70' },
                    { id: 'notifications', icon: Bell, label: 'Notifications', color: 'from-emerald-500 to-emerald-600', bgColor: 'bg-emerald-50', hoverBg: 'hover:bg-emerald-100/70' }
                  ].map(({ id, icon: Icon, label, color, bgColor, hoverBg }) => (
                    <button
                      key={id}
                      className={`group relative w-full flex items-center px-4 py-3 text-left font-medium rounded-xl transition-all duration-300 transform ${
                        activeTab === id
                          ? `bg-gradient-to-r ${color} text-white shadow-[0_4px_12px_rgba(0,_0,_0,_0.15),_inset_0_1px_0_rgba(255,_255,_255,_0.2)] scale-[1.02] translate-x-1`
                          : `text-slate-700 ${hoverBg} hover:scale-[1.01] hover:translate-x-0.5 hover:shadow-[0_2px_8px_rgba(0,_0,_0,_0.08)]`
                      }`}
                      onClick={() => setActiveTab(id)}
                    >
                      <div className={`absolute inset-0 rounded-xl transition-all duration-300 ${
                        activeTab === id ? '' : `${bgColor} opacity-0 group-hover:opacity-100`
                      }`}></div>
                      <div className={`relative p-2 rounded-lg mr-3 transition-all duration-300 ${
                        activeTab === id 
                          ? 'bg-white/25 shadow-inner' 
                          : 'bg-white/60 group-hover:bg-white/80 group-hover:shadow-sm'
                      }`}>
                        <Icon size={16} />
                      </div>
                      <span className="relative text-sm">{label}</span>
                      {activeTab === id && (
                        <div className="ml-auto w-1.5 h-1.5 bg-white/70 rounded-full"></div>
                      )}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Enhanced Content Area with Subtle 3D */}
              <div className="flex-1 p-8 bg-gradient-to-br from-white to-slate-50/30">
                
                {/* Profile Settings */}
                {activeTab === 'profile' && (
                  <div className="space-y-6">
                    <div className="relative">
                      <h2 className="text-lg font-semibold text-slate-800 mb-2">Profile Information</h2>
                      <div className="w-16 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full shadow-sm"></div>
                      <p className="text-slate-600 text-xs mt-1">Update your personal information</p>
                    </div>
                    
                    {isLoadingProfile ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="flex items-center space-x-3">
                          <svg className="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          <span className="text-slate-600 text-sm">Loading profile...</span>
                        </div>
                      </div>
                    ) : (
                      <form onSubmit={handleProfileUpdate} className="space-y-5">
                        <div className="grid gap-5 md:grid-cols-2">
                          <div className="group">
                            <label htmlFor="name" className="block text-xs font-medium text-slate-700 mb-2">
                              Full Name *
                            </label>
                            <div className="relative">
                              <div className="absolute inset-0 bg-blue-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                              <input
                                type="text"
                                id="name"
                                className="relative w-full px-4 py-2.5 bg-white border border-slate-200 rounded-lg text-sm text-slate-800 placeholder-slate-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 focus:shadow-[0_4px_12px_rgba(59,_130,_246,_0.1)] transition-all duration-200 transform focus:scale-[1.01]"
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                                placeholder="Enter your full name"
                                required
                              />
                            </div>
                          </div>

                          <div className="group">
                            <label htmlFor="email" className="block text-xs font-medium text-slate-700 mb-2">
                              Email Address *
                            </label>
                            <div className="relative">
                              <div className="absolute inset-0 bg-blue-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                              <input
                                type="email"
                                id="email"
                                className="relative w-full px-4 py-2.5 bg-white border border-slate-200 rounded-lg text-sm text-slate-800 placeholder-slate-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 focus:shadow-[0_4px_12px_rgba(59,_130,_246,_0.1)] transition-all duration-200 transform focus:scale-[1.01]"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                placeholder="Enter your email address"
                                required
                              />
                            </div>
                          </div>
                        </div>



                      <div className="pt-2">
                        <button
                          type="submit"
                          className="relative group px-6 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-sm font-medium rounded-lg shadow-[0_4px_12px_rgba(59,_130,_246,_0.25)] hover:shadow-[0_6px_16px_rgba(59,_130,_246,_0.35)] transform hover:scale-[1.02] hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none overflow-hidden"
                          disabled={isLoading}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                          <div className="relative flex items-center">
                            {isLoading ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Saving...
                              </>
                            ) : (
                              <>
                                <Save size={16} className="mr-2" />
                                Save Changes
                              </>
                            )}
                          </div>
                        </button>
                      </div>
                    </form>
                    )}
                  </div>
                )}

                {/* Password Settings */}
                {activeTab === 'password' && (
                  <div className="space-y-6">
                    <div className="relative">
                      <h2 className="text-lg font-semibold text-slate-800 mb-2">Security Settings</h2>
                      <div className="w-16 h-0.5 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full shadow-sm"></div>
                      <p className="text-slate-600 text-xs mt-1">Update your account password</p>
                    </div>
                    
                    <form onSubmit={handlePasswordChange} className="space-y-5">
                      <div className="space-y-4">
                        <div className="group">
                          <label htmlFor="currentPassword" className="block text-xs font-medium text-slate-700 mb-2">
                            Current Password *
                          </label>
                          <div className="relative">
                            <div className="absolute inset-0 bg-purple-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                            <input
                              type="password"
                              id="currentPassword"
                              className="relative w-full px-4 py-2.5 bg-white border border-slate-200 rounded-lg text-sm text-slate-800 placeholder-slate-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 focus:shadow-[0_4px_12px_rgba(147,_51,_234,_0.1)] transition-all duration-200 transform focus:scale-[1.01]"
                              value={currentPassword}
                              onChange={(e) => setCurrentPassword(e.target.value)}
                              placeholder="Enter current password"
                              required
                            />
                          </div>
                        </div>

                        <div className="group">
                          <label htmlFor="newPassword" className="block text-xs font-medium text-slate-700 mb-2">
                            New Password *
                          </label>
                          <div className="relative">
                            <div className="absolute inset-0 bg-purple-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                            <input
                              type="password"
                              id="newPassword"
                              className="relative w-full px-4 py-2.5 bg-white border border-slate-200 rounded-lg text-sm text-slate-800 placeholder-slate-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 focus:shadow-[0_4px_12px_rgba(147,_51,_234,_0.1)] transition-all duration-200 transform focus:scale-[1.01]"
                              value={newPassword}
                              onChange={(e) => setNewPassword(e.target.value)}
                              placeholder="Enter new password"
                              required
                            />
                          </div>
                        </div>

                        <div className="group">
                          <label htmlFor="confirmPassword" className="block text-xs font-medium text-slate-700 mb-2">
                            Confirm New Password *
                          </label>
                          <div className="relative">
                            <div className="absolute inset-0 bg-purple-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                            <input
                              type="password"
                              id="confirmPassword"
                              className="relative w-full px-4 py-2.5 bg-white border border-slate-200 rounded-lg text-sm text-slate-800 placeholder-slate-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 focus:shadow-[0_4px_12px_rgba(147,_51,_234,_0.1)] transition-all duration-200 transform focus:scale-[1.01]"
                              value={confirmPassword}
                              onChange={(e) => setConfirmPassword(e.target.value)}
                              placeholder="Confirm new password"
                              required
                            />
                          </div>
                        </div>
                      </div>

                      {passwordError && (
                        <div className="relative">
                          <div className="absolute inset-0 bg-red-50 rounded-lg blur-sm"></div>
                          <div className="relative bg-red-50 border border-red-200 p-4 rounded-lg shadow-sm">
                            <p className="text-red-700 text-xs font-medium flex items-center">
                              <AlertCircle size={14} className="mr-2" />
                              {passwordError}
                            </p>
                          </div>
                        </div>
                      )}

                      <div className="pt-2">
                        <button
                          type="submit"
                          className="relative group px-6 py-2.5 bg-gradient-to-r from-purple-600 to-purple-700 text-white text-sm font-medium rounded-lg shadow-[0_4px_12px_rgba(147,_51,_234,_0.25)] hover:shadow-[0_6px_16px_rgba(147,_51,_234,_0.35)] transform hover:scale-[1.02] hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none overflow-hidden"
                          disabled={isLoading}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                          <div className="relative flex items-center">
                            {isLoading ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Updating...
                              </>
                            ) : (
                              <>
                                <Key size={16} className="mr-2" />
                                Update Password
                              </>
                            )}
                          </div>
                        </button>
                      </div>
                    </form>
                  </div>
                )}

                {/* Notification Settings */}
                {activeTab === 'notifications' && (
                  <div className="space-y-6">
                    <div className="relative">
                      <h2 className="text-lg font-semibold text-slate-800 mb-2">Notification Preferences</h2>
                      <div className="w-16 h-0.5 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full shadow-sm"></div>
                      <p className="text-slate-600 text-xs mt-1">Configure your notification settings</p>
                    </div>
                    
                    <form onSubmit={handleNotificationUpdate} className="space-y-4">
                      <div className="grid gap-3">
                        {[
                          { id: 'emailNotifications', label: 'Email Notifications', desc: 'Receive project updates via email', state: emailNotifications, setState: setEmailNotifications },
                          { id: 'pushNotifications', label: 'Push Notifications', desc: 'Browser notifications for real-time updates', state: pushNotifications, setState: setPushNotifications },
                          { id: 'taskReminders', label: 'Task Reminders', desc: 'Deadline and task completion alerts', state: taskReminders, setState: setTaskReminders },
                          { id: 'projectUpdates', label: 'Project Updates', desc: 'Status changes and milestone notifications', state: projectUpdates, setState: setProjectUpdates },
                          { id: 'securityAlerts', label: 'Security Alerts', desc: 'Account security and login notifications', state: securityAlerts, setState: setSecurityAlerts }
                        ].map(({ id, label, desc, state, setState }) => (
                          <div key={id} className="group relative">
                            <div className="absolute inset-0 bg-emerald-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 blur-sm"></div>
                            <div className="relative bg-white border border-slate-200 rounded-lg p-4 hover:border-emerald-300 hover:shadow-[0_4px_12px_rgba(16,_185,_129,_0.08)] transform hover:scale-[1.01] transition-all duration-200">
                              <div className="flex items-start space-x-3">
                                <input
                                  type="checkbox"
                                  id={id}
                                  className="w-4 h-4 text-emerald-600 bg-white border-2 border-slate-300 rounded focus:ring-emerald-500 focus:ring-2 mt-0.5 transition-all duration-200"
                                  checked={state}
                                  onChange={(e) => setState(e.target.checked)}
                                />
                                <div className="flex-1">
                                  <label htmlFor={id} className="block text-sm font-medium text-slate-800 cursor-pointer mb-0.5">
                                    {label}
                                  </label>
                                  <p className="text-slate-600 text-xs leading-relaxed">{desc}</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="pt-4">
                        <button
                          type="submit"
                          className="relative group px-6 py-2.5 bg-gradient-to-r from-emerald-600 to-emerald-700 text-white text-sm font-medium rounded-lg shadow-[0_4px_12px_rgba(16,_185,_129,_0.25)] hover:shadow-[0_6px_16px_rgba(16,_185,_129,_0.35)] transform hover:scale-[1.02] hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none overflow-hidden"
                          disabled={isLoading}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                          <div className="relative flex items-center">
                            {isLoading ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Saving...
                              </>
                            ) : (
                              <>
                                <Save size={16} className="mr-2" />
                                Save Preferences
                              </>
                            )}
                          </div>
                        </button>
                      </div>
                    </form>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;