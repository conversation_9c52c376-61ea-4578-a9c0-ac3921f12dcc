import React from 'react';
import { use<PERSON>tom } from 'jotai';
import { currentUserAtom } from '../store';
import { UserRole } from '../types';
import TaskTable from '../components/Tasks/TaskTable';
import { List, LayoutGrid, Calendar, CheckSquare } from 'lucide-react';

const AssignedTasksPage: React.FC = () => {
  const [currentUser] = useAtom(currentUserAtom);

  // Get role-specific title and description
  const getPageContent = () => {
    switch (currentUser?.role) {
      case UserRole.TEAM_LEAD:
        return {
          title: "My Tasks",
          description: "View and manage tasks assigned to you and create subtasks for your team"
        };
      case UserRole.PROJECT_MANAGER:
        return {
          title: "Project Tasks",
          description: "View and manage all tasks across your projects"
        };
      case UserRole.DIRECTOR:
        return {
          title: "All Tasks",
          description: "View and manage all tasks across the organization"
        };
      default:
        return {
          title: "Tasks",
          description: "View and manage tasks"
        };
    }
  };

  const { title, description } = getPageContent();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-1">{title}</h1>
          <p className="text-gray-600">{description}</p>
        </div>
        <div className="flex items-center space-x-2">
          <button className="btn btn-sm btn-outline">
            <List size={16} className="mr-1" />
            List
          </button>
          <button className="btn btn-sm btn-outline">
            <LayoutGrid size={16} className="mr-1" />
            Board
          </button>
        </div>
      </div>

      {/* Role-specific info for Team Leads */}
      {currentUser?.role === UserRole.TEAM_LEAD && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <CheckSquare className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-blue-900">Team Lead Tasks</h3>
              <p className="text-sm text-blue-700 mt-1">
                As a Team Lead, you can view tasks assigned to you, create subtasks for engineers,
                and manage task progress. Use the "Add" button next to tasks to create subtasks for your team members.
              </p>
            </div>
          </div>
        </div>
      )}

      <TaskTable />
    </div>
  );
};

export default AssignedTasksPage;