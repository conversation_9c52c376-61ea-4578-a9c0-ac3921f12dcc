import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { currentUserAtom, isAuthenticatedAtom } from '../store';
import { api } from '../services/api';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  department: string;
  profileImage?: string;
  passwordChanged?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (user: User) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useAtom(currentUserAtom);
  const [isAuthenticated, setIsAuthenticated] = useAtom(isAuthenticatedAtom);

  const login = async (email: string, password: string) => {
    const response = await api.login(email, password);
    const { token, ...userData } = response.data;
    
    localStorage.setItem('token', token);
    setCurrentUser(userData);
    setIsAuthenticated(true);
  };

  const logout = () => {
    localStorage.removeItem('token');
    setCurrentUser(null);
    setIsAuthenticated(false);
  };

  const updateUser = (user: User) => {
    setCurrentUser(user);
  };

  return (
    <AuthContext.Provider value={{ user: currentUser, isAuthenticated, login, logout, updateUser }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
