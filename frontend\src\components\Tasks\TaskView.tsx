import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAtom } from 'jotai';
import { engineersAtom, userTasksAtom, projectsAtom } from '../../store';
import { Task, TaskStatus } from '../../types';
import { formatDate } from '../../utils/dateFormatter';
import { tasksAPI } from '../../services/api';
import { taskCommentsAPI, TaskComment } from '../../services/commentAPI';
import CommentSection from '../Comments/CommentSection';
import {
  ArrowLeft,
  Calendar,
  Clock,
  CheckCircle,
  Circle,
  AlertCircle,
  PauseCircle,
  PlayCircle,
  User,
  FolderOpen,
  Star,
  Target,
  Building,
  Edit3,
  Check,
  X
} from 'lucide-react';

const TaskView: React.FC = () => {
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();
  const [engineers] = useAtom(engineersAtom);
  const [userTasks] = useAtom(userTasksAtom);
  const [projects, setProjects] = useAtom(projectsAtom);
  const [task, setTask] = useState<Task | null>(null);
  const [loading, setLoading] = useState(true);
  const [comments, setComments] = useState<TaskComment[]>([]);
  const [commentsLoading, setCommentsLoading] = useState(true);
  const [editingDescription, setEditingDescription] = useState(false);
  const [editedDescription, setEditedDescription] = useState('');
  const [updatingDescription, setUpdatingDescription] = useState(false);

  useEffect(() => {
    const loadTaskAndComments = async () => {
      if (!taskId) return;

      setLoading(true);
      setCommentsLoading(true);

      try {
        // First try to find task in local state for immediate display
        const foundTask = userTasks.find(t => t.id === taskId);
        if (foundTask) {
          setTask(foundTask);
        }

        // Then fetch fresh data from API to ensure we have the latest
        try {
          const freshTask = await tasksAPI.getTask(taskId);
          setTask(freshTask.data);
        } catch (error) {
          console.error('Error loading fresh task data:', error);
          // If API fails, fall back to local data
          if (!foundTask) {
            console.error('Task not found in local state either');
          }
        }

        // Load comments
        try {
          const taskComments = await taskCommentsAPI.getTaskComments(taskId);
          setComments(taskComments);
        } catch (error) {
          console.error('Error loading comments:', error);
          setComments([]);
        }
      } catch (error) {
        console.error('Error loading task:', error);
      } finally {
        setLoading(false);
        setCommentsLoading(false);
      }
    };

    loadTaskAndComments();
  }, [taskId]);

  const getEngineerName = (id: string) => {
    const engineer = engineers.find(e => e.id === id);
    return engineer ? engineer.name : 'Unassigned';
  };

  const getProjectName = (id: string) => {
    const project = projects.find(p => p.id === id);
    return project ? project.name : 'Unknown Project';
  };

  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.COMPLETED:
        return <CheckCircle size={18} className="text-green-600" />;
      case TaskStatus.IN_PROGRESS:
        return <PlayCircle size={18} className="text-blue-600" />;
      case TaskStatus.NOT_STARTED:
        return <Circle size={18} className="text-gray-400" />;
      case TaskStatus.DELAYED:
        return <AlertCircle size={18} className="text-red-600" />;
      case TaskStatus.ON_HOLD:
        return <PauseCircle size={18} className="text-yellow-600" />;
      default:
        return <Circle size={18} className="text-gray-400" />;
    }
  };

  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.COMPLETED:
        return 'from-green-500 to-green-600';
      case TaskStatus.IN_PROGRESS:
        return 'from-blue-500 to-blue-600';
      case TaskStatus.NOT_STARTED:
        return 'from-gray-400 to-gray-500';
      case TaskStatus.DELAYED:
        return 'from-red-500 to-red-600';
      case TaskStatus.ON_HOLD:
        return 'from-yellow-500 to-yellow-600';
      default:
        return 'from-gray-400 to-gray-500';
    }
  };

  const formatDateDisplay = (dateStr: string) => {
    try {
      return formatDate(dateStr);
    } catch (error) {
      return 'Invalid date';
    }
  };



  // Description handlers
  const handleEditDescription = () => {
    setEditingDescription(true);
    setEditedDescription(task?.description || '');
  };

  const handleSaveDescription = async () => {
    if (!taskId || !task) return;

    setUpdatingDescription(true);
    try {
      const updatedTask = await tasksAPI.updateTask(taskId, { description: editedDescription });
      setTask(updatedTask.data);

      // Update the global projects state to reflect the description change
      setProjects(prevProjects => {
        return prevProjects.map(project => {
          if (project.id === task.projectId) {
            return {
              ...project,
              tasks: project.tasks?.map(t =>
                t.id === taskId ? { ...t, description: editedDescription } : t
              ) || []
            };
          }
          return project;
        });
      });

      setEditingDescription(false);
    } catch (error) {
      console.error('Error updating description:', error);
    } finally {
      setUpdatingDescription(false);
    }
  };

  const handleCancelDescription = () => {
    setEditingDescription(false);
    setEditedDescription('');
  };

  // Comment handlers
  const handleAddComment = async (content: string) => {
    if (!taskId) return;

    try {
      const newComment = await taskCommentsAPI.createTaskComment(taskId, { content });
      setComments(prev => {
        const safePrev = Array.isArray(prev) ? prev : [];
        return [...safePrev, newComment];
      });
    } catch (error) {
      console.error('Error adding comment:', error);
      throw error;
    }
  };

  const handleUpdateComment = async (commentId: string, content: string) => {
    if (!taskId) return;

    try {
      const updatedComment = await taskCommentsAPI.updateTaskComment(taskId, commentId, { content });
      setComments(prev => {
        const safePrev = Array.isArray(prev) ? prev : [];
        return safePrev.map(comment =>
          comment.id === commentId ? updatedComment : comment
        );
      });
    } catch (error) {
      console.error('Error updating comment:', error);
      throw error;
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (!taskId) return;

    try {
      await taskCommentsAPI.deleteTaskComment(taskId, commentId);
      setComments(prev => {
        const safePrev = Array.isArray(prev) ? prev : [];
        return safePrev.filter(comment => comment.id !== commentId);
      });
    } catch (error) {
      console.error('Error deleting comment:', error);
      throw error;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600 mx-auto shadow-lg"></div>
            <div className="absolute inset-0 rounded-full bg-blue-100 opacity-20 animate-ping"></div>
          </div>
          <p className="text-gray-600 mt-4 font-medium">Loading task details...</p>
        </div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white flex items-center justify-center">
        <div className="text-center bg-white p-8 rounded-xl shadow-lg">
          <AlertCircle size={48} className="mx-auto text-red-500 mb-4" />
          <p className="text-gray-600 font-medium">Task not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="w-full pt-2 pb-6">
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mx-4">
          {/* Enhanced Header */}
          <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <button
                  className="mr-4 p-2 text-gray-500 hover:text-gray-700 hover:bg-white rounded-lg transition-all duration-200 transform hover:scale-105 shadow-md"
                  onClick={() => navigate(-1)}
                >
                  <ArrowLeft size={20} />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-800 mb-1">{task.name}</h1>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <span className="bg-white px-3 py-1 rounded-full shadow-sm">
                      {task.displayId || task.id}
                    </span>
                    <span>•</span>
                    <span>{getProjectName(task.projectId)}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className={`flex items-center space-x-2 px-4 py-2 bg-gradient-to-r ${getStatusColor(task.status)} rounded-full text-white shadow-lg transform hover:scale-105 transition-all duration-200`}>
                  {getStatusIcon(task.status)}
                  <span className="font-medium">{task.status.replace(/_/g, ' ')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Details Section */}
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Details */}
              <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-md border border-gray-100 p-6 transform hover:scale-[1.01] transition-all duration-200">
                <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-800">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 shadow-lg">
                    <Target size={16} className="text-white" />
                  </div>
                  Task Details
                </h2>
                <div className="space-y-4">
                  <div className="flex items-center p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
                    <span className="w-24 text-gray-600 font-medium">Assignee:</span>
                    <div className="flex items-center ml-4">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center mr-2 shadow-md">
                        <User size={14} className="text-white" />
                      </div>
                      <span className="text-gray-800 font-medium">{getEngineerName(task.assigneeId)}</span>
                    </div>
                  </div>

                  <div className="flex items-center p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
                    <span className="w-24 text-gray-600 font-medium">Department:</span>
                    <div className="flex items-center ml-4">
                      <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-500 rounded-full flex items-center justify-center mr-2 shadow-md">
                        <Building size={14} className="text-white" />
                      </div>
                      <span className="text-gray-800 font-medium">{task.department}</span>
                    </div>
                  </div>

                  <div className="flex items-center p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
                    <span className="w-24 text-gray-600 font-medium">Project:</span>
                    <div className="flex items-center ml-4">
                      <div className="w-8 h-8 bg-gradient-to-br from-indigo-400 to-indigo-500 rounded-full flex items-center justify-center mr-2 shadow-md">
                        <FolderOpen size={14} className="text-white" />
                      </div>
                      <span className="text-gray-800 font-medium">{getProjectName(task.projectId)}</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mr-2 shadow-md">
                          <Calendar size={14} className="text-white" />
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Start Date</p>
                          <p className="text-gray-800 font-medium">{formatDateDisplay(task.startDate)}</p>
                        </div>
                      </div>
                    </div>

                    <div className="p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center mr-2 shadow-md">
                          <Calendar size={14} className="text-white" />
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">End Date</p>
                          <p className="text-gray-800 font-medium">{formatDateDisplay(task.endDate)}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column - Description */}
              <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-md border border-gray-100 p-6 transform hover:scale-[1.01] transition-all duration-200">
                <h2 className="text-xl font-semibold mb-6 flex items-center justify-between text-gray-800">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 shadow-lg">
                      <FolderOpen size={16} className="text-white" />
                    </div>
                    Description
                  </div>
                  {!editingDescription && (
                    <button
                      onClick={handleEditDescription}
                      className="p-2 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-all duration-200 transform hover:scale-110"
                      title="Edit description"
                    >
                      <Edit3 size={16} />
                    </button>
                  )}
                </h2>
                <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 min-h-[200px]">
                  {editingDescription ? (
                    <div className="space-y-3">
                      <textarea
                        value={editedDescription}
                        onChange={(e) => setEditedDescription(e.target.value)}
                        className="w-full p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-purple-500 focus:border-transparent shadow-inner transition-all duration-200"
                        rows={8}
                        placeholder="Enter task description..."
                        disabled={updatingDescription}
                      />
                      <div className="flex space-x-2">
                        <button
                          onClick={handleSaveDescription}
                          disabled={updatingDescription}
                          className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-sm shadow-lg transform hover:scale-105 transition-all duration-200"
                        >
                          <Check size={16} className="mr-2" />
                          {updatingDescription ? 'Saving...' : 'Save'}
                        </button>
                        <button
                          onClick={handleCancelDescription}
                          disabled={updatingDescription}
                          className="px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-sm shadow-lg transform hover:scale-105 transition-all duration-200"
                        >
                          <X size={16} className="mr-2" />
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="text-gray-700 leading-relaxed p-2 rounded transition-all duration-200 cursor-pointer hover:bg-gray-50"
                      onClick={handleEditDescription}
                      title="Click to edit description"
                    >
                      {task.description || (
                        <span className="text-gray-400 italic">
                          No description provided for this task. Click to add one.
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Comments section */}
          <CommentSection
            title="Task Comments"
            comments={comments}
            loading={commentsLoading}
            onAddComment={handleAddComment}
            onUpdateComment={handleUpdateComment}
            onDeleteComment={handleDeleteComment}
            placeholder="Add a comment about this task..."
          />
        </div>
      </div>
    </div>
  );
};

export default TaskView;