import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAtom } from 'jotai';
import { currentUserAtom } from '../store';
import { paymentsAPI } from '../services/api';
import {
  ArrowLeft,
  Clock,
  AlertTriangle,
  Building2,
  Calendar,
  DollarSign,
  Search,
  Filter,
  Download,
  Eye,
  TrendingDown
} from 'lucide-react';

interface PendingProject {
  id: string;
  name: string;
  code: string;
  customerName: string;
  poValue: number;
  paidAmount: number;
  pendingAmount: number;
  pendingPercentage: number;
  lastPaymentDate?: string;
  daysSinceLastPayment?: number;
  projectManager: string;
  status: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
}

const PendingPaymentsPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentUser] = useAtom(currentUserAtom);
  const [pendingProjects, setPendingProjects] = useState<PendingProject[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState('');
  const [selectedPriority, setSelectedPriority] = useState('');
  const [minPendingAmount, setMinPendingAmount] = useState('');
  const [sortBy, setSortBy] = useState<'pendingAmount' | 'percentage' | 'days'>('pendingAmount');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Check if user is Director
  const isDirector = currentUser?.role === 'DIRECTOR';

  useEffect(() => {
    if (!isDirector) {
      navigate('/dashboard');
      return;
    }

    fetchPendingData();
  }, [isDirector, navigate]);

  const fetchPendingData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await paymentsAPI.getPaymentData();

      if (response.success && response.data.payments) {
        const pendingData: PendingProject[] = response.data.payments
          .filter((project: any) => project.pendingAmount > 0)
          .map((project: any) => {
            const pendingAmount = project.pendingAmount;
            const pendingPercentage = (pendingAmount / project.poValue) * 100;
            
            // Calculate priority based on pending amount and percentage
            let priority: 'HIGH' | 'MEDIUM' | 'LOW' = 'LOW';
            if (pendingAmount > 50000 || pendingPercentage > 70) {
              priority = 'HIGH';
            } else if (pendingAmount > 20000 || pendingPercentage > 40) {
              priority = 'MEDIUM';
            }

            // Mock last payment date and days calculation
            const lastPaymentDate = project.lastPaymentDate || new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString();
            const daysSinceLastPayment = Math.floor((Date.now() - new Date(lastPaymentDate).getTime()) / (24 * 60 * 60 * 1000));

            return {
              id: project.id,
              name: project.projectName,
              code: project.projectCode,
              customerName: project.customerName,
              poValue: project.poValue,
              paidAmount: project.paidAmount,
              pendingAmount,
              pendingPercentage,
              lastPaymentDate,
              daysSinceLastPayment,
              projectManager: project.projectManager || 'Not Assigned',
              status: project.status || 'Active',
              priority
            };
          });

        setPendingProjects(pendingData);
      } else {
        setError('Failed to fetch pending payments data');
      }
    } catch (err) {
      console.error('Error fetching pending data:', err);
      setError('Failed to load pending payments data');
    } finally {
      setLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'bg-red-100 text-red-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'LOW': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get unique customers for filter
  const customers = Array.from(new Set(pendingProjects.map(p => p.customerName))).sort();

  // Filter and sort projects
  const filteredProjects = pendingProjects
    .filter(project => {
      const matchesSearch = !searchTerm || 
        project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.projectManager.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCustomer = !selectedCustomer || project.customerName === selectedCustomer;
      const matchesPriority = !selectedPriority || project.priority === selectedPriority;
      const matchesMinAmount = !minPendingAmount || project.pendingAmount >= parseFloat(minPendingAmount);
      
      return matchesSearch && matchesCustomer && matchesPriority && matchesMinAmount;
    })
    .sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'pendingAmount':
          aValue = a.pendingAmount;
          bValue = b.pendingAmount;
          break;
        case 'percentage':
          aValue = a.pendingPercentage;
          bValue = b.pendingPercentage;
          break;
        case 'days':
          aValue = a.daysSinceLastPayment || 0;
          bValue = b.daysSinceLastPayment || 0;
          break;
        default:
          return 0;
      }
      
      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

  // Calculate totals
  const totalPendingAmount = filteredProjects.reduce((sum, project) => sum + project.pendingAmount, 0);
  const highPriorityCount = filteredProjects.filter(p => p.priority === 'HIGH').length;
  const averagePendingAmount = filteredProjects.length > 0 ? totalPendingAmount / filteredProjects.length : 0;

  if (!isDirector) {
    return null;
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-orange-500 border-t-transparent mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Loading Pending Payments</h2>
          <p className="text-gray-600">Please wait while we fetch the pending payment information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Data</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-orange-50 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/dashboard')}
              className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Dashboard
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Pending Payments</h1>
              <p className="text-gray-600">Projects with outstanding payment amounts</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => navigate('/payment-list')}
              className="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              <Eye className="w-4 h-4 mr-2" />
              All Payments
            </button>
            <button className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Projects with Pending</p>
              <p className="text-2xl font-bold text-gray-900">{filteredProjects.length}</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <Building2 className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Pending Amount</p>
              <p className="text-2xl font-bold text-orange-600">{formatCurrency(totalPendingAmount)}</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <DollarSign className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">High Priority</p>
              <p className="text-2xl font-bold text-red-600">{highPriorityCount}</p>
              <p className="text-xs text-gray-500">Urgent attention needed</p>
            </div>
            <div className="p-3 bg-red-100 rounded-lg">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Pending</p>
              <p className="text-2xl font-bold text-purple-600">{formatCurrency(averagePendingAmount)}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <TrendingDown className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Pending Payments Table */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Pending Payment Details</h3>
            <span className="text-sm text-gray-500">
              Showing {filteredProjects.length} of {pendingProjects.length} projects
            </span>
          </div>

          {/* Filters and Sorting */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search projects..."
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>

            {/* Customer Filter */}
            <select
              value={selectedCustomer}
              onChange={(e) => setSelectedCustomer(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="">All Customers</option>
              {customers.map(customer => (
                <option key={customer} value={customer}>{customer}</option>
              ))}
            </select>

            {/* Priority Filter */}
            <select
              value={selectedPriority}
              onChange={(e) => setSelectedPriority(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="">All Priorities</option>
              <option value="HIGH">High Priority</option>
              <option value="MEDIUM">Medium Priority</option>
              <option value="LOW">Low Priority</option>
            </select>

            {/* Min Amount Filter */}
            <input
              type="number"
              value={minPendingAmount}
              onChange={(e) => setMinPendingAmount(e.target.value)}
              placeholder="Min pending amount"
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />

            {/* Sort By */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'pendingAmount' | 'percentage' | 'days')}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="pendingAmount">Sort by Amount</option>
              <option value="percentage">Sort by Percentage</option>
              <option value="days">Sort by Days</option>
            </select>

            {/* Sort Order */}
            <select
              value={sortOrder}
              onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            >
              <option value="desc">High to Low</option>
              <option value="asc">Low to High</option>
            </select>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Project Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pending Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Payment
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProjects.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                    No pending payments found matching the current filters.
                  </td>
                </tr>
              ) : (
                filteredProjects.map((project) => (
                  <tr key={project.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{project.name}</div>
                        <div className="text-sm text-gray-500">{project.code}</div>
                        <div className="text-xs text-gray-400">{project.projectManager}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {project.customerName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div>PO: {formatCurrency(project.poValue)}</div>
                        <div className="text-green-600">Paid: {formatCurrency(project.paidAmount)}</div>
                        <div className="text-xs text-gray-500">{(100 - project.pendingPercentage).toFixed(1)}% completed</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-bold text-orange-600">{formatCurrency(project.pendingAmount)}</div>
                      <div className="text-xs text-gray-500">{project.pendingPercentage.toFixed(1)}% remaining</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(project.priority)}`}>
                        {project.priority}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {project.lastPaymentDate ? (
                        <div>
                          <div>{formatDate(project.lastPaymentDate)}</div>
                          <div className="text-xs">{project.daysSinceLastPayment} days ago</div>
                        </div>
                      ) : (
                        'No payments yet'
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Summary Footer */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>
              Total Pending: {formatCurrency(totalPendingAmount)} | 
              High Priority: {highPriorityCount} projects | 
              Average: {formatCurrency(averagePendingAmount)}
            </span>
            <span>Last updated: {new Date().toLocaleDateString()}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PendingPaymentsPage;
