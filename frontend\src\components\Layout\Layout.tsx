import React, { useState } from 'react';
import { Outlet, Navigate } from 'react-router-dom';
import { useAtom } from 'jotai';
import { isAuthenticatedAtom, sidebarExpandedAtom } from '../../store';
import Sidebar from './Sidebar';
import Header from './Header';

const Layout: React.FC = () => {
  const [isAuthenticated] = useAtom(isAuthenticatedAtom);
  const [sidebarExpanded] = useAtom(sidebarExpandedAtom);
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen);
  };

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <div
        className={`fixed inset-0 z-40 lg:hidden ${
          mobileSidebarOpen ? 'block' : 'hidden'
        }`}
      >
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-75"
          onClick={toggleMobileSidebar}
          aria-hidden="true"
        />

        <div className="fixed inset-y-0 left-0 flex flex-col z-40 max-w-xs w-full bg-[#051440]">
          <Sidebar />
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className={`hidden lg:block relative ${
        sidebarExpanded ? 'w-60' : 'w-20'
      }`}>
        <Sidebar />
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header toggleMobileSidebar={toggleMobileSidebar} />

        <main className="flex-1 overflow-y-auto bg-gray-50 p-4 md:p-6">
          <div className="max-w-full mx-auto">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default Layout;