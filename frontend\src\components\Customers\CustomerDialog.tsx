import React, { useState, useEffect } from 'react';
import { X, CheckCircle2, User, Mail, Phone, MapPin, Building } from 'lucide-react';
import { Customer } from '../../types';
import { v4 as uuidv4 } from 'uuid';
import { normalizeEmail } from '../../utils/dateValidation';
import { useRealTimeValidation } from '../../hooks/useRealTimeValidation';
import { SmartInput } from '../common/SmartInput';

interface CustomerFormData {
  id?: string;
  name: string;
  code: string;
  contactName?: string;
  email?: string;
  phone?: string;
  address?: string;
}

const validationConfig = {
  name: [
    { type: 'name' as const, message: 'Company name can only contain letters, spaces, hyphens, apostrophes, and dots' },
    { type: 'custom' as const, message: 'Company name is required',
      customValidator: (value: string) => {
        if (!value || value.trim().length === 0) {
          return { isValid: false, message: 'Company name is required' };
        }
        return { isValid: true };
      }
    }
  ],
  code: [
    { type: 'code' as const, message: 'Code must be 2-10 uppercase letters or numbers' },
    { type: 'custom' as const, message: 'Customer code is required',
      customValidator: (value: string) => {
        if (!value || value.trim().length === 0) {
          return { isValid: false, message: 'Customer code is required' };
        }
        return { isValid: true };
      }
    }
  ],
  contactName: [
    { type: 'name' as const, message: 'Contact name can only contain letters, spaces, hyphens, apostrophes, and dots' }
  ],
  email: [
    { type: 'email' as const, message: 'Please enter a valid email address' },
    { type: 'custom' as const, message: 'Email address is required',
      customValidator: (value: string) => {
        if (!value || value.trim().length === 0) {
          return { isValid: false, message: 'Email address is required' };
        }
        return { isValid: true };
      }
    }
  ],
  phone: [
    { type: 'custom' as const, message: 'Phone number must be exactly 10 digits',
      customValidator: (value: string) => {
        if (!value || value.trim().length === 0) {
          return { isValid: false, message: 'Phone number is required' };
        }
        const digitsOnly = value.replace(/\D/g, '');
        if (digitsOnly.length !== 10) {
          return { isValid: false, message: 'Phone number must be exactly 10 digits' };
        }
        return { isValid: true };
      }
    }
  ]
};

interface CustomerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (customer: Customer) => Promise<void>;
  initialData?: Partial<CustomerFormData>;
}

const CustomerDialog: React.FC<CustomerDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  initialData = {}
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [address, setAddress] = useState('');

  const { getFieldProps, isFormValid, setFieldValues, resetAllFields } = useRealTimeValidation(validationConfig);

  // Reset form when initialData changes
  useEffect(() => {
    if (isOpen) {
      setFieldValues({
        name: initialData.name || '',
        code: initialData.code || '',
        contactName: initialData.contactName || '',
        email: initialData.email || '',
        phone: initialData.phone || ''
      });
      setAddress(initialData.address || '');
    }
  }, [isOpen, initialData, setFieldValues]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isFormValid) return;

    setIsSubmitting(true);

    try {
      const nameField = getFieldProps('name');
      const codeField = getFieldProps('code');
      const contactNameField = getFieldProps('contactName');
      const emailField = getFieldProps('email');
      const phoneField = getFieldProps('phone');

      // Create customer data object
      const customerData: Partial<Customer> = {
        name: nameField.value,
        code: codeField.value,
        contactName: contactNameField.value || null,
        email: emailField.value ? normalizeEmail(emailField.value) : null,
        phone: phoneField.value || null,
        address: address || null,
      };

      // If editing, include the ID
      if (initialData.id) {
        customerData.id = initialData.id;
      }

      // Save customer via API
      await onSave(customerData as Customer);
      onClose();
    } catch (error) {
      console.error('Error submitting customer:', error);
      // Error is handled by the parent component
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm"
      style={{
        background: 'radial-gradient(circle at center, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.9))'
      }}
    >
      <div 
        className="bg-white rounded-2xl shadow-2xl w-full max-w-md transform transition-all duration-300 hover:scale-[1.01]"
        style={{
          background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.2)',
          transformStyle: 'preserve-3d'
        }}
      >
        <div 
          className="flex justify-between items-center py-6 px-0 border-b border-gray-100 rounded-t-2xl"
          style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            boxShadow: 'inset 0 1px 0 rgba(255, 255, 255, 0.1)'
          }}
        >
          <h2 className="text-xl font-bold text-white drop-shadow-lg flex items-center">
            <Building size={20} className="mr-2" />
            {initialData?.id ? 'Edit Customer' : 'Add New Customer'}
          </h2>
          <button
            onClick={onClose}
            className="text-white/80 hover:text-white transition-all duration-200 hover:scale-110 hover:rotate-90 p-1 rounded-full hover:bg-white/10"
            style={{ transformStyle: 'preserve-3d' }}
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="py-4 px-0 space-y-4">
          {/* Company Name */}
          <div className="transform transition-all duration-200 hover:translateZ-2 hover:scale-[1.02] px-6">
            <SmartInput
              label="Company Name *"
              type="text"
              placeholder="Enter company name (letters only)"
              required
              helpText="Only letters, spaces, hyphens, apostrophes, and dots allowed"
              maxLength={100}
              showCharacterCount
              showValidationIcon
              {...getFieldProps('name')}
            />
          </div>

          {/* Customer Code */}
          <div className="transform transition-all duration-200 hover:translateZ-2 hover:scale-[1.02] px-6">
            <SmartInput
              label="Customer Code *"
              type="text"
              placeholder="Enter customer code (e.g. ACME)"
              required
              helpText="2-10 uppercase letters or numbers only"
              maxLength={10}
              showCharacterCount
              showValidationIcon
              {...getFieldProps('code')}
            />
          </div>

          {/* Contact Person */}
          <div className="transform transition-all duration-200 hover:translateZ-2 hover:scale-[1.02] px-6">
            <SmartInput
              label="Contact Person"
              type="text"
              placeholder="Enter primary contact name"
              helpText="Only letters, spaces, hyphens, apostrophes, and dots allowed"
              maxLength={100}
              showCharacterCount
              showValidationIcon
              {...getFieldProps('contactName')}
            />
          </div>

          {/* Email and Phone Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 px-6">
            <div className="transform transition-all duration-200 hover:translateZ-2 hover:scale-[1.02]">
              <SmartInput
                label="Email *"
                type="email"
                placeholder="Enter email address"
                required
                helpText="Valid email address format required"
                inputMode="email"
                showValidationIcon
                {...getFieldProps('email')}
              />
            </div>

            <div className="transform transition-all duration-200 hover:translateZ-2 hover:scale-[1.02]">
              <SmartInput
                label="Phone *"
                type="tel"
                placeholder="Enter 10-digit phone number"
                required
                helpText="Must be exactly 10 digits (numbers only)"
                inputMode="tel"
                maxLength={15}
                showValidationIcon
                showCharacterCount
                {...getFieldProps('phone')}
              />
            </div>
          </div>

          {/* Address */}
          <div className="transform transition-all duration-200 hover:translateZ-2 hover:scale-[1.02] px-6">
            <label htmlFor="address" className="flex items-center text-sm font-semibold text-gray-700 mb-2">
              <MapPin size={16} className="mr-2 text-red-500" />
              Address
            </label>
            <textarea
              id="address"
              className="w-full px-4 py-3 rounded-xl border border-gray-200 bg-gray-50 hover:bg-white transition-all duration-200 focus:ring-4 focus:ring-red-200 focus:border-red-500 hover:shadow-lg resize-none"
              style={{
                boxShadow: '0 4px 14px 0 rgba(0, 0, 0, 0.05), inset 0 2px 4px 0 rgba(0, 0, 0, 0.02)'
              }}
              rows={3}
              placeholder="Enter customer address"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end pt-6 space-x-4 px-6">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-semibold transition-all duration-200 hover:scale-105 hover:shadow-lg transform hover:-translate-y-1"
              style={{
                boxShadow: '0 4px 14px 0 rgba(0, 0, 0, 0.1)',
                transformStyle: 'preserve-3d'
              }}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`px-6 py-3 text-white font-semibold rounded-xl transition-all duration-200 hover:scale-105 hover:shadow-xl transform hover:-translate-y-1 flex items-center ${
                !isFormValid || isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              style={{
                background: isFormValid && !isSubmitting
                  ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                  : 'linear-gradient(135deg, #9ca3af 0%, #6b7280 100%)',
                boxShadow: '0 8px 25px -8px rgba(102, 126, 234, 0.5), 0 4px 14px 0 rgba(0, 0, 0, 0.1)',
                transformStyle: 'preserve-3d'
              }}
              disabled={!isFormValid || isSubmitting}
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </span>
              ) : (
                <span className="flex items-center">
                  <CheckCircle2 size={20} className="mr-2" />
                  Save Customer
                </span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CustomerDialog;