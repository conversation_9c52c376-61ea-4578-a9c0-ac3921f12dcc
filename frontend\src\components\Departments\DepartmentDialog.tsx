import React, { useState, useEffect, useCallback } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { X, CheckCircle2, Building2, Hash, FileText } from 'lucide-react';
import { Department } from '../../types';
import { v4 as uuidv4 } from 'uuid';

interface DepartmentFormData {
  name: string;
  code: string;
  description?: string;
}

interface DepartmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (department: Department) => void;
  initialData?: Partial<Department>;
}

const DepartmentDialog: React.FC<DepartmentDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  initialData = {}
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<DepartmentFormData>({
    mode: 'onChange',
    defaultValues: {
      name: '',
      code: '',
      description: ''
    }
  });

  const { register, handleSubmit, formState: { errors }, reset } = form;

  // Initialize form data when dialog opens
  useEffect(() => {
    if (isOpen) {
      setError(null);
      reset({
        name: initialData.name || '',
        code: initialData.code || '',
        description: initialData.description || ''
      });
    }
  }, [isOpen, initialData.name, initialData.code, initialData.description, reset]);

  const onSubmit: SubmitHandler<DepartmentFormData> = useCallback(async (data) => {
    try {
      setIsSubmitting(true);
      setError(null);

      const department: Department = {
        id: initialData.id || uuidv4(),
        ...data
      };

      await onSave(department);
      onClose();
    } catch (error: any) {
      console.error('Department dialog error:', error);
      setError(error.response?.data?.message || error.message || 'Failed to save department');
    } finally {
      setIsSubmitting(false);
    }
  }, [initialData.id, onSave, onClose]);

  const handleClose = useCallback(() => {
    setError(null);
    onClose();
  }, [onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-md flex items-center justify-center z-50 p-4">
      {/* Animated background particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400/30 rounded-full animate-ping"></div>
        <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-purple-400/40 rounded-full animate-pulse"></div>
        <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-indigo-400/30 rounded-full animate-bounce"></div>
      </div>

      <div 
        className="relative bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl w-full max-w-md transform transition-all duration-500 ease-out scale-100 hover:scale-[1.01]"
        style={{
          boxShadow: `
            0 32px 64px -12px rgba(0, 0, 0, 0.25),
            0 0 0 1px rgba(255, 255, 255, 0.8),
            inset 0 1px 0 rgba(255, 255, 255, 0.9),
            0 0 120px rgba(99, 102, 241, 0.15)
          `,
        }}
      >
        {/* Glassmorphism overlay */}
        <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/30 via-white/10 to-transparent pointer-events-none" />
        
        {/* Animated border */}
        <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 rounded-3xl opacity-20 blur-sm animate-pulse" />
        
        <div className="relative bg-gradient-to-br from-white via-gray-50/90 to-white rounded-3xl border border-white/30 overflow-hidden">
          {/* Header */}
          <div className="relative px-8 py-6 bg-gradient-to-r from-blue-600 to-purple-600">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-purple-600/90 backdrop-blur-sm"></div>
            <div className="relative flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Building2 size={24} className="text-white" />
                </div>
                <h2 className="text-xl font-bold text-white">
                  {initialData.id ? 'Edit Department' : 'Add New Department'}
                </h2>
              </div>
              <button
                onClick={handleClose}
                type="button"
                className="p-2 text-white/80 hover:text-white hover:bg-white/20 rounded-xl transition-all duration-200 transform hover:scale-110"
              >
                <X size={20} />
              </button>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-8 space-y-6">
            {error && (
              <div className="relative p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-2xl">
                <div className="absolute inset-0 bg-red-500/5 rounded-2xl"></div>
                <p className="relative text-red-700 text-sm font-medium">{error}</p>
              </div>
            )}

            {/* Department Name Field */}
            <div className="space-y-2">
              <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700">
                <Building2 size={16} className="text-blue-600" />
                <span>Department Name <span className="text-red-500">*</span></span>
              </label>
              <div className="relative">
                <input
                  type="text"
                  {...register('name', { 
                    required: 'Department name is required',
                    minLength: {
                      value: 2,
                      message: 'Department name must be at least 2 characters'
                    },
                    maxLength: {
                      value: 100,
                      message: 'Department name must be less than 100 characters'
                    }
                  })}
                  className={`w-full px-4 py-3 bg-white/80 backdrop-blur-sm border-2 rounded-xl transition-all duration-300 focus:outline-none focus:ring-0 ${
                    errors.name 
                      ? 'border-red-300 focus:border-red-500 bg-red-50/50' 
                      : 'border-gray-200 focus:border-blue-500 focus:bg-white'
                  }`}
                  placeholder="Enter department name"
                  autoComplete="off"
                  disabled={isSubmitting}
                  style={{
                    boxShadow: errors.name 
                      ? '0 4px 20px rgba(239, 68, 68, 0.1)' 
                      : '0 4px 20px rgba(0, 0, 0, 0.05)'
                  }}
                />
                {errors.name && (
                  <p className="mt-2 text-sm text-red-600 font-medium">
                    {errors.name.message}
                  </p>
                )}
              </div>
            </div>

            {/* Department Code Field */}
            <div className="space-y-2">
              <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700">
                <Hash size={16} className="text-purple-600" />
                <span>Department Code <span className="text-red-500">*</span></span>
              </label>
              <div className="relative">
                <input
                  type="text"
                  {...register('code', { 
                    required: 'Department code is required',
                    minLength: {
                      value: 2,
                      message: 'Department code must be at least 2 characters'
                    },
                    maxLength: {
                      value: 20,
                      message: 'Department code must be less than 20 characters'
                    },
                    pattern: {
                      value: /^[A-Z0-9_-]+$/i,
                      message: 'Code can only contain letters, numbers, hyphens, and underscores'
                    }
                  })}
                  className={`w-full px-4 py-3 bg-white/80 backdrop-blur-sm border-2 rounded-xl transition-all duration-300 focus:outline-none focus:ring-0 ${
                    errors.code 
                      ? 'border-red-300 focus:border-red-500 bg-red-50/50' 
                      : 'border-gray-200 focus:border-purple-500 focus:bg-white'
                  }`}
                  placeholder="Enter department code"
                  autoComplete="off"
                  disabled={isSubmitting}
                  style={{
                    boxShadow: errors.code 
                      ? '0 4px 20px rgba(239, 68, 68, 0.1)' 
                      : '0 4px 20px rgba(0, 0, 0, 0.05)'
                  }}
                />
                {errors.code && (
                  <p className="mt-2 text-sm text-red-600 font-medium">
                    {errors.code.message}
                  </p>
                )}
              </div>
            </div>

            {/* Description Field */}
            <div className="space-y-2">
              <label className="flex items-center space-x-2 text-sm font-semibold text-gray-700">
                <FileText size={16} className="text-indigo-600" />
                <span>Description</span>
              </label>
              <textarea
                {...register('description', {
                  maxLength: {
                    value: 500,
                    message: 'Description must be less than 500 characters'
                  }
                })}
                rows={4}
                className="w-full px-4 py-3 bg-white/80 backdrop-blur-sm border-2 border-gray-200 rounded-xl transition-all duration-300 focus:outline-none focus:ring-0 focus:border-indigo-500 focus:bg-white resize-none"
                placeholder="Enter department description (optional)"
                autoComplete="off"
                disabled={isSubmitting}
                style={{
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
                }}
              />
              {errors.description && (
                <p className="mt-2 text-sm text-red-600 font-medium">
                  {errors.description.message}
                </p>
              )}
            </div>

            {/* Buttons */}
            <div className="flex justify-end space-x-4 pt-6">
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
                className="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)' }}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="relative px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden"
                style={{
                  boxShadow: '0 8px 25px rgba(99, 102, 241, 0.3)'
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] hover:translate-x-[100%] transition-transform duration-1000"></div>
                <div className="relative flex items-center space-x-2">
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle2 size={18} />
                      <span>{initialData.id ? 'Update Department' : 'Save Department'}</span>
                    </>
                  )}
                </div>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default DepartmentDialog;