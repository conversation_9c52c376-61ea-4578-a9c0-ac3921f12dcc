import React, { useEffect, useCallback, useRef } from 'react';
import UserManager from '../components/Master/UserManager';
import { BuildingOfficeIcon } from '@heroicons/react/24/solid';
import { dataService } from '../services/dataServiceSingleton';

// Debounce utility function for user operations
const useDebounce = (callback: Function, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback((...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

const UsersPage: React.FC = () => {
  // Debounced refresh function to reduce API calls
  const debouncedRefreshUsers = useDebounce(async () => {
    try {
      console.log('🔄 Refreshing users data...');
      await dataService.loadUsers();
      await dataService.loadDepartments(); // Also refresh departments as they're related
      console.log('✅ Users and departments data refreshed successfully');
    } catch (error) {
      console.error('❌ Error refreshing users data:', error);
    }
  }, 1000); // 1 second debounce

  // Auto-refresh mechanism for real-time updates
  useEffect(() => {
    // Add visibility change listener to refresh when page becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('📱 Users page became visible, refreshing data...');
        debouncedRefreshUsers();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Refresh every 30 seconds when page is visible
    const refreshInterval = setInterval(() => {
      if (!document.hidden) {
        console.log('⏰ Periodic refresh of users data...');
        debouncedRefreshUsers();
      }
    }, 30000);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(refreshInterval);
    };
  }, [debouncedRefreshUsers]);

  return (
    <div className="space-y-6">
      {/* Header Section with 3D Effects */}
      <div className="bg-white rounded-xl p-6 transform transition-all duration-300 hover:shadow-2xl hover:scale-[1.01] border border-gray-100"
           style={{
             background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
             boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
           }}>
        <div className="flex items-center gap-2">
          <div className="p-2 rounded-md transform transition-all duration-300 hover:scale-110"
               style={{
                 background: 'linear-gradient(135deg, #3b82f6 0%, #1e3a8a 100%)',
                 boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
               }}>
            <BuildingOfficeIcon className="w-6 h-6 text-white" 
                               style={{ filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))' }} />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-1 transform transition-all duration-300 hover:scale-105"
                style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
              User Management
            </h1>
            <p className="text-gray-600 transform transition-all duration-300 hover:text-gray-700">
              Manage users and their roles within the organization
            </p>
          </div>
        </div>
      </div>

      {/* UserManager Component */}
      <UserManager />
    </div>
  );
};

export default UsersPage;