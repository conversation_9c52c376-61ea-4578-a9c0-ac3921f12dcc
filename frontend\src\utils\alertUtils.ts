import { Alert } from '../types';
import { alertsAPI } from '../services/api';
import { dataService } from '../services/dataServiceSingleton';

/**
 * Utility functions for managing alert state consistently across the application
 */

// Batch processing configuration
const BATCH_SIZE = 5; // Process alerts in batches of 5
const BATCH_DELAY = 100; // 100ms delay between batches

/**
 * Mark multiple alerts as read with optimized batching
 * @param alerts - Current alerts array
 * @param alertIds - Array of alert IDs to mark as read
 * @param setAlerts - Function to update alerts state
 * @returns Promise that resolves when backend calls complete
 */
export const markAlertsAsRead = async (
  alerts: Alert[],
  alertIds: string[],
  setAlerts: (updater: (prev: Alert[]) => Alert[]) => void
): Promise<void> => {
  if (alertIds.length === 0) return;

  try {
    console.log(`🔄 Marking ${alertIds.length} alerts as read with batching...`);

    // Update local state immediately for instant UI feedback
    setAlerts(prevAlerts =>
      prevAlerts.map(alert =>
        alertIds.includes(alert.id) ? { ...alert, read: true } : alert
      )
    );

    // Process alerts in batches to reduce server load
    const batches = [];
    for (let i = 0; i < alertIds.length; i += BATCH_SIZE) {
      batches.push(alertIds.slice(i, i + BATCH_SIZE));
    }

    console.log(`📦 Processing ${batches.length} batches of alerts...`);

    // Process batches sequentially with delay to prevent overwhelming the server
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`🔄 Processing batch ${i + 1}/${batches.length} (${batch.length} alerts)`);

      try {
        // Process current batch in parallel
        await Promise.all(
          batch.map(id => alertsAPI.markAsRead(id))
        );

        console.log(`✅ Batch ${i + 1}/${batches.length} completed successfully`);

        // Add delay between batches (except for the last one)
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, BATCH_DELAY));
        }
      } catch (batchError) {
        console.error(`❌ Error in batch ${i + 1}:`, batchError);
        // Continue with next batch even if current batch fails
      }
    }

    // Refresh alerts data to ensure consistency
    await dataService.loadAlerts();
    console.log('✅ Alerts data refreshed after marking as read');

  } catch (error) {
    console.error('❌ Error marking alerts as read:', error);

    // Revert the local state if backend call fails
    setAlerts(prevAlerts =>
      prevAlerts.map(alert =>
        alertIds.includes(alert.id) ? { ...alert, read: false } : alert
      )
    );

    throw error; // Re-throw for caller to handle
  }
};

/**
 * Mark all unread alerts as read
 * @param alerts - Current alerts array
 * @param setAlerts - Function to update alerts state
 * @returns Promise that resolves when backend calls complete
 */
export const markAllUnreadAlertsAsRead = async (
  alerts: Alert[],
  setAlerts: (updater: (prev: Alert[]) => Alert[]) => void
): Promise<void> => {
  const unreadAlertIds = alerts
    .filter(alert => !alert.read)
    .map(alert => alert.id);

  return markAlertsAsRead(alerts, unreadAlertIds, setAlerts);
};

/**
 * Get unread alerts count
 * @param alerts - Current alerts array
 * @returns Number of unread alerts
 */
export const getUnreadAlertsCount = (alerts: Alert[]): number => {
  return alerts.filter(alert => !alert.read).length;
};

/**
 * Filter alerts by user permissions
 * @param alerts - All alerts
 * @param currentUser - Current user object
 * @returns Filtered alerts that the user can see
 */
export const filterAlertsByUser = (alerts: Alert[], currentUser: any): Alert[] => {
  if (!currentUser) return [];

  return alerts.filter(alert => {
    if (currentUser.role === 'DIRECTOR') {
      return true; // Directors can see all alerts
    } else if (currentUser.role === 'PROJECT_MANAGER') {
      // Project managers can see all alerts (they manage projects)
      return true;
    } else if (currentUser.role === 'TEAM_LEAD') {
      // Team leads can see alerts in their department or assigned to them
      return alert.department === currentUser.department || alert.assigneeId === currentUser.id;
    } else if (currentUser.role === 'ENGINEER') {
      // Engineers only see alerts assigned to them
      return alert.assigneeId === currentUser.id;
    }
    return false;
  });
};

/**
 * Sort alerts by read status and date
 * @param alerts - Alerts to sort
 * @returns Sorted alerts array
 */
export const sortAlerts = (alerts: Alert[]): Alert[] => {
  return [...alerts].sort((a, b) => {
    // First sort by read status (unread first)
    if (a.read !== b.read) {
      return a.read ? 1 : -1;
    }

    // Then by creation date (newest first)
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });
};



/**
 * Get alert type icon
 * @param type - Alert type
 * @returns Icon component name or null
 */
export const getAlertTypeIcon = (type: string): string => {
  switch (type) {
    case 'SYSTEM':
      return 'InfoIcon';
    case 'PROJECT':
      return 'FolderIcon';
    case 'TASK':
      return 'CheckSquare';
    case 'DEADLINE':
      return 'Clock';
    case 'SECURITY':
      return 'Shield';
    case 'MAINTENANCE':
      return 'Settings';
    default:
      return 'Bell';
  }
};
