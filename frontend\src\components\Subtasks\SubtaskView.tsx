import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAtom } from 'jotai';
import { engineersAtom, userSubtasksAtom, projectsAtom } from '../../store';
import { Subtask, TaskStatus } from '../../types';
import { formatDate } from '../../utils/dateFormatter';
import { subtasksAPI } from '../../services/api';
import { subtaskCommentsAPI, SubtaskComment } from '../../services/commentAPI';
import CommentSection from '../Comments/CommentSection';
import {
  ArrowLeft,
  Calendar,
  Clock,
  CheckCircle,
  Circle,
  AlertCircle,
  PauseCircle,
  PlayCircle,
  User,
  FolderOpen,
  Star,
  Target,
  Edit3,
  Check,
  X
} from 'lucide-react';

const SubtaskView: React.FC = () => {
  const navigate = useNavigate();
  const { subtaskId } = useParams<{ subtaskId: string }>();
  const [engineers] = useAtom(engineersAtom);
  const [userSubtasks] = useAtom(userSubtasksAtom);
  const [projects, setProjects] = useAtom(projectsAtom);

  const [subtask, setSubtask] = useState<Subtask | null>(null);
  const [loading, setLoading] = useState(true);
  const [comments, setComments] = useState<SubtaskComment[]>([]);
  const [commentsLoading, setCommentsLoading] = useState(true);
  const [editingDescription, setEditingDescription] = useState(false);
  const [editedDescription, setEditedDescription] = useState('');
  const [updatingDescription, setUpdatingDescription] = useState(false);

  useEffect(() => {
    const loadSubtaskAndComments = async () => {
      if (!subtaskId) return;

      setLoading(true);
      setCommentsLoading(true);

      try {
        // First try to find subtask in local state for immediate display
        const foundSubtask = userSubtasks.find(s => s.id === subtaskId);
        if (foundSubtask) {
          setSubtask(foundSubtask);
        }

        // Then fetch fresh data from API to ensure we have the latest
        try {
          const freshSubtask = await subtasksAPI.getSubtask(subtaskId);
          setSubtask(freshSubtask.data);
        } catch (error) {
          console.error('Error loading fresh subtask data:', error);
          // If API fails, fall back to local data
          if (!foundSubtask) {
            console.error('Subtask not found in local state either');
          }
        }

        // Load comments
        try {
          const subtaskComments = await subtaskCommentsAPI.getSubtaskComments(subtaskId);
          setComments(subtaskComments);
        } catch (error) {
          console.error('Error loading comments:', error);
          setComments([]);
        }
      } catch (error) {
        console.error('Error loading subtask:', error);
      } finally {
        setLoading(false);
        setCommentsLoading(false);
      }
    };

    loadSubtaskAndComments();
  }, [subtaskId]);

  const getEngineerName = (id: string) => {
    const engineer = engineers.find(e => e.id === id);
    return engineer ? engineer.name : 'Unassigned';
  };

  const getProjectName = (projectId: string) => {
    const project = projects.find(p => p.id === projectId);
    return project ? project.name : 'Unknown Project';
  };

  const formatDateDisplay = (dateString: string) => {
    try {
      return formatDate(dateString);
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.COMPLETED:
        return <CheckCircle size={18} className="text-green-600" />;
      case TaskStatus.IN_PROGRESS:
        return <PlayCircle size={18} className="text-blue-600" />;
      case TaskStatus.DELAYED:
        return <AlertCircle size={18} className="text-red-600" />;
      case TaskStatus.ON_HOLD:
        return <PauseCircle size={18} className="text-yellow-600" />;
      default:
        return <Circle size={18} className="text-gray-400" />;
    }
  };

  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.COMPLETED:
        return 'from-green-500 to-green-600';
      case TaskStatus.IN_PROGRESS:
        return 'from-blue-500 to-blue-600';
      case TaskStatus.DELAYED:
        return 'from-red-500 to-red-600';
      case TaskStatus.ON_HOLD:
        return 'from-yellow-500 to-yellow-600';
      default:
        return 'from-gray-400 to-gray-500';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High':
        return 'from-red-500 to-red-600 text-white';
      case 'Medium':
        return 'from-yellow-500 to-yellow-600 text-white';
      case 'Low':
        return 'from-green-500 to-green-600 text-white';
      default:
        return 'from-gray-400 to-gray-500 text-white';
    }
  };

  // Description handlers
  const handleEditDescription = () => {
    setEditingDescription(true);
    setEditedDescription(subtask?.description || '');
  };

  const handleSaveDescription = async () => {
    if (!subtaskId || !subtask) return;

    setUpdatingDescription(true);
    try {
      const updatedSubtask = await subtasksAPI.updateSubtask(subtaskId, { description: editedDescription });
      setSubtask(updatedSubtask.data);

      // Update the global projects state to reflect the subtask description change
      setProjects(prevProjects => {
        return prevProjects.map(project => {
          return {
            ...project,
            tasks: project.tasks?.map(task => {
              return {
                ...task,
                subtasks: task.subtasks?.map(s =>
                  s.id === subtaskId ? { ...s, description: editedDescription } : s
                ) || []
              };
            }) || []
          };
        });
      });

      setEditingDescription(false);
    } catch (error) {
      console.error('Error updating description:', error);
    } finally {
      setUpdatingDescription(false);
    }
  };

  const handleCancelDescription = () => {
    setEditingDescription(false);
    setEditedDescription('');
  };

  // Comment handlers
  const handleAddComment = async (content: string) => {
    if (!subtaskId) return;

    try {
      const newComment = await subtaskCommentsAPI.createSubtaskComment(subtaskId, { content });
      setComments(prev => {
        const safePrev = Array.isArray(prev) ? prev : [];
        return [...safePrev, newComment];
      });
    } catch (error) {
      console.error('Error adding comment:', error);
      throw error;
    }
  };

  const handleUpdateComment = async (commentId: string, content: string) => {
    if (!subtaskId) return;

    try {
      const updatedComment = await subtaskCommentsAPI.updateSubtaskComment(subtaskId, commentId, { content });
      setComments(prev => {
        const safePrev = Array.isArray(prev) ? prev : [];
        return safePrev.map(comment =>
          comment.id === commentId ? updatedComment : comment
        );
      });
    } catch (error) {
      console.error('Error updating comment:', error);
      throw error;
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (!subtaskId) return;

    try {
      await subtaskCommentsAPI.deleteSubtaskComment(subtaskId, commentId);
      setComments(prev => {
        const safePrev = Array.isArray(prev) ? prev : [];
        return safePrev.filter(comment => comment.id !== commentId);
      });
    } catch (error) {
      console.error('Error deleting comment:', error);
      throw error;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600 mx-auto shadow-lg"></div>
            <div className="absolute inset-0 rounded-full bg-blue-100 opacity-20 animate-ping"></div>
          </div>
          <p className="text-gray-600 mt-4 font-medium">Loading subtask details...</p>
        </div>
      </div>
    );
  }

  if (!subtask) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white flex items-center justify-center">
        <div className="text-center bg-white p-8 rounded-xl shadow-lg">
          <AlertCircle size={48} className="mx-auto text-red-500 mb-4" />
          <p className="text-gray-600 font-medium">Subtask not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="w-full pt-2 pb-6">
        <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden mx-4">
          {/* Enhanced Header */}
          <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <button
                  className="mr-4 p-2 text-gray-500 hover:text-gray-700 hover:bg-white rounded-lg transition-all duration-200 transform hover:scale-105 shadow-md"
                  onClick={() => navigate(-1)}
                >
                  <ArrowLeft size={20} />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-800 mb-1">{subtask.name}</h1>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <span className="bg-white px-3 py-1 rounded-full shadow-sm">
                      {subtask.displayId || subtask.id}
                    </span>
                    <span>•</span>
                    <span>{subtask.taskName}</span>
                    <span>•</span>
                    <span>{subtask.projectName}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className={`flex items-center space-x-2 px-4 py-2 bg-gradient-to-r ${getStatusColor(subtask.status)} rounded-full text-white shadow-lg transform hover:scale-105 transition-all duration-200`}>
                  {getStatusIcon(subtask.status)}
                  <span className="font-medium">{subtask.status.replace(/_/g, ' ')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Details Section */}
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Details */}
              <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-md border border-gray-100 p-6 transform hover:scale-[1.01] transition-all duration-200">
                <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-800">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3 shadow-lg">
                    <Target size={16} className="text-white" />
                  </div>
                  Task Details
                </h2>
                <div className="space-y-4">
                  <div className="flex items-center p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
                    <span className="w-24 text-gray-600 font-medium">Assignee:</span>
                    <div className="flex items-center ml-4">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center mr-2 shadow-md">
                        <User size={14} className="text-white" />
                      </div>
                      <span className="text-gray-800 font-medium">{getEngineerName(subtask.assigneeId)}</span>
                    </div>
                  </div>

                  <div className="flex items-center p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
                    <span className="w-24 text-gray-600 font-medium">Parent Task:</span>
                    <div className="flex items-center ml-4">
                      <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-500 rounded-full flex items-center justify-center mr-2 shadow-md">
                        <FolderOpen size={14} className="text-white" />
                      </div>
                      <span className="text-gray-800 font-medium">{subtask.taskName}</span>
                    </div>
                  </div>

                  <div className="flex items-center p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
                    <span className="w-24 text-gray-600 font-medium">Project:</span>
                    <span className="text-gray-800 font-medium ml-4">{subtask.projectName}</span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mr-2 shadow-md">
                          <Calendar size={14} className="text-white" />
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">Start Date</p>
                          <p className="text-gray-800 font-medium">{formatDateDisplay(subtask.startDate)}</p>
                        </div>
                      </div>
                    </div>

                    <div className="p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center mr-2 shadow-md">
                          <Calendar size={14} className="text-white" />
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wide">End Date</p>
                          <p className="text-gray-800 font-medium">{formatDateDisplay(subtask.endDate)}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {subtask.priority && (
                    <div className="flex items-center p-3 bg-white rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
                      <span className="w-24 text-gray-600 font-medium">Priority:</span>
                      <div className="ml-4">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium shadow-lg bg-gradient-to-r ${getPriorityColor(subtask.priority)} transform hover:scale-105 transition-all duration-200`}>
                          <Star size={12} className="inline mr-1" />
                          {subtask.priority}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Right Column - Description */}
              <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-md border border-gray-100 p-6 transform hover:scale-[1.01] transition-all duration-200">
                <h2 className="text-xl font-semibold mb-6 flex items-center justify-between text-gray-800">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3 shadow-lg">
                      <FolderOpen size={16} className="text-white" />
                    </div>
                    Description
                  </div>
                  {!editingDescription && (
                    <button
                      onClick={handleEditDescription}
                      className="p-2 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-all duration-200 transform hover:scale-110"
                      title="Edit description"
                    >
                      <Edit3 size={16} />
                    </button>
                  )}
                </h2>
                <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 min-h-[200px]">
                  {editingDescription ? (
                    <div className="space-y-3">
                      <textarea
                        value={editedDescription}
                        onChange={(e) => setEditedDescription(e.target.value)}
                        className="w-full p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-purple-500 focus:border-transparent shadow-inner transition-all duration-200"
                        rows={8}
                        placeholder="Enter subtask description..."
                        disabled={updatingDescription}
                      />
                      <div className="flex space-x-2">
                        <button
                          onClick={handleSaveDescription}
                          disabled={updatingDescription}
                          className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-sm shadow-lg transform hover:scale-105 transition-all duration-200"
                        >
                          <Check size={16} className="mr-2" />
                          {updatingDescription ? 'Saving...' : 'Save'}
                        </button>
                        <button
                          onClick={handleCancelDescription}
                          disabled={updatingDescription}
                          className="px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-sm shadow-lg transform hover:scale-105 transition-all duration-200"
                        >
                          <X size={16} className="mr-2" />
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="text-gray-700 leading-relaxed cursor-pointer hover:bg-gray-50 p-2 rounded transition-all duration-200"
                      onClick={handleEditDescription}
                      title="Click to edit description"
                    >
                      {subtask.description || (
                        <span className="text-gray-400 italic">No description provided for this subtask. Click to add one.</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Comments section */}
          <CommentSection
            title="Subtask Comments"
            comments={comments}
            loading={commentsLoading}
            onAddComment={handleAddComment}
            onUpdateComment={handleUpdateComment}
            onDeleteComment={handleDeleteComment}
            placeholder="Add a comment about this subtask..."
          />
        </div>
      </div>
    </div>
  );
};

export default SubtaskView;