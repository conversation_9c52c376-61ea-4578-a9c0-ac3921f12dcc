import React from 'react';
import { use<PERSON>tom } from 'jotai';
import { projectsAtom, currentUser<PERSON>tom } from '../../store';
import { format } from 'date-fns';
import { ArrowUpRight, ArrowDownRight, Minus, ExternalLink } from 'lucide-react';
import { Project, Task } from '../../types';

const ProjectsProgress: React.FC = () => {
  const [projects] = useAtom(projectsAtom);
  const [currentUser] = useAtom(currentUserAtom);

  // Filter projects based on user role and assignment
  const userProjects = projects.filter(project => {
    if (currentUser?.role === 'DIRECTOR') {
      return true; // Directors can see all projects
    } else if (currentUser?.role === 'PROJECT_MANAGER') {
      // Project managers can see projects they manage
      return project.projectManagerId === currentUser.id;
    } else if (currentUser?.role === 'TEAM_LEAD') {
      // Team leads can see projects where they are assigned tasks or projects in their department
      return project.department === currentUser.department ||
             project.tasks?.some(task => task.assigneeId === currentUser.id) || false;
    } else if (currentUser?.role === 'ENGINEER') {
      // Engineers only see projects where they are assigned tasks
      return project.tasks?.some(task => task.assigneeId === currentUser.id) || false;
    }
    return false;
  });

  // Show more recent projects (limit to 6 for expanded view)
  const recentProjects = [...userProjects]
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 6);

  return (
    <div className="
      relative group h-96 flex flex-col
      bg-gradient-to-br from-slate-50 via-white to-slate-100
      border border-slate-200/50
      rounded-2xl
      shadow-2xl shadow-slate-300/20
      transform-gpu transition-all duration-500 ease-out
      hover:shadow-3xl hover:shadow-slate-400/30
      backdrop-blur-sm
      before:absolute before:inset-0 before:rounded-2xl
      before:bg-gradient-to-t before:from-black/[0.02] before:to-white/10
      before:opacity-0 before:transition-opacity before:duration-300
      hover:before:opacity-100
      p-8
    ">
      {/* Top highlight for 3D effect */}
      <div className="absolute top-0 left-6 right-6 h-px bg-gradient-to-r from-transparent via-white/60 to-transparent"></div>

      {/* Left edge highlight */}
      <div className="absolute top-6 bottom-6 left-0 w-px bg-gradient-to-b from-transparent via-slate-300/30 to-transparent"></div>

      {/* Header */}
      <div className="flex justify-between items-center mb-8 flex-shrink-0">
        <h3 className="text-xl font-bold text-slate-800 drop-shadow-sm">Recent Projects</h3>
        <a
          href="/projects"
          className="
            group/link flex items-center gap-2 px-4 py-2
            text-sm font-medium text-slate-600
            bg-gradient-to-r from-slate-100 to-slate-200
            border border-slate-300/50
            rounded-xl shadow-lg shadow-slate-200/50
            hover:shadow-xl hover:shadow-slate-300/60
            hover:from-slate-200 hover:to-slate-300
            hover:text-slate-800
            transform transition-all duration-300
            hover:scale-105 hover:-translate-y-0.5
            active:scale-95 active:translate-y-0
          "
        >
          <span>View All Projects</span>
          <ExternalLink size={14} className="transition-transform duration-300 group-hover/link:translate-x-0.5 group-hover/link:-translate-y-0.5" />
        </a>
      </div>

      <div className="space-y-6 flex-1 overflow-y-auto min-h-0">
        {recentProjects.map((project, index) => {
          // Calculate days remaining based on project status and dates
          const today = new Date();
          const startDate = new Date(project.startDate);
          const endDate = new Date(project.endDate);

          let daysRemainingInfo: { days: number; type: 'start' | 'end' | 'overdue' };

          // If project hasn't started yet, always show days until start (regardless of current date)
          if (project.status === 'NOT_STARTED') {
            const diffTime = startDate.getTime() - today.getTime();
            daysRemainingInfo = {
              days: Math.abs(Math.ceil(diffTime / (1000 * 60 * 60 * 24))),
              type: 'start'
            };
          } else {
            // If project is active or completed, show days until end (or overdue)
            const diffTime = endDate.getTime() - today.getTime();
            const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            daysRemainingInfo = {
              days: Math.abs(days),
              type: days < 0 ? 'overdue' : 'end'
            };
          }

          // Calculate progress
          const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
          const daysPassed = Math.ceil((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

          let timeProgress = Math.round((daysPassed / totalDays) * 100);
          timeProgress = Math.max(0, Math.min(100, timeProgress)); // Ensure between 0-100

          // Calculate task progress from actual task completion
          const projectTasks = project.tasks || [];
          const completedTasks = projectTasks.filter(task => task.status === 'COMPLETED').length;
          const taskProgress = projectTasks.length > 0
            ? Math.round((completedTasks / projectTasks.length) * 100)
            : 0;

          // Determine progress status
          let status: 'ahead' | 'behind' | 'on-track' = 'on-track';
          let difference = taskProgress - timeProgress;

          if (difference >= 10) {
            status = 'ahead';
          } else if (difference <= -10) {
            status = 'behind';
          }

          const getStatusColors = () => {
            switch (status) {
              case 'ahead':
                return {
                  bg: 'bg-gradient-to-r from-emerald-500 to-green-600',
                  shadow: 'shadow-emerald-500/30',
                  indicator: 'text-emerald-600 bg-emerald-50 border-emerald-200',
                };
              case 'behind':
                return {
                  bg: 'bg-gradient-to-r from-red-500 to-rose-600',
                  shadow: 'shadow-red-500/30',
                  indicator: 'text-red-600 bg-red-50 border-red-200',
                };
              default:
                return {
                  bg: 'bg-gradient-to-r from-blue-500 to-indigo-600',
                  shadow: 'shadow-blue-500/30',
                  indicator: 'text-slate-600 bg-slate-50 border-slate-200',
                };
            }
          };

          const colors = getStatusColors();

          return (
            <div
              key={project.id}
              className="
                relative group/item
                bg-gradient-to-br from-white via-slate-50/50 to-white
                border border-slate-200/60
                rounded-xl p-6
                shadow-lg shadow-slate-200/40
                hover:shadow-xl hover:shadow-slate-300/50
                transform transition-all duration-300
                hover:scale-[1.02] hover:-translate-y-1
                last:mb-0
                backdrop-blur-sm
                before:absolute before:inset-0 before:rounded-xl
                before:bg-gradient-to-br before:from-white/40 before:to-transparent
                before:opacity-0 before:transition-opacity before:duration-300
                hover:before:opacity-100
              "
              style={{
                animationDelay: `${index * 100}ms`,
              }}
            >
              {/* Inner highlight */}
              <div className="absolute top-0 left-4 right-4 h-px bg-gradient-to-r from-transparent via-white/80 to-transparent"></div>

              <div className="relative z-10">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h4 className="font-bold text-slate-900 mb-1 drop-shadow-sm text-lg">
                      {project.name}
                    </h4>
                    <span className="
                      inline-block px-3 py-1
                      text-sm font-medium text-slate-600
                      bg-gradient-to-r from-slate-100 to-slate-200
                      border border-slate-300/50
                      rounded-lg shadow-sm
                    ">
                      {project.customer?.name || "Unknown Customer"}
                    </span>
                  </div>
                  <div className="text-right ml-4">
                    <div className={`
                      inline-flex items-center px-3 py-1 mb-1
                      text-sm font-bold rounded-lg
                      shadow-lg border
                      ${daysRemainingInfo.type === 'overdue'
                        ? 'text-red-700 bg-gradient-to-r from-red-100 to-red-200 border-red-300/50 shadow-red-200/50'
                        : daysRemainingInfo.type === 'start'
                        ? 'text-blue-700 bg-gradient-to-r from-blue-100 to-blue-200 border-blue-300/50 shadow-blue-200/50'
                        : 'text-slate-700 bg-gradient-to-r from-slate-100 to-slate-200 border-slate-300/50 shadow-slate-200/50'
                      }
                    `}>
                      {daysRemainingInfo.type === 'overdue' ? (
                        <span>Overdue by {daysRemainingInfo.days} days</span>
                      ) : daysRemainingInfo.type === 'start' ? (
                        <span>{daysRemainingInfo.days} days until start</span>
                      ) : (
                        <span>{daysRemainingInfo.days} days left</span>
                      )}
                    </div>
                    <div className="text-xs font-medium text-slate-500 drop-shadow-sm">
                      Due {format(new Date(project.endDate), 'MMM d, yyyy')}
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <div className="flex justify-between items-center mb-3">
                    <span className="text-sm font-semibold text-slate-700 drop-shadow-sm">Progress</span>
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold text-slate-800 drop-shadow-sm">{taskProgress}%</span>
                      <div className={`
                        flex items-center gap-1 px-2 py-1
                        text-xs font-bold rounded-lg border
                        shadow-lg backdrop-blur-sm
                        ${colors.indicator}
                      `}>
                        {status === 'ahead' && (
                          <>
                            <ArrowUpRight size={12} />
                            <span>+{difference}%</span>
                          </>
                        )}
                        {status === 'behind' && (
                          <>
                            <ArrowDownRight size={12} />
                            <span>{difference}%</span>
                          </>
                        )}
                        {status === 'on-track' && (
                          <>
                            <Minus size={12} />
                            <span>On track</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Progress Bar Container */}
                  <div className="
                    relative w-full h-4
                    bg-gradient-to-r from-slate-200 via-slate-300 to-slate-200
                    rounded-full
                    shadow-inner shadow-slate-400/30
                    border border-slate-300/50
                    overflow-hidden
                  ">
                    {/* Progress Bar Fill */}
                    <div
                      className={`
                        h-full rounded-full
                        ${colors.bg}
                        shadow-lg ${colors.shadow}
                        transition-all duration-1000 ease-out
                        relative overflow-hidden
                        before:absolute before:inset-0
                        before:bg-gradient-to-r before:from-white/20 before:via-white/10 before:to-transparent
                        before:animate-pulse
                      `}
                      style={{
                        width: `${taskProgress}%`,
                        transform: 'translateZ(0)',
                      }}
                    >
                      {/* Shimmer effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-0 group-hover/item:opacity-100 transition-opacity duration-500">
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%] group-hover/item:translate-x-[200%] transition-transform duration-1000"></div>
                      </div>
                    </div>

                    {/* Progress bar highlight */}
                    <div className="absolute top-0 left-1 right-1 h-1 bg-gradient-to-r from-white/40 via-white/20 to-transparent rounded-full"></div>
                  </div>
                </div>
              </div>

              {/* Bottom shadow for depth */}
              <div className="absolute -bottom-1 left-2 right-2 h-2 bg-slate-900/5 rounded-full blur-sm transform scale-95 group-hover/item:scale-100 transition-transform duration-300"></div>
            </div>
          );
        })}

        {recentProjects.length === 0 && (
          <div className="
            text-center py-12 flex-1 flex items-center justify-center
            bg-gradient-to-br from-slate-50 to-slate-100
            border-2 border-dashed border-slate-300
            rounded-xl
          ">
            <p className="text-slate-500 font-medium text-lg drop-shadow-sm">No projects found</p>
          </div>
        )}
      </div>

      {/* Glossy overlay */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/5 via-transparent to-slate-900/[0.02] pointer-events-none"></div>

      {/* Bottom shadow for main container depth */}
      <div className="absolute -bottom-2 left-4 right-4 h-4 bg-slate-900/5 rounded-full blur-md transform scale-95 group-hover:scale-100 transition-transform duration-300"></div>
    </div>
  );
};

export default ProjectsProgress;
