const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5002;

// Middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/', (req, res) => {
  res.json({ message: 'Test server is running', timestamp: new Date().toISOString() });
});

// Test payments route
app.get('/api/payments', (req, res) => {
  res.json({
    success: true,
    data: {
      payments: [],
      summary: {
        totalProjects: 0,
        totalValue: 0,
        totalPaid: 0,
        totalPending: 0
      }
    }
  });
});

// Start server
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`Test server is running on port ${PORT}`);
  console.log(`Local access: http://localhost:${PORT}`);
});

server.on('error', (err) => {
  console.error('Server error:', err);
});
