const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function testPayment() {
  try {
    // Get a project to test with
    const project = await prisma.project.findFirst({
      where: {
        poValue: {
          not: null,
          gt: 0
        }
      },
      select: {
        id: true,
        name: true,
        poValue: true
      }
    });

    if (!project) {
      console.log('No projects with PO Value found');
      return;
    }

    console.log('Testing with project:', project.name, 'PO Value:', project.poValue);

    // Get a director user
    const director = await prisma.user.findFirst({
      where: {
        role: 'DIRECTOR'
      },
      select: {
        id: true,
        name: true
      }
    });

    if (!director) {
      console.log('No director user found');
      return;
    }

    // Create a test payment
    const payment = await prisma.payment.create({
      data: {
        projectId: project.id,
        amount: 50000, // ₹50,000 test payment
        paymentDate: new Date(),
        paymentMethod: 'BANK_TRANSFER',
        referenceNumber: 'TEST-REF-001',
        description: 'Test payment for demonstration',
        createdBy: director.id
      }
    });

    console.log('Payment created successfully:', {
      id: payment.id,
      amount: payment.amount,
      paymentDate: payment.paymentDate,
      paymentMethod: payment.paymentMethod,
      referenceNumber: payment.referenceNumber
    });

    // Check current payments for this project
    const allPayments = await prisma.payment.findMany({
      where: {
        projectId: project.id
      },
      select: {
        amount: true,
        paymentDate: true,
        paymentMethod: true,
        referenceNumber: true
      }
    });

    const totalPaid = allPayments.reduce((sum, p) => sum + Number(p.amount), 0);
    const pendingAmount = Number(project.poValue) - totalPaid;

    console.log('\nProject Payment Summary:');
    console.log('PO Value:', Number(project.poValue).toLocaleString('en-IN'));
    console.log('Total Paid:', totalPaid.toLocaleString('en-IN'));
    console.log('Pending Amount:', pendingAmount.toLocaleString('en-IN'));
    console.log('Number of payments:', allPayments.length);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPayment();
