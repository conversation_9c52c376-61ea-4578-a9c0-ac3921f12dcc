import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAtom } from 'jotai';
import { currentUserAtom } from '../store';
import { authAPI } from '../services/api';
import { AlertCircle, CheckCircle2 } from 'lucide-react';

const PasswordChangePage: React.FC = () => {
  const navigate = useNavigate();
  const [currentUser, setCurrentUser] = useAtom(currentUserAtom);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    // Validate passwords
    if (newPassword !== confirmPassword) {
      setError('New passwords do not match');
      return;
    }

    if (newPassword.length < 8) {
      setError('New password must be at least 8 characters long');
      return;
    }

    try {
      setIsLoading(true);
      const response = await authAPI.changePassword(currentPassword, newPassword);
      console.log('Password change response:', response);

      // Update the current user's passwordChanged status
      if (currentUser) {
        const updatedUser = {
          ...currentUser,
          passwordChanged: "Y"
        };
        console.log('Updating user state:', updatedUser);
        setCurrentUser(updatedUser);
      }

      // Show success message and redirect to dashboard after a delay
      setSuccess('Password changed successfully! Redirecting to dashboard...');

      // Fetch the latest user data to ensure we have the updated passwordChanged value
      try {
        const userResponse = await authAPI.getCurrentUser();
        console.log('Updated user data:', userResponse.data);
        setCurrentUser(userResponse.data);
      } catch (userError) {
        console.error('Error fetching updated user data:', userError);
      }

      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to change password';
      setError(errorMessage);
      console.error('Password change failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto px-4 py-8">
      <div className="card p-8 space-y-6">
        <div className="mb-2 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-1">Change Password</h2>
          <p className="text-gray-600">Please create a new password for your account</p>
        </div>

        {error && (
          <div className="bg-error/10 text-error-dark p-3 rounded-md flex items-center space-x-2">
            <AlertCircle size={18} />
            <span>{error}</span>
          </div>
        )}

        {success && (
          <div className="bg-success/10 text-success-dark p-3 rounded-md flex items-center space-x-2">
            <CheckCircle2 size={18} />
            <span>{success}</span>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="currentPassword" className="form-label">Current Password</label>
            <input
              id="currentPassword"
              type="password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              className="form-input"
              placeholder="Enter your current password"
              required
            />
          </div>

          <div>
            <label htmlFor="newPassword" className="form-label">New Password</label>
            <input
              id="newPassword"
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="form-input"
              placeholder="Enter your new password"
              required
            />
          </div>

          <div>
            <label htmlFor="confirmPassword" className="form-label">Confirm New Password</label>
            <input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="form-input"
              placeholder="Confirm your new password"
              required
            />
          </div>

          <div className="pt-2">
            <button
              type="submit"
              className="btn btn-primary w-full flex items-center justify-center"
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Changing Password...
                </span>
              ) : (
                <span className="flex items-center">
                  <CheckCircle2 size={18} className="mr-2" />
                  Change Password
                </span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PasswordChangePage;