import React, { useState, useEffect, useCallback } from 'react';
import { useAtom } from 'jotai';
import { normalizedProjectsAtom, projectsAtom, engineersAtom, currentUserAtom } from '../../store';
import { Section, Task, TaskStatus, UserRole, Project } from '../../types';
import { ChevronDown, ChevronRight, Plus, Edit, Trash2, MoreVertical, Check, X, Search, Filter, MessageSquare } from 'lucide-react';
import { tasksAPI, subtasksAPI } from '../../services/api';
import { useNotification } from '../../contexts/NotificationContext';
import { dataService } from '../../services/dataServiceSingleton';
import TaskFormModal from './TaskFormModal';
import SubtaskFormModal from './SubtaskFormModal';
import { toCamelCase } from '../../utils/textUtils';

interface SectionTaskViewProps {
  projectId: string;
}

const SectionTaskView: React.FC<SectionTaskViewProps> = ({ projectId }) => {
  const [projects] = useAtom(normalizedProjectsAtom);
  const [, setProjectsBase] = useAtom(projectsAtom); // Use the writable base atom for updates
  const [engineers] = useAtom(engineersAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const { showSuccess, showError } = useNotification();

  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());

  // Inline editing state
  const [editingTaskId, setEditingTaskId] = useState<string | null>(null);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>('');

  // New task creation state - always show empty row for each section
  const [newTaskData, setNewTaskData] = useState<Record<string, {
    name: string;
    description: string;
    assigneeId: string;
    startDate: string;
    endDate: string;
    priority: string;
  }>>({});

  // Subtask creation state
  const [newSubtaskForTask, setNewSubtaskForTask] = useState<string | null>(null);
  const [newSubtaskData, setNewSubtaskData] = useState({
    name: '',
    description: '',
    assigneeId: '',
    startDate: '',
    endDate: '',
    priority: 'Medium'
  });

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');

  // Modal state
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [showSubtaskModal, setShowSubtaskModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [selectedTaskForSubtask, setSelectedTaskForSubtask] = useState<Task | null>(null);

  // Get the current project
  const project = projects.find(p => p.id === projectId);
  const sections = project?.sections || [];

  // Refresh project data without page reload
  const refreshProjectData = useCallback(async () => {
    try {
      console.log('🔄 Refreshing project data...');
      await dataService.loadProjects(); // This will update the atom
      console.log('✅ Project data refreshed successfully');
    } catch (error) {
      console.error('❌ Failed to refresh project data:', error);
      showError('Refresh Failed', 'Failed to refresh project data');
    }
  }, [showError]);



  // Filter engineers for task assignment (only team leads)
  const teamLeads = engineers.filter(user => user.role === UserRole.TEAM_LEAD);
  // Filter engineers for subtask assignment (only engineers)
  const engineersOnly = engineers.filter(user => user.role === UserRole.ENGINEER);

  // Initialize all sections as expanded and create empty task data for each section
  useEffect(() => {
    if (sections.length > 0) {
      const sectionIds = sections.map(section => section.id);
      setExpandedSections(new Set(sectionIds));

      // Auto-expand tasks that have subtasks
      const tasksWithSubtasks = new Set<string>();
      sections.forEach(section => {
        if (section.tasks) {
          section.tasks.forEach(task => {
            if (task.subtasks && task.subtasks.length > 0) {
              tasksWithSubtasks.add(task.id);
            }
          });
        }
      });
      setExpandedTasks(tasksWithSubtasks);

      // Initialize empty task data for each section
      const initialTaskData: Record<string, any> = {};
      sections.forEach(section => {
        if (!newTaskData[section.id]) {
          initialTaskData[section.id] = {
            name: '',
            description: '',
            assigneeId: '',
            startDate: '',
            endDate: '',
            priority: 'Medium'
          };
        }
      });

      if (Object.keys(initialTaskData).length > 0) {
        setNewTaskData(prev => ({ ...prev, ...initialTaskData }));
      }
    }
  }, [sections]);

  // Toggle section expansion
  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  // Check if user can manage tasks (create/delete)
  const canManageTasks = (): boolean => {
    if (!currentUser) return false;
    return [UserRole.DIRECTOR, UserRole.PROJECT_MANAGER].includes(currentUser.role);
  };

  // Check if user can edit a specific field
  const canEditField = (field: string): boolean => {
    if (!currentUser) return false;

    // Directors and Project Managers can edit all fields
    if ([UserRole.DIRECTOR, UserRole.PROJECT_MANAGER].includes(currentUser.role)) {
      return true;
    }

    // Team leads can only edit status and end date
    if (currentUser.role === UserRole.TEAM_LEAD) {
      return ['status', 'endDate'].includes(field);
    }

    // Engineers cannot edit any fields in this view
    return false;
  };

  // Toggle task expansion (for subtasks)
  const toggleTaskExpansion = (taskId: string) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
      } else {
        newSet.add(taskId);
      }
      return newSet;
    });
  };

  // Handle inline editing
  const handleEditField = (item: Task | any, field: string, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    // Check if user has permission to edit this field
    if (!canEditField(field)) {
      showNotification('You do not have permission to edit this field', 'error');
      return;
    }

    setEditingTaskId(item.id);
    setEditingField(field);

    switch (field) {
      case 'name':
        setEditValue(item.name);
        break;
      case 'assigneeId':
        setEditValue(item.assigneeId);
        break;
      case 'status':
        setEditValue(item.status);
        break;
      case 'startDate':
        setEditValue(item.startDate ? new Date(item.startDate).toISOString().split('T')[0] : '');
        break;
      case 'endDate':
        setEditValue(item.endDate ? new Date(item.endDate).toISOString().split('T')[0] : '');
        break;
      case 'priority':
        setEditValue(item.priority || 'Medium');
        break;
      default:
        setEditValue('');
    }
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingTaskId(null);
    setEditingField(null);
    setEditValue('');
  };

  // Save edit
  const handleSaveEdit = async () => {
    if (!editingTaskId || !editingField) return;

    // Optimistic update - update UI immediately
    const optimisticUpdate = () => {
      setProjectsBase(prevProjects => {
        return prevProjects.map(proj => {
          if (proj.id !== projectId) return proj;

          return {
            ...proj,
            sections: proj.sections?.map(section => ({
              ...section,
              tasks: section.tasks?.map(task => {
                if (task.id === editingTaskId) {
                  return { ...task, [editingField!]: editValue };
                }
                return {
                  ...task,
                  subtasks: task.subtasks?.map(subtask => {
                    if (subtask.id === editingTaskId) {
                      return { ...subtask, [editingField!]: editValue };
                    }
                    return subtask;
                  })
                };
              })
            }))
          };
        });
      });
    };

    // Apply optimistic update
    optimisticUpdate();
    handleCancelEdit();

    try {
      const updatePayload: Record<string, any> = {};
      updatePayload[editingField] = editingField === 'name' ? toCamelCase(editValue) : editValue;

      // Determine if this is a task or subtask by checking if it exists in any task's subtasks
      let isSubtask = false;
      let parentTask = null;

      // Check all sections and tasks to find if this ID belongs to a subtask
      for (const section of sections) {
        for (const task of section.tasks || []) {
          if (task.subtasks?.some(subtask => subtask.id === editingTaskId)) {
            isSubtask = true;
            parentTask = task;
            break;
          }
        }
        if (isSubtask) break;
      }

      let apiResponse;
      if (isSubtask) {
        apiResponse = await subtasksAPI.updateSubtask(editingTaskId, updatePayload);
      } else {
        apiResponse = await tasksAPI.updateTask(editingTaskId, updatePayload);
      }

      // Update state with actual server response to get correct calculated values
      if (apiResponse?.data) {
        setProjectsBase(prevProjects => {
          return prevProjects.map(proj => {
            if (proj.id !== projectId) return proj;

            return {
              ...proj,
              sections: proj.sections?.map(section => ({
                ...section,
                tasks: section.tasks?.map(task => {
                  if (task.id === editingTaskId) {
                    // Update task with server response
                    return { ...task, ...apiResponse.data };
                  }
                  return {
                    ...task,
                    subtasks: task.subtasks?.map(subtask => {
                      if (subtask.id === editingTaskId) {
                        // Update subtask with server response
                        return { ...subtask, ...apiResponse.data };
                      }
                      return subtask;
                    })
                  };
                })
              }))
            };
          });
        });
      }

      showSuccess('Updated', 'Item updated successfully!');
    } catch (error: any) {
      console.error('Error updating item:', error);
      showError('Update Failed', `Failed to update: ${error.message || 'Unknown error'}`);
      // Revert optimistic update by refreshing data
      await refreshProjectData();
    }
  };

  // Handle keyboard events for inline editing
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEdit();
    }
  };

  // Handle keyboard events for subtask creation (separate from inline editing)
  const handleSubtaskKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      saveNewSubtask();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      cancelNewSubtask();
    }
  };

  // Handle input change - allow normal typing, apply camelCase on save
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const newValue = e.target.value;
    // Allow normal typing - camelCase conversion will happen on save
    setEditValue(newValue);
  };

  // Delete task/subtask
  const handleDeleteItem = async (itemId: string) => {
    console.log('🔧 Attempting to delete item:', itemId);

    // Determine if this is a task or subtask by checking if it exists in any task's subtasks
    let isSubtask = false;

    // Check all sections and tasks to find if this ID belongs to a subtask
    for (const section of sections) {
      for (const task of section.tasks || []) {
        if (task.subtasks?.some(subtask => subtask.id === itemId)) {
          isSubtask = true;
          break;
        }
      }
      if (isSubtask) break;
    }

    const itemType = isSubtask ? 'subtask' : 'task';
    console.log('🔧 Item type detected:', itemType);

    // Confirmation dialog removed as requested
    {
      // Optimistic delete - remove from UI immediately
      setProjectsBase(prevProjects => {
        return prevProjects.map(proj => {
          if (proj.id !== projectId) return proj;

          return {
            ...proj,
            sections: proj.sections?.map(section => ({
              ...section,
              tasks: section.tasks?.filter(task => {
                if (task.id === itemId) return false; // Remove task
                return {
                  ...task,
                  subtasks: task.subtasks?.filter(subtask => subtask.id !== itemId) // Remove subtask
                };
              }).map(task => ({
                ...task,
                subtasks: task.subtasks?.filter(subtask => subtask.id !== itemId)
              }))
            }))
          };
        });
      });

      try {
        if (isSubtask) {
          await subtasksAPI.deleteSubtask(itemId);
        } else {
          await tasksAPI.deleteTask(itemId);
        }

        // Success notification removed as requested
      } catch (error: any) {
        console.error(`Error deleting ${itemType}:`, error);

        // Handle specific error cases
        if (error.message?.includes('not found') || error.message?.includes('404')) {
          showError('Already Deleted', `This ${itemType} has already been deleted or doesn't exist. Refreshing data...`);
          // Refresh data to sync with current state
          setTimeout(() => refreshProjectData(), 2000);
        } else {
          showError('Delete Failed', `Failed to delete ${itemType}: ${error.message || 'Unknown error'}`);
        }
        // Revert optimistic delete by refreshing data
        await refreshProjectData();
      }
    }
  };

  // Update task data for a specific section - allow normal typing
  const updateNewTaskData = (sectionId: string, field: string, value: string) => {
    // Allow normal typing - camelCase conversion will happen on save
    setNewTaskData(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        [field]: value
      }
    }));
  };

  // Clear task data for a specific section
  const clearNewTaskData = (sectionId: string) => {
    setNewTaskData(prev => ({
      ...prev,
      [sectionId]: {
        name: '',
        description: '',
        assigneeId: '',
        startDate: '',
        endDate: '',
        priority: 'Medium'
      }
    }));
  };

  // Start creating a subtask
  const startNewSubtask = (taskId: string) => {
    console.log('🔧 Starting new subtask for task:', taskId);
    setNewSubtaskForTask(taskId);
    setNewSubtaskData({
      name: '',
      description: '',
      assigneeId: '',
      startDate: '',
      endDate: '',
      priority: 'Medium'
    });
    // Expand the task to show subtasks
    setExpandedTasks(prev => new Set([...prev, taskId]));
  };

  // Cancel subtask creation
  const cancelNewSubtask = () => {
    setNewSubtaskForTask(null);
    setNewSubtaskData({
      name: '',
      description: '',
      assigneeId: '',
      startDate: '',
      endDate: '',
      priority: 'Medium'
    });
  };

  // Save new task for a specific section
  const saveNewTask = async (sectionId: string) => {
    const taskData = newTaskData[sectionId];
    if (!taskData?.name || !taskData?.assigneeId) {
      showError('Validation Error', 'Please fill in task name and assignee');
      return;
    }

    try {
      const payload = {
        projectId,
        sectionId,
        name: toCamelCase(taskData.name),
        description: taskData.description,
        assigneeId: taskData.assigneeId,
        assigneeType: 'ENGINEER',
        department: project?.department || 'GENERAL',
        startDate: taskData.startDate,
        ...(taskData.endDate && { endDate: taskData.endDate }),
        status: TaskStatus.NOT_STARTED,
        priority: taskData.priority
      };

      await tasksAPI.createTask(payload);
      showSuccess('Task Created', 'Task created successfully!');

      // Clear the form for this section
      clearNewTaskData(sectionId);

      // Refresh project data without page reload
      await refreshProjectData();
    } catch (error: any) {
      console.error('Error creating task:', error);
      showError('Create Failed', `Failed to create task: ${error.message || 'Unknown error'}`);
    }
  };

  // Save new subtask
  const saveNewSubtask = async () => {
    if (!newSubtaskForTask || !newSubtaskData.name || !newSubtaskData.assigneeId) {
      showError('Validation Error', 'Please fill in subtask name and assignee');
      return;
    }

    try {
      const payload = {
        taskId: newSubtaskForTask,
        name: toCamelCase(newSubtaskData.name),
        description: newSubtaskData.description,
        assigneeId: newSubtaskData.assigneeId,
        assigneeType: 'ENGINEER',
        startDate: newSubtaskData.startDate,
        ...(newSubtaskData.endDate && { endDate: newSubtaskData.endDate }),
        status: TaskStatus.NOT_STARTED,
        priority: newSubtaskData.priority,
        createdBy: currentUser?.id // Add required createdBy field
      };

      await subtasksAPI.createSubtask(payload);
      showSuccess('Subtask Created', 'Subtask created successfully!');

      // Clear the form
      cancelNewSubtask();

      // Refresh project data without page reload
      await refreshProjectData();
    } catch (error: any) {
      console.error('Error creating subtask:', error);
      showError('Create Failed', `Failed to create subtask: ${error.message || 'Unknown error'}`);
    }
  };

  // Get status badge class
  const getStatusBadgeClass = (status: TaskStatus): string => {
    switch (status) {
      case TaskStatus.NOT_STARTED:
        return "px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800";
      case TaskStatus.IN_PROGRESS:
        return "px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800";
      case TaskStatus.COMPLETED:
        return "px-2 py-1 text-xs rounded-full bg-green-100 text-green-800";
      case TaskStatus.DELAYED:
        return "px-2 py-1 text-xs rounded-full bg-red-100 text-red-800";
      case TaskStatus.ON_HOLD:
        return "px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800";
      default:
        return "px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800";
    }
  };

  // Get engineer name
  const getEngineerName = (id: string) => {
    const engineer = engineers.find(e => e.id === id);
    return engineer ? engineer.name : 'Unassigned';
  };

  if (!project) {
    return <div>Project not found</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">Project Sections</h3>
        <div className="text-sm text-gray-500">
          {sections.length} sections
        </div>
      </div>

      {sections.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>No sections found for this project.</p>
          <p className="text-sm">Sections should be automatically created when the project is created.</p>
        </div>
      ) : (
        <div className="space-y-3">
          {sections.map((section) => (
            <div key={section.id} className="border border-gray-200 rounded-lg bg-white shadow-sm">
              {/* Section Header */}
              <div
                className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50"
                onClick={() => toggleSection(section.id)}
              >
                <div className="flex items-center space-x-3">
                  {expandedSections.has(section.id) ? (
                    <ChevronDown size={20} className="text-gray-500" />
                  ) : (
                    <ChevronRight size={20} className="text-gray-500" />
                  )}
                  <div>
                    <h4 className="font-medium text-gray-900">{section.name}</h4>
                    {section.description && (
                      <p className="text-sm text-gray-500">{section.description}</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">
                    {section.tasks?.length || 0} tasks
                  </span>
                </div>
              </div>

              {/* Section Content */}
              {expandedSections.has(section.id) && (
                <div className="border-t border-gray-200">
                  {/* Excel-like Tasks Table */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 border-separate border-spacing-0">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8"></th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">ID</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Task Name</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Owner</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {/* Existing Tasks */}
                        {section.tasks && section.tasks.length > 0 ? (
                          section.tasks
                            .sort((a, b) => (a.sequence || 0) - (b.sequence || 0)) // Sort by sequence in ascending order
                            .map((task) => (
                            <React.Fragment key={task.id}>
                              {/* Main Task Row */}
                              <tr className="hover:bg-gray-50">
                                <td className="px-4 py-3">
                                  {task.subtasks && task.subtasks.length > 0 && (
                                    <button
                                      onClick={() => toggleTaskExpansion(task.id)}
                                      className="p-1 hover:bg-gray-200 rounded"
                                      aria-label={expandedTasks.has(task.id) ? "Collapse subtasks" : "Expand subtasks"}
                                      title={expandedTasks.has(task.id) ? "Collapse subtasks" : "Expand subtasks"}
                                    >
                                      {expandedTasks.has(task.id) ? (
                                        <ChevronDown size={14} />
                                      ) : (
                                        <ChevronRight size={14} />
                                      )}
                                    </button>
                                  )}
                                </td>
                                <td className="px-4 py-3 text-sm font-mono text-gray-500">{task.displayId}</td>
                                <td className="px-4 py-3">
                                  {editingTaskId === task.id && editingField === 'name' ? (
                                    <input
                                      type="text"
                                      value={editValue}
                                      onChange={handleInputChange}
                                      onKeyDown={handleKeyDown}
                                      className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                      autoFocus
                                    />
                                  ) : (
                                    <span
                                      className={`px-2 py-1 rounded text-sm ${
                                        canEditField('name')
                                          ? 'cursor-pointer hover:bg-blue-50'
                                          : 'cursor-not-allowed text-gray-500 bg-gray-100'
                                      }`}
                                      onClick={(e) => handleEditField(task, 'name', e)}
                                      title={!canEditField('name') ? 'You cannot edit this field' : 'Click to edit'}
                                    >
                                      {task.name}
                                    </span>
                                  )}
                                </td>
                                <td className="px-4 py-3">
                                  {editingTaskId === task.id && editingField === 'assigneeId' ? (
                                    <select
                                      value={editValue}
                                      onChange={handleInputChange}
                                      onKeyDown={handleKeyDown}
                                      className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                      autoFocus
                                    >
                                       <option value="">Select Team Lead</option>
                                      {teamLeads.map(lead => (
                                        <option key={lead.id} value={lead.id}>{lead.name}</option>
                                      ))}
                                    </select>
                                  ) : (
                                    <span
                                      className={`px-2 py-1 rounded text-sm ${
                                        canEditField('assigneeId')
                                          ? 'cursor-pointer hover:bg-blue-50'
                                          : 'cursor-not-allowed text-gray-500 bg-gray-100'
                                      }`}
                                      onClick={(e) => handleEditField(task, 'assigneeId', e)}
                                      title={!canEditField('assigneeId') ? 'You cannot edit this field' : 'Click to edit'}
                                    >
                                      {getEngineerName(task.assigneeId)}
                                    </span>
                                  )}
                                </td>
                                <td className="px-4 py-3">
                                  {editingTaskId === task.id && editingField === 'status' ? (
                                    <select
                                      value={editValue}
                                      onChange={handleInputChange}
                                      onKeyDown={handleKeyDown}
                                      className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                      autoFocus
                                    >
                                      <option value={TaskStatus.NOT_STARTED}>Not Started</option>
                                      <option value={TaskStatus.IN_PROGRESS}>In Progress</option>
                                      <option value={TaskStatus.COMPLETED}>Completed</option>
                                      <option value={TaskStatus.DELAYED}>Delayed</option>
                                      <option value={TaskStatus.ON_HOLD}>On Hold</option>
                                    </select>
                                  ) : (
                                    <span
                                      className={`cursor-pointer hover:opacity-80 ${getStatusBadgeClass(task.status)}`}
                                      onClick={(e) => handleEditField(task, 'status', e)}
                                    >
                                      {task.status.replace(/_/g, ' ')}
                                    </span>
                                  )}
                                </td>
                                <td className="px-4 py-3">
                                  {editingTaskId === task.id && editingField === 'startDate' ? (
                                    <input
                                      type="date"
                                      value={editValue}
                                      onChange={handleInputChange}
                                      onKeyDown={handleKeyDown}
                                      className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                      autoFocus
                                    />
                                  ) : (
                                    <span
                                      className={`px-2 py-1 rounded text-sm ${
                                        canEditField('startDate')
                                          ? 'cursor-pointer hover:bg-blue-50'
                                          : 'cursor-not-allowed text-gray-500 bg-gray-100'
                                      }`}
                                      onClick={(e) => handleEditField(task, 'startDate', e)}
                                      title={!canEditField('startDate') ? 'You cannot edit this field' : 'Click to edit'}
                                    >
                                      {task.startDate ? new Date(task.startDate).toLocaleDateString() : '-'}
                                    </span>
                                  )}
                                </td>
                                <td className="px-4 py-3">
                                  {editingTaskId === task.id && editingField === 'endDate' ? (
                                    <input
                                      type="date"
                                      value={editValue}
                                      onChange={handleInputChange}
                                      onKeyDown={handleKeyDown}
                                      className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                      autoFocus
                                    />
                                  ) : (
                                    <span
                                      className="cursor-pointer hover:bg-blue-50 px-2 py-1 rounded text-sm"
                                      onClick={(e) => handleEditField(task, 'endDate', e)}
                                    >
                                      {task.endDate ? new Date(task.endDate).toLocaleDateString() : '-'}
                                    </span>
                                  )}
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-500">
                                  {task.startDate && task.endDate ?
                                    Math.ceil((new Date(task.endDate).getTime() - new Date(task.startDate).getTime()) / (1000 * 60 * 60 * 24)) + ' days'
                                    : '-'
                                  }
                                </td>
                                <td className="px-4 py-3">
                                  {editingTaskId === task.id && editingField === 'priority' ? (
                                    <select
                                      value={editValue}
                                      onChange={handleInputChange}
                                      onKeyDown={handleKeyDown}
                                      className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                      autoFocus
                                    >
                                      <option value="Low">Low</option>
                                      <option value="Medium">Medium</option>
                                      <option value="High">High</option>
                                    </select>
                                  ) : (
                                    <span
                                      className={`px-2 py-1 rounded text-sm ${
                                        canEditField('priority')
                                          ? 'cursor-pointer hover:bg-blue-50'
                                          : 'cursor-not-allowed text-gray-500 bg-gray-100'
                                      }`}
                                      onClick={(e) => handleEditField(task, 'priority', e)}
                                      title={!canEditField('priority') ? 'You cannot edit this field' : 'Click to edit'}
                                    >
                                      {task.priority || 'Medium'}
                                    </span>
                                  )}
                                </td>
                                <td className="px-4 py-3">
                                  <div className="flex space-x-1">
                                    {editingTaskId === task.id ? (
                                      <>
                                        <button
                                          onClick={handleSaveEdit}
                                          className="p-1 text-green-600 hover:bg-green-50 rounded"
                                          title="Save"
                                          aria-label="Save changes"
                                        >
                                          <Check size={14} />
                                        </button>
                                        <button
                                          onClick={handleCancelEdit}
                                          className="p-1 text-red-600 hover:bg-red-50 rounded"
                                          title="Cancel"
                                          aria-label="Cancel edit"
                                        >
                                          <X size={14} />
                                        </button>
                                      </>
                                    ) : (
                                      <>
                                        {canManageTasks() && (
                                          <>
                                            <button
                                              onClick={() => startNewSubtask(task.id)}
                                              className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                                              title="Add Subtask"
                                              aria-label="Add subtask"
                                            >
                                              <Plus size={14} />
                                            </button>
                                            <button
                                              onClick={() => window.open(`/tasks/${task.id}`, '_blank')}
                                              className="p-1 text-gray-600 hover:bg-gray-50 rounded"
                                              title="View Comments"
                                              aria-label="View comments"
                                            >
                                              <MessageSquare size={14} />
                                            </button>
                                            <button
                                              onClick={() => handleDeleteItem(task.id)}
                                              className="p-1 text-red-600 hover:bg-red-50 rounded"
                                              title="Delete"
                                              aria-label="Delete task"
                                            >
                                              <Trash2 size={14} />
                                            </button>
                                          </>
                                        )}
                                      </>
                                    )}
                                  </div>
                                </td>
                              </tr>

                              {/* Subtasks */}
                              {expandedTasks.has(task.id) && task.subtasks && task.subtasks.map((subtask) => (
                                <tr key={subtask.id} className="bg-gray-50">
                                  <td className="px-4 py-2 pl-8"></td>
                                  <td className="px-4 py-2 text-sm font-mono text-gray-400">
                                    <span className="ml-4">{subtask.displayId}</span>
                                  </td>
                                  <td className="px-4 py-2">
                                    {editingTaskId === subtask.id && editingField === 'name' ? (
                                      <input
                                        type="text"
                                        value={editValue}
                                        onChange={handleInputChange}
                                        onKeyDown={handleKeyDown}
                                        className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                        autoFocus
                                      />
                                    ) : (
                                      <span
                                        className={`px-2 py-1 rounded text-sm ${
                                          canEditField('name')
                                            ? 'cursor-pointer hover:bg-blue-50'
                                            : 'cursor-not-allowed text-gray-500 bg-gray-100'
                                        }`}
                                        onClick={(e) => handleEditField(subtask, 'name', e)}
                                        title={!canEditField('name') ? 'You cannot edit this field' : 'Click to edit'}
                                      >
                                        {subtask.name}
                                      </span>
                                    )}
                                  </td>
                                  <td className="px-4 py-2">
                                    {editingTaskId === subtask.id && editingField === 'assigneeId' ? (
                                      <select
                                        value={editValue}
                                        onChange={handleInputChange}
                                        onKeyDown={handleKeyDown}
                                        className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                        autoFocus
                                      > 
                                      <option value="">Select Team Lead</option>
                                        {engineersOnly.map(engineer => (
                                          <option key={engineer.id} value={engineer.id}>{engineer.name}</option>
                                        ))}
                                      </select>
                                    ) : (
                                      <span
                                        className={`px-2 py-1 rounded text-sm ${
                                          canEditField('assigneeId')
                                            ? 'cursor-pointer hover:bg-blue-50'
                                            : 'cursor-not-allowed text-gray-500 bg-gray-100'
                                        }`}
                                        onClick={(e) => handleEditField(subtask, 'assigneeId', e)}
                                        title={!canEditField('assigneeId') ? 'You cannot edit this field' : 'Click to edit'}
                                      >
                                        {getEngineerName(subtask.assigneeId)}
                                      </span>
                                    )}
                                  </td>
                                  <td className="px-4 py-2">
                                    {editingTaskId === subtask.id && editingField === 'status' ? (
                                      <select
                                        value={editValue}
                                        onChange={handleInputChange}
                                        onKeyDown={handleKeyDown}
                                        className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                        autoFocus
                                      >
                                        <option value={TaskStatus.NOT_STARTED}>Not Started</option>
                                        <option value={TaskStatus.IN_PROGRESS}>In Progress</option>
                                        <option value={TaskStatus.COMPLETED}>Completed</option>
                                        <option value={TaskStatus.DELAYED}>Delayed</option>
                                        <option value={TaskStatus.ON_HOLD}>On Hold</option>
                                      </select>
                                    ) : (
                                      <span
                                        className={`cursor-pointer hover:opacity-80 ${getStatusBadgeClass(subtask.status)}`}
                                        onClick={(e) => handleEditField(subtask, 'status', e)}
                                      >
                                        {subtask.status.replace(/_/g, ' ')}
                                      </span>
                                    )}
                                  </td>
                                  <td className="px-4 py-2">
                                    <span className="text-sm text-gray-500">
                                      {subtask.startDate ? new Date(subtask.startDate).toLocaleDateString() : '-'}
                                    </span>
                                  </td>
                                  <td className="px-4 py-2">
                                    <span className="text-sm text-gray-500">
                                      {subtask.endDate ? new Date(subtask.endDate).toLocaleDateString() : '-'}
                                    </span>
                                  </td>
                                  <td className="px-4 py-2 text-sm text-gray-500">
                                    {subtask.startDate && subtask.endDate ?
                                      Math.ceil((new Date(subtask.endDate).getTime() - new Date(subtask.startDate).getTime()) / (1000 * 60 * 60 * 24)) + ' days'
                                      : '-'
                                    }
                                  </td>
                                  <td className="px-4 py-2">
                                    <span className="text-sm text-gray-500">{subtask.priority || 'Medium'}</span>
                                  </td>
                                  <td className="px-4 py-2">
                                    <div className="flex space-x-1">
                                      {editingTaskId === subtask.id ? (
                                        <>
                                          <button
                                            onClick={handleSaveEdit}
                                            className="p-1 text-green-600 hover:bg-green-50 rounded"
                                            title="Save"
                                            aria-label="Save subtask changes"
                                          >
                                            <Check size={14} />
                                          </button>
                                          <button
                                            onClick={handleCancelEdit}
                                            className="p-1 text-red-600 hover:bg-red-50 rounded"
                                            title="Cancel"
                                            aria-label="Cancel subtask edit"
                                          >
                                            <X size={14} />
                                          </button>
                                        </>
                                      ) : (
                                        <>
                                          {canManageTasks() && (
                                            <button
                                              onClick={() => handleDeleteItem(subtask.id)}
                                              className="p-1 text-red-600 hover:bg-red-50 rounded"
                                              title="Delete"
                                              aria-label="Delete subtask"
                                            >
                                              <Trash2 size={14} />
                                            </button>
                                          )}
                                        </>
                                      )}
                                    </div>
                                  </td>
                                </tr>
                              ))}

                              {/* New Subtask Row - appears after all existing subtasks */}
                              {expandedTasks.has(task.id) && newSubtaskForTask === task.id && (
                                <tr className="bg-green-50">
                                  <td className="px-4 py-2 pl-8">
                                    <Plus size={14} className="text-green-600" />
                                  </td>
                                  <td className="px-4 py-2 text-sm text-gray-500">
                                    <span className="ml-4">New</span>
                                  </td>
                                  <td className="px-4 py-2">
                                    <input
                                      type="text"
                                      value={newSubtaskData.name}
                                      onChange={(e) => setNewSubtaskData(prev => ({ ...prev, name: e.target.value }))}
                                      onKeyDown={handleSubtaskKeyDown}
                                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                                      placeholder="Enter subtask name"
                                      autoFocus
                                    />
                                  </td>
                                  <td className="px-4 py-2">
                                    <select
                                      value={newSubtaskData.assigneeId}
                                      onChange={(e) => setNewSubtaskData(prev => ({ ...prev, assigneeId: e.target.value }))}
                                      onKeyDown={handleSubtaskKeyDown}
                                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                                    >
                                      <option value="">Select Engineer</option>
                                      {engineersOnly.map(engineer => (
                                        <option key={engineer.id} value={engineer.id}>{engineer.name}</option>
                                      ))}
                                    </select>
                                  </td>
                                  <td className="px-4 py-2">
                                    <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">NOT STARTED</span>
                                  </td>
                                  <td className="px-4 py-2">
                                    <input
                                      type="date"
                                      value={newSubtaskData.startDate}
                                      onChange={(e) => setNewSubtaskData(prev => ({ ...prev, startDate: e.target.value }))}
                                      onKeyDown={handleSubtaskKeyDown}
                                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                                    />
                                  </td>
                                  <td className="px-4 py-2">
                                    <input
                                      type="date"
                                      value={newSubtaskData.endDate}
                                      onChange={(e) => setNewSubtaskData(prev => ({ ...prev, endDate: e.target.value }))}
                                      onKeyDown={handleSubtaskKeyDown}
                                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                                    />
                                  </td>
                                  <td className="px-4 py-2 text-sm text-gray-500">-</td>
                                  <td className="px-4 py-2">
                                    <select
                                      value={newSubtaskData.priority}
                                      onChange={(e) => setNewSubtaskData(prev => ({ ...prev, priority: e.target.value }))}
                                      onKeyDown={handleSubtaskKeyDown}
                                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500"
                                    >
                                      <option value="Low">Low</option>
                                      <option value="Medium">Medium</option>
                                      <option value="High">High</option>
                                    </select>
                                  </td>
                                  <td className="px-4 py-2">
                                    <div className="flex space-x-1">
                                      <button
                                        onClick={saveNewSubtask}
                                        className="p-1 text-green-600 hover:bg-green-50 rounded"
                                        title="Save"
                                        aria-label="Save new subtask"
                                      >
                                        <Check size={14} />
                                      </button>
                                      <button
                                        onClick={cancelNewSubtask}
                                        className="p-1 text-red-600 hover:bg-red-50 rounded"
                                        title="Cancel"
                                        aria-label="Cancel new subtask"
                                      >
                                        <X size={14} />
                                      </button>
                                    </div>
                                  </td>
                                </tr>
                              )}
                            </React.Fragment>
                          ))
                        ) : (
                          // Show empty message if no tasks and user cannot manage tasks
                          !canManageTasks() && (
                            <tr>
                              <td colSpan={10} className="px-4 py-6 text-center text-gray-500">
                                <p className="text-sm">No tasks in this section yet.</p>
                              </td>
                            </tr>
                          )
                        )}

                        {/* Always show empty task creation row at the bottom */}
                        {canManageTasks() && newTaskData[section.id] && (
                          <tr className="bg-blue-50 border-t-2 border-blue-200">
                            <td className="px-4 py-3">
                              <Plus size={16} className="text-blue-600" />
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-500">New</td>
                            <td className="px-4 py-3">
                              <input
                                type="text"
                                value={newTaskData[section.id]?.name || ''}
                                onChange={(e) => updateNewTaskData(section.id, 'name', e.target.value)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                placeholder="Enter task name"
                              />
                            </td>
                            <td className="px-4 py-3">
                              <select
                                value={newTaskData[section.id]?.assigneeId || ''}
                                onChange={(e) => updateNewTaskData(section.id, 'assigneeId', e.target.value)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              >
                                <option value="">Select Team Lead</option>
                                {teamLeads.map(lead => (
                                  <option key={lead.id} value={lead.id}>{lead.name}</option>
                                ))}
                              </select>
                            </td>
                            <td className="px-4 py-3">
                              <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">NOT STARTED</span>
                            </td>
                            <td className="px-4 py-3">
                              <input
                                type="date"
                                value={newTaskData[section.id]?.startDate || ''}
                                onChange={(e) => updateNewTaskData(section.id, 'startDate', e.target.value)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              />
                            </td>
                            <td className="px-4 py-3">
                              <input
                                type="date"
                                value={newTaskData[section.id]?.endDate || ''}
                                onChange={(e) => updateNewTaskData(section.id, 'endDate', e.target.value)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              />
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-500">-</td>
                            <td className="px-4 py-3">
                              <select
                                value={newTaskData[section.id]?.priority || 'Medium'}
                                onChange={(e) => updateNewTaskData(section.id, 'priority', e.target.value)}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                              >
                                <option value="Low">Low</option>
                                <option value="Medium">Medium</option>
                                <option value="High">High</option>
                              </select>
                            </td>
                            <td className="px-4 py-3">
                              <div className="flex space-x-1">
                                <button
                                  onClick={() => saveNewTask(section.id)}
                                  className="p-1 text-green-600 hover:bg-green-50 rounded"
                                  title="Save"
                                  aria-label="Save new task"
                                >
                                  <Check size={14} />
                                </button>
                                <button
                                  onClick={() => clearNewTaskData(section.id)}
                                  className="p-1 text-red-600 hover:bg-red-50 rounded"
                                  title="Clear"
                                  aria-label="Clear new task"
                                >
                                  <X size={14} />
                                </button>
                              </div>
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SectionTaskView;
