import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

// @desc    Get all sections for a project
// @route   GET /api/projects/:projectId/sections
// @access  Private
export const getSectionsByProject = async (req: Request, res: Response): Promise<void> => {
  try {
    const { projectId } = req.params;

    // Check if project exists
    const projectExists = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!projectExists) {
      res.status(404).json({
        success: false,
        message: 'Project not found',
      });
      return;
    }

    // Get sections with their tasks
    const sections = await prisma.section.findMany({
      where: { projectId },
      orderBy: { sequence: 'asc' },
      include: {
        task: {
          include: {
            subtask: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: { sequence: 'asc' },
        },
      },
    });

    res.status(200).json({
      success: true,
      data: sections,
    });
  } catch (error) {
    console.error('Error fetching sections:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create a new section
// @route   POST /api/projects/:projectId/sections
// @access  Private
export const createSection = async (req: Request, res: Response): Promise<void> => {
  try {
    const { projectId } = req.params;
    const { name, description } = req.body;

    // Validate required fields
    if (!name) {
      res.status(400).json({
        success: false,
        message: 'Section name is required',
      });
      return;
    }

    // Check if project exists
    const projectExists = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!projectExists) {
      res.status(404).json({
        success: false,
        message: 'Project not found',
      });
      return;
    }

    // Check if section name already exists in this project
    const existingSection = await prisma.section.findFirst({
      where: {
        projectId,
        name,
      },
    });

    if (existingSection) {
      res.status(400).json({
        success: false,
        message: 'Section with this name already exists in the project',
      });
      return;
    }

    // Get next sequence number
    const lastSection = await prisma.section.findFirst({
      where: { projectId },
      orderBy: { sequence: 'desc' },
    });

    const nextSequence = lastSection ? lastSection.sequence + 1 : 1;

    // Create section
    const section = await prisma.section.create({
      data: {
        id: uuidv4(),
        projectId,
        name,
        description: description || '',
        sequence: nextSequence,
      },
    });

    res.status(201).json({
      success: true,
      data: section,
    });
  } catch (error) {
    console.error('Error creating section:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update a section
// @route   PUT /api/sections/:id
// @access  Private
export const updateSection = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;

    // Check if section exists
    const sectionExists = await prisma.section.findUnique({
      where: { id },
    });

    if (!sectionExists) {
      res.status(404).json({
        success: false,
        message: 'Section not found',
      });
      return;
    }

    // If name is being updated, check for duplicates
    if (name && name !== sectionExists.name) {
      const existingSection = await prisma.section.findFirst({
        where: {
          projectId: sectionExists.projectId,
          name,
          id: { not: id },
        },
      });

      if (existingSection) {
        res.status(400).json({
          success: false,
          message: 'Section with this name already exists in the project',
        });
        return;
      }
    }

    // Update section
    const updatedSection = await prisma.section.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
      },
    });

    res.status(200).json({
      success: true,
      data: updatedSection,
    });
  } catch (error) {
    console.error('Error updating section:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete a section
// @route   DELETE /api/sections/:id
// @access  Private
export const deleteSection = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // Check if section exists
    const sectionExists = await prisma.section.findUnique({
      where: { id },
      include: {
        task: true,
      },
    });

    if (!sectionExists) {
      res.status(404).json({
        success: false,
        message: 'Section not found',
      });
      return;
    }

    // Check if section has tasks
    if (sectionExists.task.length > 0) {
      res.status(400).json({
        success: false,
        message: 'Cannot delete section that contains tasks. Please move or delete all tasks first.',
      });
      return;
    }

    // Delete section
    await prisma.section.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: 'Section deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting section:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
