const fs = require('fs');
const path = require('path');

// Read the schema file
const schemaPath = path.join(__dirname, 'prisma', 'schema.prisma');
let schema = fs.readFileSync(schemaPath, 'utf8');

console.log('Fixing Prisma schema for SQLite compatibility...');

// Remove all map attributes from @relation
schema = schema.replace(/, map: "[^"]*"/g, '');

// Remove @db.Text annotations
schema = schema.replace(/@db\.Text/g, '');

// Remove @db.VarChar annotations
schema = schema.replace(/@db\.VarChar\(\d+\)/g, '');

// Remove all @@index with map attributes
schema = schema.replace(/@@index\([^)]*\), map: "[^"]*"/g, (match) => {
  return match.replace(/, map: "[^"]*"/, '');
});

// Write the fixed schema back
fs.writeFileSync(schemaPath, schema);

console.log('Schema fixed for SQLite compatibility!');
console.log('Changes made:');
console.log('- Removed all map attributes from @relation');
console.log('- Removed @db.Text annotations');
console.log('- Removed @db.VarChar annotations');
console.log('- Cleaned up @@index map attributes');
