import React, { createContext, useContext, ReactNode } from 'react';
import useConfirmation from '../hooks/useConfirmation';
import ConfirmationDialog from '../components/common/ConfirmationDialog';
import { ToastContainer } from '../components/common/Toast';

type ConfirmationContextType = ReturnType<typeof useConfirmation>;

const ConfirmationContext = createContext<ConfirmationContextType | undefined>(undefined);

interface ConfirmationProviderProps {
  children: ReactNode;
}

export const ConfirmationProvider: React.FC<ConfirmationProviderProps> = ({ children }) => {
  const confirmationHook = useConfirmation();

  return (
    <ConfirmationContext.Provider value={confirmationHook}>
      {children}
      
      {/* Confirmation Dialog */}
      {confirmationHook.confirmation && (
        <ConfirmationDialog
          isOpen={confirmationHook.confirmation.isOpen}
          onClose={confirmationHook.confirmation.onCancel}
          onConfirm={confirmationHook.confirmation.onConfirm}
          title={confirmationHook.confirmation.title}
          message={confirmationHook.confirmation.message}
          type={confirmationHook.confirmation.type}
          confirmText={confirmationHook.confirmation.confirmText}
          cancelText={confirmationHook.confirmation.cancelText}
          details={confirmationHook.confirmation.details}
          isLoading={confirmationHook.isLoading}
        />
      )}
      
      {/* Toast Container */}
      <ToastContainer
        toasts={confirmationHook.toasts}
        onRemoveToast={confirmationHook.removeToast}
      />
    </ConfirmationContext.Provider>
  );
};

export const useConfirmationContext = () => {
  const context = useContext(ConfirmationContext);
  if (context === undefined) {
    throw new Error('useConfirmationContext must be used within a ConfirmationProvider');
  }
  return context;
};

export default ConfirmationProvider;
