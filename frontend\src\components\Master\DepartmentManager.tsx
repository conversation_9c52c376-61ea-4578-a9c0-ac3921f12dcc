import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { departmentsAtom, currentUserAtom } from '../../store';
import { Department, UserRole } from '../../types';
import {
  Edit,
  Trash2,
  CheckCircle2,
  XCircle,
  Search,
  Shield
} from 'lucide-react';
import { departmentsAPI } from '../../services/api';

interface DepartmentManagerProps {
  showDialog: boolean;
  setShowDialog: (show: boolean) => void;
  editingDepartment: Department | null;
  setEditingDepartment: (dept: Department | null) => void;
}

interface APIResponse {
  success: boolean;
  data: Department;
  message?: string;
}

const DepartmentManager: React.FC<DepartmentManagerProps> = ({
  showDialog,
  setShowDialog,
  editingDepartment,
  setEditingDepartment
}) => {
  const [departments, setDepartments] = useAtom(departmentsAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [searchQuery, setSearchQuery] = useState('');
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Role-based permission functions
  const canManageDepartments = (): boolean => {
    return currentUser?.role === UserRole.DIRECTOR;
  };

  const canViewDepartments = (): boolean => {
    if (!currentUser) return false;
    return [UserRole.DIRECTOR, UserRole.PROJECT_MANAGER, UserRole.TEAM_LEAD].includes(currentUser.role as UserRole);
  };

  // Load departments on component mount
  useEffect(() => {
    loadDepartments();
  }, []);

  const loadDepartments = async () => {
    try {
      setIsLoading(true);
      const response = await departmentsAPI.getDepartments();
      setDepartments(response.data);
    } catch (error) {
      setErrorMessage('Failed to load departments');
      console.error('Error loading departments:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter departments based on search query
  const filteredDepartments = departments.filter(
    dept =>
      dept.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      dept.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (dept.description && dept.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const openDialog = (department?: Department) => {
    setEditingDepartment(department || null);
    setShowDialog(true);
    setErrorMessage(null);
  };

  const closeDialog = () => {
    setShowDialog(false);
    setEditingDepartment(null);
    setErrorMessage(null);
  };

  const handleSaveDepartment = async (department: Department) => {
    try {
      setIsLoading(true);
      setErrorMessage(null);

      if (editingDepartment) {
        // Update existing department
        const response = await departmentsAPI.updateDepartment(editingDepartment.id, department) as APIResponse;
        if (response.success) {
          setDepartments(prevDepartments =>
            prevDepartments.map(dept =>
              dept.id === editingDepartment.id ? response.data : dept
            )
          );
          setSuccessMessage('Department updated successfully!');
        } else {
          throw new Error(response.message || 'Failed to update department');
        }
      } else {
        // Create new department
        const response = await departmentsAPI.createDepartment(department) as APIResponse;
        if (response.success) {
          setDepartments(prevDepartments => [...prevDepartments, response.data]);
          setSuccessMessage('Department created successfully!');
        } else {
          throw new Error(response.message || 'Failed to create department');
        }
      }

      closeDialog();
    } catch (error: any) {
      console.error('Error saving department:', error);
      setErrorMessage(error.response?.data?.message || error.message || 'Failed to save department');
    } finally {
      setIsLoading(false);
    }

    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const deleteDepartment = async (id: string) => {
    if (!confirm('Are you sure you want to delete this department?')) {
      return;
    }

    try {
      setIsLoading(true);
      setErrorMessage(null);

      await departmentsAPI.deleteDepartment(id);
      setDepartments(prevDepartments => prevDepartments.filter(dept => dept.id !== id));
      setSuccessMessage('Department deleted successfully!');
    } catch (error: any) {
      console.error('Error deleting department:', error);
      setErrorMessage(error.response?.data?.message || error.message || 'Failed to delete department');
    } finally {
      setIsLoading(false);
    }

    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  // Early return if user doesn't have permission to view departments
  if (!canViewDepartments()) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="relative inline-block transform hover:scale-105 transition-transform duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full blur-xl opacity-30"></div>
            <div className="relative bg-white shadow-lg rounded-full p-6">
              <Shield size={48} className="mx-auto text-gray-400" />
            </div>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2 mt-6">Access Restricted</h3>
          <p className="text-gray-600">
            You don't have permission to view department management.
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Only Directors, Project Managers, and Team Leads can access this section.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {!canManageDepartments() && (
        <div className="bg-warning/10 text-warning-dark p-4 rounded-lg border border-warning/20 shadow-md transform hover:shadow-lg transition-shadow duration-300"
             style={{
               background: 'linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%)',
               backdropFilter: 'blur(10px)'
             }}>
          <div className="flex items-center">
            <div className="bg-warning/20 rounded-full p-2 mr-3">
              <Shield size={18} className="text-warning-dark" />
            </div>
            <div>
              <p className="font-medium">Read-Only Access</p>
              <p className="text-sm">
                Only Directors can create, edit, or delete departments. You can view the department list below.
              </p>
            </div>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="bg-success/10 text-success-dark p-4 rounded-lg border border-success/20 shadow-md transform hover:shadow-lg transition-all duration-300 fade-in"
             style={{
               background: 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%)',
               backdropFilter: 'blur(10px)'
             }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="bg-success/20 rounded-full p-2 mr-3">
                <CheckCircle2 size={18} className="text-success-dark" />
              </div>
              <span>{successMessage}</span>
            </div>
            <button
              onClick={() => setSuccessMessage(null)}
              className="text-gray-500 hover:text-gray-700 transform hover:scale-110 transition-transform duration-200"
            >
              <XCircle size={18} />
            </button>
          </div>
        </div>
      )}

      {errorMessage && (
        <div className="bg-error/10 text-error p-4 rounded-lg border border-error/20 shadow-md transform hover:shadow-lg transition-all duration-300 fade-in"
             style={{
               background: 'linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%)',
               backdropFilter: 'blur(10px)'
             }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="bg-error/20 rounded-full p-2 mr-3">
                <XCircle size={18} className="text-error" />
              </div>
              <span>{errorMessage}</span>
            </div>
            <button
              onClick={() => setErrorMessage(null)}
              className="text-gray-500 hover:text-gray-700 transform hover:scale-110 transition-transform duration-200"
            >
              <XCircle size={18} />
            </button>
          </div>
        </div>
      )}

      <div className="mb-4 relative">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-10">
          <Search size={18} className="text-gray-400" />
        </div>
        <input
          type="search"
          className="form-input pl-10 w-full rounded-lg border-gray-200 shadow-sm focus:shadow-lg transition-all duration-300"
          placeholder="Search departments..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          style={{
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%)',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.8)'
          }}
        />
      </div>

      <div className="card overflow-hidden rounded-xl shadow-lg border-0"
           style={{
             background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',
             backdropFilter: 'blur(20px)',
             boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8)'
           }}>
        <table className="min-w-full divide-y divide-gray-200/50">
          <thead style={{
            background: 'linear-gradient(135deg, rgba(249, 250, 251, 0.8) 0%, rgba(243, 244, 246, 0.8) 100%)'
          }}>
            <tr>
              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                Department Name
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                Code
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                Description
              </th>
              <th scope="col" className="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200/30">
            {isLoading ? (
              <tr>
                <td colSpan={4} className="px-6 py-8 text-center">
                  <div className="flex justify-center">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/40 rounded-full blur-sm"></div>
                      <svg className="relative animate-spin h-6 w-6 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </div>
                  </div>
                </td>
              </tr>
            ) : (
              <>
                {filteredDepartments.map((department, index) => (
                  <tr key={department.id} 
                      className="hover:bg-gradient-to-r hover:from-gray-50/50 hover:to-blue-50/30 transition-all duration-300 transform hover:scale-[1.01]"
                      style={{
                        animationDelay: `${index * 50}ms`
                      }}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{department.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {department.code}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {department.description || '—'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      {canManageDepartments() ? (
                        <div className="flex items-center justify-end space-x-3">
                          <button
                            onClick={() => openDialog(department)}
                            className="text-primary hover:text-primary-dark transform hover:scale-110 active:scale-95 transition-all duration-200 p-2 rounded-lg hover:bg-primary/10"
                            disabled={isLoading}
                            title="Edit Department"
                          >
                            <Edit size={18} />
                          </button>
                          <button
                            onClick={() => deleteDepartment(department.id)}
                            className="text-gray-400 hover:text-error transform hover:scale-110 active:scale-95 transition-all duration-200 p-2 rounded-lg hover:bg-red-50"
                            disabled={isLoading}
                            title="Delete Department"
                          >
                            <Trash2 size={18} />
                          </button>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm bg-gray-100/50 px-3 py-1 rounded-full">
                          Read-only
                        </span>
                      )}
                    </td>
                  </tr>
                ))}

                {filteredDepartments.length === 0 && (
                  <tr>
                    <td colSpan={4} className="px-6 py-12 text-center">
                      <div className="text-gray-500">
                        <div className="relative inline-block mb-4">
                          <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full blur-xl opacity-20"></div>
                          <div className="relative bg-gray-100 rounded-full p-4">
                            <Search size={24} className="text-gray-400" />
                          </div>
                        </div>
                        <p className="text-sm">No departments found</p>
                      </div>
                    </td>
                  </tr>
                )}
              </>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DepartmentManager;