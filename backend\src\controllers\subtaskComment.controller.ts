import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

// @desc    Get comments for a subtask
// @route   GET /api/subtasks/:subtaskId/comments
// @access  Private
export const getSubtaskComments = async (req: Request, res: Response): Promise<void> => {
  try {
    const { subtaskId } = req.params;

    // Check if subtask exists and user has access
    const subtask = await prisma.subtask.findUnique({
      where: { id: subtaskId },
      include: {
        task: {
          include: {
            project: {
              select: {
                id: true,
                department: true
              }
            },
            user: {
              select: {
                id: true,
                department: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            department: true
          }
        }
      }
    });

    if (!subtask) {
      res.status(404).json({
        success: false,
        message: 'Subtask not found',
      });
      return;
    }

    // Check user permissions
    const user = req.user;
    const hasAccess =
      user.role === 'DIRECTOR' ||
      user.role === 'PROJECT_MANAGER' ||
      (user.role === 'TEAM_LEAD' && (
        subtask.task.project.department === user.department || // Project in same department
        subtask.task.assigneeId === user.id || // Assigned to parent task
        subtask.assigneeId === user.id || // Assigned to subtask
        subtask.user.department === user.department // Subtask assignee in same department
      )) ||
      (user.role === 'ENGINEER' && subtask.assigneeId === user.id);

    if (!hasAccess) {
      res.status(403).json({
        success: false,
        message: 'Access denied',
      });
      return;
    }

    const comments = await prisma.subtaskcomment.findMany({
      where: { subtaskId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    res.status(200).json({
      success: true,
      count: comments.length,
      data: comments,
    });
  } catch (error) {
    console.error('Get subtask comments error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create a comment for a subtask
// @route   POST /api/subtasks/:subtaskId/comments
// @access  Private
export const createSubtaskComment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { subtaskId } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    if (!content || !content.trim()) {
      res.status(400).json({
        success: false,
        message: 'Comment content is required',
      });
      return;
    }

    // Check if subtask exists and user has access
    const subtask = await prisma.subtask.findUnique({
      where: { id: subtaskId },
      include: {
        task: {
          include: {
            project: {
              select: {
                id: true,
                department: true
              }
            },
            user: {
              select: {
                id: true,
                department: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            department: true
          }
        }
      }
    });

    if (!subtask) {
      res.status(404).json({
        success: false,
        message: 'Subtask not found',
      });
      return;
    }

    // Check user permissions
    const user = req.user;
    const hasAccess =
      user.role === 'DIRECTOR' ||
      user.role === 'PROJECT_MANAGER' ||
      (user.role === 'TEAM_LEAD' && (
        subtask.task.project.department === user.department || // Project in same department
        subtask.task.assigneeId === user.id || // Assigned to parent task
        subtask.assigneeId === user.id || // Assigned to subtask
        subtask.user.department === user.department // Subtask assignee in same department
      )) ||
      (user.role === 'ENGINEER' && subtask.assigneeId === user.id);

    if (!hasAccess) {
      res.status(403).json({
        success: false,
        message: 'Access denied',
      });
      return;
    }

    const comment = await prisma.subtaskcomment.create({
      data: {
        id: uuidv4(),
        subtaskId,
        userId,
        content: content.trim(),
        updatedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: comment,
    });
  } catch (error) {
    console.error('Create subtask comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update a subtask comment
// @route   PUT /api/subtasks/:subtaskId/comments/:commentId
// @access  Private
export const updateSubtaskComment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { subtaskId, commentId } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    if (!content || !content.trim()) {
      res.status(400).json({
        success: false,
        message: 'Comment content is required',
      });
      return;
    }

    // Check if comment exists and belongs to user
    const comment = await prisma.subtaskcomment.findUnique({
      where: { id: commentId },
      include: {
        subtask: true
      }
    });

    if (!comment) {
      res.status(404).json({
        success: false,
        message: 'Comment not found',
      });
      return;
    }

    if (comment.subtaskId !== subtaskId) {
      res.status(400).json({
        success: false,
        message: 'Comment does not belong to this subtask',
      });
      return;
    }

    if (comment.userId !== userId) {
      res.status(403).json({
        success: false,
        message: 'You can only edit your own comments',
      });
      return;
    }

    const updatedComment = await prisma.subtaskcomment.update({
      where: { id: commentId },
      data: {
        content: content.trim(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    res.status(200).json({
      success: true,
      data: updatedComment,
    });
  } catch (error) {
    console.error('Update subtask comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete a subtask comment
// @route   DELETE /api/subtasks/:subtaskId/comments/:commentId
// @access  Private
export const deleteSubtaskComment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { subtaskId, commentId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Check if comment exists
    const comment = await prisma.subtaskcomment.findUnique({
      where: { id: commentId },
      include: {
        subtask: true
      }
    });

    if (!comment) {
      res.status(404).json({
        success: false,
        message: 'Comment not found',
      });
      return;
    }

    if (comment.subtaskId !== subtaskId) {
      res.status(400).json({
        success: false,
        message: 'Comment does not belong to this subtask',
      });
      return;
    }

    // Check permissions - user can delete their own comments, or admins can delete any
    const canDelete =
      comment.userId === userId ||
      userRole === 'DIRECTOR' ||
      userRole === 'PROJECT_MANAGER';

    if (!canDelete) {
      res.status(403).json({
        success: false,
        message: 'You can only delete your own comments',
      });
      return;
    }

    await prisma.subtaskcomment.delete({
      where: { id: commentId }
    });

    res.status(200).json({
      success: true,
      message: 'Comment deleted successfully',
    });
  } catch (error) {
    console.error('Delete subtask comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
