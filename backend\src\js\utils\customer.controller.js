const { PrismaClient } = require('@prisma/client');

// Use the existing Prisma instance from the parent module
const prisma = global.prisma || new PrismaClient();

// @desc    Get all customers
// @route   GET /api/customers
// @access  Private
const getCustomers = async (req, res) => {
  try {
    const customers = await prisma.customer.findMany({
      include: {
        _count: {
          select: {
            projects: true,
            moms: true,
          },
        },
      },
    });

    res.status(200).json({
      success: true,
      count: customers.length,
      data: customers,
    });
  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single customer
// @route   GET /api/customers/:id
// @access  Private
const getCustomer = async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        projects: true,
        moms: true,
      },
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    res.status(200).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('Get customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create customer
// @route   POST /api/customers
// @access  Private/Admin
const createCustomer = async (req, res) => {
  try {
    const { name, code, contactName, email, phone, address } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a customer name',
      });
    }

    // Check if customer with same name or code already exists
    if (code) {
      const existingCustomer = await prisma.customer.findFirst({
        where: {
          OR: [
            { name },
            { code },
          ],
        },
      });

      if (existingCustomer) {
        return res.status(400).json({
          success: false,
          message: 'Customer with this name or code already exists',
        });
      }
    }

    console.log('Creating customer with data:', {
      name,
      code: code || 'Not provided',
      contactName: contactName || 'Not provided',
      email: email || 'Not provided',
      phone: phone || 'Not provided',
      address: address || 'Not provided',
    });

    // Create customer
    const customer = await prisma.customer.create({
      data: {
        name,
        code,
        contactName,
        email,
        phone,
        address,
      },
    });

    console.log('Customer created successfully:', customer.id);

    res.status(201).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('Create customer error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: `A customer with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during customer creation',
    });
  }
};

// @desc    Update customer
// @route   PUT /api/customers/:id
// @access  Private/Admin
const updateCustomer = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, contactName, email, phone, address } = req.body;

    // Check if customer exists
    const customerExists = await prisma.customer.findUnique({
      where: { id },
    });

    if (!customerExists) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Check if name or code is already in use by another customer
    if (name !== customerExists.name || code !== customerExists.code) {
      const existingCustomer = await prisma.customer.findFirst({
        where: {
          OR: [
            { name },
            { code },
          ],
          NOT: {
            id,
          },
        },
      });

      if (existingCustomer) {
        return res.status(400).json({
          success: false,
          message: 'Customer with this name or code already exists',
        });
      }
    }

    console.log('Updating customer with ID:', id);
    console.log('Update data:', {
      name: name || 'Not changed',
      code: code || 'Not changed',
      contactName: contactName || 'Not changed',
      email: email || 'Not changed',
      phone: phone || 'Not changed',
      address: address || 'Not changed',
    });

    // Update customer
    const customer = await prisma.customer.update({
      where: { id },
      data: {
        name,
        code,
        contactName,
        email,
        phone,
        address,
      },
    });

    console.log('Customer updated successfully:', customer.id);

    res.status(200).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('Update customer error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: `A customer with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during customer update',
    });
  }
};

// @desc    Delete customer
// @route   DELETE /api/customers/:id
// @access  Private/Admin
const deleteCustomer = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if customer exists
    const customerExists = await prisma.customer.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            projects: true,
            moms: true,
          },
        },
      },
    });

    if (!customerExists) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Check if customer has related projects or MOMs
    if (customerExists._count.projects > 0 || customerExists._count.moms > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete customer with associated projects or meetings',
      });
    }

    // Delete customer
    await prisma.customer.delete({
      where: { id },
    });

    console.log('Customer deleted successfully:', id);

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

module.exports = {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer
};
