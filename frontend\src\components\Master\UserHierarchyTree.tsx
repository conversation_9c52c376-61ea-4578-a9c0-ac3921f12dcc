import React, { useState } from 'react';
import { useAtom } from 'jotai';
import { currentUserAtom } from '../../store';
import { User, UserRole } from '../../types';
import {
  ChevronDown,
  ChevronRight,
  Users,
  User as UserIcon,
  Mail,
  Code,
  Calendar,
  Award
} from 'lucide-react';

interface UserHierarchyTreeProps {
  users: User[];
  onUserSelect?: (user: User) => void;
}

interface DepartmentNode {
  name: string;
  teamLeads: User[];
  engineers: User[];
}

const UserHierarchyTree: React.FC<UserHierarchyTreeProps> = ({ users, onUserSelect }) => {
  const [currentUser] = useAtom(currentUserAtom);
  const [expandedDepartments, setExpandedDepartments] = useState<Set<string>>(new Set());
  const [expandedTeamLeads, setExpandedTeamLeads] = useState<Set<string>>(new Set());

  // Filter users based on current user's role
  const getVisibleUsers = () => {
    if (currentUser?.role === UserRole.DIRECTOR) {
      // Directors can see all users
      return users;
    } else if (currentUser?.role === UserRole.PROJECT_MANAGER) {
      // Project managers can see team leads and engineers
      return users.filter(user =>
        user.role === UserRole.TEAM_LEAD ||
        user.role === UserRole.ENGINEER ||
        user.role === UserRole.PROJECT_MANAGER
      );
    } else if (currentUser?.role === UserRole.TEAM_LEAD) {
      // Team leads can see engineers in their department and themselves
      return users.filter(user =>
        (user.role === UserRole.ENGINEER && user.department === currentUser.department) ||
        user.id === currentUser.id
      );
    }
    return [];
  };

  // Organize users by department and role
  const organizeUsersByDepartment = (): DepartmentNode[] => {
    const visibleUsers = getVisibleUsers();
    const departmentMap = new Map<string, DepartmentNode>();

    visibleUsers.forEach(user => {
      if (!departmentMap.has(user.department)) {
        departmentMap.set(user.department, {
          name: user.department,
          teamLeads: [],
          engineers: []
        });
      }

      const dept = departmentMap.get(user.department)!;
      if (user.role === UserRole.TEAM_LEAD) {
        dept.teamLeads.push(user);
      } else if (user.role === UserRole.ENGINEER) {
        dept.engineers.push(user);
      }
    });

    return Array.from(departmentMap.values()).sort((a, b) => a.name.localeCompare(b.name));
  };

  const toggleDepartment = (departmentName: string) => {
    const newExpanded = new Set(expandedDepartments);
    if (newExpanded.has(departmentName)) {
      newExpanded.delete(departmentName);
    } else {
      newExpanded.add(departmentName);
    }
    setExpandedDepartments(newExpanded);
  };

  const toggleTeamLead = (teamLeadId: string) => {
    const newExpanded = new Set(expandedTeamLeads);
    if (newExpanded.has(teamLeadId)) {
      newExpanded.delete(teamLeadId);
    } else {
      newExpanded.add(teamLeadId);
    }
    setExpandedTeamLeads(newExpanded);
  };

  const handleUserClick = (user: User) => {
    if (onUserSelect) {
      onUserSelect(user);
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.PROJECT_MANAGER:
        return 'bg-purple-100 text-purple-800';
      case UserRole.TEAM_LEAD:
        return 'bg-blue-100 text-blue-800';
      case UserRole.ENGINEER:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const departments = organizeUsersByDepartment();

  if (departments.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Users size={48} className="mx-auto mb-4 opacity-50" />
        <p>No team members found</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center mb-6">
        <Users className="mr-2 text-blue-600" size={24} />
        <h2 className="text-xl font-semibold text-gray-900">Team Hierarchy</h2>
      </div>

      {departments.map(department => {
        const isDeptExpanded = expandedDepartments.has(department.name);
        const totalMembers = department.teamLeads.length + department.engineers.length;

        return (
          <div key={department.name} className="border border-gray-200 rounded-lg overflow-hidden">
            {/* Department Header */}
            <div
              className="bg-gray-50 px-4 py-3 cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={() => toggleDepartment(department.name)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  {isDeptExpanded ? (
                    <ChevronDown size={20} className="text-gray-500 mr-2" />
                  ) : (
                    <ChevronRight size={20} className="text-gray-500 mr-2" />
                  )}
                  <h3 className="font-medium text-gray-900">{department.name}</h3>
                  <span className="ml-2 px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded-full">
                    {totalMembers} members
                  </span>
                </div>
              </div>
            </div>

            {/* Department Content */}
            {isDeptExpanded && (
              <div className="bg-white">
                {/* Team Leads */}
                {department.teamLeads.map(teamLead => {
                  const isTLExpanded = expandedTeamLeads.has(teamLead.id);
                  // For this demo, we'll show engineers in the same department under team leads
                  // In a real scenario, you might have a specific teamLeadId field in the engineer record
                  const engineersUnderTL = department.engineers;

                  return (
                    <div key={teamLead.id} className="border-t border-gray-100">
                      {/* Team Lead */}
                      <div
                        className="px-6 py-3 hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => toggleTeamLead(teamLead.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            {engineersUnderTL.length > 0 && (
                              <>
                                {isTLExpanded ? (
                                  <ChevronDown size={16} className="text-gray-400 mr-2" />
                                ) : (
                                  <ChevronRight size={16} className="text-gray-400 mr-2" />
                                )}
                              </>
                            )}
                            <UserIcon size={16} className="text-blue-600 mr-3" />
                            <div>
                              <div className="flex items-center">
                                <span className="font-medium text-gray-900">{teamLead.name}</span>
                                <span className={`ml-2 px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(teamLead.role)}`}>
                                  Team Lead
                                </span>
                              </div>
                              <div className="flex items-center text-sm text-gray-500 mt-1">
                                <Mail size={12} className="mr-1" />
                                {teamLead.email}
                                {teamLead.code && (
                                  <>
                                    <Code size={12} className="ml-3 mr-1" />
                                    {teamLead.code}
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleUserClick(teamLead);
                            }}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                          >
                            View Details
                          </button>
                        </div>
                      </div>

                      {/* Engineers under Team Lead */}
                      {isTLExpanded && engineersUnderTL.length > 0 && (
                        <div className="bg-gray-25 border-l-2 border-blue-200 ml-6">
                          {engineersUnderTL.map(engineer => (
                            <div
                              key={engineer.id}
                              className="px-6 py-3 hover:bg-gray-50 cursor-pointer transition-colors border-t border-gray-100"
                              onClick={() => handleUserClick(engineer)}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <div className="w-4 h-4 mr-3">
                                    <div className="w-2 h-2 bg-green-400 rounded-full mx-auto"></div>
                                  </div>
                                  <UserIcon size={14} className="text-green-600 mr-3" />
                                  <div>
                                    <div className="flex items-center">
                                      <span className="font-medium text-gray-900">{engineer.name}</span>
                                      <span className={`ml-2 px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(engineer.role)}`}>
                                        Engineer
                                      </span>
                                    </div>
                                    <div className="flex items-center text-sm text-gray-500 mt-1">
                                      <Mail size={12} className="mr-1" />
                                      {engineer.email}
                                      {engineer.code && (
                                        <>
                                          <Code size={12} className="ml-3 mr-1" />
                                          {engineer.code}
                                        </>
                                      )}
                                      {engineer.skills && (
                                        <>
                                          <Award size={12} className="ml-3 mr-1" />
                                          {engineer.skills.split(',').slice(0, 2).join(', ')}
                                          {engineer.skills.split(',').length > 2 && '...'}
                                        </>
                                      )}
                                    </div>
                                  </div>
                                </div>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleUserClick(engineer);
                                  }}
                                  className="text-green-600 hover:text-green-800 text-sm font-medium"
                                >
                                  View Details
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                })}

                {/* Engineers without Team Leads (if any) */}
                {department.teamLeads.length === 0 && department.engineers.map(engineer => (
                  <div
                    key={engineer.id}
                    className="px-6 py-3 hover:bg-gray-50 cursor-pointer transition-colors border-t border-gray-100"
                    onClick={() => handleUserClick(engineer)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <UserIcon size={16} className="text-green-600 mr-3" />
                        <div>
                          <div className="flex items-center">
                            <span className="font-medium text-gray-900">{engineer.name}</span>
                            <span className={`ml-2 px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(engineer.role)}`}>
                              Engineer
                            </span>
                          </div>
                          <div className="flex items-center text-sm text-gray-500 mt-1">
                            <Mail size={12} className="mr-1" />
                            {engineer.email}
                            {engineer.code && (
                              <>
                                <Code size={12} className="ml-3 mr-1" />
                                {engineer.code}
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleUserClick(engineer);
                        }}
                        className="text-green-600 hover:text-green-800 text-sm font-medium"
                      >
                        View Details
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default UserHierarchyTree;
