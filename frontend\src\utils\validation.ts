// Validation utilities for form fields

export interface ValidationResult {
  isValid: boolean;
  message?: string;
}

// Validate name fields (strict for user names)
export const validateName = (name: string): ValidationResult => {
  if (!name || name.trim().length === 0) {
    return { isValid: false, message: 'Name is required' };
  }

  if (name.trim().length < 2) {
    return { isValid: false, message: 'Name must be at least 2 characters long' };
  }

  if (name.trim().length > 100) {
    return { isValid: false, message: 'Name must be less than 100 characters' };
  }

  // Allow letters, spaces, hyphens, apostrophes, and dots
  const nameRegex = /^[a-zA-Z\s\-'.]+$/;
  if (!nameRegex.test(name.trim())) {
    return { isValid: false, message: 'Name can only contain letters, spaces, hyphens, apostrophes, and dots' };
  }

  return { isValid: true };
};

// Validate task/project name fields (flexible)
export const validateTaskName = (name: string): ValidationResult => {
  if (!name || name.trim().length === 0) {
    return { isValid: false, message: 'Name is required' };
  }

  if (name.trim().length < 2) {
    return { isValid: false, message: 'Name must be at least 2 characters long' };
  }

  if (name.trim().length > 200) {
    return { isValid: false, message: 'Name must be less than 200 characters' };
  }

  // Allow letters (including accented characters), numbers, spaces, hyphens, apostrophes, dots, parentheses, and common symbols
  const nameRegex = /^[a-zA-ZÀ-ÿ0-9\s\-'.()\[\]_&@#$%+:,/]+$/;
  if (!nameRegex.test(name.trim())) {
    return { isValid: false, message: 'Name contains invalid characters. Only letters, numbers, spaces, and common symbols are allowed' };
  }

  return { isValid: true };
};

// Validate email addresses
export const validateEmail = (email: string): ValidationResult => {
  if (!email || email.trim().length === 0) {
    return { isValid: false, message: 'Email is required' };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email.trim())) {
    return { isValid: false, message: 'Please enter a valid email address' };
  }

  return { isValid: true };
};

// Validate phone numbers - must be exactly 10 digits
export const validatePhone = (phone: string): ValidationResult => {
  if (!phone || phone.trim().length === 0) {
    return { isValid: false, message: 'Phone number is required' };
  }

  // Remove all non-digit characters
  const digitsOnly = phone.replace(/\D/g, '');

  if (digitsOnly.length !== 10) {
    return { isValid: false, message: 'Phone number must be exactly 10 digits' };
  }

  return { isValid: true };
};

// Validate customer/department codes
export const validateCode = (code: string): ValidationResult => {
  if (!code || code.trim().length === 0) {
    return { isValid: false, message: 'Code is required' };
  }

  if (code.trim().length < 2) {
    return { isValid: false, message: 'Code must be at least 2 characters long' };
  }

  if (code.trim().length > 20) {
    return { isValid: false, message: 'Code must be less than 20 characters' };
  }

  // Allow letters, numbers, hyphens, and underscores
  const codeRegex = /^[a-zA-Z0-9\-_]+$/;
  if (!codeRegex.test(code.trim())) {
    return { isValid: false, message: 'Code can only contain letters, numbers, hyphens, and underscores' };
  }

  return { isValid: true };
};

// Validate project codes with specific format: ****-**-***
export const validateProjectCode = (code: string): ValidationResult => {
  if (!code || code.trim().length === 0) {
    return { isValid: false, message: 'Project code is required' };
  }

  // Check exact format: 4 chars, dash, 2 chars, dash, 3 chars
  const projectCodeRegex = /^[a-zA-Z0-9]{4}-[a-zA-Z0-9]{2}-[a-zA-Z0-9]{3}$/;

  if (!projectCodeRegex.test(code.trim())) {
    return {
      isValid: false,
      message: 'Project code must follow format: ****-**-*** (4 characters, dash, 2 characters, dash, 3 characters)'
    };
  }

  return { isValid: true };
};

// Format project code input as user types
export const formatProjectCode = (input: string): string => {
  // Remove all non-alphanumeric characters
  const cleaned = input.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();

  // Apply formatting based on length
  if (cleaned.length <= 4) {
    return cleaned;
  } else if (cleaned.length <= 6) {
    return `${cleaned.slice(0, 4)}-${cleaned.slice(4)}`;
  } else {
    return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 6)}-${cleaned.slice(6, 9)}`;
  }
};

// Validate numbers only
export const validateNumber = (value: string): ValidationResult => {
  if (!value || value.trim().length === 0) {
    return { isValid: false, message: 'Number is required' };
  }

  const numberRegex = /^\d+$/;
  if (!numberRegex.test(value.trim())) {
    return { isValid: false, message: 'Only numbers are allowed' };
  }

  return { isValid: true };
};

// Validate text fields (general purpose)
export const validateText = (text: string, minLength: number = 1, maxLength: number = 500): ValidationResult => {
  if (!text || text.trim().length === 0) {
    return { isValid: false, message: 'This field is required' };
  }

  if (text.trim().length < minLength) {
    return { isValid: false, message: `Must be at least ${minLength} characters long` };
  }

  if (text.trim().length > maxLength) {
    return { isValid: false, message: `Must be less than ${maxLength} characters` };
  }

  return { isValid: true };
};

// Validate required fields
export const validateRequired = (value: any): ValidationResult => {
  if (value === null || value === undefined || (typeof value === 'string' && value.trim().length === 0)) {
    return { isValid: false, message: 'This field is required' };
  }

  return { isValid: true };
};
