import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { AlertService } from '../services/alertService';
import { validateDates, validateTaskName, validateNumber } from '../utils/auth.utils';
import { toCamelCase } from '../utils/textUtils';

const prisma = new PrismaClient();

// Helper function to calculate task status based on subtasks
const calculateTaskStatusFromSubtasks = async (taskId: string): Promise<string> => {
  const subtasks = await prisma.subtask.findMany({
    where: { taskId },
    select: { status: true }
  });

  if (subtasks.length === 0) {
    // If no subtasks, return the current task status (don't change it)
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      select: { status: true }
    });
    return task?.status || 'NOT_STARTED';
  }

  const statusCounts = {
    COMPLETED: 0,
    IN_PROGRESS: 0,
    NOT_STARTED: 0,
    DELAYED: 0,
    ON_HOLD: 0
  };

  subtasks.forEach(subtask => {
    statusCounts[subtask.status as keyof typeof statusCounts]++;
  });

  const total = subtasks.length;

  // Business logic for task status based on subtasks:
  // 1. If ALL subtasks are COMPLETED -> Task is COMPLETED
  // 2. If ANY subtask is DELAYED -> Task is DELAYED
  // 3. If ANY subtask is IN_PROGRESS -> Task is IN_PROGRESS
  // 4. If ANY subtask is ON_HOLD and none are IN_PROGRESS/DELAYED -> Task is ON_HOLD
  // 5. If ALL subtasks are NOT_STARTED -> Task is NOT_STARTED

  if (statusCounts.COMPLETED === total) {
    return 'COMPLETED';
  } else if (statusCounts.DELAYED > 0) {
    return 'DELAYED';
  } else if (statusCounts.IN_PROGRESS > 0) {
    return 'IN_PROGRESS';
  } else if (statusCounts.ON_HOLD > 0) {
    return 'ON_HOLD';
  } else {
    return 'NOT_STARTED';
  }
};

// Helper function to update parent task status based on subtasks
const updateParentTaskStatus = async (taskId: string, isManualSubtaskUpdate: boolean = false): Promise<void> => {
  try {
    console.log(`Starting updateParentTaskStatus for task ${taskId}, manual subtask update: ${isManualSubtaskUpdate}`);

    // Get current task status
    const currentTask = await prisma.task.findUnique({
      where: { id: taskId },
      select: { status: true }
    });
    console.log(`Current task status: ${currentTask?.status}`);

    // IMPORTANT: Only preserve manually set COMPLETED/ON_HOLD from AUTOMATIC updates
    // Manual subtask updates should ALWAYS update the parent task status
    if (!isManualSubtaskUpdate) {
      // For automatic updates (time-based), preserve manually set COMPLETED/ON_HOLD
      if (currentTask && (currentTask.status === 'COMPLETED' || currentTask.status === 'ON_HOLD')) {
        console.log(`Task ${taskId} status preserved (${currentTask.status}) - manually set status not changed by automatic update`);
        return;
      }
    } else {
      // For manual subtask updates, always recalculate parent task status
      console.log(`Task ${taskId} will be updated based on subtasks (manual subtask update - overriding any manual task status)`);
    }

    const newTaskStatus = await calculateTaskStatusFromSubtasks(taskId);
    console.log(`Calculated new status: ${newTaskStatus}`);

    // Only update if status has changed
    if (currentTask && currentTask.status !== newTaskStatus) {
      await prisma.task.update({
        where: { id: taskId },
        data: {
          status: newTaskStatus as any,
          updatedAt: new Date().toISOString()
        }
      });

      console.log(`Task ${taskId} status updated from ${currentTask.status} to ${newTaskStatus} based on subtasks`);
    } else {
      console.log(`Task ${taskId} status unchanged (${currentTask?.status})`);
    }
  } catch (error) {
    console.error('Error updating parent task status:', error);
  }
};

// @desc    Get all subtasks
// @route   GET /api/subtasks
// @access  Private
export const getSubtasks = async (req: Request, res: Response): Promise<void> => {
  try {
    console.info('API', `Fetching subtasks for user ${req.user.role} (${req.user.email})`);

    // Build filter based on user role
    let whereClause: any = {};

    if (req.user.role === 'DIRECTOR') {
      // Directors can see all subtasks
      whereClause = {};
    } else if (req.user.role === 'PROJECT_MANAGER') {
      // Project managers can see subtasks in projects they manage
      const managedProjects = await prisma.project.findMany({
        where: { projectManagerId: req.user.id },
        select: { id: true }
      });
      const projectIds = managedProjects.map((p: any) => p.id);
      if (projectIds.length > 0) {
        whereClause = {
          task: {
            projectId: { in: projectIds }
          }
        };
      } else {
        // If no projects managed, return empty result
        whereClause = { id: 'non-existent-id' };
      }
    } else if (req.user.role === 'TEAM_LEAD') {
      // Team leads can see subtasks in their department or tasks assigned to them
      whereClause = {
        OR: [
          {
            task: {
              department: req.user.department
            }
          },
          {
            task: {
              assigneeId: req.user.id
            }
          }
        ]
      };
    } else if (req.user.role === 'ENGINEER') {
      // Engineers can only see subtasks assigned to them
      whereClause = {
        assigneeId: req.user.id
      };
    } else {
      // Default: no access
      whereClause = { id: 'non-existent-id' };
    }

    const subtasks = await prisma.subtask.findMany({
      where: whereClause,
      include: {
        task: {
          select: {
            id: true,
            name: true,
            projectId: true,
            assigneeId: true,
            department: true,
            project: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: {
        sequence: 'asc'
      }
    });

    // Add displayId to each subtask
    const subtasksWithDisplayId = subtasks.map((subtask: any) => ({
      ...subtask,
      displayId: `S${subtask.sequence}`
    }));

    console.log(`User ${req.user.role} (${req.user.email}) can see ${subtasksWithDisplayId.length} subtasks`);

    res.status(200).json({
      success: true,
      count: subtasksWithDisplayId.length,
      data: subtasksWithDisplayId,
    });
  } catch (error) {
    console.error('Get subtasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// @desc    Get single subtask
// @route   GET /api/subtasks/:id
// @access  Private
export const getSubtask = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const subtask = await prisma.subtask.findUnique({
      where: { id },
      include: {
        task: {
          select: {
            id: true,
            name: true,
            sequence: true,
            projectId: true,
            project: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    if (!subtask) {
      res.status(404).json({
        success: false,
        message: 'Subtask not found',
      });
      return;
    }

    // Add displayId with task sequence
    const subtaskWithDisplayId = {
      ...subtask,
      displayId: `S${subtask.task.sequence}${subtask.sequence}`
    };

    res.status(200).json({
      success: true,
      data: subtaskWithDisplayId,
    });
  } catch (error) {
    console.error('Get subtask error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// @desc    Create subtask
// @route   POST /api/subtasks
// @access  Private
export const createSubtask = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      taskId,
      name,
      description,
      assigneeId,
      assigneeType,
      startDate,
      endDate,
      status,
      priority,
      createdBy,
      totalTime
    } = req.body;

    console.log('Creating new subtask:', {
      name,
      taskId,
      assigneeId
    });

    // Validate required fields
    if (!taskId || !name || !assigneeId || !status || !createdBy) {
      res.status(400).json({
        success: false,
        message: 'Missing required fields: taskId, name, assigneeId, status, createdBy'
      });
      return;
    }

    // Validate subtask name
    const nameValidation = validateTaskName(name);
    if (!nameValidation.isValid) {
      res.status(400).json({
        success: false,
        message: nameValidation.message
      });
      return;
    }

    // Validate total time if provided
    if (totalTime !== undefined) {
      const timeValidation = validateNumber(totalTime, 'Total time');
      if (!timeValidation.isValid) {
        res.status(400).json({
          success: false,
          message: timeValidation.message
        });
        return;
      }
    }

    // Validate dates
    const dateValidation = validateDates(startDate, endDate);
    if (!dateValidation.isValid) {
      res.status(400).json({
        success: false,
        message: dateValidation.message
      });
      return;
    }

    // Check if parent task exists
    const parentTask = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        project: {
          select: {
            id: true,
            code: true
          }
        }
      }
    });

    if (!parentTask) {
      res.status(404).json({
        success: false,
        message: 'Parent task not found'
      });
      return;
    }

    // Check if assignee exists and is an engineer
    const assignee = await prisma.user.findUnique({
      where: { id: assigneeId }
    });

    if (!assignee) {
      res.status(404).json({
        success: false,
        message: 'Assignee not found'
      });
      return;
    }

    if (assignee.role !== 'ENGINEER') {
      res.status(400).json({
        success: false,
        message: 'Subtask assignees must be engineers'
      });
      return;
    }

    // Get the next sequence number for this task's subtasks
    const lastSubtask = await prisma.subtask.findFirst({
      where: { taskId },
      orderBy: { sequence: 'desc' }
    });

    const nextSequence = lastSubtask ? lastSubtask.sequence + 1 : 1;

    // Get global subtask count for unique numbering across the project
    const globalSubtaskCount = await prisma.subtask.count({
      where: {
        task: {
          projectId: parentTask.projectId
        }
      }
    });

    // Generate subtask ID
    const subtaskId = `${parentTask.project.code}T${parentTask.sequence}S${nextSequence}`;

    // Get current timestamp
    const now = new Date().toISOString();

    // Create the subtask
    const subtask = await prisma.subtask.create({
      data: {
        id: subtaskId,
        taskId,
        sequence: nextSequence,
        displayId: `T${globalSubtaskCount + 1}`,
        name: toCamelCase(name),
        description: description || '',
        assigneeId,
        assigneeType: assigneeType || 'ENGINEER',
        startDate: startDate ? new Date(startDate).toISOString() : now,
        ...(endDate && { endDate: new Date(endDate).toISOString() }), // Only set endDate if provided
        status: status || 'NOT_STARTED',
        priority: priority || 'Medium',
        totalTime: totalTime || 0,
        createdBy,
        createdAt: now,
        updatedAt: now
      },
      include: {
        task: {
          select: {
            id: true,
            name: true,
            projectId: true,
            project: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    console.log('Subtask created successfully:', subtask.id);

    // Update parent task status based on subtasks (manual subtask creation)
    await updateParentTaskStatus(taskId, true);

    // Create alerts for subtask creation and assignment - DISABLED
    // Note: Alerts for subtask creation have been disabled as requested
    // try {
    //   await AlertService.createSubtaskAlert(subtask.id, 'CREATED', createdBy);
    //   await AlertService.createSubtaskAlert(subtask.id, 'ASSIGNED', createdBy);
    // } catch (alertError) {
    //   console.error('Error creating subtask alerts:', alertError);
    //   // Don't fail the subtask creation if alert creation fails
    // }

    // Return the created subtask with displayId
    res.status(201).json({
      success: true,
      data: {
        ...subtask,
        displayId: `S${nextSequence}`
      }
    });
  } catch (error) {
    console.error('Error creating subtask:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating subtask',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// @desc    Update subtask
// @route   PUT /api/subtasks/:id
// @access  Private
export const updateSubtask = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      status,
      assigneeId,
      assigneeType,
      startDate,
      endDate,
      totalTime,
      priority
    } = req.body;

    // Validate subtask name if provided
    if (name) {
      const nameValidation = validateTaskName(name);
      if (!nameValidation.isValid) {
        res.status(400).json({
          success: false,
          message: nameValidation.message
        });
        return;
      }
    }

    // Validate total time if provided
    if (totalTime !== undefined) {
      const timeValidation = validateNumber(totalTime, 'Total time');
      if (!timeValidation.isValid) {
        res.status(400).json({
          success: false,
          message: timeValidation.message
        });
        return;
      }
    }

    // Validate dates if provided
    if (startDate || endDate) {
      const dateValidation = validateDates(startDate, endDate);
      if (!dateValidation.isValid) {
        res.status(400).json({
          success: false,
          message: dateValidation.message
        });
        return;
      }
    }

    // Check if subtask exists
    const subtaskExists = await prisma.subtask.findUnique({ where: { id } });
    if (!subtaskExists) {
      res.status(404).json({ success: false, message: 'Subtask not found' });
      return;
    }

    // Team leads can only update status and endDate fields
    if (req.user.role === 'TEAM_LEAD') {
      const allowedFields = ['status', 'endDate'];
      const requestedFields = Object.keys(req.body);
      const unauthorizedFields = requestedFields.filter(field => !allowedFields.includes(field));

      if (unauthorizedFields.length > 0) {
        res.status(403).json({
          success: false,
          message: `Team leads can only update status and end date. Unauthorized fields: ${unauthorizedFields.join(', ')}`,
        });
        return;
      }
    }

    // Validate subtask assignee if being updated - must be an engineer
    if (assigneeId) {
      const assignee = await prisma.user.findUnique({
        where: { id: assigneeId }
      });

      if (!assignee) {
        res.status(404).json({ success: false, message: 'Assignee not found' });
        return;
      }

      if (assignee.role !== 'ENGINEER') {
        res.status(400).json({ success: false, message: 'Subtask assignees must be engineers' });
        return;
      }
    }

    // Update subtask
    const subtask = await prisma.subtask.update({
      where: { id },
      data: {
        ...(name && { name: toCamelCase(name) }),
        ...(description !== undefined && { description }),
        ...(status && { status }),
        ...(assigneeId && { assigneeId }),
        ...(assigneeType && { assigneeType }),
        ...(startDate && { startDate: new Date(startDate).toISOString() }),
        ...(endDate && { endDate: new Date(endDate).toISOString() }),
        ...(totalTime !== undefined && { totalTime }),
        ...(priority && { priority }),
        updatedAt: new Date().toISOString()
      },
      include: {
        task: {
          select: {
            id: true,
            name: true,
            sequence: true,
            projectId: true
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    console.log(`Subtask ${id} updated successfully`);
    console.log(`Status field in request: ${status}`);
    console.log(`Subtask task ID: ${subtask.task.id}`);

    // ALWAYS update parent task status when subtask is updated (manual subtask update)
    console.log(`🔄 CALLING updateParentTaskStatus for task ${subtask.task.id} after subtask update`);
    await updateParentTaskStatus(subtask.task.id, true);
    console.log(`✅ FINISHED updateParentTaskStatus for task ${subtask.task.id}`);

    // Legacy condition check for debugging
    if (status) {
      console.log(`✅ Status was provided: ${status}`);
    } else {
      console.log(`⚠️ Status was not provided in request, but we updated parent task anyway`);
    }

    // Create alerts for subtask updates (consolidated to prevent duplicates)
    try {
      // Determine the most important alert type to send
      if (status === 'COMPLETED' && subtaskExists.status !== 'COMPLETED') {
        // Subtask completion is most important
        await AlertService.createSubtaskAlert(subtask.id, 'COMPLETED', req.user.id);
      } else if (assigneeId && assigneeId !== subtaskExists.assigneeId) {
        // Assignment change is next most important
        await AlertService.createSubtaskAlert(subtask.id, 'ASSIGNED', req.user.id);
      } else {
        // General update alert only if no specific action occurred
        await AlertService.createSubtaskAlert(subtask.id, 'UPDATED', req.user.id);
      }
    } catch (alertError) {
      console.error('Error creating subtask update alerts:', alertError);
      // Don't fail the subtask update if alert creation fails
    }

    // Add displayId to the response
    const subtaskWithDisplayId = {
      ...subtask,
      displayId: `S${subtask.task.sequence}${subtask.sequence}`
    };

    res.status(200).json({ success: true, data: subtaskWithDisplayId });
  } catch (error) {
    console.error('Update subtask error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// @desc    Delete subtask
// @route   DELETE /api/subtasks/:id
// @access  Private
export const deleteSubtask = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // Check if subtask exists
    const subtaskExists = await prisma.subtask.findUnique({
      where: { id },
      include: {
        task: {
          select: {
            id: true,
            projectId: true
          }
        }
      }
    });

    if (!subtaskExists) {
      res.status(404).json({
        success: false,
        message: 'Subtask not found',
      });
      return;
    }

    // Delete subtask
    await prisma.subtask.delete({
      where: { id },
    });

    console.log(`Subtask ${id} deleted successfully`);

    // Update parent task status based on remaining subtasks (manual subtask deletion)
    await updateParentTaskStatus(subtaskExists.task.id, true);

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete subtask error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
