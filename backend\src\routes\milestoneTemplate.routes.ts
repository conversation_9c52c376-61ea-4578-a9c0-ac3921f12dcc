import express from 'express';
import {
  getMilestoneTemplates,
  getMilestoneTemplate,
  createMilestoneTemplate,
  updateMilestoneTemplate,
  deleteMilestoneTemplate,
} from '../controllers/milestoneTemplate.controller';
import { protect, authorize } from '../middleware/auth.middleware';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(
    authorize('DIRECTOR'), // Only DIRECTOR can view milestone templates
    getMilestoneTemplates
  )
  .post(
    authorize('DIRECTOR'), // Only DIRECTOR can create milestone templates
    createMilestoneTemplate
  );

router.route('/:id')
  .get(
    authorize('DIRECTOR'), // Only DIRECTOR can view milestone templates
    getMilestoneTemplate
  )
  .put(
    authorize('DIRECTOR'), // Only DIRECTOR can update milestone templates
    updateMilestoneTemplate
  )
  .delete(
    authorize('DIRECTOR'), // Only DIRECTOR can delete milestone templates
    deleteMilestoneTemplate
  );

export default router;
