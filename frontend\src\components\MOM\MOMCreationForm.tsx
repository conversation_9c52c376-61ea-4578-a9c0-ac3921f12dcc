import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { projectsAtom, customersAtom, currentUserAtom } from '../../store';
import { formatDateForInput } from '../../utils/dateFormatter';
import { dataService } from '../../services/dataServiceSingleton';
import { ChevronDown, Plus, X, Save, Calendar } from 'lucide-react';

interface MOMCreationFormProps {
  onMOMCreated?: () => void;
  onCancel?: () => void;
}

interface MOMPointData {
  id: string;
  date?: string;
  discussionType?: string;
  station?: string;
  discussions: string[];  // Changed to array for multiple descriptions
  actionPlan?: string;
  responsibility?: string;
  plannedDate?: string;
  completionDate?: string;
  status: string;
  imageAttachment?: string;
  remarks?: string;
}

const MOMCreationForm: React.FC<MOMCreationFormProps> = ({ onMOMCreated, onCancel }) => {
  const [projects] = useAtom(projectsAtom);
  const [customers] = useAtom(customersAtom);
  const [currentUser] = useAtom(currentUserAtom);

  // Form state
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [meetingDate, setMeetingDate] = useState<string>(formatDateForInput(new Date()));
  const [agenda, setAgenda] = useState<string>('');

  // Attendees
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [selectedMekhosAttendees, setSelectedMekhosAttendees] = useState<string[]>([]);
  const [selectedCustomerAttendees, setSelectedCustomerAttendees] = useState<string[]>([]);

  // MOM Points
  const [momPoints, setMomPoints] = useState<MOMPointData[]>([
    {
      id: '1',
      discussions: [''],  // Initialize with one empty discussion
      status: 'PENDING'
    }
  ]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [existingMOM, setExistingMOM] = useState<any>(null);

  // Load users for attendees
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const usersResponse = await fetch('/api/users', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });
        if (usersResponse.ok) {
          const usersData = await usersResponse.json();
          setAllUsers(usersData.data || []);
        }
      } catch (error) {
        console.error('Error loading users:', error);
      }
    };

    loadUsers();
  }, []);

  // Auto-resize all textareas when momPoints change
  useEffect(() => {
    const resizeAllTextareas = () => {
      const textareas = document.querySelectorAll('textarea[data-point-id]');
      textareas.forEach((textarea) => {
        const element = textarea as HTMLTextAreaElement;
        element.style.height = 'auto';
        element.style.height = element.scrollHeight + 'px';
      });
    };

    // Delay to ensure DOM is updated
    setTimeout(resizeAllTextareas, 100);
  }, [momPoints]);

  // Get current project and customer
  const currentProject = selectedProjectId ? projects?.find(p => p.id === selectedProjectId) : null;
  const currentCustomer = currentProject ? customers?.find(c => c.id === currentProject.customerId) : null;

  // Check for existing MOM when project is selected
  useEffect(() => {
    const checkExistingMOM = async () => {
      if (selectedProjectId) {
        try {
          const response = await fetch(`/api/moms/project/${selectedProjectId}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
          });

          if (response.ok) {
            const data = await response.json();
            if (data.data) {
              setExistingMOM(data.data);
              setErrors({
                project: `A MOM already exists for this project. You can edit the existing MOM instead.`
              });
            } else {
              setExistingMOM(null);
              setErrors(prev => ({ ...prev, project: '' }));
            }
          } else {
            // No existing MOM found
            setExistingMOM(null);
            setErrors(prev => ({ ...prev, project: '' }));
          }
        } catch (error) {
          console.error('Error checking existing MOM:', error);
          setExistingMOM(null);
        }
      } else {
        setExistingMOM(null);
        setErrors(prev => ({ ...prev, project: '' }));
      }
    };

    checkExistingMOM();
  }, [selectedProjectId]);

  // Add new MOM point
  const addMOMPoint = () => {
    setMomPoints([...momPoints, {
      id: Date.now().toString(),
      discussions: [''],  // Initialize with one empty discussion
      status: 'PENDING'
    }]);
  };

  // Remove MOM point
  const removeMOMPoint = (id: string) => {
    if (momPoints.length > 1) {
      setMomPoints(momPoints.filter(point => point.id !== id));
    }
  };

  // Update MOM point
  const updateMOMPoint = (id: string, field: string, value: string) => {
    setMomPoints(momPoints.map(point =>
      point.id === id ? { ...point, [field]: value } : point
    ));
  };

  // Update specific discussion in a MOM point
  const updateDiscussion = (pointId: string, discussionIndex: number, value: string) => {
    setMomPoints(momPoints.map(point => {
      if (point.id === pointId) {
        const newDiscussions = [...point.discussions];
        newDiscussions[discussionIndex] = value;
        return { ...point, discussions: newDiscussions };
      }
      return point;
    }));
  };

  // Add new discussion to a MOM point
  const addDiscussion = (pointId: string) => {
    setMomPoints(momPoints.map(point => {
      if (point.id === pointId) {
        return { ...point, discussions: [...point.discussions, ''] };
      }
      return point;
    }));

    // Focus on the new textarea after state update
    setTimeout(() => {
      const point = momPoints.find(p => p.id === pointId);
      if (point) {
        const newIndex = point.discussions.length; // This will be the index of the new discussion
        const newTextarea = document.querySelector(
          `textarea[data-point-id="${pointId}"][data-discussion-index="${newIndex}"]`
        ) as HTMLTextAreaElement;
        if (newTextarea) {
          newTextarea.focus();
        }
      }
    }, 100);
  };

  // Remove discussion from a MOM point
  const removeDiscussion = (pointId: string, discussionIndex: number) => {
    setMomPoints(momPoints.map(point => {
      if (point.id === pointId && point.discussions.length > 1) {
        const newDiscussions = [...point.discussions];
        newDiscussions.splice(discussionIndex, 1);
        return { ...point, discussions: newDiscussions };
      }
      return point;
    }));
  };

  // Validation
  const validate = (): boolean => {
    const newErrors: {[key: string]: string} = {};

    if (!selectedProjectId) newErrors.project = 'Project is required';
    if (existingMOM) newErrors.project = 'A MOM already exists for this project. You can edit the existing MOM instead.';
    if (!meetingDate) newErrors.date = 'Meeting date is required';
    if (!agenda.trim()) newErrors.agenda = 'Meeting agenda is required';

    // Check if at least one discussion point exists
    if (!momPoints.some(point => point.discussions.some(disc => disc.trim()))) {
      newErrors.points = 'At least one discussion point is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validate()) return;

    setIsSubmitting(true);
    try {
      // Create MOM
      const response = await fetch('/api/moms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          projectId: selectedProjectId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('MOM creation failed:', errorData);
        throw new Error(errorData.message || 'Failed to create MOM');
      }

      const momData = await response.json();
      const momId = momData.data.id;

      // Update MOM with date and agenda
      await fetch(`/api/moms/${momId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          date: new Date(meetingDate).toISOString(),
        }),
      });

      // Add MOM points - create separate points for each discussion
      for (const point of momPoints) {
        for (const discussion of point.discussions) {
          if (discussion.trim()) {
            await fetch(`/api/moms/${momId}/points`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
              },
              body: JSON.stringify({
                date: point.date ? new Date(point.date).toISOString() : null,
                discussionType: point.discussionType || null,
                station: point.station || null,
                discussion: discussion,
                actionPlan: point.actionPlan || null,
                responsibility: point.responsibility || null,
                plannedDate: point.plannedDate ? new Date(point.plannedDate).toISOString() : null,
                completionDate: point.completionDate ? new Date(point.completionDate).toISOString() : null,
                status: point.status,
                imageAttachment: point.imageAttachment || null,
                remarks: point.remarks || null,
              }),
            });
          }
        }
      }

      // Refresh data
      await dataService.loadMOMs();

      if (onMOMCreated) {
        onMOMCreated();
      }
    } catch (error) {
      console.error('Error creating MOM:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create MOM. Please try again.';
      setErrors({ submit: errorMessage });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg max-w-6xl mx-auto">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">Create Minutes of Meeting</h2>
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              <X className="h-4 w-4 inline mr-2" />
              Cancel
            </button>
            <button
              type="submit"
              form="mom-form"
              disabled={isSubmitting}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              <Save className="h-4 w-4 inline mr-2" />
              {isSubmitting ? 'Saving...' : 'Save'}
            </button>
          </div>
        </div>
      </div>

      {/* Form */}
      <form id="mom-form" onSubmit={handleSubmit} className="p-6">
        {/* Basic Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Meeting Date
            </label>
            <div className="relative">
              <input
                type="date"
                value={meetingDate}
                onChange={(e) => setMeetingDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
            {errors.date && <p className="mt-1 text-sm text-red-600">{errors.date}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Project
            </label>
            <div className="relative">
              <select
                value={selectedProjectId}
                onChange={(e) => setSelectedProjectId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
              >
                <option value="">Select Project...</option>
                {projects?.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.code} - {project.name}
                  </option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
            {errors.project && (
              <div className="mt-1">
                <p className="text-sm text-red-600">{errors.project}</p>
                {existingMOM && (
                  <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-yellow-800">
                          <strong>Existing MOM found:</strong> Created on {new Date(existingMOM.createdAt).toLocaleDateString()}
                        </p>
                        <p className="text-xs text-yellow-700 mt-1">
                          {existingMOM.mompoint?.length || 0} discussion points recorded
                        </p>
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          // Navigate back to view mode to see existing MOMs
                          if (onCancel) onCancel();
                        }}
                        className="px-3 py-1 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700 transition-colors"
                      >
                        View Existing MOM
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Customer Info */}
        {currentProject && (
          <div className="mb-6 p-4 bg-gray-50 rounded-md">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Customer:</span>
                <span className="ml-2">{currentCustomer?.name || 'N/A'}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">PO Number:</span>
                <span className="ml-2">{currentProject.poNumber || 'N/A'}</span>
              </div>
            </div>
          </div>
        )}

        {/* Meeting Agenda */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Meeting Agenda
          </label>
          <textarea
            value={agenda}
            onChange={(e) => setAgenda(e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter meeting agenda..."
          />
          {errors.agenda && <p className="mt-1 text-sm text-red-600">{errors.agenda}</p>}
        </div>

        {/* Attendees Section */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Attendees</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Mekhos Attendees */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mekhos Attendees
              </label>
              <div className="space-y-2">
                {allUsers.filter(user => user.role !== 'CUSTOMER').map((user) => (
                  <label key={user.id} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedMekhosAttendees.includes(user.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedMekhosAttendees([...selectedMekhosAttendees, user.id]);
                        } else {
                          setSelectedMekhosAttendees(selectedMekhosAttendees.filter(id => id !== user.id));
                        }
                      }}
                      className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-700">{user.name} ({user.role})</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Customer Attendees */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Customer Attendees
              </label>
              <div className="space-y-2">
                <input
                  type="text"
                  placeholder="Enter customer attendee name and press Enter"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      const value = (e.target as HTMLInputElement).value.trim();
                      if (value && !selectedCustomerAttendees.includes(value)) {
                        setSelectedCustomerAttendees([...selectedCustomerAttendees, value]);
                        (e.target as HTMLInputElement).value = '';
                      }
                    }
                  }}
                />
                <div className="space-y-1">
                  {selectedCustomerAttendees.map((attendee, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-100 px-3 py-2 rounded-md">
                      <span className="text-sm text-gray-700">{attendee}</span>
                      <button
                        type="button"
                        onClick={() => setSelectedCustomerAttendees(selectedCustomerAttendees.filter((_, i) => i !== index))}
                        className="text-red-500 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Meeting Details */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Meeting Details</h3>
            <button
              type="button"
              onClick={addMOMPoint}
              className="px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-4 w-4 inline mr-1" />
              Add Row
            </button>
          </div>

          {/* Clean table without borders and colors */}
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="text-left">
                  <th className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider w-8">#</th>
                  <th className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider">Mekhos Attendees</th>
                  <th className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider">Customer Attendees</th>
                  <th className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider">Description</th>
                  <th className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider">Assignee</th>
                  <th className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider">Due Date</th>
                  <th className="px-3 py-2 text-xs font-medium text-gray-700 uppercase tracking-wider w-16">Actions</th>
                </tr>
              </thead>
              <tbody>
                {momPoints.map((point, index) => (
                  <tr key={point.id} className="hover:bg-gray-50">
                    <td className="px-3 py-2 text-sm text-gray-900">{index + 1}</td>
                    <td className="px-3 py-2">
                      <div className="flex flex-wrap gap-1">
                        {selectedMekhosAttendees.map(userId => {
                          const user = allUsers.find(u => u.id === userId);
                          return user ? (
                            <span key={userId} className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                              {user.name}
                            </span>
                          ) : null;
                        })}
                      </div>
                    </td>
                    <td className="px-3 py-2">
                      <div className="flex flex-wrap gap-1">
                        {selectedCustomerAttendees.map((attendee, idx) => (
                          <span key={idx} className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                            {attendee}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="px-3 py-2">
                      <div className="space-y-2">
                        {point.discussions.map((discussion, discIndex) => (
                          <div key={discIndex} className="flex items-start space-x-2">
                            <textarea
                              value={discussion}
                              onChange={(e) => {
                                updateDiscussion(point.id, discIndex, e.target.value);
                                // Auto-resize textarea
                                e.target.style.height = 'auto';
                                e.target.style.height = e.target.scrollHeight + 'px';
                              }}
                              placeholder="Enter discussion point"
                              className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none"
                              rows={2}
                              data-point-id={point.id}
                              data-discussion-index={discIndex}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && !e.shiftKey) {
                                  e.preventDefault();
                                  addDiscussion(point.id);
                                  // Focus on the new textarea after a short delay
                                  setTimeout(() => {
                                    const newIndex = point.discussions.length;
                                    const newTextarea = document.querySelector(
                                      `textarea[data-point-id="${point.id}"][data-discussion-index="${newIndex}"]`
                                    ) as HTMLTextAreaElement;
                                    if (newTextarea) {
                                      newTextarea.focus();
                                    }
                                  }, 50);
                                }
                              }}
                            />
                            {point.discussions.length > 1 && (
                              <button
                                type="button"
                                onClick={() => removeDiscussion(point.id, discIndex)}
                                className="mt-1 text-red-500 hover:text-red-700"
                                title="Remove discussion"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            )}
                          </div>
                        ))}
                        <button
                          type="button"
                          onClick={() => addDiscussion(point.id)}
                          className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Add Description
                        </button>
                      </div>
                    </td>
                    <td className="px-3 py-2">
                      <select
                        value={point.responsibility || ''}
                        onChange={(e) => updateMOMPoint(point.id, 'responsibility', e.target.value)}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Select Team Lead</option>
                        {allUsers.filter(user => user.role === 'TEAM_LEAD').map((user) => (
                          <option key={user.id} value={user.id}>
                            {user.name}
                          </option>
                        ))}
                      </select>
                    </td>
                    <td className="px-3 py-2">
                      <input
                        type="date"
                        value={point.plannedDate || ''}
                        onChange={(e) => updateMOMPoint(point.id, 'plannedDate', e.target.value)}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </td>
                    <td className="px-3 py-2">
                      {momPoints.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeMOMPoint(point.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {errors.points && <p className="mt-2 text-sm text-red-600">{errors.points}</p>}
        </div>

        {/* Submit Error */}
        {errors.submit && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{errors.submit}</p>
          </div>
        )}
      </form>
    </div>
  );
};

export default MOMCreationForm;
