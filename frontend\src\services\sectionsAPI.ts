import { apiClient } from './api';

export interface Section {
  id: string;
  projectId: string;
  name: string;
  sequence: number;
  description: string;
  createdAt: string;
  updatedAt: string;
  tasks?: any[];
}

export interface CreateSectionData {
  name: string;
  description?: string;
}

export interface UpdateSectionData {
  name?: string;
  description?: string;
}

export const sectionsAPI = {
  // Get all sections for a project
  getSectionsByProject: async (projectId: string) => {
    const response = await apiClient.get(`/projects/${projectId}/sections`);
    return response.data;
  },

  // Create a new section
  createSection: async (projectId: string, data: CreateSectionData) => {
    const response = await apiClient.post(`/projects/${projectId}/sections`, data);
    return response.data;
  },

  // Update a section
  updateSection: async (sectionId: string, data: UpdateSectionData) => {
    const response = await apiClient.put(`/sections/${sectionId}`, data);
    return response.data;
  },

  // Delete a section
  deleteSection: async (sectionId: string) => {
    const response = await apiClient.delete(`/sections/${sectionId}`);
    return response.data;
  },
};
