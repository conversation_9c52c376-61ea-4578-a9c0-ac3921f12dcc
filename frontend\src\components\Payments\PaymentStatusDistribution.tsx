import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { currentUserAtom } from '../../store';
import { paymentsAPI } from '../../services/api';
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip
} from 'recharts';
import {
  DollarSign,
  CheckCircle,
  Clock,
  TrendingUp,
  RefreshCw,
  Eye
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface PaymentData {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

interface PaymentSummary {
  totalProjects: number;
  totalPoValue: number;
  totalPaid: number;
  totalPending: number;
  paidPercentage: number;
  pendingPercentage: number;
}

const PaymentStatusDistribution: React.FC = () => {
  const navigate = useNavigate();
  const [currentUser] = useAtom(currentUserAtom);
  const [paymentSummary, setPaymentSummary] = useState<PaymentSummary>({
    totalProjects: 0,
    totalPoValue: 0,
    totalPaid: 0,
    totalPending: 0,
    paidPercentage: 0,
    pendingPercentage: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if user is director
  const isDirector = currentUser?.role === 'Director';

  const fetchPaymentData = async () => {
    if (!isDirector) return;
    
    try {
      setLoading(true);
      setError(null);
      const data = await paymentsAPI.getPaymentSummary();
      setPaymentSummary(data);
    } catch (err) {
      console.error('Error fetching payment data:', err);
      setError('Failed to load payment data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPaymentData();
  }, [isDirector]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Prepare chart data
  const chartData: PaymentData[] = [
    {
      name: 'Paid',
      value: paymentSummary.totalPaid,
      percentage: paymentSummary.paidPercentage,
      color: '#10B981' // Green
    },
    {
      name: 'Pending',
      value: paymentSummary.totalPending,
      percentage: paymentSummary.pendingPercentage,
      color: '#F59E0B' // Orange
    }
  ];

  // Custom tooltip for the pie chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2 mb-1">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: data.color }}
            ></div>
            <span className="font-medium text-gray-900">{data.name}</span>
          </div>
          <div className="text-sm text-gray-600">
            <div>Amount: {formatCurrency(data.value)}</div>
            <div>Percentage: {data.percentage.toFixed(1)}%</div>
          </div>
        </div>
      );
    }
    return null;
  };

  // Don't render for non-directors
  if (!isDirector) {
    return null;
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <DollarSign className="w-5 h-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Payment Status Distribution</h3>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={fetchPaymentData}
              disabled={loading}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
              title="Refresh"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={() => navigate('/payments')}
              className="flex items-center px-3 py-1.5 text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors"
            >
              <Eye className="w-4 h-4 mr-1" />
              View Details
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent mx-auto mb-3"></div>
              <p className="text-gray-600">Loading payment data...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500 mb-3">{error}</p>
              <button
                onClick={fetchPaymentData}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Try Again
              </button>
            </div>
          </div>
        ) : paymentSummary.totalPoValue === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500">No payment data available</p>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <TrendingUp className="w-5 h-5 text-blue-600" />
                </div>
                <div className="text-sm font-medium text-gray-600">Total PO Value</div>
                <div className="text-lg font-bold text-blue-600">
                  {formatCurrency(paymentSummary.totalPoValue)}
                </div>
                <div className="text-xs text-gray-500">
                  {paymentSummary.totalProjects} Projects
                </div>
              </div>

              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                </div>
                <div className="text-sm font-medium text-gray-600">Amount Received</div>
                <div className="text-lg font-bold text-green-600">
                  {formatCurrency(paymentSummary.totalPaid)}
                </div>
                <div className="text-xs text-gray-500">
                  {paymentSummary.paidPercentage.toFixed(1)}% of total
                </div>
              </div>

              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="flex items-center justify-center mb-2">
                  <Clock className="w-5 h-5 text-orange-600" />
                </div>
                <div className="text-sm font-medium text-gray-600">Outstanding</div>
                <div className="text-lg font-bold text-orange-600">
                  {formatCurrency(paymentSummary.totalPending)}
                </div>
                <div className="text-xs text-gray-500">
                  {paymentSummary.pendingPercentage.toFixed(1)}% remaining
                </div>
              </div>
            </div>

            {/* Pie Chart */}
            <div className="flex flex-col lg:flex-row items-center space-y-4 lg:space-y-0 lg:space-x-6">
              {/* Chart */}
              <div className="flex-1 h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={chartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {chartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              {/* Legend with values */}
              <div className="flex-1 lg:max-w-xs space-y-4">
                {chartData.map((entry, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div 
                        className="w-4 h-4 rounded-full" 
                        style={{ backgroundColor: entry.color }}
                      ></div>
                      <span className="text-sm font-medium text-gray-700">{entry.name}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-bold text-gray-900">
                        {formatCurrency(entry.value)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {entry.percentage.toFixed(1)}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
              <button
                onClick={() => navigate('/transaction-history')}
                className="flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                View Paid ({paymentSummary.paidPercentage.toFixed(1)}%)
              </button>
              <button
                onClick={() => navigate('/pending-payments')}
                className="flex items-center px-4 py-2 bg-orange-100 text-orange-700 rounded-lg hover:bg-orange-200 transition-colors"
              >
                <Clock className="w-4 h-4 mr-2" />
                View Pending ({paymentSummary.pendingPercentage.toFixed(1)}%)
              </button>
              <button
                onClick={() => navigate('/po-details')}
                className="flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                View All Projects
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentStatusDistribution;
