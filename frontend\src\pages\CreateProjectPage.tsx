import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { useNavigate } from 'react-router-dom';
import {
  customersAtom,
  usersAtom,
  projectsAtom,
  currentUserAtom,
  projectCategoriesAtom
} from '../store';
import { TaskStatus, UserRole, ProjectCategory } from '../types';
import { XCircle, PlusCircle, User, Calendar, Building2, Hash, FileText, Layers, DollarSign } from 'lucide-react';

import { projectsAPI, projectCategoriesAPI } from '../services/api';
import { useDataService } from '../services/dataService';
import { useConfirmationContext } from '../contexts/ConfirmationContext';
import { dataService } from '../services/dataServiceSingleton';
import { useRealTimeValidation } from '../hooks/useRealTimeValidation';
import { SuccessDialog } from '../components/common/SuccessDialog';
import { ErrorDialog } from '../components/common/ErrorDialog';
import { getMinDate } from '../utils/dateValidation';
import { formatProjectCode } from '../utils/validation';



const CreateProjectPage: React.FC = () => {
  console.log('🎯 CreateProjectPage component loaded');
  const navigate = useNavigate();

  const handleAPICall = async (data: any) => {
    console.log('Creating project with real API:', data);
    // Use the real projects API
    return await projectsAPI.createProject(data);
  };

  const [customers] = useAtom(customersAtom);
  const [users] = useAtom(usersAtom);
  const [, setProjects] = useAtom(projectsAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [projectCategories] = useAtom(projectCategoriesAtom);

  // Import data service for refreshing data
  const { refreshData } = useDataService();

  // Import confirmation context
  const { confirmAction } = useConfirmationContext();

  // Load project categories when component mounts
  useEffect(() => {
    const loadProjectCategories = async () => {
      if (projectCategories.length === 0) {
        try {
          // First try to load just project categories
          const result = await dataService.loadProjectCategories();
          if (!result) {
            // If that fails, try loading all data
            await refreshData();
          }
        } catch (error) {
          console.error('❌ Failed to load project categories:', error);
        }
      }
    };

    loadProjectCategories();
  }, [projectCategories.length, refreshData]);

  console.log('📊 Data loaded:', {
    customersCount: customers.length,
    usersCount: users.length,
    projectCategoriesCount: projectCategories.length,
    projectCategories: projectCategories,
    currentUser: currentUser?.name
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [successDetails, setSuccessDetails] = useState<string[]>([]);
  const [createdProjectId, setCreatedProjectId] = useState<string | null>(null);
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [errorDetails, setErrorDetails] = useState<string[]>([]);


  // Filter users who can be project managers - only PROJECT_MANAGER role
  const projectManagers = users.filter(user =>
    user.role === UserRole.PROJECT_MANAGER
  );

  // Real-time validation configuration
  const validationConfig = {
    projectName: [
      { type: 'required' as const, message: 'Project name is required' },
      { type: 'taskName' as const, message: 'Name contains invalid characters. Only letters, numbers, spaces, and common symbols are allowed' }
    ],
    projectCode: [
      { type: 'required' as const, message: 'Project code is required' },
      { type: 'projectCode' as const, message: 'Project code must follow format: ****-**-*** (4 characters, dash, 2 characters, dash, 3 characters)' }
    ],
    projectCategory: [
      { type: 'required' as const, message: 'Project category is required' }
    ],
    customer: [
      { type: 'required' as const, message: 'Customer is required' }
    ],
    poNumber: [
      { type: 'required' as const, message: 'PO number is required' }
    ],
    poDate: [
      { type: 'required' as const, message: 'PO date is required' }
    ],
    poValue: [
      { type: 'required' as const, message: 'PO value is required' }
    ],
    startDate: [
      { type: 'required' as const, message: 'Start date is required' },
      { type: 'date' as const, dateType: 'start' as const, message: 'Start date cannot be older than 30 days' }
    ],
    endDate: [
      { type: 'date' as const, dateType: 'end' as const, compareWith: 'startDate', message: 'End date cannot be before start date' }
    ],
    projectManager: [
      { type: 'required' as const, message: 'Project manager is required' }
    ]
  };

  // Use real-time validation
  const {
    fields,
    validateAllFields,
    resetAllFields,
    getFieldProps,
    updateField
  } = useRealTimeValidation(validationConfig);

  // Custom handler for project code formatting
  const handleProjectCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatProjectCode(e.target.value);
    updateField('projectCode', formatted);
  };



  console.log('🔍 Real-time validation fields:', fields);

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('🚀 FORM SUBMIT TRIGGERED!');

    // Validate all fields before submission
    if (!validateAllFields()) {
      console.log('Form validation failed');
      return;
    }

    console.log('🚀 Form data received:', fields);

    setIsSubmitting(true);
    console.log('Creating new project');

    try {
      const formatDate = (dateStr: string) => {
        if (!dateStr) return null;
        const date = new Date(dateStr);
        return date.toISOString();
      };

      const projectData = {
        name: fields.projectName.value,
        code: fields.projectCode.value,
        projectCategory: fields.projectCategory.value,
        customerId: fields.customer.value,
        poNumber: fields.poNumber.value,
        poDate: formatDate(fields.poDate.value) || '',
        poValue: parseFloat(fields.poValue.value) || 0,
        startDate: formatDate(fields.startDate.value) || '',
        endDate: formatDate(fields.endDate?.value || '') || '',
        projectManagerId: fields.projectManager.value,
        projectManagerIds: [fields.projectManager.value],
        status: TaskStatus.NOT_STARTED,
        tasks: []
      };

      console.log('Sending project data to API:', projectData);
      const response = await handleAPICall(projectData);
      console.log('API response received:', response);

      const savedProject = response.data;
      setProjects((prevProjects) => [...prevProjects, savedProject]);

      console.log('Project created successfully');

      // Refresh all data to ensure consistency
      await refreshData();

      // Get customer and project manager names for success dialog
      const selectedCustomer = customers.find(c => c.id === fields.customer.value);
      const selectedManager = projectManagers.find(pm => pm.id === fields.projectManager.value);

      // Set success details for the dialog
      const projectDetails = [
        `Project Name: ${savedProject.name}`,
        `Project Code: ${savedProject.code}`,
        `Customer: ${selectedCustomer?.name || 'Unknown'}`,
        `Project Manager: ${selectedManager?.name || 'Unknown'}`,
        `Start Date: ${fields.startDate.value}`,
        `End Date: ${fields.endDate?.value || 'Not specified'}`,
        `PO Number: ${savedProject.poNumber}`,
        `PO Value: ${savedProject.poValue ? `₹${Number(savedProject.poValue).toLocaleString('en-IN')}` : 'Not specified'}`
      ];

      setSuccessDetails(projectDetails);
      setCreatedProjectId(savedProject.id || null);
      setShowSuccessDialog(true);

      resetAllFields();
    } catch (error) {
      console.error('Error creating project:', error);

      let errorMessage = 'Failed to create project';
      let errorDetailsList: string[] = [];

      if (error instanceof Error) {
        errorMessage = error.message;

        // Parse backend validation errors
        if (errorMessage.includes('End date cannot be before')) {
          errorDetailsList = [
            'Date Validation Error:',
            '• End date must be after the start date',
            '• Please check your project dates and try again',
            `Time: ${new Date().toLocaleString()}`
          ];
        } else if (errorMessage.includes('Start date cannot be older than 30 days')) {
          errorDetailsList = [
            'Date Validation Error:',
            '• Start date cannot be older than 30 days',
            '• Please select a more recent start date',
            `Time: ${new Date().toLocaleString()}`
          ];
        } else if (errorMessage.includes('End date cannot be older than 30 days')) {
          errorDetailsList = [
            'Date Validation Error:',
            '• End date cannot be older than 30 days',
            '• Please select a more recent end date',
            `Time: ${new Date().toLocaleString()}`
          ];
        } else {
          // Generic error handling
          errorDetailsList = [
            'Project Creation Error:',
            `• ${errorMessage}`,
            '• Please check your input and try again',
            `Time: ${new Date().toLocaleString()}`
          ];
        }
      } else {
        errorDetailsList = [
          'Unknown Error:',
          '• An unexpected error occurred',
          '• Please try again or contact support',
          `Time: ${new Date().toLocaleString()}`
        ];
      }

      setErrorDetails(errorDetailsList);
      setShowErrorDialog(true);
    } finally {
      setIsSubmitting(false);
    }
  };



  const handleCancel = () => {
    console.log('Cancel button clicked');

    confirmAction({
      title: 'Cancel Project Creation',
      message: 'Are you sure you want to cancel? Any unsaved changes will be lost.',
      type: 'warning',
      confirmText: 'Yes, Cancel',
      onConfirm: () => {
        navigate('/projects');
      }
    });
  };

  const handleSuccessDialogAction = () => {
    setShowSuccessDialog(false);
    // Navigate to the project details page
    if (createdProjectId) {
      navigate(`/projects/${createdProjectId}`);
    } else {
      navigate('/projects');
    }
  };

  const handleErrorDialogAction = () => {
    setShowErrorDialog(false);
    // Stay on the current page to allow user to fix the error
  };

  return (
    <div className="bg-gradient-to-br from-slate-50 via-gray-50 to-blue-50">
      <div className="w-full space-y-4 p-4">
        {/* Header Section with 3D Effect */}
        <div
          className="relative overflow-hidden bg-white rounded-xl shadow-md border border-gray-200"
          style={{
            transform: 'perspective(1000px) rotateX(1deg)',
            transformStyle: 'preserve-3d'
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5" />
          <div className="relative p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-500 rounded-lg shadow-md transform hover:scale-105 transition-transform duration-200">
                <FileText className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900 mb-1">Create New Project</h1>
                <p className="text-gray-600 text-sm">
                  Set up a new project and add tasks later
                </p>
              </div>
            </div>
          </div>
        </div>





        {/* Main Form Container with 3D Effect */}
        <div
          className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden"
          style={{
            transform: 'perspective(1200px) rotateX(0.5deg)',
            transformStyle: 'preserve-3d',
            boxShadow: '0 20px 40px -12px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05)'
          }}
        >
          <form onSubmit={onSubmit} className="p-4">
            {/* Project Details Section */}
            <div
              className="bg-gradient-to-br from-slate-50 to-gray-100 p-4 rounded-lg border border-gray-200 mb-4"
              style={{
                transform: 'perspective(800px) rotateX(0.3deg)',
                boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.06)'
              }}
            >
              <div className="flex items-center mb-4">
                <div className="p-1.5 bg-slate-600 rounded-md mr-2">
                  <Building2 className="w-4 h-4 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800">Project Details</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Project Name */}
                <div className="group">
                  <label htmlFor="projectName" className="flex items-center text-xs font-medium text-gray-700 mb-1.5">
                    <FileText className="w-3 h-3 mr-1.5 text-gray-500" />
                    Project Name
                  </label>
                  <div className="relative">
                    <input
                      id="projectName"
                      type="text"
                      className={`w-full px-3 py-2 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm hover:shadow-md ${
                        fields.projectName?.error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter project name"
                      style={{
                        transform: 'perspective(500px) rotateX(0.3deg)',
                        boxShadow: '0 3px 5px -1px rgba(0, 0, 0, 0.1), 0 2px 3px -1px rgba(0, 0, 0, 0.06)'
                      }}
                      {...getFieldProps('projectName')}
                    />
                    {fields.projectName?.error && (
                      <p className="mt-1 text-xs text-red-600 flex items-center">
                        <XCircle className="w-3 h-3 mr-1" />
                        {fields.projectName.error}
                      </p>
                    )}
                  </div>
                </div>

                {/* Project Code */}
                <div className="group">
                  <label htmlFor="projectCode" className="flex items-center text-xs font-medium text-gray-700 mb-1.5">
                    <Hash className="w-3 h-3 mr-1.5 text-gray-500" />
                    Project Code
                  </label>
                  <div className="relative">
                    <input
                      id="projectCode"
                      type="text"
                      className={`w-full px-3 py-2 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm hover:shadow-md ${
                        fields.projectCode?.error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                      }`}
                      placeholder="ABCD-12-345"
                      maxLength={11}
                      style={{
                        transform: 'perspective(500px) rotateX(0.3deg)',
                        boxShadow: '0 3px 5px -1px rgba(0, 0, 0, 0.1), 0 2px 3px -1px rgba(0, 0, 0, 0.06)'
                      }}
                      value={fields.projectCode?.value || ''}
                      onChange={handleProjectCodeChange}
                      onBlur={getFieldProps('projectCode').onBlur}
                    />
                    {fields.projectCode?.error && (
                      <p className="mt-1 text-xs text-red-600 flex items-center">
                        <XCircle className="w-3 h-3 mr-1" />
                        {fields.projectCode.error}
                      </p>
                    )}
                  </div>
                </div>

                {/* Project Category */}
                <div className="group">
                  <label htmlFor="projectCategory" className="flex items-center text-xs font-medium text-gray-700 mb-1.5">
                    <Layers className="w-3 h-3 mr-1.5 text-gray-500" />
                    Project Category
                  </label>
                  <div className="relative">
                    <select
                      id="projectCategory"
                      className={`w-full px-3 py-2 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm hover:shadow-md ${
                        fields.projectCategory?.error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                      }`}
                      style={{
                        transform: 'perspective(500px) rotateX(0.3deg)',
                        boxShadow: '0 3px 5px -1px rgba(0, 0, 0, 0.1), 0 2px 3px -1px rgba(0, 0, 0, 0.06)'
                      }}
                      {...getFieldProps('projectCategory')}
                      disabled={projectCategories.length === 0}
                    >
                      <option value="">
                        {projectCategories.length === 0 ?
                          (currentUser?.role === 'DIRECTOR' || currentUser?.role === 'PROJECT_MANAGER' ? 'Loading categories...' : 'No categories available') :
                          'Select Project Category'
                        }
                      </option>
                      {projectCategories.map(category => (
                        <option key={category.id} value={category.code}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                    {fields.projectCategory?.error && (
                      <p className="mt-1 text-xs text-red-600 flex items-center">
                        <XCircle className="w-3 h-3 mr-1" />
                        {fields.projectCategory.error}
                      </p>
                    )}
                  </div>
                </div>

                {/* Project Manager */}
                <div className="group">
                  <label htmlFor="projectManager" className="flex items-center text-xs font-medium text-gray-700 mb-1.5">
                    <User className="w-3 h-3 mr-1.5 text-gray-500" />
                    Project Manager
                  </label>
                  <div className="relative">
                    <select
                      id="projectManager"
                      className={`w-full px-3 py-2 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm hover:shadow-md ${
                        fields.projectManager?.error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                      }`}
                      style={{
                        transform: 'perspective(500px) rotateX(0.3deg)',
                        boxShadow: '0 3px 5px -1px rgba(0, 0, 0, 0.1), 0 2px 3px -1px rgba(0, 0, 0, 0.06)'
                      }}
                      {...getFieldProps('projectManager')}
                    >
                      <option value="">Select Project Manager</option>
                      {projectManagers.map((manager) => (
                        <option key={manager.id} value={manager.id}>
                          {manager.name}
                        </option>
                      ))}
                    </select>
                    {fields.projectManager?.error && (
                      <p className="mt-1 text-xs text-red-600 flex items-center">
                        <XCircle className="w-3 h-3 mr-1" />
                        {fields.projectManager.error}
                      </p>
                    )}
                  </div>
                </div>

                {/* Customer */}
                <div className="group">
                  <label htmlFor="customer" className="flex items-center text-xs font-medium text-gray-700 mb-1.5">
                    <Building2 className="w-3 h-3 mr-1.5 text-gray-500" />
                    Customer
                  </label>
                  <div className="flex space-x-2">
                    <div className="flex-1">
                      <select
                        id="customer"
                        className={`w-full px-3 py-2 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm hover:shadow-md ${
                          fields.customer?.error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                        }`}
                        style={{
                          transform: 'perspective(500px) rotateX(0.3deg)',
                          boxShadow: '0 3px 5px -1px rgba(0, 0, 0, 0.1), 0 2px 3px -1px rgba(0, 0, 0, 0.06)'
                        }}
                        {...getFieldProps('customer')}
                      >
                        <option value="">Select Customer</option>
                        {customers.map((customer) => (
                          <option key={customer.id} value={customer.id}>
                            {customer.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <button
                      type="button"
                      className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 flex items-center shadow-md hover:shadow-lg transform hover:scale-105"
                      onClick={() => navigate('/customers?addCustomer=true')}
                      style={{
                        transform: 'perspective(400px) rotateX(0.5deg)',
                      }}
                    >
                      <PlusCircle size={12} className="mr-1" />
                      <span className="text-xs">New</span>
                    </button>
                  </div>
                  {fields.customer?.error && (
                    <p className="mt-1 text-xs text-red-600 flex items-center">
                      <XCircle className="w-3 h-3 mr-1" />
                      {fields.customer.error}
                    </p>
                  )}
                </div>

                {/* PO Number */}
                <div className="group">
                  <label htmlFor="poNumber" className="flex items-center text-xs font-medium text-gray-700 mb-1.5">
                    <FileText className="w-3 h-3 mr-1.5 text-gray-500" />
                    PO Number
                  </label>
                  <div className="relative">
                    <input
                      id="poNumber"
                      type="text"
                      className={`w-full px-3 py-2 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm hover:shadow-md ${
                        fields.poNumber?.error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter PO number"
                      style={{
                        transform: 'perspective(500px) rotateX(0.3deg)',
                        boxShadow: '0 3px 5px -1px rgba(0, 0, 0, 0.1), 0 2px 3px -1px rgba(0, 0, 0, 0.06)'
                      }}
                      {...getFieldProps('poNumber')}
                    />
                    {fields.poNumber?.error && (
                      <p className="mt-1 text-xs text-red-600 flex items-center">
                        <XCircle className="w-3 h-3 mr-1" />
                        {fields.poNumber.error}
                      </p>
                    )}
                  </div>
                </div>

                {/* PO Date */}
                <div className="group">
                  <label htmlFor="poDate" className="flex items-center text-xs font-medium text-gray-700 mb-1.5">
                    <Calendar className="w-3 h-3 mr-1.5 text-gray-500" />
                    PO Date
                  </label>
                  <div className="relative">
                    <input
                      id="poDate"
                      type="date"
                      min={getMinDate()}
                      className={`w-full px-3 py-2 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm hover:shadow-md ${
                        fields.poDate?.error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                      }`}
                      style={{
                        transform: 'perspective(500px) rotateX(0.3deg)',
                        boxShadow: '0 3px 5px -1px rgba(0, 0, 0, 0.1), 0 2px 3px -1px rgba(0, 0, 0, 0.06)'
                      }}
                      {...getFieldProps('poDate')}
                    />
                    {fields.poDate?.error && (
                      <p className="mt-1 text-xs text-red-600 flex items-center">
                        <XCircle className="w-3 h-3 mr-1" />
                        {fields.poDate.error}
                      </p>
                    )}
                  </div>
                </div>

                {/* PO Value */}
                <div className="group">
                  <label htmlFor="poValue" className="flex items-center text-xs font-medium text-gray-700 mb-1.5">
                    <DollarSign className="w-3 h-3 mr-1.5 text-gray-500" />
                    PO Value (₹)
                  </label>
                  <div className="relative">
                    <input
                      id="poValue"
                      type="number"
                      step="0.01"
                      min="0"
                      className={`w-full px-3 py-2 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm hover:shadow-md ${
                        fields.poValue?.error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter PO value"
                      style={{
                        transform: 'perspective(500px) rotateX(0.3deg)',
                        boxShadow: '0 3px 5px -1px rgba(0, 0, 0, 0.1), 0 2px 3px -1px rgba(0, 0, 0, 0.06)'
                      }}
                      {...getFieldProps('poValue')}
                    />
                    {fields.poValue?.error && (
                      <p className="mt-1 text-xs text-red-600 flex items-center">
                        <XCircle className="w-3 h-3 mr-1" />
                        {fields.poValue.error}
                      </p>
                    )}
                  </div>
                </div>

                {/* Start Date */}
                <div className="group">
                  <label htmlFor="startDate" className="flex items-center text-xs font-medium text-gray-700 mb-1.5">
                    <Calendar className="w-3 h-3 mr-1.5 text-gray-500" />
                    Project Start Date
                  </label>
                  <div className="relative">
                    <input
                      id="startDate"
                      type="date"
                      min={getMinDate()}
                      className={`w-full px-3 py-2 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm hover:shadow-md ${
                        fields.startDate?.error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                      }`}
                      style={{
                        transform: 'perspective(500px) rotateX(0.3deg)',
                        boxShadow: '0 3px 5px -1px rgba(0, 0, 0, 0.1), 0 2px 3px -1px rgba(0, 0, 0, 0.06)'
                      }}
                      {...getFieldProps('startDate')}
                    />
                    {fields.startDate?.error && (
                      <p className="mt-1 text-xs text-red-600 flex items-center">
                        <XCircle className="w-3 h-3 mr-1" />
                        {fields.startDate.error}
                      </p>
                    )}
                    {!fields.startDate?.error && (
                      <p className="mt-1 text-xs text-gray-500">
                        Start date can be up to 30 days in the past
                      </p>
                    )}
                  </div>
                </div>

                {/* End Date */}
                <div className="group">
                  <label htmlFor="endDate" className="flex items-center text-xs font-medium text-gray-700 mb-1.5">
                    <Calendar className="w-3 h-3 mr-1.5 text-gray-500" />
                    Project End Date (Optional)
                  </label>
                  <div className="relative">
                    <input
                      id="endDate"
                      type="date"
                      min={fields.startDate?.value || getMinDate()}
                      className={`w-full px-3 py-2 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm hover:shadow-md ${
                        fields.endDate?.error ? 'border-red-300 focus:ring-red-500' : 'border-gray-300'
                      }`}
                      style={{
                        transform: 'perspective(500px) rotateX(0.3deg)',
                        boxShadow: '0 3px 5px -1px rgba(0, 0, 0, 0.1), 0 2px 3px -1px rgba(0, 0, 0, 0.06)'
                      }}
                      {...getFieldProps('endDate')}
                    />
                    {fields.endDate?.error && (
                      <p className="mt-1 text-xs text-red-600 flex items-center">
                        <XCircle className="w-3 h-3 mr-1" />
                        {fields.endDate.error}
                      </p>
                    )}
                    {!fields.endDate?.error && (
                      <p className="mt-1 text-xs text-gray-500">
                        {fields.startDate?.value
                          ? `End date must be after ${fields.startDate.value} (optional)`
                          : 'End date can be up to 30 days in the past (optional)'
                        }
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200 font-medium shadow-sm hover:shadow-md transform hover:scale-105 text-sm"
                onClick={handleCancel}
                disabled={isSubmitting}
                style={{
                  transform: 'perspective(400px) rotateX(0.5deg)',
                }}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 font-medium shadow-md hover:shadow-lg transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                disabled={isSubmitting}
                onClick={() => console.log('🔥 SUBMIT BUTTON CLICKED!')}
                style={{
                  transform: 'perspective(400px) rotateX(0.5deg)',
                  boxShadow: '0 8px 20px -5px rgba(59, 130, 246, 0.4), 0 8px 8px -5px rgba(59, 130, 246, 0.04)'
                }}
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating Project...
                  </span>
                ) : (
                  'Create Project'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Success Dialog */}
      <SuccessDialog
        isOpen={showSuccessDialog}
        onClose={() => setShowSuccessDialog(false)}
        title="Project Created Successfully!"
        message="Your new project has been created and is ready for use."
        details={successDetails}
        actionText="View Project"
        onAction={handleSuccessDialogAction}
      />

      {/* Error Dialog */}
      <ErrorDialog
        isOpen={showErrorDialog}
        onClose={() => setShowErrorDialog(false)}
        title="Failed to Create Project"
        message="There was an error creating your project. Please check the details below and try again."
        details={errorDetails}
        actionText="OK"
        onAction={handleErrorDialogAction}
      />

    </div>
  );
};

export default CreateProjectPage;