import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAtom } from 'jotai';
import { currentUserAtom } from '../../store';
import { paymentsAPI } from '../../services/api';
import {
  CreditCard,
  ArrowRight,
  Calendar,
  Building2,
  User,
  DollarSign,
  TrendingUp,
  Eye,
  RefreshCw
} from 'lucide-react';

interface RecentTransaction {
  id: string;
  projectName: string;
  projectCode: string;
  customerName: string;
  amount: number;
  paymentDate: string;
  paymentMethod: string;
  referenceNumber?: string;
  createdBy: string;
  userName: string;
  createdAt: string;
}

const RecentTransactions: React.FC = () => {
  const navigate = useNavigate();
  const [currentUser] = useAtom(currentUserAtom);
  const [transactions, setTransactions] = useState<RecentTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if user is Director
  const isDirector = currentUser?.role === 'DIRECTOR';

  useEffect(() => {
    if (isDirector) {
      fetchRecentTransactions();
    }
  }, [isDirector]);

  const fetchRecentTransactions = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await paymentsAPI.getPaymentRecords();

      if (response.success) {
        // Get the 5 most recent transactions
        const recentTransactions = response.data.payments
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, 5)
          .map(payment => ({
            id: payment.id,
            projectName: payment.projectName,
            projectCode: payment.projectCode,
            customerName: payment.customerName,
            amount: payment.amount,
            paymentDate: payment.paymentDate,
            paymentMethod: payment.paymentMethod,
            referenceNumber: payment.referenceNumber,
            createdBy: payment.createdBy,
            userName: payment.user.name,
            createdAt: payment.createdAt
          }));

        setTransactions(recentTransactions);
      } else {
        setError('Failed to fetch recent transactions');
      }
    } catch (err) {
      console.error('Error fetching recent transactions:', err);
      setError('Failed to load recent transactions');
    } finally {
      setLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      month: 'short',
      day: 'numeric'
    });
  };

  // Get time ago
  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  // Get payment method display
  const getPaymentMethodDisplay = (method: string) => {
    const methods: Record<string, string> = {
      'BANK_TRANSFER': 'Bank Transfer',
      'UPI': 'UPI',
      'CASH': 'Cash',
      'CHEQUE': 'Cheque',
      'CREDIT_CARD': 'Credit Card',
      'NEFT': 'NEFT',
      'RTGS': 'RTGS',
      'OTHER': 'Other'
    };
    return methods[method] || method;
  };

  // Get payment method color
  const getPaymentMethodColor = (method: string) => {
    const colors: Record<string, string> = {
      'BANK_TRANSFER': 'bg-blue-100 text-blue-800',
      'UPI': 'bg-purple-100 text-purple-800',
      'CASH': 'bg-green-100 text-green-800',
      'CHEQUE': 'bg-yellow-100 text-yellow-800',
      'CREDIT_CARD': 'bg-red-100 text-red-800',
      'NEFT': 'bg-indigo-100 text-indigo-800',
      'RTGS': 'bg-pink-100 text-pink-800',
      'OTHER': 'bg-gray-100 text-gray-800'
    };
    return colors[method] || 'bg-gray-100 text-gray-800';
  };

  // Don't render for non-directors
  if (!isDirector) {
    return null;
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CreditCard className="w-5 h-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Recent Transactions</h3>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={fetchRecentTransactions}
              disabled={loading}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
              title="Refresh"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </button>
            <button
              onClick={() => navigate('/transaction-history')}
              className="flex items-center px-3 py-1.5 text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors"
            >
              View All
              <ArrowRight className="w-4 h-4 ml-1" />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent"></div>
            <span className="ml-3 text-gray-600">Loading transactions...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500 mb-3">{error}</p>
            <button
              onClick={fetchRecentTransactions}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Try Again
            </button>
          </div>
        ) : transactions.length === 0 ? (
          <div className="text-center py-8">
            <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500">No recent transactions found</p>
            <button
              onClick={() => navigate('/transaction-history')}
              className="mt-2 text-blue-600 hover:text-blue-800 font-medium"
            >
              View Payment History
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {transactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                onClick={() => navigate('/transaction-history')}
              >
                {/* Left side - Transaction details */}
                <div className="flex items-center space-x-4">
                  {/* Payment method icon */}
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <DollarSign className="w-5 h-5 text-blue-600" />
                    </div>
                  </div>

                  {/* Transaction info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {transaction.projectName}
                      </p>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getPaymentMethodColor(transaction.paymentMethod)}`}>
                        {getPaymentMethodDisplay(transaction.paymentMethod)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span className="flex items-center">
                        <Building2 className="w-3 h-3 mr-1" />
                        {transaction.customerName}
                      </span>
                      <span className="flex items-center">
                        <User className="w-3 h-3 mr-1" />
                        {transaction.userName}
                      </span>
                      <span className="flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatDate(transaction.paymentDate)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Right side - Amount and time */}
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-sm font-bold text-green-600">
                      {formatCurrency(transaction.amount)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {getTimeAgo(transaction.createdAt)}
                    </p>
                  </div>
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                </div>
              </div>
            ))}

            {/* Summary footer */}
            <div className="mt-6 pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center text-gray-600">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  <span>
                    Total: {formatCurrency(transactions.reduce((sum, t) => sum + t.amount, 0))}
                  </span>
                </div>
                <button
                  onClick={() => navigate('/transaction-history')}
                  className="flex items-center text-blue-600 hover:text-blue-800 font-medium"
                >
                  <Eye className="w-4 h-4 mr-1" />
                  View All Transactions
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentTransactions;
