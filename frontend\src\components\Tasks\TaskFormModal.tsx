import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { useForm, SubmitHandler } from 'react-hook-form';
import { engineersAtom, projectsAtom, currentUserAtom } from '../../store';
import { Task, TaskStatus, TaskAssigneeType } from '../../types';
import { X, Save } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import { toCamelCase } from '../../utils/textUtils';

interface TaskFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (task: Task) => void;
  projectId: string;
  task?: Task; // If provided, we're editing an existing task
  title?: string;
}

interface FormValues {
  name: string;
  description: string;
  assigneeId: string;
  startDate: string;
  endDate: string;
  status: TaskStatus;
}

const TaskFormModal: React.FC<TaskFormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  projectId,
  task,
  title = 'Add Task'
}) => {
  const [engineers] = useAtom(engineersAtom);
  const [projects] = useAtom(projectsAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [taskName, setTaskName] = useState('');

  const project = projects.find(p => p.id === projectId);

  // Filter users for task assignment - only team leads
  const teamLeads = engineers.filter(user => user.role === 'TEAM_LEAD');

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setValue
  } = useForm<FormValues>({
    defaultValues: {
      name: '',
      description: '',
      assigneeId: '',
      startDate: project?.startDate || '',
      endDate: project?.endDate || '',
      status: TaskStatus.NOT_STARTED
    }
  });

  // Initialize form with task data if editing
  useEffect(() => {
    if (task) {
      setTaskName(task.name);
      reset({
        name: task.name,
        description: task.description || '',
        assigneeId: '',
        startDate: task.startDate,
        endDate: task.endDate,
        status: task.status
      });
    } else {
      setTaskName('');
      reset({
        name: '',
        description: '',
        assigneeId: '',
        startDate: project?.startDate || '',
        endDate: project?.endDate || '',
        status: TaskStatus.NOT_STARTED
      });
    }
  }, [task, reset, project]);

  const handleFormSubmit: SubmitHandler<FormValues> = async (data) => {
    setIsSubmitting(true);

    try {
      const taskData: Task = task
        ? {
            ...task,
            name: toCamelCase(data.name),
            description: data.description,
            assigneeId: data.assigneeId,
            startDate: data.startDate,
            ...(data.endDate && { endDate: data.endDate }),
            status: data.status,
          }
        : {
            id: uuidv4(),
            displayId: '',
            projectId,
            sectionId: '',
            name: toCamelCase(data.name),
            description: data.description,
            assigneeId: data.assigneeId,
            assigneeType: TaskAssigneeType.ENGINEER,
            department: (project && 'department' in project ? (project as any).department : ''),
            startDate: data.startDate,
            endDate: data.endDate || '',
            status: data.status,
            subtasks: [],
            createdBy: currentUser?.id || '',
            createdAt: new Date().toISOString()
          };

      onSubmit(taskData);
      onClose();
    } catch (error) {
      console.error('Error submitting task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6 space-y-4">
          <div className="space-y-4">
            <div>
              <label className="form-label">Task Name</label>
              <input
                type="text"
                className={`form-input w-full ${errors.name ? 'border-error' : ''}`}
                placeholder="Enter task name"
                value={taskName}
                onChange={(e) => {
                  setTaskName(e.target.value);
                  setValue('name', e.target.value);
                }}
                {...register('name', { required: 'Task name is required' })}
              />
              {errors.name && (
                <p className="text-error text-sm mt-1">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="form-label">Description</label>
              <textarea
                className="form-textarea w-full"
                rows={3}
                placeholder="Enter task description"
                {...register('description')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="form-label">Assignee</label>
                <select
                  className="form-select w-full"
                  {...register('assigneeId')}
                >
                  <option value="">Select Team Lead</option>
                  {teamLeads.map(teamLead => (
                    <option key={teamLead.id} value={teamLead.id}>
                      {teamLead.name} ({teamLead.department})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="form-label">Status</label>
                <select
                  className="form-select w-full"
                  {...register('status')}
                >
                  {Object.values(TaskStatus).map(status => (
                    <option key={status} value={status}>
                      {status.replace(/_/g, ' ')}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="form-label">Start Date</label>
                <input
                  type="date"
                  className="form-input w-full"
                  {...register('startDate', { required: 'Start date is required' })}
                  min={project?.startDate}
                  max={project?.endDate}
                />
                {errors.startDate && (
                  <p className="text-error text-sm mt-1">{errors.startDate.message}</p>
                )}
              </div>

              <div>
                <label className="form-label">End Date</label>
                <input
                  type="date"
                  className="form-input w-full"
                  {...register('endDate')}
                  min={project?.startDate}
                  max={project?.endDate}
                />
                {errors.endDate && (
                  <p className="text-error text-sm mt-1">{errors.endDate.message}</p>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t mt-6">
            <button
              type="button"
              className="btn btn-outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary flex items-center"
              disabled={isSubmitting}
            >
              <Save size={18} className="mr-2" />
              {isSubmitting ? 'Saving...' : 'Save Task'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskFormModal;
