import React, { useEffect, useState } from 'react';
import { useAtom } from 'jotai';
import { currentUserAtom, projectsAtom, alertsAtom, userTasksAtom, userSubtasksAtom } from '../store';
import { useDataService } from '../services/dataService';
import DashboardCards from '../components/Dashboard/DashboardCards';
import TaskChart from '../components/Dashboard/TaskChart';
import SubtaskChart from '../components/Dashboard/SubtaskChart';
import ProjectsProgress from '../components/Dashboard/ProjectsProgress';
import RecentSubtasks from '../components/Dashboard/RecentSubtasks';
import RecentAlerts from '../components/Dashboard/RecentAlerts';
import RecentTransactions from '../components/Dashboard/RecentTransactions';
import PaymentChart from '../components/Dashboard/PaymentChart';

const DashboardPage: React.FC = () => {
  const [currentUser] = useAtom(currentUserAtom);
  const [projects] = useAtom(projectsAtom);
  const [alerts] = useAtom(alertsAtom);
  const [userTasks] = useAtom(userTasksAtom);
  const [userSubtasks] = useAtom(userSubtasksAtom);
  const [isDataLoading, setIsDataLoading] = useState(true);
  const dataService = useDataService();

  // Check if essential data is loaded
  useEffect(() => {
    const checkDataLoaded = () => {
      // Consider data loaded if we have at least some basic data structures
      const hasBasicData = projects !== null && alerts !== null && userTasks !== null && userSubtasks !== null;
      setIsDataLoading(!hasBasicData);
    };

    checkDataLoaded();
  }, [projects, alerts, userTasks, userSubtasks]);

  // Show loading state if data is still loading
  if (isDataLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20 px-4 py-4">
        {/* Background decorative elements */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-32 w-96 h-96 bg-gradient-to-br from-blue-400/5 to-purple-400/5 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-32 w-96 h-96 bg-gradient-to-tr from-green-400/5 to-blue-400/5 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto">
          <div className="flex flex-col justify-center items-center py-32">
            <div className="relative mb-8">
              <div className="animate-spin rounded-full h-20 w-20 border-4 border-blue-200"></div>
              <div className="animate-spin rounded-full h-20 w-20 border-4 border-blue-600 border-t-transparent absolute top-0 left-0"></div>
            </div>
            <div className="text-center space-y-2">
              <h2 className="text-2xl font-bold text-gray-800">Loading Dashboard</h2>
              <p className="text-gray-600">Please wait while we fetch your data...</p>
              <div className="flex items-center justify-center space-x-1 mt-4">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20 px-4 py-4">
      {/* Background decorative elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-32 w-96 h-96 bg-gradient-to-br from-blue-400/5 to-purple-400/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-32 w-96 h-96 bg-gradient-to-tr from-green-400/5 to-blue-400/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 w-full space-y-5">
        {/* Enhanced Header Section */}
        <div className="relative">
          <div className="bg-gradient-to-r from-white via-white/95 to-white/90 rounded-xl shadow-lg border border-white/20 p-5 backdrop-blur-sm overflow-hidden">
            {/* Header background pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-blue-50/20 to-purple-50/20"></div>
            <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/60 to-transparent"></div>

            <div className="relative z-10">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent drop-shadow-sm">
                    Dashboard
                  </h1>
                  <p className="text-base text-gray-600 font-medium">
                    Welcome back, <span className="text-blue-600 font-semibold">{currentUser?.name}</span>!
                    Here's an overview of your projects and tasks.
                  </p>
                </div>

                {/* Optional: Add a greeting time indicator */}
                <div className="hidden md:flex items-center space-x-4">
                  <div className="px-3 py-1.5 bg-gradient-to-r from-blue-100 to-purple-100 rounded-lg border border-blue-200/50 shadow-sm">
                    <span className="text-xs font-medium text-gray-700">
                      {new Date().toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Header shadow */}
          <div className="absolute -bottom-2 left-4 right-4 h-4 bg-gray-900/10 rounded-xl blur-lg -z-10"></div>
        </div>

        {/* Dashboard Cards Section */}
        <div className="transform transition-all duration-500 hover:scale-[1.01]">
          <DashboardCards />
        </div>

        {/* Charts Section - Left Pie Charts, Right Recent Projects */}
        <div className="flex gap-8 min-h-[600px]">
          {/* Left Side - Pie Charts Stacked Vertically */}
          <div className="w-[35%] flex flex-col gap-8">
            {/* Task Status Chart */}
            <div className="group transform transition-all duration-300 hover:scale-[1.01] flex-1 min-h-[280px]">
              <div className="relative h-full">
                {currentUser?.role === 'ENGINEER' ? <SubtaskChart /> : <TaskChart />}
                {/* Individual component shadow */}
                <div className="absolute -bottom-1 left-3 right-3 h-3 bg-gray-900/10 rounded-lg blur-md -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            </div>

            {/* Payment Chart - Directors Only */}
            {currentUser?.role === 'DIRECTOR' && (
              <div className="group transform transition-all duration-300 hover:scale-[1.01] flex-1 min-h-[280px]">
                <div className="relative h-full">
                  <PaymentChart />
                  {/* Individual component shadow */}
                  <div className="absolute -bottom-1 left-3 right-3 h-3 bg-gray-900/10 rounded-lg blur-md -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </div>
            )}
          </div>

          {/* Right Side - Recent Projects Full Height */}
          <div className="group transform transition-all duration-300 hover:scale-[1.01] flex-1">
            <div className="relative h-full">
              {currentUser?.role === 'ENGINEER' ? <RecentSubtasks /> : <ProjectsProgress />}
              {/* Individual component shadow */}
              <div className="absolute -bottom-1 left-3 right-3 h-3 bg-gray-900/10 rounded-lg blur-md -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>
        </div>

        {/* Recent Transactions Section - Directors Only */}
        {currentUser?.role === 'DIRECTOR' && (
          <div className="transform transition-all duration-300 hover:scale-[1.005]">
            <div className="relative">
              <RecentTransactions />
              {/* Individual component shadow */}
              <div className="absolute -bottom-1 left-3 right-3 h-3 bg-gray-900/10 rounded-lg blur-md -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>
        )}

        {/* Alerts Section - Full Width */}
        <div className="transform transition-all duration-300 hover:scale-[1.005]">
          <RecentAlerts />
        </div>

        {/* Bottom spacing */}
        <div className="h-4"></div>
      </div>
    </div>
  );
};

export default DashboardPage;