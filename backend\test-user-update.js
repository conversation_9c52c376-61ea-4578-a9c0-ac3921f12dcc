const axios = require('axios');

const BASE_URL = 'http://localhost:5002';

async function testUserUpdate() {
  try {
    console.log('🧪 Testing User Update Functionality...\n');

    // Step 1: Login to get a token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'password'
    });

    if (!loginResponse.data.success) {
      throw new Error('Login failed: ' + loginResponse.data.message);
    }

    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');

    // Step 2: Get list of users
    console.log('\n2. Getting list of users...');
    const usersResponse = await axios.get(`${BASE_URL}/api/users`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (!usersResponse.data.success) {
      throw new Error('Get users failed: ' + usersResponse.data.message);
    }

    const users = usersResponse.data.data;
    console.log(`✅ Found ${users.length} users`);

    // Find a user to update (let's use the first engineer)
    const userToUpdate = users.find(user => user.role === 'ENGINEER');
    if (!userToUpdate) {
      throw new Error('No engineer found to update');
    }

    console.log(`📝 Selected user to update: ${userToUpdate.name} (${userToUpdate.email})`);

    // Step 3: Update the user
    console.log('\n3. Updating user...');
    const updateData = {
      name: userToUpdate.name + ' (Updated)',
      skills: 'JavaScript,React,Node.js,Testing'
    };

    const updateResponse = await axios.put(`${BASE_URL}/api/users/${userToUpdate.id}`, updateData, {
      headers: { 
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!updateResponse.data.success) {
      throw new Error('Update failed: ' + updateResponse.data.message);
    }

    console.log('✅ User updated successfully!');
    console.log('📊 Updated data:', updateResponse.data.data);

    // Step 4: Verify the update
    console.log('\n4. Verifying update...');
    const verifyResponse = await axios.get(`${BASE_URL}/api/users`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    const updatedUser = verifyResponse.data.data.find(user => user.id === userToUpdate.id);
    if (updatedUser && updatedUser.name === updateData.name) {
      console.log('✅ Update verified successfully!');
      console.log(`📝 Name changed from "${userToUpdate.name}" to "${updatedUser.name}"`);
      console.log(`📝 Skills updated to: "${updatedUser.skills}"`);
    } else {
      console.log('❌ Update verification failed');
    }

    console.log('\n🎉 User update functionality is working correctly!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('📊 Response data:', error.response.data);
      console.error('📊 Response status:', error.response.status);
    }
    process.exit(1);
  }
}

// Run the test
testUserUpdate();
