import { Request, Response } from 'express';
import { prisma } from '../index';
import { hashPassword, validateName, validateEmail, validateCode, validatePhone, normalizeEmail } from '../utils/auth.utils';
import { v4 as uuidv4 } from 'uuid';

// @desc    Get all users
// @route   GET /api/users
// @access  Private/Admin
export const getUsers = async (req: Request, res: Response): Promise<void> => {
  try {
    // Build filter based on user role
    let whereClause: any = {};

    if (req.user.role === 'DIRECTOR') {
      // Directors can see all users
      whereClause = {};
    } else if (req.user.role === 'PROJECT_MANAGER') {
      // Project managers can see team leads and engineers
      whereClause = {
        role: {
          in: ['TEAM_LEAD', 'ENGINEER', 'PROJECT_MANAGER']
        }
      };
    } else if (req.user.role === 'TEAM_LEAD') {
      // Team leads can see engineers in their department and themselves
      whereClause = {
        OR: [
          {
            role: 'ENGINEER',
            department: req.user.department
          },
          {
            id: req.user.id // Include themselves
          }
        ]
      };
    } else {
      // Engineers can only see themselves
      whereClause = {
        id: req.user.id
      };
    }

    const users = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        department: true,
        profileImage: true,
        code: true,
        skills: true,
        joinDate: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: [
        { role: 'asc' },
        { name: 'asc' }
      ]
    });

    console.log(`User ${req.user.role} (${req.user.email}) requested users, returning ${users.length} users`);

    res.status(200).json({
      success: true,
      count: users.length,
      data: users,
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Private/Admin
export const getUser = async (req: Request, res: Response): Promise<void> => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.params.id },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        department: true,
        profileImage: true,
        code: true,
        skills: true,
        joinDate: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create user
// @route   POST /api/users
// @access  Private/Admin
export const createUser = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      email,
      password,
      name,
      role,
      department,
      profileImage,
      code,
      skills,
      joinDate
    } = req.body;

    // Validate required fields
    if (!email || !password || !name || !role || !department) {
      res.status(400).json({
        success: false,
        message: 'Please provide all required fields: email, password, name, role, department',
      });
      return;
    }

    // Role-based restrictions for user creation
    const currentUserRole = req.user.role;
    const targetRole = role;

    // DIRECTOR can create anyone
    if (currentUserRole === 'DIRECTOR') {
      // No restrictions
    }
    // PROJECT_MANAGER can create TEAM_LEAD and ENGINEER
    else if (currentUserRole === 'PROJECT_MANAGER') {
      if (!['TEAM_LEAD', 'ENGINEER'].includes(targetRole)) {
        res.status(403).json({
          success: false,
          message: 'Project Managers can only create Team Leads and Engineers',
        });
        return;
      }
    }
    // TEAM_LEAD can only create ENGINEER in their department
    else if (currentUserRole === 'TEAM_LEAD') {
      if (targetRole !== 'ENGINEER') {
        res.status(403).json({
          success: false,
          message: 'Team Leads can only create Engineers',
        });
        return;
      }
      // Check if the department matches the team lead's department
      if (department !== req.user.department) {
        res.status(403).json({
          success: false,
          message: 'Team Leads can only create Engineers in their own department',
        });
        return;
      }
    }
    // Other roles cannot create users
    else {
      res.status(403).json({
        success: false,
        message: 'You do not have permission to create users',
      });
      return;
    }

    // Validate user name
    const nameValidation = validateName(name);
    if (!nameValidation.isValid) {
      res.status(400).json({
        success: false,
        message: nameValidation.message
      });
      return;
    }

    // Validate email format
    const emailValidation = validateEmail(email);
    if (!emailValidation.isValid) {
      res.status(400).json({
        success: false,
        message: emailValidation.message
      });
      return;
    }

    // Validate user code if provided
    if (code) {
      const codeValidation = validateCode(code, 'User code');
      if (!codeValidation.isValid) {
        res.status(400).json({
          success: false,
          message: codeValidation.message
        });
        return;
      }
    }

    // Validate password strength
    if (password.length < 6) {
      res.status(400).json({
        success: false,
        message: 'Password must be at least 6 characters long',
      });
      return;
    }

    // Normalize email to lowercase
    const normalizedEmail = normalizeEmail(email);

    // Check if user already exists
    const userExists = await prisma.user.findUnique({
      where: { email: normalizedEmail },
    });

    if (userExists) {
      res.status(400).json({
        success: false,
        message: 'User with this email already exists',
      });
      return;
    }

    // Check if code is unique if provided
    if (code) {
      const codeExists = await prisma.user.findUnique({
        where: { code },
      });

      if (codeExists) {
        res.status(400).json({
          success: false,
          message: 'Employee code already exists',
        });
        return;
      }
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    console.log('Creating user with data:', {
      email,
      name,
      role,
      department,
      profileImage: profileImage ? 'Provided' : 'Not provided',
      code: code || 'Not provided',
      skills: skills || 'Not provided',
      joinDate: joinDate ? 'Provided' : 'Not provided',
      password: 'Hashed (not shown)'
    });

    // Create user
    const userId = uuidv4();
    const user = await prisma.user.create({
      data: {
        id: userId,
        email: normalizedEmail,
        password: hashedPassword,
        name,
        role,
        department,
        profileImage,
        code,
        skills,
        joinDate: joinDate ? new Date(joinDate) : null,
      },
    });

    console.log('User created successfully:', user.id);

    res.status(201).json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department,
        profileImage: user.profileImage,
        code: user.code,
        skills: user.skills,
        joinDate: user.joinDate,
      },
    });
  } catch (error: any) {
    console.error('Create user error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      res.status(400).json({
        success: false,
        message: `A user with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during user creation',
    });
  }
};

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private/Admin
export const updateUser = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      email,
      name,
      role,
      department,
      profileImage,
      password,
      code,
      skills,
      joinDate
    } = req.body;

    // Check if user exists
    const userExists = await prisma.user.findUnique({
      where: { id: req.params.id },
    });

    if (!userExists) {
      res.status(404).json({
        success: false,
        message: 'User not found',
      });
      return;
    }

    // Role-based restrictions for user updates
    const currentUserRole = req.user.role;
    const currentUserId = req.user.id;
    const targetUserId = req.params.id;
    const targetUserRole = userExists.role;

    // Users can always update their own profile (except role)
    const isUpdatingSelf = currentUserId === targetUserId;

    // If updating role, apply strict restrictions
    if (role !== undefined && role !== targetUserRole) {
      // DIRECTOR can change anyone's role
      if (currentUserRole === 'DIRECTOR') {
        // No restrictions
      }
      // PROJECT_MANAGER can change TEAM_LEAD and ENGINEER roles
      else if (currentUserRole === 'PROJECT_MANAGER') {
        if (!['TEAM_LEAD', 'ENGINEER'].includes(role) || !['TEAM_LEAD', 'ENGINEER'].includes(targetUserRole)) {
          res.status(403).json({
            success: false,
            message: 'Project Managers can only modify Team Lead and Engineer roles',
          });
          return;
        }
      }
      // TEAM_LEAD can only change ENGINEER roles in their department
      else if (currentUserRole === 'TEAM_LEAD') {
        if (role !== 'ENGINEER' || targetUserRole !== 'ENGINEER') {
          res.status(403).json({
            success: false,
            message: 'Team Leads can only modify Engineer roles',
          });
          return;
        }
        if (userExists.department !== req.user.department) {
          res.status(403).json({
            success: false,
            message: 'Team Leads can only modify Engineers in their own department',
          });
          return;
        }
      }
      // Other roles cannot change roles
      else {
        res.status(403).json({
          success: false,
          message: 'You do not have permission to change user roles',
        });
        return;
      }
    }

    // If updating department, apply restrictions
    if (department !== undefined && department !== userExists.department) {
      // Only DIRECTOR and PROJECT_MANAGER can change departments
      if (!['DIRECTOR', 'PROJECT_MANAGER'].includes(currentUserRole)) {
        res.status(403).json({
          success: false,
          message: 'Only Directors and Project Managers can change user departments',
        });
        return;
      }
    }

    // General update permissions (for non-role, non-department fields)
    if (!isUpdatingSelf) {
      // DIRECTOR can update anyone
      if (currentUserRole === 'DIRECTOR') {
        // No restrictions
      }
      // PROJECT_MANAGER can update TEAM_LEAD and ENGINEER
      else if (currentUserRole === 'PROJECT_MANAGER') {
        if (!['TEAM_LEAD', 'ENGINEER'].includes(targetUserRole)) {
          res.status(403).json({
            success: false,
            message: 'Project Managers can only update Team Leads and Engineers',
          });
          return;
        }
      }
      // TEAM_LEAD can only update ENGINEER in their department
      else if (currentUserRole === 'TEAM_LEAD') {
        if (targetUserRole !== 'ENGINEER' || userExists.department !== req.user.department) {
          res.status(403).json({
            success: false,
            message: 'Team Leads can only update Engineers in their own department',
          });
          return;
        }
      }
      // Other roles cannot update other users
      else {
        res.status(403).json({
          success: false,
          message: 'You do not have permission to update other users',
        });
        return;
      }
    }

    // Validate user name if provided
    if (name) {
      const nameValidation = validateName(name);
      if (!nameValidation.isValid) {
        res.status(400).json({
          success: false,
          message: nameValidation.message
        });
        return;
      }
    }

    // Validate user code if provided
    if (code) {
      const codeValidation = validateCode(code, 'User code');
      if (!codeValidation.isValid) {
        res.status(400).json({
          success: false,
          message: codeValidation.message
        });
        return;
      }
    }

    // Normalize email if provided
    const normalizedEmail = email ? normalizeEmail(email) : undefined;

    // Validate email format if provided
    if (normalizedEmail && normalizedEmail !== userExists.email) {
      const emailValidation = validateEmail(normalizedEmail);
      if (!emailValidation.isValid) {
        res.status(400).json({
          success: false,
          message: emailValidation.message
        });
        return;
      }

      // Check if email is already in use by another user
      const emailExists = await prisma.user.findFirst({
        where: {
          email: normalizedEmail,
          NOT: { id: req.params.id }
        },
      });

      if (emailExists) {
        res.status(400).json({
          success: false,
          message: 'Email is already in use by another user',
        });
        return;
      }
    }

    // Validate password strength if provided
    if (password && password.length < 6) {
      res.status(400).json({
        success: false,
        message: 'Password must be at least 6 characters long',
      });
      return;
    }

    // Check if code is unique if provided and changed
    if (code && code !== userExists.code) {
      const codeExists = await prisma.user.findUnique({
        where: { code },
      });

      if (codeExists) {
        res.status(400).json({
          success: false,
          message: 'Employee code already exists',
        });
        return;
      }
    }

    // Prepare update data - only include fields that are provided
    const updateData: any = {};

    if (normalizedEmail !== undefined) updateData.email = normalizedEmail;
    if (name !== undefined) updateData.name = name;
    if (role !== undefined) updateData.role = role;
    if (department !== undefined) updateData.department = department;
    if (profileImage !== undefined) updateData.profileImage = profileImage;
    if (code !== undefined) updateData.code = code;
    if (skills !== undefined) updateData.skills = skills;
    if (joinDate !== undefined) updateData.joinDate = joinDate ? new Date(joinDate) : null;

    // If password is provided, hash it
    if (password) {
      updateData.password = await hashPassword(password);
    }

    console.log('Updating user with ID:', req.params.id);
    console.log('Update data:', {
      ...updateData,
      password: updateData.password ? 'Hashed (not shown)' : 'Not changed'
    });

    // Update user
    const user = await prisma.user.update({
      where: { id: req.params.id },
      data: updateData,
    });

    console.log('User updated successfully:', user.id);

    res.status(200).json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department,
        profileImage: user.profileImage,
        code: user.code,
        skills: user.skills,
        joinDate: user.joinDate,
      },
    });
  } catch (error: any) {
    console.error('Update user error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      res.status(400).json({
        success: false,
        message: `A user with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during user update',
    });
  }
};

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private/Admin
export const deleteUser = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if user exists
    const userExists = await prisma.user.findUnique({
      where: { id: req.params.id },
    });

    if (!userExists) {
      res.status(404).json({
        success: false,
        message: 'User not found',
      });
      return;
    }

    // Delete user
    await prisma.user.delete({
      where: { id: req.params.id },
    });

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
