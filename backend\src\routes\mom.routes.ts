import express, { Request, Response } from 'express';
import {
  getMOMs,
  getMOM,
  getMOMByProject,
  createMOM,
  updateMOM,
  deleteMOM,
  addMOMPoint,
  updateMOMPoint,
  deleteMOMPoint,
} from '../controllers/mom.controller';
import { protect, authorize } from '../middleware/auth.middleware';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(getMOMs)
  .post(createMOM);

router.route('/project/:projectId')
  .get(getMOMByProject);

router.route('/:id')
  .get(getMOM)
  .put(updateMOM)
  .delete(authorize('DIRECTOR'), deleteMOM);

router.route('/:id/points')
  .post(addMOMPoint);

router.route('/:id/points/:pointId')
  .put(updateMOMPoint)
  .delete(deleteMOMPoint);

export default router;
