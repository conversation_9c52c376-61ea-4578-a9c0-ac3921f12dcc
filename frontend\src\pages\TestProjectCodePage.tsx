import React, { useState } from 'react';
import { formatProjectCode, validateProjectCode } from '../utils/validation';

const TestProjectCodePage: React.FC = () => {
  const [projectCode, setProjectCode] = useState('');
  const [validationResult, setValidationResult] = useState<{ isValid: boolean; message?: string } | null>(null);

  const handleProjectCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatProjectCode(e.target.value);
    setProjectCode(formatted);

    // Validate the formatted code
    const validation = validateProjectCode(formatted);
    setValidationResult(validation);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Project Code Test</h1>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Project Code (4-2-3 format)
            </label>
            <input
              type="text"
              value={projectCode}
              onChange={handleProjectCodeChange}
              placeholder="Type any characters..."
              maxLength={11}
              className={`w-full px-4 py-3 border-2 rounded-lg transition-all duration-200 ${
                validationResult?.isValid === false
                  ? 'border-red-500 bg-red-50'
                  : validationResult?.isValid === true
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-300'
              }`}
            />

            {validationResult && (
              <div className={`mt-2 text-sm ${
                validationResult.isValid ? 'text-green-600' : 'text-red-600'
              }`}>
                {validationResult.isValid ? '✅ Valid format!' : `❌ ${validationResult.message}`}
              </div>
            )}
          </div>

          <div className="bg-gray-100 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">How it works:</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Type any characters (letters, numbers, symbols)</li>
              <li>• Only letters and numbers are kept</li>
              <li>• Automatically converts to uppercase</li>
              <li>• Formats as XXXX-XX-XXX (4-2-3 pattern)</li>
              <li>• Maximum 9 characters (excluding dashes)</li>
            </ul>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">Examples:</h3>
            <div className="text-sm text-blue-700 space-y-1">
              <div>Input: "abc123def" → Output: "ABC1-23-DEF"</div>
              <div>Input: "test@#$456xyz" → Output: "TEST-45-6XY"</div>
              <div>Input: "proj-01-web" → Output: "PROJ-01-WEB"</div>
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="font-medium text-yellow-900 mb-2">Project Status Logic:</h3>
            <div className="text-sm text-yellow-700 space-y-1">
              <div>• Project status automatically updates based on task completion</div>
              <div>• NOT_STARTED → All tasks are not started</div>
              <div>• IN_PROGRESS → At least one task is in progress</div>
              <div>• DELAYED → At least one task is delayed</div>
              <div>• COMPLETED → All tasks are completed</div>
              <div>• ON_HOLD → Manually set (preserved from automatic updates)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestProjectCodePage;
