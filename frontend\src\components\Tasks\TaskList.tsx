import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { useNavigate } from 'react-router-dom';
import {
  userTasksAtom,
  userSubtasksAtom,
  engineersAtom,
  currentUserAtom,
  projectsAtom
} from '../../store';
import { UserRole, TaskStatus, TaskAssigneeType } from '../../types';
import { format } from 'date-fns';
import { tasksAPI, subtasksAPI } from '../../services/api';
import {
  ArrowUpRight,
  ArrowDownRight,
  Clock,
  CheckSquare,
  XCircle,
  AlertTriangle,
  PauseCircle,
  ChevronDown,
  ChevronUp,
  Plus,
  Calendar,
  CheckCircle2,
  ExternalLink
} from 'lucide-react';

const TaskList: React.FC = () => {
  const navigate = useNavigate();

  // Get tasks from the store
  const [userTasks] = useAtom(userTasksAtom);
  const [userSubtasks] = useAtom(userSubtasksAtom);
  const [engineers] = useAtom(engineersAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [projects, setProjects] = useAtom(projectsAtom);

  // Local state for tasks to allow immediate UI updates
  const [localTasks, setLocalTasks] = useState<typeof userTasks>([]);

  const [expandedTaskIds, setExpandedTaskIds] = useState<string[]>([]);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [showSubtaskModal, setShowSubtaskModal] = useState(false);

  // State to track status changes
  const [taskStatusChanges, setTaskStatusChanges] = useState<Record<string, TaskStatus>>({});
  const [subtaskStatusChanges, setSubtaskStatusChanges] = useState<Record<string, TaskStatus>>({});

  // State for success messages, loading, and server status
  const [statusUpdateSuccess, setStatusUpdateSuccess] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  const [isServerRunning, setIsServerRunning] = useState<boolean>(true);



  // Calculate task status based on subtasks
  const calculateTaskStatus = (task: any) => {
    // IMPORTANT: Never override manually set COMPLETED or ON_HOLD statuses
    // These should be preserved regardless of subtask changes
    if (task.status === TaskStatus.COMPLETED || task.status === TaskStatus.ON_HOLD) {
      return task.status; // Preserve manually set status
    }

    if (!task.subtasks || task.subtasks.length === 0) {
      return task.status; // No subtasks, keep original status
    }

    const subtasks = task.subtasks;
    const statusCounts = {
      COMPLETED: 0,
      IN_PROGRESS: 0,
      NOT_STARTED: 0,
      DELAYED: 0,
      ON_HOLD: 0
    };

    subtasks.forEach((subtask: any) => {
      statusCounts[subtask.status as keyof typeof statusCounts]++;
    });

    const total = subtasks.length;

    // Business logic for task status based on subtasks:
    // 1. If ANY subtask is DELAYED -> Task is DELAYED (highest priority)
    // 2. If ALL subtasks are COMPLETED -> Task is COMPLETED
    // 3. If ANY subtask is IN_PROGRESS -> Task is IN_PROGRESS
    // 4. If ANY subtask is ON_HOLD and none are IN_PROGRESS/DELAYED -> Task is ON_HOLD
    // 5. If ALL subtasks are NOT_STARTED -> Task is NOT_STARTED

    if (statusCounts.DELAYED > 0) {
      return 'DELAYED';
    } else if (statusCounts.COMPLETED === total) {
      return 'COMPLETED';
    } else if (statusCounts.IN_PROGRESS > 0) {
      return 'IN_PROGRESS';
    } else if (statusCounts.ON_HOLD > 0) {
      return 'ON_HOLD';
    } else {
      return 'NOT_STARTED';
    }
  };

  // Sync local tasks with userTasks from store and calculate correct status
  useEffect(() => {
    const tasksWithCalculatedStatus = userTasks.map(task => ({
      ...task,
      status: calculateTaskStatus(task)
    }));
    setLocalTasks(tasksWithCalculatedStatus);
  }, [userTasks]);

  // Auto-expand tasks that have subtasks
  useEffect(() => {
    if (localTasks.length > 0) {
      const tasksWithSubtasks = localTasks
        .filter(task => task.subtasks && task.subtasks.length > 0)
        .map(task => task.id);
      setExpandedTaskIds(tasksWithSubtasks);
    }
  }, [localTasks]);

  // Check server status when component mounts
  useEffect(() => {
    const checkServer = async () => {
      const serverRunning = await checkServerConnection();
      setIsServerRunning(serverRunning);
    };

    checkServer();
  }, []);

  // Toggle task expansion
  const toggleTaskExpand = (taskId: string) => {
    setExpandedTaskIds(prevIds =>
      prevIds.includes(taskId)
        ? prevIds.filter(id => id !== taskId)
        : [...prevIds, taskId]
    );
  };

  // Navigate to task detail page
  const handleViewTask = (taskId: string) => {
    navigate(`/tasks/${taskId}`);
  };

  // Get status badge styles
  const getStatusBadge = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.NOT_STARTED:
        return <span className="badge bg-gray-200 text-gray-800">Not Started</span>;
      case TaskStatus.IN_PROGRESS:
        return <span className="badge bg-accent/20 text-accent-dark">In Progress</span>;
      case TaskStatus.COMPLETED:
        return <span className="badge bg-success/20 text-success-dark">Completed</span>;
      case TaskStatus.DELAYED:
        return <span className="badge bg-error/20 text-error-dark">Delayed</span>;
      case TaskStatus.ON_HOLD:
        return <span className="badge bg-warning/20 text-warning-dark">On Hold</span>;
      default:
        return <span className="badge bg-gray-200 text-gray-800">Unknown</span>;
    }
  };

  // Get status icon
  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.NOT_STARTED:
        return <Clock size={16} />;
      case TaskStatus.IN_PROGRESS:
        return <ArrowUpRight size={16} />;
      case TaskStatus.COMPLETED:
        return <CheckSquare size={16} />;
      case TaskStatus.DELAYED:
        return <AlertTriangle size={16} />;
      case TaskStatus.ON_HOLD:
        return <PauseCircle size={16} />;
      default:
        return <Clock size={16} />;
    }
  };

  // Get engineer name by ID
  const getEngineerName = (engineerId: string | undefined) => {
    if (!engineerId) return 'Unknown';
    const engineer = engineers.find(eng => eng.id === engineerId);
    return engineer ? engineer.name : 'Unknown';
  };

  // Function to open subtask creation modal
  const openSubtaskModal = (taskId: string) => {
    setSelectedTaskId(taskId);
    setShowSubtaskModal(true);
  };

  // Check if user can update task status
  const canUpdateTaskStatus = () => {
    if (!currentUser) return false;
    return [UserRole.DIRECTOR, UserRole.GENERAL_MANAGER, UserRole.MANAGER, UserRole.TEAM_LEAD].includes(currentUser.role as UserRole);
  };

  // Check if user can add subtasks
  const canAddSubtasks = () => {
    if (!currentUser) return false;
    return [UserRole.DIRECTOR, UserRole.PROJECT_MANAGER].includes(currentUser.role as UserRole);
  };

  // Check if the backend server is running
  const checkServerConnection = async (): Promise<boolean> => {
    try {
      // Make a simple request to check if the server is responding
      // Use the root endpoint which should always exist
      const response = await fetch('http://localhost:5002', {
        method: 'GET',
        // Add a timeout to avoid waiting too long
        signal: AbortSignal.timeout(3000)
      });

      // If we get any response, consider the server running
      return response.status !== 0;
    } catch (error) {
      console.error('Server connection check failed:', error);
      return false;
    }
  };

  // Handle task status change
  const handleTaskStatusChange = (taskId: string, newStatus: TaskStatus) => {
    setTaskStatusChanges(prev => ({
      ...prev,
      [taskId]: newStatus
    }));
  };

  // Handle subtask status change
  const handleSubtaskStatusChange = (subtaskId: string, newStatus: TaskStatus) => {
    setSubtaskStatusChanges(prev => ({
      ...prev,
      [subtaskId]: newStatus
    }));
  };

  const updateTaskStatus = async (taskId: string) => {
    const newStatus = taskStatusChanges[taskId];
    if (!newStatus) return;

    // Set updating state to show loading indicator
    setIsUpdating(true);

    // Find the task name for the success message
    const task = localTasks.find(t => t.id === taskId);
    const taskName = task?.name || 'Task';

    // Check if the server is running before making the API call
    const serverRunning = await checkServerConnection();
    setIsServerRunning(serverRunning);

    // Always update the UI regardless of server status
    const updateUI = () => {
      // Update the projects atom
      setProjects(prevProjects => {
        const updatedProjects = prevProjects.map(project => {
          // Check if this project contains the task
          const taskIndex = project.tasks?.findIndex(t => t.id === taskId);
          if (taskIndex === undefined || taskIndex === -1) return project;

          // Create a new tasks array with the updated task
          const updatedTasks = [...project.tasks];
          updatedTasks[taskIndex] = {
            ...updatedTasks[taskIndex],
            status: newStatus
          };

          // Return the updated project
          return {
            ...project,
            tasks: updatedTasks
          };
        });

        return updatedProjects;
      });

      // Update local tasks for immediate UI update
      setLocalTasks(prevTasks => {
        return prevTasks.map(t => {
          if (t.id === taskId) {
            return {
              ...t,
              status: newStatus
            };
          }
          return t;
        });
      });

      // Clear the status change for this task
      setTaskStatusChanges(prev => {
        const newChanges = { ...prev };
        delete newChanges[taskId];
        return newChanges;
      });
    };

    try {
      if (!serverRunning) {
        // If server is not running, just update the UI and show a warning
        updateUI();
        setStatusUpdateSuccess(`${taskName} status updated in UI only. Changes will not be saved to the database until the server is available.`);
      } else {
        // If server is running, try to update via API
        try {
          // Call the API to update the task status in the backend
          await tasksAPI.updateTask(taskId, { status: newStatus });

          // Update the UI
          updateUI();

          // Show success message
          setStatusUpdateSuccess(`${taskName} status updated to ${newStatus.replace(/_/g, ' ')}`);
        } catch (apiError: any) {
          console.error('API Error updating task status:', apiError);

          // Still update the UI even if the API call fails
          updateUI();

          // Show error message but indicate UI was updated
          setStatusUpdateSuccess(`Warning: ${taskName} status updated in UI only. Server error: ${apiError.message}`);

          // If the task doesn't exist, show a specific message
          if (apiError.message && apiError.message.includes('Server error: 404')) {
            setStatusUpdateSuccess(`Warning: Task may not exist in the database or server returned 404. UI has been updated.`);
          }
        }
      }
    } catch (error: any) {
      console.error('Error in task status update process:', error);
      setStatusUpdateSuccess(`Error: ${error.message || 'Failed to update task status. Please try again.'}`);
    } finally {
      // Reset loading state
      setIsUpdating(false);

      // Clear success message after 5 seconds (increased from 3 for longer messages)
      setTimeout(() => {
        setStatusUpdateSuccess(null);
      }, 5002);
    }
  };

  // Update subtask status in the store
  const updateSubtaskStatus = async (subtaskId: string) => {
    const newStatus = subtaskStatusChanges[subtaskId];
    if (!newStatus) return;

    // Set updating state to show loading indicator
    setIsUpdating(true);

    // Find the subtask name for the success message
    const subtask = userSubtasks.find(st => st.id === subtaskId);
    const subtaskName = subtask?.name || 'Subtask';

    // Check if the server is running before making the API call
    const serverRunning = await checkServerConnection();
    setIsServerRunning(serverRunning);

    // Find the parent task that contains this subtask
    let parentTaskId: string | null = null;
    let parentTask = null;

    for (const project of projects) {
      if (!project.tasks) continue;

      for (const task of project.tasks) {
        if (!task.subtasks) continue;

        const foundSubtask = task.subtasks.find(st => st.id === subtaskId);
        if (foundSubtask) {
          parentTaskId = task.id;
          parentTask = task;
          break;
        }
      }

      if (parentTaskId) break;
    }

    if (!parentTaskId || !parentTask) {
      setIsUpdating(false);
      setStatusUpdateSuccess('Error: Could not find the parent task for this subtask. The data may be inconsistent.');
      setTimeout(() => setStatusUpdateSuccess(null), 5002);
      return;
    }

    // Always update the UI regardless of server status
    const updateUI = () => {
      // Update the projects atom
      setProjects(prevProjects => {
        const updatedProjects = prevProjects.map(project => {
          // Create a new copy of the project
          const updatedProject = { ...project };

          // Check each task in the project
          if (updatedProject.tasks) {
            updatedProject.tasks = updatedProject.tasks.map(task => {
              // Check if this task contains the subtask
              const subtaskIndex = task.subtasks?.findIndex(st => st.id === subtaskId);
              if (subtaskIndex === undefined || subtaskIndex === -1) return task;

              // Create a new subtasks array with the updated subtask
              const updatedSubtasks = [...(task.subtasks || [])];
              updatedSubtasks[subtaskIndex] = {
                ...updatedSubtasks[subtaskIndex],
                status: newStatus
              };

              // Return the updated task
              return {
                ...task,
                subtasks: updatedSubtasks
              };
            });
          }

          return updatedProject;
        });

        return updatedProjects;
      });

      // Update local tasks for immediate UI update
      setLocalTasks(prevTasks => {
        return prevTasks.map(task => {
          if (task.subtasks) {
            const updatedSubtasks = task.subtasks.map(st => {
              if (st.id === subtaskId) {
                return {
                  ...st,
                  status: newStatus
                };
              }
              return st;
            });

            return {
              ...task,
              subtasks: updatedSubtasks
            };
          }
          return task;
        });
      });

      // Clear the status change for this subtask
      setSubtaskStatusChanges(prev => {
        const newChanges = { ...prev };
        delete newChanges[subtaskId];
        return newChanges;
      });
    };

    try {
      if (!serverRunning) {
        // If server is not running, just update the UI and show a warning
        updateUI();
        setStatusUpdateSuccess(`${subtaskName} status updated in UI only. Changes will not be saved to the database until the server is available.`);
      } else {
        // If server is running, try to update via API
        try {
          // Call the subtask API to update the subtask status directly
          await subtasksAPI.updateSubtask(subtaskId, {
            status: newStatus
          });

          // Update the UI immediately (optimistic update)
          updateUI();

          // Update parent task status locally using our calculation logic
          if (parentTaskId) {
            setLocalTasks(prevTasks => {
              return prevTasks.map(task => {
                if (task.id === parentTaskId) {
                  const updatedTask = { ...task };
                  // Recalculate parent task status based on updated subtasks
                  const calculatedStatus = calculateTaskStatus(updatedTask);
                  return { ...updatedTask, status: calculatedStatus };
                }
                return task;
              });
            });
          }

          // Show success message
          setStatusUpdateSuccess(`${subtaskName} status updated to ${newStatus.replace(/_/g, ' ')}. Parent task status automatically updated.`);
        } catch (apiError: any) {
          console.error('API Error updating subtask status:', apiError);

          // Still update the UI even if the API call fails
          updateUI();

          // Show error message but indicate UI was updated
          setStatusUpdateSuccess(`Warning: ${subtaskName} status updated in UI only. Server error: ${apiError.message}`);

          // If the task doesn't exist, show a specific message
          if (apiError.message && apiError.message.includes('Server error: 404')) {
            setStatusUpdateSuccess(`Warning: Parent task may not exist in the database or server returned 404. UI has been updated.`);
          }
        }
      }
    } catch (error: any) {
      console.error('Error in subtask status update process:', error);
      setStatusUpdateSuccess(`Error: ${error.message || 'Failed to update subtask status. Please try again.'}`);
    } finally {
      // Reset loading state
      setIsUpdating(false);

      // Clear success message after 5 seconds (increased from 3 for longer messages)
      setTimeout(() => {
        setStatusUpdateSuccess(null);
      }, 5002);
    }
  };

  return (
    <div className="space-y-6">
      {!isServerRunning && (
        <div className="bg-error/10 text-error-dark p-4 rounded-md flex items-center justify-between mb-4 fade-in">
          <div className="flex items-center">
            <AlertTriangle size={18} className="mr-2" />
            <span>Backend server is not running or not accessible. Task status updates will not be saved to the database.</span>
          </div>
          <button
            onClick={() => checkServerConnection().then(setIsServerRunning)}
            className="btn btn-sm btn-error ml-2"
          >
            Retry Connection
          </button>
        </div>
      )}

      {statusUpdateSuccess && (
        <div className={`${statusUpdateSuccess.includes('Error') ? 'bg-error/10 text-error-dark' : 'bg-success/10 text-success-dark'} p-4 rounded-md flex items-center justify-between mb-4 fade-in`}>
          <div className="flex items-center">
            {statusUpdateSuccess.includes('Error') ? (
              <AlertTriangle size={18} className="mr-2" />
            ) : (
              <CheckCircle2 size={18} className="mr-2" />
            )}
            <span>{statusUpdateSuccess}</span>
          </div>
          <button
            onClick={() => setStatusUpdateSuccess(null)}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle size={18} />
          </button>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Assigned Tasks</h2>
        <div className="flex items-center space-x-2">
          <select className="form-select py-1 text-sm" defaultValue="all">
            <option value="all">All Tasks</option>
            <option value="not_started">Not Started</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="delayed">Delayed</option>
            <option value="on_hold">On Hold</option>
          </select>
        </div>
      </div>

      {localTasks.length === 0 ? (
        <div className="card p-8 text-center">
          <p className="text-gray-500">No tasks assigned yet</p>
        </div>
      ) : (
        <div className="space-y-4">
          {localTasks.map(task => {
            // Count subtasks by status
            const totalSubtasks = task.subtasks?.length || 0;
            const completedSubtasks = task.subtasks?.filter(
              subtask => subtask.status === TaskStatus.COMPLETED
            )?.length || 0;

            const progressPercentage = totalSubtasks > 0
              ? Math.round((completedSubtasks / totalSubtasks) * 100)
              : 0;

            const isExpanded = expandedTaskIds.includes(task.id);

            return (
              <div key={task.id} className="card overflow-hidden">
                <div
                  className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => toggleTaskExpand(task.id)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h3 className="font-medium text-gray-900">{task.name}</h3>
                        <button className="ml-2 text-gray-400">
                          {isExpanded ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewTask(task.id);
                          }}
                          className="ml-2 text-blue-600 hover:text-blue-800 flex items-center text-sm"
                          title="View task details and comments"
                        >
                          <ExternalLink size={14} className="mr-1" />
                          View Task
                        </button>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        Project: {task.projectName}
                      </p>
                    </div>
                    <div className="text-right">
                      {getStatusBadge(task.status)}
                      <p className="text-xs text-gray-500 mt-1">
                        Due: {task.endDate ? format(new Date(task.endDate), 'MMM d, yyyy') : 'No date set'}
                      </p>
                    </div>
                  </div>

                  <div className="mt-2">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-gray-600">Subtasks Progress</span>
                      <span className="text-sm font-medium">
                        {completedSubtasks}/{totalSubtasks} ({progressPercentage}%)
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="h-2 rounded-full bg-primary"
                        style={{ width: `${progressPercentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>

                {isExpanded && (
                  <div className="border-t border-gray-100 p-4 bg-gray-50">
                    <div className="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Task Details</h4>
                        <div className="text-sm text-gray-600 space-y-1">
                          <p><span className="font-medium">Assignee:</span> {getEngineerName(task.assigneeId)}</p>
                          <p><span className="font-medium">Department:</span> {task.department}</p>
                          <p className="flex items-center">
                            <Calendar size={14} className="mr-1" />
                            <span className="font-medium mr-1">Timeline:</span>
                            {task.startDate ? format(new Date(task.startDate), 'MMM d, yyyy') : 'No start date'} -
                            {task.endDate ? format(new Date(task.endDate), 'MMM d, yyyy') : 'No end date'}
                          </p>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Description</h4>
                        <p className="text-sm text-gray-600">{task.description}</p>
                      </div>
                    </div>

                    {canUpdateTaskStatus() && (
                      <div className="mb-4 p-3 border border-gray-200 rounded bg-white">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Update Status</h4>
                        <div className="flex items-center space-x-2">
                          <select
                            className="form-select py-1 text-sm"
                            value={taskStatusChanges[task.id] || task.status}
                            onChange={(e) => handleTaskStatusChange(task.id, e.target.value as TaskStatus)}
                          >
                            {Object.values(TaskStatus).map(status => (
                              <option key={status} value={status}>
                                {status.replace(/_/g, ' ')}
                              </option>
                            ))}
                          </select>
                          <button
                            className="btn btn-primary py-1 px-3 text-sm"
                            onClick={() => updateTaskStatus(task.id)}
                            disabled={!taskStatusChanges[task.id] || isUpdating}
                          >
                            {isUpdating ? 'Updating...' : 'Update'}
                          </button>
                        </div>
                      </div>
                    )}

                    <div className="mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="text-sm font-medium text-gray-700">Subtasks</h4>
                        {canAddSubtasks() && (
                          <button
                            onClick={() => openSubtaskModal(task.id)}
                            className="btn btn-outline py-1 px-2 text-xs flex items-center"
                          >
                            <Plus size={14} className="mr-1" />
                            Add Subtask
                          </button>
                        )}
                      </div>

                      {!task.subtasks || task.subtasks.length === 0 ? (
                        <p className="text-sm text-gray-500 italic">No subtasks available</p>
                      ) : (
                        <div className="space-y-2">
                          {task.subtasks.map(subtask => (
                            <div key={subtask.id} className="border border-gray-200 rounded p-3 bg-white">
                              <div className="flex justify-between items-start">
                                <div>
                                  <h5 className="text-sm font-medium text-gray-800">{subtask.name}</h5>
                                  <p className="text-xs text-gray-500">
                                    Assignee: {getEngineerName(subtask.assigneeId)} ({subtask.assigneeType.replace(/_/g, ' ')})
                                  </p>
                                </div>
                                <div className="flex items-center">
                                  {getStatusBadge(subtask.status)}
                                </div>
                              </div>

                              <p className="text-xs text-gray-600 mt-2">{subtask.description}</p>

                              <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
                                <span>
                                  {subtask.startDate ? format(new Date(subtask.startDate), 'MMM d, yyyy') : 'No start date'} -
                                  {subtask.endDate ? format(new Date(subtask.endDate), 'MMM d, yyyy') : 'No end date'}
                                </span>
                                <span>
                                  Total Time: ~{subtask.totalTime || 0} hours
                                </span>
                              </div>

                              {canUpdateTaskStatus() && (
                                <div className="mt-2 pt-2 border-t border-gray-100 flex justify-end">
                                  <div className="flex items-center space-x-2">
                                    <select
                                      className="form-select py-1 text-xs w-auto"
                                      value={subtaskStatusChanges[subtask.id] || subtask.status}
                                      onChange={(e) => handleSubtaskStatusChange(subtask.id, e.target.value as TaskStatus)}
                                    >
                                      {Object.values(TaskStatus).map(status => (
                                        <option key={status} value={status}>
                                          {status.replace(/_/g, ' ')}
                                        </option>
                                      ))}
                                    </select>
                                    <button
                                      className="btn btn-primary py-1 px-2 text-xs"
                                      onClick={() => updateSubtaskStatus(subtask.id)}
                                      disabled={!subtaskStatusChanges[subtask.id] || isUpdating}
                                    >
                                      {isUpdating ? (
                                        <div className="flex items-center">
                                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                                          Updating...
                                        </div>
                                      ) : 'Update'}
                                    </button>
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {canUpdateTaskStatus() && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Add Remarks</h4>
                        <textarea
                          className="form-input text-sm"
                          rows={2}
                          placeholder="Add your remarks or notes about this task..."
                        />
                        <div className="mt-2 flex justify-end">
                          <button className="btn btn-outline py-1 px-3 text-sm">
                            Save Remarks
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}

      {/* Subtask Creation Modal */}
      {showSubtaskModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Create New Subtask</h3>
              <button
                onClick={() => setShowSubtaskModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <XCircle size={20} />
              </button>
            </div>

            <form className="space-y-4">
              <div>
                <label className="form-label">Subtask Name</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="Enter subtask name"
                />
              </div>

              <div>
                <label className="form-label">Description</label>
                <textarea
                  className="form-input"
                  rows={3}
                  placeholder="Enter subtask description"
                />
              </div>

              <div>
                <label className="form-label">Assignee Type</label>
                <select className="form-select">
                  <option value={TaskAssigneeType.ENGINEER}>Engineer</option>
                  <option value={TaskAssigneeType.OUTSIDE_VENDOR}>Outside Vendor</option>
                  <option value={TaskAssigneeType.CUSTOMER}>Customer</option>
                </select>
              </div>

              <div>
                <label className="form-label">Assignee</label>
                <select className="form-select">
                  <option value="">Select Assignee</option>
                  {engineers.map(engineer => (
                    <option key={engineer.id} value={engineer.id}>
                      {engineer.name} ({engineer.department})
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="form-label">Start Date</label>
                  <input
                    type="date"
                    className="form-input"
                  />
                </div>

                <div>
                  <label className="form-label">End Date</label>
                  <input
                    type="date"
                    className="form-input"
                  />
                </div>
              </div>

              <div>
                <label className="form-label">Status</label>
                <select className="form-select" defaultValue={TaskStatus.NOT_STARTED}>
                  {Object.values(TaskStatus).map(status => (
                    <option key={status} value={status}>
                      {status.replace(/_/g, ' ')}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex justify-end space-x-2 pt-2">
                <button
                  type="button"
                  className="btn btn-outline"
                  onClick={() => setShowSubtaskModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-primary flex items-center"
                >
                  <CheckCircle2 size={16} className="mr-1" />
                  Create Subtask
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskList;