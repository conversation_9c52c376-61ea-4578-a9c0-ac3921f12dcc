import React, { useState, useRef, useEffect, KeyboardEvent } from 'react';
import { Plus, Trash2, Check, X, Building2 } from 'lucide-react';
import { Department } from '../../types';
import { departmentsAPI } from '../../services/api';
import { validateName, validateCode } from '../../utils/validation';

interface DepartmentGridProps {
  departments: Department[];
  onDepartmentsChange: (departments: Department[]) => void;
  canManage: boolean;
  onError: (error: string | null) => void;
  onSuccess: (message: string | null) => void;
  showNewRow?: boolean;
  onAddDepartment?: () => void;
  onHideNewRow?: () => void;
}

const DepartmentGrid: React.FC<DepartmentGridProps> = ({
  departments,
  onDepartmentsChange,
  canManage,
  onError,
  onSuccess,
  showNewRow = false,
  onAddDepartment,
  onHideNewRow
}) => {
  const [editingCell, setEditingCell] = useState<{ id: string; field: string } | null>(null);
  const [editValue, setEditValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});
  
  // New department row state
  const [newDepartmentRow, setNewDepartmentRow] = useState({
    name: '',
    code: '',
    description: ''
  });

  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (editingCell && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [editingCell]);

  const startEdit = (department: Department, field: string) => {
    if (!canManage) return;
    
    setEditingCell({ id: department.id, field });
    setEditValue(department[field as keyof Department] as string || '');
    setValidationErrors({});
  };

  const cancelEdit = () => {
    setEditingCell(null);
    setEditValue('');
    setValidationErrors({});
  };

  const validateField = (field: string, value: string): string | null => {
    switch (field) {
      case 'name':
        const nameResult = validateName(value);
        return nameResult.isValid ? null : nameResult.message || 'Invalid name';
      case 'code':
        const codeResult = validateCode(value);
        return codeResult.isValid ? null : codeResult.message || 'Invalid code';
      default:
        return null;
    }
  };

  const saveEdit = async () => {
    if (!editingCell) return;

    const { id, field } = editingCell;
    const trimmedValue = editValue.trim();

    // Validate the field
    const validationError = validateField(field, trimmedValue);
    if (validationError) {
      setValidationErrors({ [field]: validationError });
      return;
    }

    // Check for duplicate codes
    if (field === 'code') {
      const existingDept = departments.find(d => d.id !== id && d.code.toLowerCase() === trimmedValue.toLowerCase());
      if (existingDept) {
        setValidationErrors({ [field]: 'Department code already exists' });
        return;
      }
    }

    // Check for duplicate names
    if (field === 'name') {
      const existingDept = departments.find(d => d.id !== id && d.name.toLowerCase() === trimmedValue.toLowerCase());
      if (existingDept) {
        setValidationErrors({ [field]: 'Department name already exists' });
        return;
      }
    }

    setIsSubmitting(true);
    try {
      const department = departments.find(d => d.id === id);
      if (!department) return;

      const updatedDepartment = { ...department, [field]: trimmedValue };

      console.log('Updating department:', updatedDepartment);
      const response = await departmentsAPI.updateDepartment(id, updatedDepartment);
      console.log('Update response:', response);

      // The API service returns the response directly with data property
      onDepartmentsChange(departments.map(d => d.id === id ? response.data : d));
      onSuccess(`Department ${field} updated successfully`);
      setEditingCell(null);
      setEditValue('');
      setValidationErrors({});
    } catch (error: any) {
      console.error('Error updating department:', error);
      onError(error.response?.data?.message || error.message || 'Failed to update department');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      saveEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      cancelEdit();
    }
  };

  const deleteDepartment = async (id: string) => {
    if (!confirm('Are you sure you want to delete this department?')) {
      return;
    }

    setDeletingId(id);
    try {
      await departmentsAPI.deleteDepartment(id);
      onDepartmentsChange(departments.filter(d => d.id !== id));
      onSuccess('Department deleted successfully');
    } catch (error: any) {
      console.error('Error deleting department:', error);
      onError(error.response?.data?.message || error.message || 'Failed to delete department');
    } finally {
      setDeletingId(null);
    }
  };

  const renderEditableCell = (department: Department, field: string, value: string) => {
    const isEditing = editingCell?.id === department.id && editingCell?.field === field;
    const hasError = validationErrors[field];

    if (isEditing) {
      return (
        <div className="relative flex items-center space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyPress}
            disabled={isSubmitting}
            className={`flex-1 px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              hasError ? 'border-red-500 bg-red-50' : 'border-gray-300'
            }`}
            placeholder={`Enter ${field}`}
          />
          <div className="flex items-center space-x-1">
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                saveEdit();
              }}
              disabled={isSubmitting}
              className="bg-green-100 hover:bg-green-200 text-green-700 p-1 rounded hover:shadow-lg transform hover:scale-110 transition-all duration-200"
              title="Save Changes"
            >
              {isSubmitting ? (
                <svg className="animate-spin h-3 w-3 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <Check size={12} />
              )}
            </button>
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                cancelEdit();
              }}
              disabled={isSubmitting}
              className="bg-red-100 hover:bg-red-200 text-red-700 p-1 rounded hover:shadow-lg transform hover:scale-110 transition-all duration-200"
              title="Cancel Changes"
            >
              <X size={12} />
            </button>
          </div>
          {hasError && (
            <div className="absolute top-full left-0 mt-1 text-xs text-red-600 bg-red-50 px-2 py-1 rounded shadow-lg z-10 whitespace-nowrap">
              {hasError}
            </div>
          )}
        </div>
      );
    }

    return (
      <div
        className={`px-2 py-1 text-sm cursor-pointer hover:bg-blue-50 rounded transition-colors ${
          canManage ? 'hover:bg-blue-50' : ''
        }`}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          startEdit(department, field);
        }}
        title={canManage ? `Click to edit ${field}` : 'Read-only'}
      >
        {value || '—'}
      </div>
    );
  };

  // Handle new department row changes
  const handleNewRowChange = (field: string, value: string) => {
    setNewDepartmentRow(prev => ({ ...prev, [field]: value }));
    // Clear validation errors when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleNewRowKeyPress = (e: KeyboardEvent<HTMLInputElement>, field: string) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      saveNewDepartment();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      clearNewDepartmentRow();
    }
  };

  const saveNewDepartment = async () => {
    const { name, code, description } = newDepartmentRow;
    
    // Validate required fields
    const errors: { [key: string]: string } = {};

    const nameResult = validateName(name);
    if (!nameResult.isValid) errors.name = nameResult.message || 'Invalid name';

    const codeResult = validateCode(code);
    if (!codeResult.isValid) errors.code = codeResult.message || 'Invalid code';

    // Check for duplicates
    const existingName = departments.find(d => d.name.toLowerCase() === name.toLowerCase());
    if (existingName) errors.name = 'Department name already exists';

    const existingCode = departments.find(d => d.code.toLowerCase() === code.toLowerCase());
    if (existingCode) errors.code = 'Department code already exists';

    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return;
    }

    setIsSubmitting(true);
    try {
      const newDepartment = {
        name: name.trim(),
        code: code.trim(),
        description: description.trim() || undefined
      };

      console.log('Creating department:', newDepartment);
      const response = await departmentsAPI.createDepartment(newDepartment);
      console.log('API response:', response);

      // The API service returns the response directly with data property
      onDepartmentsChange([...departments, response.data]);
      onSuccess('Department created successfully');
      clearNewDepartmentRow();
    } catch (error: any) {
      console.error('Error creating department:', error);
      onError(error.response?.data?.message || error.message || 'Failed to create department');
    } finally {
      setIsSubmitting(false);
    }
  };

  const clearNewDepartmentRow = () => {
    setNewDepartmentRow({ name: '', code: '', description: '' });
    setValidationErrors({});
    if (onHideNewRow) onHideNewRow();
  };

  const renderNewRowCell = (field: string) => {
    const value = newDepartmentRow[field as keyof typeof newDepartmentRow];
    const hasError = validationErrors[field];

    return (
      <div className="relative">
        <input
          type="text"
          value={value}
          onChange={(e) => handleNewRowChange(field, e.target.value)}
          onKeyDown={(e) => handleNewRowKeyPress(e, field)}
          disabled={isSubmitting}
          className={`w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            hasError ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'
          }`}
          placeholder={`Enter ${field}`}
        />
        {hasError && (
          <div className="absolute top-full left-0 mt-1 text-xs text-red-600 bg-red-50 px-2 py-1 rounded shadow-lg z-10 whitespace-nowrap">
            {hasError}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-2xl shadow-xl border border-white/20 backdrop-blur-sm relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-indigo-600/5"></div>
      <div className="relative z-10 overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gradient-to-r from-gray-50 to-blue-50">
            <tr>
              <th className="pl-6 pr-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Department Name</th>
              <th className="px-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Code</th>
              <th className="px-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Description</th>
              {canManage && (
                <th className="pr-6 pl-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {departments.map((department, index) => (
              <tr key={department.id} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 group`}>
                <td className="pl-6 pr-2 py-2">
                  {renderEditableCell(department, 'name', department.name)}
                </td>
                <td className="px-2 py-2">
                  {renderEditableCell(department, 'code', department.code)}
                </td>
                <td className="px-2 py-2">
                  {renderEditableCell(department, 'description', department.description || '')}
                </td>
                {canManage && (
                  <td className="pr-6 pl-2 py-4">
                    <button
                      type="button"
                      className="bg-red-100 hover:bg-red-200 text-red-700 p-2 rounded-lg hover:shadow-lg transform hover:scale-110 transition-all duration-200"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        deleteDepartment(department.id);
                      }}
                      disabled={deletingId === department.id}
                      title="Delete Department"
                    >
                      {deletingId === department.id ? (
                        <svg className="animate-spin h-4 w-4 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      ) : (
                        <Trash2 size={14} />
                      )}
                    </button>
                  </td>
                )}
              </tr>
            ))}

            {/* New department row - only show when showNewRow is true */}
            {canManage && showNewRow && (
              <tr className={`border-t-2 border-blue-200 ${departments.length % 2 === 0 ? 'bg-blue-50/30' : 'bg-blue-100/30'} hover:bg-blue-50/50 transition-all duration-300`}>
                <td className="pl-6 pr-2 py-2">
                  {renderNewRowCell('name')}
                </td>
                <td className="px-2 py-2">
                  {renderNewRowCell('code')}
                </td>
                <td className="px-2 py-2">
                  {renderNewRowCell('description')}
                </td>
                <td className="pr-6 pl-2 py-4">
                  <div className="flex items-center space-x-2">
                    {(newDepartmentRow.name.trim() && newDepartmentRow.code.trim()) ? (
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          saveNewDepartment();
                        }}
                        disabled={isSubmitting}
                        className="bg-green-100 hover:bg-green-200 text-green-700 p-2 rounded-lg hover:shadow-lg transform hover:scale-110 transition-all duration-200"
                        title="Save New Department"
                      >
                        {isSubmitting ? (
                          <svg className="animate-spin h-4 w-4 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        ) : (
                          <Check size={14} />
                        )}
                      </button>
                    ) : (
                      <span className="text-gray-400 text-sm">Fill name & code</span>
                    )}
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        clearNewDepartmentRow();
                      }}
                      disabled={isSubmitting}
                      className="bg-red-100 hover:bg-red-200 text-red-700 p-2 rounded-lg hover:shadow-lg transform hover:scale-110 transition-all duration-200"
                      title="Cancel/Clear"
                    >
                      <X size={14} />
                    </button>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DepartmentGrid;
