import { api } from './api';

export interface Comment {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

export interface TaskComment extends Comment {
  taskId: string;
  userId: string;
}

export interface SubtaskComment extends Comment {
  subtaskId: string;
  userId: string;
}

export interface CreateCommentRequest {
  content: string;
}

export interface UpdateCommentRequest {
  content: string;
}

// Task Comments API
export const taskCommentsAPI = {
  // Get all comments for a task
  getTaskComments: async (taskId: string): Promise<TaskComment[]> => {
    const response = await api.get(`/tasks/${taskId}/comments`);
    return response.data.data;
  },

  // Create a new comment for a task
  createTaskComment: async (taskId: string, data: CreateCommentRequest): Promise<TaskComment> => {
    const response = await api.post(`/tasks/${taskId}/comments`, data);
    return response.data.data;
  },

  // Update a task comment
  updateTaskComment: async (taskId: string, commentId: string, data: UpdateCommentRequest): Promise<TaskComment> => {
    const response = await api.put(`/tasks/${taskId}/comments/${commentId}`, data);
    return response.data.data;
  },

  // Delete a task comment
  deleteTaskComment: async (taskId: string, commentId: string): Promise<void> => {
    await api.delete(`/tasks/${taskId}/comments/${commentId}`);
  },
};

// Subtask Comments API
export const subtaskCommentsAPI = {
  // Get all comments for a subtask
  getSubtaskComments: async (subtaskId: string): Promise<SubtaskComment[]> => {
    const response = await api.get(`/subtasks/${subtaskId}/comments`);
    return response.data.data;
  },

  // Create a new comment for a subtask
  createSubtaskComment: async (subtaskId: string, data: CreateCommentRequest): Promise<SubtaskComment> => {
    const response = await api.post(`/subtasks/${subtaskId}/comments`, data);
    return response.data.data;
  },

  // Update a subtask comment
  updateSubtaskComment: async (subtaskId: string, commentId: string, data: UpdateCommentRequest): Promise<SubtaskComment> => {
    const response = await api.put(`/subtasks/${subtaskId}/comments/${commentId}`, data);
    return response.data.data;
  },

  // Delete a subtask comment
  deleteSubtaskComment: async (subtaskId: string, commentId: string): Promise<void> => {
    await api.delete(`/subtasks/${subtaskId}/comments/${commentId}`);
  },
};
