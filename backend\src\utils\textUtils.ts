/**
 * Text utility functions for formatting and processing text
 */

/**
 * Converts text to camelCase
 * @param text - The text to convert
 * @returns The camelCase version of the text
 */
export const toCamelCase = (text: string): string => {
  if (!text) return '';
  
  return text
    .trim()
    .toLowerCase()
    .replace(/[^a-zA-Z0-9\s]/g, ' ') // Replace special characters with spaces
    .split(/\s+/) // Split by whitespace
    .filter(word => word.length > 0) // Remove empty strings
    .map((word, index) => {
      if (index === 0) {
        return word.toLowerCase();
      }
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join('');
};

/**
 * Converts text to PascalCase
 * @param text - The text to convert
 * @returns The PascalCase version of the text
 */
export const toPascalCase = (text: string): string => {
  if (!text) return '';
  
  return text
    .trim()
    .toLowerCase()
    .replace(/[^a-zA-Z0-9\s]/g, ' ') // Replace special characters with spaces
    .split(/\s+/) // Split by whitespace
    .filter(word => word.length > 0) // Remove empty strings
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('');
};

/**
 * Formats text for display (preserves original formatting but cleans up)
 * @param text - The text to format
 * @returns The formatted text
 */
export const formatDisplayText = (text: string): string => {
  if (!text) return '';
  
  return text
    .trim()
    .replace(/\s+/g, ' '); // Replace multiple spaces with single space
};

/**
 * Validates if text is suitable for camelCase conversion
 * @param text - The text to validate
 * @returns True if text can be converted to camelCase
 */
export const isValidForCamelCase = (text: string): boolean => {
  if (!text || text.trim().length === 0) return false;
  
  // Check if text contains only letters, numbers, spaces, and common punctuation
  const validPattern = /^[a-zA-Z0-9\s\-_.,()[\]&@#$%+:\/]+$/;
  return validPattern.test(text.trim());
};
