import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useAtom } from 'jotai';
import { alertsAtom, currentUserAtom } from '../store';
import { Alert, AlertPriority, AlertStatus } from '../types';
import {
  Bell,
  AlertTriangle,
  AlertCircle,
  Clock,
  Calendar,
  RefreshCw,
  User,
  ChevronRight,
  ChevronDown,
  MoreVertical,
  CheckCircle,
  XCircle,
  Info
} from 'lucide-react';
import { formatDateTimeAMPM, formatDate } from '../utils/dateFormatter';
import { useDataService } from '../services/dataService';
import { markAllUnreadAlertsAsRead, filterAlertsByUser, sortAlerts } from '../utils/alertUtils';
import { alertsAPI } from '../services/api';
import { dataService } from '../services/dataServiceSingleton';

// Debounce utility function for alerts
const useDebounce = (callback: Function, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback((...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

const AlertsPage: React.FC = () => {
  const [alerts, setAlerts] = useAtom(alertsAtom);
  const [currentUser] = useAtom(currentUserAtom);

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [expandedAlert, setExpandedAlert] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState({
    today: true,
    yesterday: true,
    other: true
  });

  // Track if component is mounted to prevent state updates after unmount
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Track if auto-marking has been done to prevent multiple executions
  const [autoMarkingDone, setAutoMarkingDone] = useState(false);

  // Debounced auto-marking function to reduce API calls
  const debouncedAutoMarkAsRead = useDebounce(async (alertsToMark: Alert[]) => {
    if (isMountedRef.current && alertsToMark.length > 0) {
      try {
        console.log(`🔄 Auto-marking ${alertsToMark.length} unread alerts as read (debounced)`);
        await markAllUnreadAlertsAsRead(alertsToMark, setAlerts);
        setAutoMarkingDone(true);
        console.log('✅ Auto-marking completed successfully');
      } catch (error) {
        console.error('❌ Error automatically marking alerts as read:', error);
      }
    }
  }, 2000); // 2 second debounce for auto-marking

  // Automatically mark all unread alerts as read when alerts are loaded (optimized)
  useEffect(() => {
    if (alerts.length > 0 && !autoMarkingDone) {
      const unreadAlerts = alerts.filter(alert => !alert.read);

      if (unreadAlerts.length > 0) {
        // Use debounced function to prevent multiple rapid calls
        debouncedAutoMarkAsRead(alerts);
      } else {
        setAutoMarkingDone(true);
      }
    }
  }, [alerts, autoMarkingDone, debouncedAutoMarkAsRead]);

  // Debounced refresh function to reduce API calls
  const debouncedRefreshAlerts = useDebounce(async () => {
    try {
      console.log('🔄 Refreshing alerts data...');
      await dataService.loadAlerts();
      console.log('✅ Alerts data refreshed successfully');
    } catch (error) {
      console.error('❌ Error refreshing alerts:', error);
    }
  }, 1000); // 1 second debounce

  // Optimized useEffect: Only fetch alerts when AlertsPage is accessed
  useEffect(() => {
    console.log('🚀 AlertsPage mounted - fetching alerts only when alerts section is accessed');

    // Load alerts immediately when component mounts (user navigated to /alerts)
    loadAlerts();

    // Set up auto-refresh only while on alerts page
    const refreshInterval = setInterval(() => {
      if (!document.hidden) {
        console.log('⏰ Periodic refresh of alerts data (alerts page active)...');
        debouncedRefreshAlerts();
      }
    }, 30000); // Refresh every 30 seconds while on alerts page

    // Add visibility change listener to refresh when page becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('📱 Alerts page became visible, refreshing data...');
        debouncedRefreshAlerts();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup function - runs when component unmounts (user leaves alerts page)
    return () => {
      console.log('🚪 AlertsPage unmounted - stopping alerts refresh');
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(refreshInterval);
    };
  }, []); // Empty dependency array - only run on mount/unmount

  // Optimized loadAlerts function using dataService singleton
  const loadAlerts = async () => {
    console.log('🔄 loadAlerts function called, setting isRefreshing to true');
    setIsRefreshing(true);
    try {
      console.log('🔄 Calling dataService.loadAlerts()...');
      await dataService.loadAlerts();
      console.log('✅ dataService.loadAlerts() completed successfully');
      console.log('📊 Current alerts count:', alerts.length);
    } catch (error) {
      console.error('❌ Error in loadAlerts:', error);
    } finally {
      console.log('🏁 Setting isRefreshing to false');
      setIsRefreshing(false);
    }
  };

  // Filter alerts based on user role and assignment
  const userAlerts = filterAlertsByUser(alerts, currentUser);

  // Group alerts by date categories
  const groupAlertsByDate = (alerts: Alert[]) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const groups = {
      today: [] as Alert[],
      yesterday: [] as Alert[],
      other: [] as Alert[]
    };

    alerts.forEach(alert => {
      const alertDate = new Date(alert.createdAt);
      const isToday = alertDate.toDateString() === today.toDateString();
      const isYesterday = alertDate.toDateString() === yesterday.toDateString();

      if (isToday) {
        groups.today.push(alert);
      } else if (isYesterday) {
        groups.yesterday.push(alert);
      } else {
        groups.other.push(alert);
      }
    });

    // Sort each group
    groups.today = sortAlerts(groups.today);
    groups.yesterday = sortAlerts(groups.yesterday);
    groups.other = sortAlerts(groups.other);

    return groups;
  };

  const groupedAlerts = groupAlertsByDate(userAlerts);

  // Get priority icon and color
  const getPriorityInfo = (priority: AlertPriority) => {
    switch (priority) {
      case AlertPriority.HIGH:
        return { icon: AlertTriangle, color: 'text-red-500', bg: 'bg-red-50', border: 'border-red-200' };
      case AlertPriority.MEDIUM:
        return { icon: AlertCircle, color: 'text-yellow-500', bg: 'bg-yellow-50', border: 'border-yellow-200' };
      case AlertPriority.LOW:
        return { icon: Info, color: 'text-blue-500', bg: 'bg-blue-50', border: 'border-blue-200' };
      default:
        return { icon: Info, color: 'text-gray-500', bg: 'bg-gray-50', border: 'border-gray-200' };
    }
  };

  // Get status badge with enhanced design
  const getStatusBadge = (status: AlertStatus) => {
    switch (status) {
      case AlertStatus.NEW:
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-sm border border-purple-400">
            <div className="w-1 h-1 bg-white rounded-full mr-1.5 animate-pulse"></div>
            New
          </span>
        );
      case AlertStatus.IN_PROGRESS:
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-indigo-500 to-indigo-600 text-white shadow-sm border border-indigo-400">
            <div className="w-1 h-1 bg-white rounded-full mr-1.5"></div>
            In Progress
          </span>
        );
      case AlertStatus.RESOLVED:
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-green-500 to-green-600 text-white shadow-sm border border-green-400">
            <CheckCircle size={10} className="mr-1" />
            Resolved
          </span>
        );
      case AlertStatus.DISMISSED:
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-gray-500 to-gray-600 text-white shadow-sm border border-gray-400">
            <XCircle size={10} className="mr-1" />
            Dismissed
          </span>
        );
      default:
        return null;
    }
  };

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const renderAlertItem = (alert: Alert, index: number) => {
    const priorityInfo = getPriorityInfo(alert.priority as AlertPriority);
    const PriorityIcon = priorityInfo.icon;
    const isExpanded = expandedAlert === alert.id;

    return (
      <div
        key={alert.id}
        className={`group relative transition-all duration-300 hover:bg-gray-50/80 ${
          !alert.read
            ? 'bg-gradient-to-r from-blue-50/60 to-indigo-50/60 border-l-4 border-blue-400'
            : ''
        }`}
        style={{
          animationDelay: `${index * 50}ms`,
          animation: 'slideInUp 0.4s ease-out forwards'
        }}
      >
        <div className="p-4 ml-6">
          <div className="flex items-start justify-between gap-3">
            <div className="flex items-start gap-3 flex-1 min-w-0">
              {/* Priority Icon */}
              <div className={`w-9 h-9 ${priorityInfo.bg} ${priorityInfo.border} rounded-lg flex items-center justify-center shadow-sm flex-shrink-0`}>
                <PriorityIcon size={16} className={priorityInfo.color} />
              </div>

              {/* Alert Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  {!alert.read && (
                    <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full shadow-sm animate-pulse"></div>
                  )}
                  {getStatusBadge(alert.status as AlertStatus)}
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    {alert.priority} Priority
                  </span>
                </div>

                <h3 className="text-sm font-semibold text-gray-900 mb-1 group-hover:text-blue-600 transition-colors duration-200">
                  {alert.title}
                </h3>

                <p className="text-gray-700 text-sm leading-relaxed mb-3 line-clamp-2">
                  {alert.message}
                </p>

                {/* Enhanced Metadata */}
                <div className="flex flex-wrap items-center gap-2 text-xs">
                  <div className="flex items-center gap-1.5 bg-white/80 px-2 py-1 rounded-md shadow-sm border border-gray-200">
                    <Calendar size={12} className="text-blue-500 flex-shrink-0" />
                    <span className="font-medium text-gray-700">
                      Created: {formatDateTimeAMPM(alert.createdAt)}
                    </span>
                  </div>

                  {alert.dueDate && (
                    <div className="flex items-center gap-1.5 bg-amber-50/80 px-2 py-1 rounded-md shadow-sm border border-amber-200">
                      <Clock size={12} className="text-amber-600 flex-shrink-0" />
                      <span className="font-medium text-amber-700">
                        Due: {formatDate(alert.dueDate)}
                      </span>
                    </div>
                  )}

                  {alert.assignedTo && (
                    <div className="flex items-center gap-1.5 bg-green-50/80 px-2 py-1 rounded-md shadow-sm border border-green-200">
                      <User size={12} className="text-green-600 flex-shrink-0" />
                      <span className="font-medium text-green-700">
                        Assigned: {alert.assignedTo}
                      </span>
                    </div>
                  )}
                </div>

                {/* Additional Details (if expanded) */}
                {isExpanded && alert.description && (
                  <div className="mt-3 p-3 bg-gray-50/80 rounded-lg border border-gray-200">
                    <h4 className="font-medium text-gray-800 mb-1 text-sm">Description:</h4>
                    <p className="text-gray-700 text-xs leading-relaxed">{alert.description}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-1 flex-shrink-0">
              {alert.description && (
                <button
                  className="p-1.5 rounded-md hover:bg-gray-100 transition-colors duration-200"
                  onClick={() => setExpandedAlert(isExpanded ? null : alert.id)}
                  title={isExpanded ? "Collapse" : "Expand details"}
                >
                  <ChevronRight
                    size={14}
                    className={`text-gray-400 transition-transform duration-200 ${
                      isExpanded ? 'rotate-90' : ''
                    }`}
                  />
                </button>
              )}
              <button className="p-1.5 rounded-md hover:bg-gray-100 transition-colors duration-200">
                <MoreVertical size={14} className="text-gray-400" />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderDateSection = (
    title: string,
    alerts: Alert[],
    sectionKey: keyof typeof expandedSections,
    icon: React.ReactNode
  ) => {
    const isExpanded = expandedSections[sectionKey];
    const unreadCount = alerts.filter(alert => !alert.read).length;

    if (alerts.length === 0) return null;

    return (
      <div className="mb-4">
        {/* Section Header */}
        <div
          className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200 rounded-lg cursor-pointer hover:from-gray-100 hover:to-gray-150 transition-all duration-200"
          onClick={() => toggleSection(sectionKey)}
        >
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              {isExpanded ? (
                <ChevronDown size={18} className="text-gray-600" />
              ) : (
                <ChevronRight size={18} className="text-gray-600" />
              )}
              {icon}
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-800">{title}</h2>
              <div className="flex items-center gap-3 text-sm">
                <span className="text-gray-600">{alerts.length} alerts</span>
                {unreadCount > 0 && (
                  <span className="bg-red-100 text-red-700 px-2 py-0.5 rounded-full text-xs font-medium">
                    {unreadCount} unread
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Section Content */}
        {isExpanded && (
          <div className="mt-2 bg-white/90 backdrop-blur-sm shadow-lg rounded-lg border border-white/50 overflow-hidden">
            <div className="divide-y divide-gray-100">
              {alerts.map((alert, index) => renderAlertItem(alert, index))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const totalUnreadCount = userAlerts.filter(alert => !alert.read).length;
  const totalCount = userAlerts.length;

  return (
    <>
      <style>
        {`
          @keyframes slideInUp {
            from {
              opacity: 0;
              transform: translateY(30px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
            50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.6); }
          }

          .pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite;
          }
        `}
      </style>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 w-full">
        <div className="w-full px-4">
          {/* Enhanced Header Section */}
          <div className="bg-white/90 backdrop-blur-sm shadow-lg border-b border-white/50 p-4 mb-6">
            <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
              <div className="flex items-start space-x-3">
                <div className="relative">
                  <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-xl shadow-lg flex items-center justify-center border border-red-400">
                    <AlertTriangle size={20} className="text-white drop-shadow-sm" />
                  </div>
                  {totalUnreadCount > 0 && (
                    <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-sm pulse-glow">
                      <span className="text-white text-xs font-medium">{totalUnreadCount}</span>
                    </div>
                  )}
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900 drop-shadow-sm mb-1">
                    Alerts Dashboard
                  </h1>
                  <p className="text-gray-600 text-sm font-medium mb-2">
                    Monitor and manage system alerts and notifications
                  </p>
                  <div className="flex flex-wrap items-center gap-3 text-xs">
                    <div className="flex items-center gap-1.5 bg-blue-50 px-2 py-1 rounded-md border border-blue-200">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                      <span className="font-medium text-blue-700">
                        {totalCount} Total Alerts
                      </span>
                    </div>
                    {totalUnreadCount > 0 && (
                      <div className="flex items-center gap-1.5 bg-red-50 px-2 py-1 rounded-md border border-red-200">
                        <div className="w-1.5 h-1.5 bg-red-500 rounded-full animate-pulse"></div>
                        <span className="font-medium text-red-700">
                          {totalUnreadCount} Unread
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                <button
                  className="group relative px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-medium rounded-lg shadow-lg border border-blue-400/50 backdrop-blur-sm transition-all duration-300 hover:shadow-blue-200"
                  onClick={loadAlerts}
                  disabled={isRefreshing}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-center text-sm">
                    <RefreshCw size={16} className={`mr-2 ${isRefreshing ? 'animate-spin' : ''} drop-shadow-sm`} />
                    {isRefreshing ? 'Refreshing...' : 'Refresh Alerts'}
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* Tree Structure Alerts */}
          <div className="space-y-4">
            {totalCount === 0 ? (
              <div className="bg-white/90 backdrop-blur-sm shadow-lg rounded-xl border border-white/50 p-12 text-center">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-4 shadow-lg">
                  <Bell className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">No alerts found</h3>
                <p className="text-gray-600 text-sm max-w-md mx-auto">
                  You don't have any alerts. Great job staying on top of things!
                </p>
              </div>
            ) : (
              <>
                {renderDateSection(
                  "Today",
                  groupedAlerts.today,
                  'today',
                  <Calendar size={20} className="text-blue-600" />
                )}

                {renderDateSection(
                  "Yesterday",
                  groupedAlerts.yesterday,
                  'yesterday',
                  <Clock size={20} className="text-amber-600" />
                )}

                {renderDateSection(
                  "Other Dates",
                  groupedAlerts.other,
                  'other',
                  <Bell size={20} className="text-gray-600" />
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default AlertsPage;