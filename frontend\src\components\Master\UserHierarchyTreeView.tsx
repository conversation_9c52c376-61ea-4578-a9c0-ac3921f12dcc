import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { currentUserAtom } from '../../store';
import { User, UserRole } from '../../types';
import {
  ChevronDown,
  ChevronRight,
  Users,
  User as UserIcon,
  Mail,
  Code,
  Calendar,
  Award,
  Edit,
  Trash2,
  Lock,
  Building2,
  Crown,
  Shield,
  Star,
  Wrench
} from 'lucide-react';

interface UserHierarchyTreeViewProps {
  users: User[];
  onEditUser: (user: User) => void;
  onResetPassword: (user: User) => void;
  onDeleteUser: (userId: string) => void;
  canEditUser: (user: User) => boolean;
  canResetUserPassword: (user: User) => boolean;
  canDeleteUser: (user: User) => boolean;
}

interface HierarchyNode {
  user: User;
  children: HierarchyNode[];
}

const UserHierarchyTreeView: React.FC<UserHierarchyTreeViewProps> = ({
  users,
  onEditUser,
  onResetPassword,
  onDeleteUser,
  canEditUser,
  canResetUserPassword,
  canDeleteUser
}) => {
  const [currentUser] = useAtom(currentUserAtom);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  // Expand all nodes by default when users change
  useEffect(() => {
    const allNodeIds = new Set<string>();

    // Add all user IDs and department IDs to expanded set
    users.forEach(user => {
      allNodeIds.add(user.id);
      if (user.department) {
        allNodeIds.add(`dept-${user.department}`);
      }
    });

    setExpandedNodes(allNodeIds);
  }, [users]);

  // Build hierarchy structure
  const buildHierarchy = (): HierarchyNode[] => {
    const directors = users.filter(user => user.role === UserRole.DIRECTOR);
    const projectManagers = users.filter(user => user.role === UserRole.PROJECT_MANAGER);
    const teamLeads = users.filter(user => user.role === UserRole.TEAM_LEAD);
    const engineers = users.filter(user => user.role === UserRole.ENGINEER);

    // Group by department
    const departmentGroups = new Map<string, {
      projectManagers: User[];
      teamLeads: User[];
      engineers: User[];
    }>();

    // Initialize department groups
    [...projectManagers, ...teamLeads, ...engineers].forEach(user => {
      if (!departmentGroups.has(user.department)) {
        departmentGroups.set(user.department, {
          projectManagers: [],
          teamLeads: [],
          engineers: []
        });
      }
    });

    // Populate department groups
    projectManagers.forEach(pm => {
      departmentGroups.get(pm.department)?.projectManagers.push(pm);
    });

    teamLeads.forEach(tl => {
      departmentGroups.get(tl.department)?.teamLeads.push(tl);
    });

    engineers.forEach(eng => {
      departmentGroups.get(eng.department)?.engineers.push(eng);
    });

    // Build hierarchy
    const hierarchy: HierarchyNode[] = [];

    // Add directors at the top
    directors.forEach(director => {
      hierarchy.push({
        user: director,
        children: []
      });
    });

    // Add departments with their hierarchies
    Array.from(departmentGroups.entries()).forEach(([department, group]) => {
      // Create department node (virtual)
      const departmentNode: HierarchyNode = {
        user: {
          id: `dept-${department}`,
          name: department,
          email: '',
          role: 'DEPARTMENT' as any,
          department: department,
          code: '',
        },
        children: []
      };

      // Add project managers to department
      group.projectManagers.forEach(pm => {
        const pmNode: HierarchyNode = {
          user: pm,
          children: []
        };

        // Add team leads under project managers
        group.teamLeads.forEach(tl => {
          const tlNode: HierarchyNode = {
            user: tl,
            children: []
          };

          // Add engineers under team leads (same department)
          group.engineers
            .filter(eng => eng.department === tl.department)
            .forEach(eng => {
              tlNode.children.push({
                user: eng,
                children: []
              });
            });

          pmNode.children.push(tlNode);
        });

        // Add engineers without team leads directly under project managers
        const engineersWithoutTL = group.engineers.filter(eng =>
          !group.teamLeads.some(tl => tl.department === eng.department)
        );

        engineersWithoutTL.forEach(eng => {
          pmNode.children.push({
            user: eng,
            children: []
          });
        });

        departmentNode.children.push(pmNode);
      });

      // Add team leads without project managers directly to department
      const teamLeadsWithoutPM = group.teamLeads.filter(tl =>
        !group.projectManagers.some(pm => pm.department === tl.department)
      );

      teamLeadsWithoutPM.forEach(tl => {
        const tlNode: HierarchyNode = {
          user: tl,
          children: []
        };

        // Add engineers under this team lead
        group.engineers
          .filter(eng => eng.department === tl.department)
          .forEach(eng => {
            tlNode.children.push({
              user: eng,
              children: []
            });
          });

        departmentNode.children.push(tlNode);
      });

      // Add engineers without team leads or project managers directly to department
      const orphanEngineers = group.engineers.filter(eng =>
        !group.teamLeads.some(tl => tl.department === eng.department) &&
        !group.projectManagers.some(pm => pm.department === eng.department)
      );

      orphanEngineers.forEach(eng => {
        departmentNode.children.push({
          user: eng,
          children: []
        });
      });

      if (departmentNode.children.length > 0) {
        hierarchy.push(departmentNode);
      }
    });

    return hierarchy;
  };

  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const getRoleIcon = (role: UserRole | string) => {
    switch (role) {
      case UserRole.DIRECTOR:
        return <Crown size={16} className="text-purple-600" />;
      case UserRole.PROJECT_MANAGER:
        return <Shield size={16} className="text-blue-600" />;
      case UserRole.TEAM_LEAD:
        return <Star size={16} className="text-green-600" />;
      case UserRole.ENGINEER:
        return <Wrench size={16} className="text-orange-600" />;
      case 'DEPARTMENT':
        return <Building2 size={16} className="text-gray-600" />;
      default:
        return <UserIcon size={16} className="text-gray-600" />;
    }
  };

  const getRoleColor = (role: UserRole | string) => {
    switch (role) {
      case UserRole.DIRECTOR:
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case UserRole.PROJECT_MANAGER:
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case UserRole.TEAM_LEAD:
        return 'bg-green-100 text-green-800 border-green-200';
      case UserRole.ENGINEER:
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'DEPARTMENT':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const renderNode = (node: HierarchyNode, level: number = 0): React.ReactNode => {
    const isExpanded = expandedNodes.has(node.user.id);
    const hasChildren = node.children.length > 0;
    const isDepartment = node.user.role === 'DEPARTMENT';

    return (
      <div key={node.user.id} className="select-none">
        <div
          className={`flex items-center py-2 px-3 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer ${
            isDepartment ? 'bg-gray-25 border border-gray-200 mb-2' : ''
          }`}
          style={{ marginLeft: `${level * 24}px` }}
          onClick={() => hasChildren && toggleNode(node.user.id)}
        >
          {/* Expand/Collapse Icon */}
          <div className="w-6 h-6 flex items-center justify-center mr-2">
            {hasChildren ? (
              isExpanded ? (
                <ChevronDown size={16} className="text-gray-500" />
              ) : (
                <ChevronRight size={16} className="text-gray-500" />
              )
            ) : (
              <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            )}
          </div>

          {/* Role Icon */}
          <div className="mr-3">
            {getRoleIcon(node.user.role)}
          </div>

          {/* User Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3">
              <span className={`font-medium ${isDepartment ? 'text-gray-700' : 'text-gray-900'}`}>
                {node.user.name}
              </span>

              {!isDepartment && (
                <span className={`px-2 py-1 text-xs font-semibold rounded-full border ${getRoleColor(node.user.role)}`}>
                  {node.user.role.replace(/_/g, ' ')}
                </span>
              )}
            </div>

            {!isDepartment && (
              <div className="flex items-center text-sm text-gray-500 mt-1 space-x-4">
                <div className="flex items-center">
                  <Mail size={12} className="mr-1" />
                  <span>{node.user.email}</span>
                </div>
                {node.user.code && (
                  <div className="flex items-center">
                    <Code size={12} className="mr-1" />
                    <span>{node.user.code}</span>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          {!isDepartment && (
            <div className="flex items-center space-x-2 ml-4">
              {canEditUser(node.user) && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onEditUser(node.user);
                  }}
                  className="text-indigo-600 hover:text-indigo-900 p-1 rounded"
                  title="Edit User"
                >
                  <Edit size={14} />
                </button>
              )}
              {canResetUserPassword(node.user) && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onResetPassword(node.user);
                  }}
                  className="text-amber-600 hover:text-amber-900 p-1 rounded"
                  title="Reset Password"
                >
                  <Lock size={14} />
                </button>
              )}
              {canDeleteUser(node.user) && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteUser(node.user.id);
                  }}
                  className="text-red-600 hover:text-red-900 p-1 rounded"
                  title="Delete User"
                >
                  <Trash2 size={14} />
                </button>
              )}
            </div>
          )}
        </div>

        {/* Children */}
        {hasChildren && isExpanded && (
          <div className="ml-6 border-l border-gray-200 pl-2">
            {node.children.map(child => renderNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const hierarchy = buildHierarchy();

  if (hierarchy.length === 0) {
    return (
      <div className="text-center py-12 text-gray-500">
        <Users size={48} className="mx-auto mb-4 opacity-50" />
        <p>No users found</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center mb-6">
        <Users className="mr-3 text-blue-600" size={24} />
        <h2 className="text-xl font-semibold text-gray-900">Organization Hierarchy</h2>
      </div>

      <div className="space-y-2">
        {hierarchy.map(node => renderNode(node))}
      </div>
    </div>
  );
};

export default UserHierarchyTreeView;
