const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function addAnotherPayment() {
  try {
    // Get the Agri-Tech project
    const project = await prisma.project.findFirst({
      where: {
        name: 'Agri-Tech'
      },
      select: {
        id: true,
        name: true,
        poValue: true
      }
    });

    if (!project) {
      console.log('Agri-Tech project not found');
      return;
    }

    // Get a director user
    const director = await prisma.user.findFirst({
      where: {
        role: 'DIRECTOR'
      },
      select: {
        id: true,
        name: true
      }
    });

    // Create another test payment
    const payment = await prisma.payment.create({
      data: {
        projectId: project.id,
        amount: 25000, // ₹25,000 additional payment
        paymentDate: new Date(),
        paymentMethod: 'UPI',
        referenceNumber: 'UPI-TXN-789',
        description: 'Second installment payment',
        createdBy: director.id
      }
    });

    console.log('Additional payment created:', {
      amount: payment.amount,
      paymentMethod: payment.paymentMethod,
      referenceNumber: payment.referenceNumber
    });

    // Check updated totals
    const allPayments = await prisma.payment.findMany({
      where: {
        projectId: project.id
      }
    });

    const totalPaid = allPayments.reduce((sum, p) => sum + Number(p.amount), 0);
    const pendingAmount = Number(project.poValue) - totalPaid;

    console.log('\nUpdated Project Payment Summary:');
    console.log('PO Value: ₹' + Number(project.poValue).toLocaleString('en-IN'));
    console.log('Total Paid: ₹' + totalPaid.toLocaleString('en-IN'));
    console.log('Pending Amount: ₹' + pendingAmount.toLocaleString('en-IN'));
    console.log('Payment Status:', pendingAmount <= 0 ? 'FULLY_PAID' : totalPaid > 0 ? 'PARTIALLY_PAID' : 'PENDING');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addAnotherPayment();
