import React, { useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import MOMReadOnlyTable from '../components/MOM/MOMReadOnlyTable';
import { Plus, FileText } from 'lucide-react';
import { dataService } from '../services/dataServiceSingleton';

// Debounce utility function for MOM operations
const useDebounce = (callback: Function, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback((...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

const MOMPage: React.FC = () => {
  const navigate = useNavigate();

  // Debounced refresh function to reduce API calls
  const debouncedRefreshMOMs = useDebounce(async () => {
    try {
      console.log('🔄 Refreshing MOMs data...');
      await dataService.loadMOMs();
      console.log('✅ MOMs data refreshed successfully');
    } catch (error) {
      console.error('❌ Error refreshing MOMs:', error);
    }
  }, 1000); // 1 second debounce

  // Handle navigation to create MOM page
  const handleCreateMOM = () => {
    navigate('/mom/create');
  };

  // Auto-refresh mechanism for real-time updates
  useEffect(() => {
    // Add visibility change listener to refresh when page becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('📱 MOM page became visible, refreshing data...');
        debouncedRefreshMOMs();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Refresh every 30 seconds when page is visible
    const refreshInterval = setInterval(() => {
      if (!document.hidden) {
        console.log('⏰ Periodic refresh of MOMs data...');
        debouncedRefreshMOMs();
      }
    }, 30000);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(refreshInterval);
    };
  }, [debouncedRefreshMOMs]);

  return (
    <div className="w-full min-h-screen bg-gray-50 p-6">
      {/* Header with Create Button */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
            <FileText size={24} className="text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Minutes of Meeting</h1>
            <p className="text-gray-600 mt-1">Create and manage meeting minutes with customers and team members</p>
          </div>
        </div>

        {/* Create Button */}
        <button
          onClick={handleCreateMOM}
          className="inline-flex items-center px-6 py-3 rounded-lg text-sm font-medium transition-colors bg-green-600 text-white hover:bg-green-700 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          <Plus size={18} className="mr-2" />
          Create MOM
        </button>
      </div>

      {/* Main Content - Full Width */}
      <MOMReadOnlyTable onMOMCreated={() => {}} />
    </div>
  );
};

export default MOMPage;