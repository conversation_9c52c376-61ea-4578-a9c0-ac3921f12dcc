import express, { Request, Response } from 'express';
import path from 'path';
import fs from 'fs';
import {
  getProjects,
  getProject,
  createProject,
  updateProject,
  deleteProject,
} from '../controllers/project.controller';
import {
  importProjectExcelData,
  exportProjectExcelData
} from '../controllers/excel.controller';
import { protect, authorize } from '../middleware/auth.middleware';
import { prisma } from '../index';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(getProjects)
  .post(
    authorize('DIRECTOR', 'PROJECT_MANAGER', 'TEAM_LEAD'),
    createProject
  );

router.route('/:id')
  .get(getProject)
  .put(
    authorize('DIRECTOR'), // Only DIRECTOR can edit projects
    updateProject
  )
  .delete(
    authorize('DIRECTOR'), // Only DIRECTOR can delete projects
    deleteProject
  );

// Excel import/export routes
router.route('/:id/excel-import')
  .post(
    authorize('DIRECTOR', 'PROJECT_MANAGER'),
    importProjectExcelData
  );

router.route('/:id/excel-export')
  .get(
    authorize('DIRECTOR', 'PROJECT_MANAGER'),
    exportProjectExcelData
  );

// Excel template download route - serves the actual file
router.route('/:id/excel-template')
  .get(
    authorize('DIRECTOR', 'PROJECT_MANAGER', 'TEAM_LEAD'),
    async (req: Request, res: Response): Promise<void> => {
      try {
        const { id } = req.params;
        console.log(`📥 Template download request for project: ${id}`);

        // Get project details for naming
        const project = await prisma.project.findUnique({
          where: { id },
          select: {
            name: true,
            code: true
          }
        });

        if (!project) {
          console.log(`❌ Project not found: ${id}`);
          res.status(404).json({
            success: false,
            message: 'Project not found'
          });
          return;
        }

        console.log(`✅ Project found: ${project.name}`);

        // Path to the template file - try multiple locations
        const possiblePaths = [
          path.join(__dirname, '../../Sample_Template.xlsx'),
          path.join(__dirname, '../../../Sample_Template.xlsx'),
          path.join(process.cwd(), 'Sample_Template.xlsx'),
          path.join(process.cwd(), 'backend/Sample_Template.xlsx')
        ];

        let templatePath = '';
        for (const testPath of possiblePaths) {
          console.log(`🔍 Checking template path: ${testPath}`);
          if (fs.existsSync(testPath)) {
            templatePath = testPath;
            console.log(`✅ Template file found at: ${templatePath}`);
            break;
          }
        }

        // Check if template file exists
        if (!templatePath || !fs.existsSync(templatePath)) {
          console.log(`❌ Template file not found in any of these locations:`);
          possiblePaths.forEach(p => console.log(`   - ${p}`));
          res.status(404).json({
            success: false,
            message: 'Template file not found. Please ensure Sample_Template.xlsx is in the backend directory.'
          });
          return;
        }

        // Create filename with project name
        const sanitizedProjectName = project.name.replace(/[^a-zA-Z0-9\s-_]/g, '').replace(/\s+/g, '_');
        const filename = `${sanitizedProjectName}_Template.xlsx`;
        console.log(`📄 Serving template as: ${filename}`);

        // Set headers for file download
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Cache-Control', 'no-cache');

        // Stream the file
        const fileStream = fs.createReadStream(templatePath);
        fileStream.pipe(res);

        fileStream.on('error', (error) => {
          console.error('Error streaming template file:', error);
          if (!res.headersSent) {
            res.status(500).json({
              success: false,
              message: 'Error downloading template file'
            });
          }
        });

        fileStream.on('end', () => {
          console.log(`✅ Template download completed: ${filename}`);
        });

      } catch (error) {
        console.error('Error downloading template:', error);
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            message: 'Failed to download template'
          });
        }
      }
    }
  );

// Excel template info route - returns metadata
router.route('/excel-template-info')
  .get(
    authorize('DIRECTOR', 'PROJECT_MANAGER', 'TEAM_LEAD'),
    async (req: Request, res: Response): Promise<void> => {
      try {
        // Get current milestone templates
        const milestoneTemplates = await prisma.milestone_template.findMany({
          orderBy: { sequence: 'asc' },
          select: {
            name: true,
            description: true
          }
        });

        // Get available users for reference
        const users = await prisma.user.findMany({
          select: {
            name: true,
            role: true
          }
        });

        res.status(200).json({
          success: true,
          data: {
            milestones: milestoneTemplates,
            users: users,
            templateStructure: {
              requiredFields: ['TaskName', 'MilestoneCategory'],
              optionalFields: ['TaskID', 'ParentTask', 'AssignedTo', 'Status', 'StartDate', 'EndDate', 'Priority'],
              statusOptions: ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'DELAYED', 'ON_HOLD'],
              priorityOptions: ['Low', 'Medium', 'High']
            }
          }
        });
      } catch (error) {
        console.error('Error getting template data:', error);
        res.status(500).json({
          success: false,
          message: 'Failed to get template data'
        });
      }
    }
  );

export default router;
