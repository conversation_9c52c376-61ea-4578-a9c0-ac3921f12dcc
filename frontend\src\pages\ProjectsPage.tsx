import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useAtom } from 'jotai';
import { projectsAtom, customersAtom, currentUserAtom } from '../store';
import { Project, TaskStatus, ProjectCategory } from '../types';
import {
  Plus,
  Search,
  Calendar,
  Clock,
  ArrowUpRight,
  Edit,
  Filter,
  Grid3X3,
  RefreshCw,
  Trash2,
  Layers,
  Clipboard
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { formatDateRange } from '../utils/dateFormatter';
import { dataService } from '../services/dataServiceSingleton';
import { projectsAPI } from '../services/api';
import { useConfirmationContext } from '../contexts/ConfirmationContext';
import ProjectEditModal from '../components/Projects/ProjectEditModal';

// Debounce utility function for project operations
const useDebounce = (callback: Function, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback((...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

const ProjectsPage: React.FC = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useAtom(projectsAtom);
  const [customers] = useAtom(customersAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const { confirmDelete } = useConfirmationContext();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [customerFilter, setCustomerFilter] = useState<string>('ALL');
  const [projectCategoryFilter, setProjectCategoryFilter] = useState<string>('ALL');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Edit project modal state
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);

  // Debounced refresh function to reduce API calls
  const debouncedRefreshProjects = useDebounce(async () => {
    try {
      console.log('🔄 Refreshing projects data...');
      await dataService.loadProjects();
      console.log('✅ Projects data refreshed successfully');
    } catch (error) {
      console.error('❌ Error refreshing projects:', error);
    }
  }, 1000); // 1 second debounce

  // Auto-refresh mechanism for real-time updates
  useEffect(() => {
    // Add visibility change listener to refresh when page becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('📱 Projects page became visible, refreshing data...');
        debouncedRefreshProjects();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Refresh every 30 seconds when page is visible
    const refreshInterval = setInterval(() => {
      if (!document.hidden) {
        console.log('⏰ Periodic refresh of projects data...');
        debouncedRefreshProjects();
      }
    }, 30000);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(refreshInterval);
    };
  }, [debouncedRefreshProjects]);

  // We're no longer loading projects here since they're already loaded by App.tsx
  // This prevents duplicate API calls and reduces the risk of ERR_INSUFFICIENT_RESOURCES errors

  // Filter projects based on user role and assignment
  const userProjects = projects.filter(project => {
    if (currentUser?.role === 'DIRECTOR') {
      return true; // Directors can see all projects
    } else if (currentUser?.role === 'PROJECT_MANAGER') {
      // Project managers can see projects they manage
      return project.projectManagerId === currentUser.id;
    } else if (currentUser?.role === 'TEAM_LEAD') {
      // Team leads can see projects where they are assigned tasks or projects in their department
      return project.department === currentUser.department ||
             project.tasks?.some(task => task.assigneeId === currentUser.id) || false;
    } else if (currentUser?.role === 'ENGINEER') {
      // Engineers only see projects where they are assigned tasks
      return project.tasks?.some(task => task.assigneeId === currentUser.id) || false;
    }
    return false;
  });

  // Apply filters
  const filteredProjects = userProjects.filter(project => {
    // Search filter
    const matchesSearch =
      searchTerm === '' ||
      project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.code.toLowerCase().includes(searchTerm.toLowerCase());

    // Status filter
    const matchesStatus =
      statusFilter === 'ALL' ||
      project.status === statusFilter;

    // Customer filter
    const matchesCustomer =
      customerFilter === 'ALL' ||
      project.customerId === customerFilter;

    // Project category filter
    const matchesProjectCategory =
      projectCategoryFilter === 'ALL' ||
      project.projectCategory === projectCategoryFilter;

    return matchesSearch && matchesStatus && matchesCustomer && matchesProjectCategory;
  });

  // Get unique customers from projects
  const projectCustomers = [...new Set(projects.map(project => project.customerId))];

  // Calculate project stats
  const getProjectProgress = (project: Project): number => {
    const tasks = project.tasks || [];
    if (tasks.length === 0) return 0;

    const completedTasks = tasks.filter(task => task.status === TaskStatus.COMPLETED).length;
    return Math.round((completedTasks / tasks.length) * 100);
  };

  // Calculate days remaining based on project status and dates
  const getDaysRemaining = (startDate: string, endDate: string, status: string): { days: number; type: 'start' | 'end' | 'overdue' } => {
    const today = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);

    // If project hasn't started yet, always show days until start (regardless of current date)
    if (status === 'NOT_STARTED') {
      const diffTime = start.getTime() - today.getTime();
      return {
        days: Math.abs(Math.ceil(diffTime / (1000 * 60 * 60 * 24))),
        type: 'start'
      };
    }

    // If project is active or completed, show days until end (or overdue)
    const diffTime = end.getTime() - today.getTime();
    const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return {
      days: Math.abs(days),
      type: days < 0 ? 'overdue' : 'end'
    };
  };

  // Get status color with enhanced styling
  const getStatusColor = (status: string): string => {
    switch (status) {
      case TaskStatus.COMPLETED:
        return 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white shadow-emerald-200';
      case TaskStatus.IN_PROGRESS:
        return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-blue-200';
      case TaskStatus.DELAYED:
        return 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-red-200';
      case TaskStatus.ON_HOLD:
        return 'bg-gradient-to-r from-amber-500 to-amber-600 text-white shadow-amber-200';
      case TaskStatus.NOT_STARTED:
        return 'bg-gradient-to-r from-slate-500 to-slate-600 text-white shadow-slate-200';
      default:
        return 'bg-gradient-to-r from-gray-500 to-gray-600 text-white shadow-gray-200';
    }
  };

  // Get customer name by ID
  const getCustomerName = (customerId: string): string => {
    const customer = customers.find(c => c.id === customerId);
    return customer ? customer.name : 'Unknown Customer';
  };

  // Get project category display name
  const getProjectCategoryDisplayName = (projectCategory: ProjectCategory): string => {
    switch (projectCategory) {
      case ProjectCategory.PROJECTS:
        return 'Projects';
      case ProjectCategory.PRECISION_PROJECTS:
        return 'Precision Projects';
      case ProjectCategory.SPARE:
        return 'Spare';
      case ProjectCategory.SERVICE:
        return 'Service';
      default:
        return 'Unknown Category';
    }
  };

  // Handle edit project - Only for DIRECTOR
  const handleEditProject = (project: Project) => {
    setEditingProject(project);
    setShowEditModal(true);
  };

  // Handle delete project - Only for DIRECTOR
  const handleDeleteProject = (project: Project) => {
    const customerName = getCustomerName(project.customerId);

    confirmDelete(
      `Project "${project.name}"`,
      async () => {
        try {
          if (!project.id) {
            throw new Error('Project ID is required for deletion');
          }

          await projectsAPI.deleteProject(project.id);

          // Update projects atom by removing the deleted project
          const updatedProjects = projects.filter(p => p.id !== project.id);
          setProjects(updatedProjects);

          console.log('Project deleted successfully:', project.name);
        } catch (error) {
          console.error('Error deleting project:', error);
          throw error; // Let the confirmation context handle the error
        }
      },
      [
        `Project Name: ${project.name}`,
        `Project Code: ${project.code}`,
        `Customer: ${customerName}`,
        `Status: ${project.status.replace(/_/g, ' ')}`,
        `Tasks: ${project.tasks?.length || 0} tasks will be deleted`,
        '',
        '⚠️ This action cannot be undone!'
      ]
    );
  };

  // Handle project save
  const handleProjectSave = (updatedProject: Project) => {
    console.log('Project updated:', updatedProject);
    // The ProjectEditModal already updates the projects atom
  };

  // Handle modal close
  const handleModalClose = () => {
    setShowEditModal(false);
    setEditingProject(null);
  };

  // Handle refresh projects
  const handleRefresh = () => {
    setIsRefreshing(true);
    try {
      debouncedRefreshProjects();
      console.log('Projects refresh initiated');
      // Reset loading state after a short delay since debounced function doesn't return a promise
      setTimeout(() => setIsRefreshing(false), 1500);
    } catch (error) {
      console.error('Error refreshing projects:', error);
      setIsRefreshing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-4">
      <div className="w-full space-y-4">
        {/* Header Section with 3D Card Effect */}
        <div className="bg-white rounded-2xl shadow-xl shadow-blue-100/50 border border-white/20 backdrop-blur-sm px-6 py-4 transform perspective-1000 hover:shadow-2xl hover:shadow-blue-200/60 transition-all duration-500">
          <div className="flex justify-between items-start">
            <div className="space-y-2">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-200/50 transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <Grid3X3 size={24} className="text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                    Projects
                  </h1>
                  <p className="text-slate-600 font-medium">
                    Manage and track all your projects with precision
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* Refresh Button */}
              <button
                className="group bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white px-4 py-3 rounded-xl flex items-center font-semibold shadow-lg shadow-slate-200/50 hover:shadow-xl hover:shadow-slate-300/60 transform hover:-translate-y-1 transition-all duration-300"
                onClick={handleRefresh}
                disabled={isRefreshing}
                title="Refresh projects data"
              >
                <RefreshCw size={18} className={`mr-2 transition-transform duration-300 ${isRefreshing ? 'animate-spin' : 'group-hover:rotate-180'}`} />
                {isRefreshing ? 'Refreshing...' : 'Refresh'}
              </button>

              {/* New Project Button */}
              {(currentUser?.role === 'DIRECTOR' || currentUser?.role === 'PROJECT_MANAGER') && (
                <button
                  className="group bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl flex items-center font-semibold shadow-lg shadow-blue-200/50 hover:shadow-xl hover:shadow-blue-300/60 transform hover:-translate-y-1 transition-all duration-300"
                  onClick={() => navigate('/create-project')}
                >
                  <Plus size={20} className="mr-2 group-hover:rotate-90 transition-transform duration-300" />
                  New Project
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Filters Section with Enhanced 3D Cards */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl shadow-slate-100/50 border border-white/40 px-6 py-4 transform hover:shadow-2xl hover:shadow-slate-200/60 transition-all duration-500">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Bar */}
            <div className="flex-1 relative group">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                <Search size={20} className="text-slate-400 group-focus-within:text-blue-500 transition-colors duration-200" />
              </div>
              <input
                type="text"
                className="w-full pl-12 pr-4 py-3 bg-white/70 border border-slate-200/60 rounded-xl text-slate-700 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-400 shadow-inner backdrop-blur-sm transition-all duration-300 hover:shadow-md"
                placeholder="Search projects by name or code..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative group">
                <Filter size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 z-10" />
                <select
                  className="pl-10 pr-8 py-3 bg-white/70 border border-slate-200/60 rounded-xl text-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-400 shadow-inner backdrop-blur-sm appearance-none cursor-pointer min-w-44 transition-all duration-300 hover:shadow-md"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <option value="ALL">All Statuses</option>
                  {Object.values(TaskStatus).map(status => (
                    <option key={status} value={status}>
                      {status.replace(/_/g, ' ')}
                    </option>
                  ))}
                </select>
              </div>

              <div className="relative group">
                <Layers size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 z-10" />
                <select
                  className="pl-10 pr-8 py-3 bg-white/70 border border-slate-200/60 rounded-xl text-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-400 shadow-inner backdrop-blur-sm appearance-none cursor-pointer min-w-44 transition-all duration-300 hover:shadow-md"
                  value={projectCategoryFilter}
                  onChange={(e) => setProjectCategoryFilter(e.target.value)}
                >
                  <option value="ALL">All Categories</option>
                  {Object.values(ProjectCategory).map(category => (
                    <option key={category} value={category}>
                      {getProjectCategoryDisplayName(category)}
                    </option>
                  ))}
                </select>
              </div>

              <div className="relative">
                <select
                  className="pl-4 pr-8 py-3 bg-white/70 border border-slate-200/60 rounded-xl text-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-400 shadow-inner backdrop-blur-sm appearance-none cursor-pointer min-w-44 transition-all duration-300 hover:shadow-md"
                  value={customerFilter}
                  onChange={(e) => setCustomerFilter(e.target.value)}
                >
                  <option value="ALL">All Customers</option>
                  {projectCustomers.map(customerId => (
                    <option key={customerId} value={customerId}>
                      {getCustomerName(customerId)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Projects Table/Content */}
        {filteredProjects.length === 0 ? (
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl shadow-slate-100/50 border border-white/40 p-12 text-center transform hover:shadow-2xl transition-all duration-500">
            <div className="mx-auto w-32 h-32 bg-gradient-to-br from-slate-100 to-slate-200 rounded-3xl flex items-center justify-center mb-6 shadow-inner transform rotate-3 hover:rotate-0 transition-transform duration-500">
              <Clipboard className="h-16 w-16 text-slate-400" />
            </div>
            <h3 className="text-2xl font-bold text-slate-800 mb-3">No projects found</h3>
            <p className="text-slate-600 max-w-md mx-auto mb-6 text-lg">
              {searchTerm || statusFilter !== 'ALL' || customerFilter !== 'ALL' || projectCategoryFilter !== 'ALL'
                ? "Try adjusting your search or filters to find what you're looking for."
                : "Get started by creating your first project."}
            </p>

            {(currentUser?.role === 'DIRECTOR' || currentUser?.role === 'GENERAL_MANAGER' || currentUser?.role === 'MANAGER') && (
              <button
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-4 rounded-xl flex items-center mx-auto font-semibold shadow-lg shadow-blue-200/50 hover:shadow-xl hover:shadow-blue-300/60 transform hover:-translate-y-1 transition-all duration-300"
                onClick={() => navigate('/create-project')}
              >
                <Plus size={20} className="mr-2" />
                Create Your First Project
              </button>
            )}
          </div>
        ) : (
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl shadow-slate-100/50 border border-white/40 overflow-hidden transform hover:shadow-2xl transition-all duration-500">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200/60">
                    <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">
                      Project
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">
                      Category
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">
                      Timeline
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">
                      Progress
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-bold text-slate-600 uppercase tracking-wider">
                      Tasks
                    </th>
                    <th className="px-6 py-4 text-right text-xs font-bold text-slate-600 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-100">
                  {filteredProjects.map((project, index) => {
                    const progress = getProjectProgress(project);
                    const daysRemainingInfo = getDaysRemaining(project.startDate, project.endDate, project.status);

                    return (
                      <tr
                        key={project.id}
                        className="group hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue-100/50 hover:transform hover:scale-[1.01]"
                        style={{ animationDelay: `${index * 50}ms` }}
                      >
                        <td className="px-6 py-5">
                          <div className="space-y-1">
                            <div className="text-sm font-bold text-slate-800 group-hover:text-blue-700 transition-colors duration-200">
                              {project.name}
                            </div>
                            <div className="text-xs font-medium text-slate-500 bg-slate-100 px-2 py-1 rounded-lg inline-block">
                              {project.code}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-5">
                          <div className="flex items-center">
                            <Layers size={14} className="mr-2 text-indigo-500" />
                            <span className="text-sm font-medium text-slate-700 bg-indigo-50 px-3 py-1 rounded-lg">
                              {getProjectCategoryDisplayName(project.projectCategory)}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-5">
                          <div className="text-sm font-medium text-slate-700">
                            {getCustomerName(project.customerId)}
                          </div>
                        </td>
                        <td className="px-6 py-5 space-y-2">
                          <div className="flex items-center text-xs text-slate-600 bg-slate-50 px-3 py-1 rounded-lg">
                            <Calendar size={14} className="mr-2 text-blue-500" />
                            <span className="font-medium">
                              {formatDateRange(project.startDate, project.endDate)}
                            </span>
                          </div>
                          <div className="flex items-center text-xs">
                            <Clock size={14} className="mr-2 text-amber-500" />
                            <span className={`font-medium px-2 py-1 rounded-lg ${
                              daysRemainingInfo.type === 'overdue'
                                ? 'text-red-600 bg-red-50'
                                : daysRemainingInfo.type === 'start'
                                  ? 'text-blue-600 bg-blue-50'
                                  : daysRemainingInfo.days <= 7
                                    ? 'text-amber-600 bg-amber-50'
                                    : 'text-emerald-600 bg-emerald-50'
                            }`}>
                              {daysRemainingInfo.type === 'overdue'
                                ? `${daysRemainingInfo.days} days overdue`
                                : daysRemainingInfo.type === 'start'
                                ? `${daysRemainingInfo.days} days until start`
                                : `${daysRemainingInfo.days} days left`}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-5">
                          <div className="space-y-2">
                            <div className="w-full bg-slate-200 rounded-full h-3 shadow-inner overflow-hidden">
                              <div
                                className="bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full shadow-sm transition-all duration-500 ease-out"
                                style={{ width: `${progress}%` }}
                              ></div>
                            </div>
                            <div className="text-xs font-bold text-slate-600">{progress}% complete</div>
                          </div>
                        </td>
                        <td className="px-6 py-5">
                          <span className={`px-3 py-2 inline-flex text-xs font-bold rounded-xl shadow-lg ${getStatusColor(project.status)} transform hover:scale-105 transition-transform duration-200`}>
                            {project.status.replace(/_/g, ' ')}
                          </span>
                        </td>
                        <td className="px-6 py-5">
                          <div className="bg-slate-100 px-3 py-2 rounded-lg inline-block">
                            <span className="text-sm font-bold text-slate-700">
                              {project.tasks?.length || 0}
                            </span>
                            <span className="text-xs text-slate-500 ml-1">tasks</span>
                          </div>
                        </td>
                        <td className="px-6 py-5">
                          <div className="flex items-center justify-end space-x-3">
                            <button
                              className="p-2 text-blue-600 hover:text-white hover:bg-blue-600 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-blue-200/50 transform hover:scale-110"
                              onClick={() => navigate(`/projects/${project.id}`)}
                              title="View Project"
                            >
                              <ArrowUpRight size={18} />
                            </button>

                            {currentUser?.role === 'DIRECTOR' && (
                              <>
                                <button
                                  className="p-2 text-slate-600 hover:text-white hover:bg-slate-600 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-slate-200/50 transform hover:scale-110"
                                  onClick={() => handleEditProject(project)}
                                  title="Edit Project"
                                >
                                  <Edit size={16} />
                                </button>

                                <button
                                  className="p-2 text-red-600 hover:text-white hover:bg-red-600 rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-red-200/50 transform hover:scale-110"
                                  onClick={() => handleDeleteProject(project)}
                                  title="Delete Project"
                                >
                                  <Trash2 size={16} />
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Project Edit Modal - Only for DIRECTOR */}
        <ProjectEditModal
          project={editingProject}
          isOpen={showEditModal}
          onClose={handleModalClose}
          onSave={handleProjectSave}
        />
      </div>
    </div>
  );
};

// Enhanced clipboard icon component
const ClipboardIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
  </svg>
);

export default ProjectsPage;