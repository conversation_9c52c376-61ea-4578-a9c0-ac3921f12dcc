import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAtom } from 'jotai';
import { projectsAtom, engineersAtom, currentUserAtom } from '../store';
import { useForm, useFieldArray, SubmitHandler } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';
import { Task, Subtask, TaskStatus, TaskAssigneeType, UserRole } from '../types';
import { Plus, Trash2, CheckCircle, XCircle, ArrowLeft, Lock } from 'lucide-react';
import { tasksAPI } from '../services/api';
import { useDataService } from '../services/dataService';
import { useNotification } from '../contexts/NotificationContext';

type FormValues = {
  tasks: {
    name: string;
    description: string;
    assigneeId: string;
    startDate: string;
    endDate: string;
    status: TaskStatus;
    subtasks: {
      name: string;
      description: string;
      assigneeId: string;
      assigneeType: TaskAssigneeType;
      startDate: string;
      endDate: string;
      status: TaskStatus;
    }[];
  }[];
};

const AddTaskPage: React.FC = () => {
  const { showSuccess, showError } = useNotification();

  const navigate = useNavigate();
  const { projectId } = useParams<{ projectId?: string }>();
  const [projects, setProjects] = useAtom(projectsAtom);
  const [engineers] = useAtom(engineersAtom);
  const [currentUser] = useAtom(currentUserAtom);

  // Data service for refreshing data
  const { refreshData } = useDataService();

  // Filter users for task assignment - only team leads
  const teamLeads = engineers.filter(user => user.role === 'TEAM_LEAD');
  // Filter users for subtask assignment - only engineers
  const engineersOnly = engineers.filter(user => user.role === 'ENGINEER');

  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedProject, setSelectedProject] = useState<string>(projectId || '');

  // Reference to track if the form has been initialized for the current project
  const initializedRef = React.useRef(false);

  // Reset the initialization flag when the selected project changes
  useEffect(() => {
    if (selectedProject) {
      initializedRef.current = false;
    }
  }, [selectedProject]);

  // Get the project details if projectId is provided
  const project = projects.find(p => p.id === selectedProject);

  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors }
  } = useForm<FormValues>({
    defaultValues: {
      tasks: [
        {
          name: '',
          description: '',
          assigneeId: '',
          startDate: project?.startDate || '',
          endDate: project?.endDate || '',
          status: TaskStatus.NOT_STARTED,
          subtasks: []
        }
      ]
    }
  });

  // Create the main tasks field array
  const { fields: taskFields, append: appendTask, remove: removeTask } = useFieldArray({
    control,
    name: "tasks"
  });

  // Create a subtasks field array for each task
  // This avoids creating hooks conditionally or in loops
  const subtasksFieldArrays = taskFields.map((_, index) =>
    useFieldArray({
      control,
      name: `tasks.${index}.subtasks`
    })
  );

  // Update form defaults when project changes
  useEffect(() => {
    // Only reset the form when the project changes and not on every render
    if (project && !initializedRef.current) {
      initializedRef.current = true;
      reset({
        tasks: [
          {
            name: '',
            description: '',
            assigneeId: '',
            startDate: getCurrentDateOrProjectStart(project.startDate),
            endDate: project.endDate,
            status: TaskStatus.NOT_STARTED,
            subtasks: []
          }
        ]
      });
    }
  }, [project?.id, reset]); // Only depend on project.id and reset, not the entire project object

  // This effect is no longer needed since React will automatically re-render
  // when taskFields changes, and subtasksFieldArrays will be recalculated during render

  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    if (!selectedProject || !project) {
      showError('Validation Error', 'Please select a project first');
      return;
    }

    setIsSubmitting(true);
    console.log('Adding tasks to project:', project.name);

    try {
      // Create a copy of the current projects
      const updatedProjects = [...projects];
      const projectIndex = updatedProjects.findIndex(p => p.id === selectedProject);

      if (projectIndex === -1) {
        throw new Error('Project not found');
      }

      // Process tasks and subtasks
      const newTasks: Task[] = [];

      // Process each task
      for (const taskData of data.tasks) {
        const newTask: Task = {
          id: uuidv4(),
          projectId: selectedProject,
          name: taskData.name,
          description: taskData.description,
          assigneeId: taskData.assigneeId,
          assigneeType: TaskAssigneeType.ENGINEER,
          department: project.department,
          startDate: taskData.startDate,
          endDate: taskData.endDate,
          status: taskData.status,
          subtasks: [],
          createdBy: currentUser?.id || '',
          createdAt: new Date().toISOString()
        };

        // Process subtasks
        if (taskData.subtasks && taskData.subtasks.length > 0) {
          taskData.subtasks.forEach((subtaskData) => {
            const newSubtask: Subtask = {
              id: uuidv4(),
              taskId: newTask.id,
              name: subtaskData.name,
              description: subtaskData.description,
              assigneeId: subtaskData.assigneeId,
              assigneeType: subtaskData.assigneeType,
              startDate: subtaskData.startDate,
              endDate: subtaskData.endDate,
              status: subtaskData.status,
              totalTime: calculateTotalHours(subtaskData.startDate, subtaskData.endDate),
              createdBy: currentUser?.id || '',
              createdAt: new Date().toISOString()
            };

            newTask.subtasks.push(newSubtask);
          });
        }

        // Send the task to the backend API using the proper API service
        const response = await tasksAPI.createTask(newTask);
        const savedTask = response.data;

        // Use the saved task with real ID from backend
        newTasks.push(savedTask);
      }

      // Add the new tasks to the project
      updatedProjects[projectIndex] = {
        ...updatedProjects[projectIndex],
        tasks: [...updatedProjects[projectIndex].tasks, ...newTasks]
      };

      // Update the projects state
      setProjects(updatedProjects);

      // IMPORTANT: Refresh all data to ensure consistency across all stores
      await refreshData();
      console.log('✅ All data refreshed after task creation');

      // Log success
      console.log(`Added ${newTasks.length} tasks to project:`, project.name);

      // Show success message
      setSuccess(`${newTasks.length} tasks added to project successfully!`);

      // Reset the form after submission with project dates (or current date if later)
      reset({
        tasks: [
          {
            name: '',
            description: '',
            assigneeId: '',
            startDate: getCurrentDateOrProjectStart(project.startDate),
            endDate: project.endDate,
            status: TaskStatus.NOT_STARTED,
            subtasks: []
          }
        ]
      });

      // Navigate to the project details page after a delay
      setTimeout(() => {
        navigate(`/projects/${selectedProject}`);
      }, 2000);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Error adding tasks to project:', errorMessage);
      console.error('Error details:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to get the current date or project start date, whichever is later
  const getCurrentDateOrProjectStart = (projectStartDate: string): string => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day

    const projectStart = new Date(projectStartDate);

    // Use the later of today or project start date
    const effectiveDate = projectStart > today ? projectStart : today;

    // Format as YYYY-MM-DD for the input element
    return effectiveDate.toISOString().split('T')[0];
  };

  // Helper function to calculate hours between dates
  const calculateTotalHours = (startDate: string, endDate: string): number => {
    if (!startDate || !endDate) return 0;

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Calculate difference in hours
    const diffInMs = end.getTime() - start.getTime();
    const diffInHours = diffInMs / (1000 * 60 * 60);

    // Return an estimated number of working hours (8 hours per day)
    const workingDays = Math.ceil(diffInHours / 24);
    return workingDays * 8;
  };

  // Check if user has permission to create tasks
  const canCreateTasks = currentUser?.role === UserRole.DIRECTOR || currentUser?.role === UserRole.PROJECT_MANAGER;

  // If user doesn't have permission, show access denied
  if (!canCreateTasks) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/projects')}
            className="mr-4 text-gray-500 hover:text-gray-700"
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-1">Add Tasks to Project</h1>
            <p className="text-gray-600">
              Add tasks and subtasks to an existing project
            </p>
          </div>
        </div>

        <div className="card p-8 text-center">
          <div className="flex flex-col items-center space-y-4">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <Lock size={32} className="text-red-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900">Access Restricted</h2>
            <p className="text-gray-600 max-w-md">
              You don't have permission to create tasks. Only Directors and Project Managers can create new tasks.
            </p>
            <button
              onClick={() => navigate('/projects')}
              className="btn btn-primary"
            >
              Back to Projects
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <button
          onClick={() => navigate('/projects')}
          className="mr-4 text-gray-500 hover:text-gray-700"
        >
          <ArrowLeft size={20} />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-1">Add Tasks to Project</h1>
          <p className="text-gray-600">
            Add tasks and subtasks to an existing project
          </p>
        </div>
      </div>

      <div className="card p-6">
        {success && (
          <div className="bg-success/10 text-success-dark p-4 rounded-md flex items-center justify-between mb-6 fade-in">
            <div className="flex items-center">
              <CheckCircle size={18} className="mr-2" />
              <span>{success}</span>
            </div>
            <button
              onClick={() => setSuccess(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              <XCircle size={18} />
            </button>
          </div>
        )}

        <div className="mb-6">
          <label htmlFor="projectSelect" className="form-label">Select Project</label>
          <select
            id="projectSelect"
            className="form-select"
            value={selectedProject}
            onChange={(e) => setSelectedProject(e.target.value)}
            disabled={!!projectId}
          >
            <option value="">Select a Project</option>
            {projects.map((project) => (
              <option key={project.id} value={project.id}>
                {project.name} ({project.code})
              </option>
            ))}
          </select>
        </div>

        {selectedProject && project ? (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-800">Tasks for {project.name}</h3>
                <button
                  type="button"
                  className="btn btn-outline flex items-center"
                  onClick={() => {
                    // Add a new task with default dates from the project (or current date if later)
                    appendTask({
                      name: '',
                      description: '',
                      assigneeId: '',
                      startDate: getCurrentDateOrProjectStart(project.startDate),
                      endDate: project.endDate,
                      status: TaskStatus.NOT_STARTED,
                      subtasks: []
                    });
                  }}
                >
                  <Plus size={18} className="mr-1" />
                  Add Task
                </button>
              </div>

              {taskFields.map((taskField, taskIndex) => {
                // Get the subtasks field array for this task from our pre-created array
                const subtasksFieldArray = subtasksFieldArrays[taskIndex];

                return (
                  <div
                    key={taskField.id}
                    className="border border-gray-200 rounded-md p-4 space-y-4 relative"
                  >
                    <div className="absolute top-4 right-4">
                      {taskFields.length > 1 && (
                        <button
                          type="button"
                          className="text-gray-400 hover:text-error transition-colors"
                          onClick={() => removeTask(taskIndex)}
                        >
                          <Trash2 size={18} />
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="form-label">Task Name</label>
                        <input
                          type="text"
                          className={`form-input ${errors.tasks?.[taskIndex]?.name ? 'border-error' : ''}`}
                          placeholder="Enter task name"
                          {...register(`tasks.${taskIndex}.name`, { required: "Task name is required" })}
                        />
                        {errors.tasks?.[taskIndex]?.name && (
                          <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.name?.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">Assignee</label>
                        <select
                          className={`form-select ${errors.tasks?.[taskIndex]?.assigneeId ? 'border-error' : ''}`}
                          {...register(`tasks.${taskIndex}.assigneeId`, { required: "Assignee is required" })}
                        >
                          <option value="">Select Team Lead</option>
                          {teamLeads.map((teamLead) => (
                            <option key={teamLead.id} value={teamLead.id}>
                              {teamLead.name} ({teamLead.department})
                            </option>
                          ))}
                        </select>
                        {errors.tasks?.[taskIndex]?.assigneeId && (
                          <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.assigneeId?.message}</p>
                        )}
                      </div>

                      <div className="md:col-span-2">
                        <label className="form-label">Description</label>
                        <textarea
                          className={`form-input ${errors.tasks?.[taskIndex]?.description ? 'border-error' : ''}`}
                          rows={2}
                          placeholder="Enter task description"
                          {...register(`tasks.${taskIndex}.description`, { required: "Description is required" })}
                        />
                        {errors.tasks?.[taskIndex]?.description && (
                          <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.description?.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">Start Date</label>
                        <input
                          type="date"
                          className={`form-input ${errors.tasks?.[taskIndex]?.startDate ? 'border-error' : ''}`}
                          min={getCurrentDateOrProjectStart(project.startDate)}
                          max={project.endDate}
                          {...register(`tasks.${taskIndex}.startDate`, {
                            required: "Start date is required",
                            validate: value => {
                              // Ensure date is within project date range and not in the past
                              const date = new Date(value);
                              const today = new Date();
                              today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison
                              const projectStart = new Date(project.startDate);
                              const projectEnd = new Date(project.endDate);

                              // Use the later of today or project start date
                              const effectiveStartDate = projectStart > today ? projectStart : today;

                              if (date < effectiveStartDate) {
                                return "Start date cannot be in the past or before project start date";
                              }
                              if (date > projectEnd) {
                                return "Start date cannot be after project end date";
                              }
                              return true;
                            }
                          })}
                        />
                        {errors.tasks?.[taskIndex]?.startDate && (
                          <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.startDate?.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">End Date</label>
                        <input
                          type="date"
                          className={`form-input ${errors.tasks?.[taskIndex]?.endDate ? 'border-error' : ''}`}
                          min={getCurrentDateOrProjectStart(taskFields[taskIndex]?.startDate || project.startDate)}
                          max={project.endDate}
                          {...register(`tasks.${taskIndex}.endDate`, {
                            required: "End date is required",
                            validate: value => {
                              // Ensure date is within project date range and not in the past
                              const date = new Date(value);
                              const today = new Date();
                              today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison
                              const projectStart = new Date(project.startDate);
                              const projectEnd = new Date(project.endDate);
                              const taskStart = new Date(taskFields[taskIndex]?.startDate || project.startDate);

                              // Use the later of today or task start date
                              const effectiveStartDate = taskStart > today ? taskStart : today;

                              if (date < effectiveStartDate) {
                                return "End date cannot be in the past or before task start date";
                              }
                              if (date > projectEnd) {
                                return "End date cannot be after project end date";
                              }
                              return true;
                            }
                          })}
                        />
                        {errors.tasks?.[taskIndex]?.endDate && (
                          <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.endDate?.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">Status</label>
                        <select
                          className="form-select"
                          {...register(`tasks.${taskIndex}.status`)}
                        >
                          {Object.values(TaskStatus).map((status) => (
                            <option key={status} value={status}>
                              {status.replace(/_/g, ' ')}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    {/* Subtasks Section */}
                    <div className="mt-6 bg-gray-50 p-4 rounded border border-gray-100">
                      <div className="flex justify-between items-center mb-3">
                        <h4 className="text-md font-medium text-gray-700">Subtasks</h4>
                        <button
                          type="button"
                          className="btn btn-outline py-1 px-2 text-sm flex items-center"
                          onClick={() => {
                            // Add a new subtask with default dates from the task (or current date if later)
                            subtasksFieldArray.append({
                              name: '',
                              description: '',
                              assigneeId: '',
                              assigneeType: TaskAssigneeType.ENGINEER,
                              startDate: getCurrentDateOrProjectStart(taskFields[taskIndex]?.startDate || project.startDate),
                              endDate: taskFields[taskIndex]?.endDate || project.endDate,
                              status: TaskStatus.NOT_STARTED
                            });
                          }}
                        >
                          <Plus size={14} className="mr-1" />
                          Add Subtask
                        </button>
                      </div>

                      {subtasksFieldArray.fields.length === 0 ? (
                        <p className="text-sm text-gray-500 italic">No subtasks added yet</p>
                      ) : (
                        <div className="space-y-3">
                          {subtasksFieldArray.fields.map((subtaskField, subtaskIndex) => (
                            <div key={subtaskField.id} className="border border-gray-100 rounded p-3 bg-white relative">
                              <button
                                type="button"
                                className="absolute top-2 right-2 text-gray-400 hover:text-error transition-colors"
                                onClick={() => subtasksFieldArray.remove(subtaskIndex)}
                              >
                                <Trash2 size={14} />
                              </button>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <label className="form-label text-sm">Subtask Name</label>
                                  <input
                                    type="text"
                                    className="form-input w-full"
                                    placeholder="Enter subtask name"
                                    {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.name`, { required: "Subtask name is required" })}
                                  />
                                  {errors.tasks?.[taskIndex]?.subtasks?.[subtaskIndex]?.name && (
                                    <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.subtasks?.[subtaskIndex]?.name?.message}</p>
                                  )}
                                </div>

                                <div>
                                  <label className="form-label text-sm">Assignee</label>
                                  <select
                                    className="form-select w-full"
                                    {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.assigneeId`, { required: "Assignee is required" })}
                                  >
                                    <option value="">Select Engineer</option>
                                    {engineersOnly.map((engineer) => (
                                      <option key={engineer.id} value={engineer.id}>
                                        {engineer.name} ({engineer.department})
                                      </option>
                                    ))}
                                  </select>
                                  {errors.tasks?.[taskIndex]?.subtasks?.[subtaskIndex]?.assigneeId && (
                                    <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.subtasks?.[subtaskIndex]?.assigneeId?.message}</p>
                                  )}
                                </div>

                                <div className="md:col-span-2">
                                  <label className="form-label text-sm">Description</label>
                                  <textarea
                                    className="form-input w-full"
                                    rows={2}
                                    placeholder="Enter subtask description"
                                    {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.description`, { required: "Description is required" })}
                                  />
                                  {errors.tasks?.[taskIndex]?.subtasks?.[subtaskIndex]?.description && (
                                    <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.subtasks?.[subtaskIndex]?.description?.message}</p>
                                  )}
                                </div>

                                <div>
                                  <label className="form-label text-sm">Start Date</label>
                                  <input
                                    type="date"
                                    className="form-input w-full"
                                    {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.startDate`, { required: "Start date is required" })}
                                  />
                                  {errors.tasks?.[taskIndex]?.subtasks?.[subtaskIndex]?.startDate && (
                                    <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.subtasks?.[subtaskIndex]?.startDate?.message}</p>
                                  )}
                                </div>

                                <div>
                                  <label className="form-label text-sm">End Date</label>
                                  <input
                                    type="date"
                                    className="form-input w-full"
                                    {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.endDate`, { required: "End date is required" })}
                                  />
                                  {errors.tasks?.[taskIndex]?.subtasks?.[subtaskIndex]?.endDate && (
                                    <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.subtasks?.[subtaskIndex]?.endDate?.message}</p>
                                  )}
                                </div>

                                <div>
                                  <label className="form-label text-sm">Status</label>
                                  <select
                                    className="form-select w-full"
                                    {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.status`)}
                                  >
                                    {Object.values(TaskStatus).map((status) => (
                                      <option key={status} value={status}>
                                        {status.replace(/_/g, ' ')}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => navigate('/projects')}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary flex items-center"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Adding Tasks...
                  </>
                ) : (
                  <>
                    <CheckCircle size={18} className="mr-2" />
                    Add Tasks to Project
                  </>
                )}
              </button>
            </div>
          </form>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">Please select a project to add tasks to.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AddTaskPage;