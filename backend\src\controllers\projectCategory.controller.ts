import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Get all project categories
export const getProjectCategories = async (req: Request, res: Response): Promise<void> => {
  try {
    const categories = await prisma.project_category_item.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Error fetching project categories:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch project categories',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get single project category
export const getProjectCategory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const category = await prisma.project_category_item.findUnique({
      where: { id }
    });

    if (!category) {
      res.status(404).json({
        success: false,
        message: 'Project category not found'
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error('Error fetching project category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch project category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Create project category
export const createProjectCategory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, description } = req.body;

    // Validate required fields
    if (!name) {
      res.status(400).json({
        success: false,
        message: 'Name is required'
      });
      return;
    }

    // Check if category with same name already exists
    const existingCategory = await prisma.project_category_item.findFirst({
      where: { name: name.trim() }
    });

    if (existingCategory) {
      res.status(400).json({
        success: false,
        message: 'Project category with this name already exists'
      });
      return;
    }

    // Auto-generate code from name
    const generateCode = (name: string): string => {
      return name
        .trim()
        .toUpperCase()
        .replace(/[^A-Z0-9\s]/g, '') // Remove special characters
        .replace(/\s+/g, '_') // Replace spaces with underscores
        .substring(0, 20); // Limit to 20 characters
    };

    let code = generateCode(name);

    // Ensure code is unique
    let counter = 1;
    let originalCode = code;
    while (await prisma.project_category_item.findFirst({ where: { code } })) {
      code = `${originalCode}_${counter}`;
      counter++;
    }

    const category = await prisma.project_category_item.create({
      data: {
        name: name.trim(),
        code: code.trim().toUpperCase(),
        description: description?.trim() || null
      }
    });

    res.status(201).json({
      success: true,
      data: category,
      message: 'Project category created successfully'
    });
  } catch (error) {
    console.error('Error creating project category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create project category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update project category
export const updateProjectCategory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;

    // Check if category exists
    const existingCategory = await prisma.project_category_item.findUnique({
      where: { id }
    });

    if (!existingCategory) {
      res.status(404).json({
        success: false,
        message: 'Project category not found'
      });
      return;
    }

    // Check for duplicate name (excluding current category)
    if (name && name.trim() !== existingCategory.name) {
      const duplicateCategory = await prisma.project_category_item.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            { name: name.trim() }
          ]
        }
      });

      if (duplicateCategory) {
        res.status(400).json({
          success: false,
          message: 'Project category with this name already exists'
        });
        return;
      }
    }

    const updateData: any = {};
    if (name !== undefined) {
      updateData.name = name.trim();

      // Auto-generate new code if name is changing
      if (name.trim() !== existingCategory.name) {
        const generateCode = (name: string): string => {
          return name
            .trim()
            .toUpperCase()
            .replace(/[^A-Z0-9\s]/g, '') // Remove special characters
            .replace(/\s+/g, '_') // Replace spaces with underscores
            .substring(0, 20); // Limit to 20 characters
        };

        let newCode = generateCode(name);

        // Ensure code is unique
        let counter = 1;
        let originalCode = newCode;
        while (await prisma.project_category_item.findFirst({
          where: {
            code: newCode,
            id: { not: id }
          }
        })) {
          newCode = `${originalCode}_${counter}`;
          counter++;
        }

        updateData.code = newCode;
      }
    }
    if (description !== undefined) updateData.description = description?.trim() || null;

    const category = await prisma.project_category_item.update({
      where: { id },
      data: updateData
    });

    res.status(200).json({
      success: true,
      data: category,
      message: 'Project category updated successfully'
    });
  } catch (error) {
    console.error('Error updating project category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update project category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete project category
export const deleteProjectCategory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // Check if category exists
    const existingCategory = await prisma.project_category_item.findUnique({
      where: { id }
    });

    if (!existingCategory) {
      res.status(404).json({
        success: false,
        message: 'Project category not found'
      });
      return;
    }

    await prisma.project_category_item.delete({
      where: { id }
    });

    res.status(200).json({
      success: true,
      message: 'Project category deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting project category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete project category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
