import React, { useState, useEffect } from 'react';
import { Plus, Edit2, Trash2, Layers, Search, AlertCircle } from 'lucide-react';
import { useAtom } from 'jotai';
import { currentUserAtom } from '../store';
import { projectCategoriesAPI } from '../services/api';
import { ProjectCategoryItem, UserRole } from '../types';

const ProjectCategoriesPage: React.FC = () => {
  const [currentUser] = useAtom(currentUserAtom);
  const [categories, setCategories] = useState<ProjectCategoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ProjectCategoryItem | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Check if user can manage categories (DIRECTOR and PROJECT_MANAGER)
  const canManageCategories = currentUser?.role === UserRole.DIRECTOR || currentUser?.role === UserRole.PROJECT_MANAGER;

  // If user is not a Director or Project Manager, they shouldn't be here (but just in case)
  if (!canManageCategories) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center max-w-md">
          <div className="text-red-600 mb-4">
            <AlertCircle size={48} className="mx-auto" />
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">Access Restricted</h2>
          <p className="text-gray-600 mb-4">
            Project Categories management is only available to Directors and Project Managers.
          </p>
          <button
            onClick={() => window.history.back()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // Load categories
  const loadCategories = async () => {
    try {
      setLoading(true);
      const response = await projectCategoriesAPI.getProjectCategories();
      setCategories(response.data);
    } catch (error) {
      console.error('Error loading project categories:', error);
      setError('Failed to load project categories');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCategories();
  }, []);

  // Filter categories based on search term
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    try {
      if (editingCategory) {
        // Update existing category
        await projectCategoriesAPI.updateProjectCategory(editingCategory.id, formData);
        setSuccess('Project category updated successfully');
      } else {
        // Create new category
        await projectCategoriesAPI.createProjectCategory(formData);
        setSuccess('Project category created successfully');
      }

      // Reset form and reload data
      setFormData({ name: '', description: '' });
      setShowCreateModal(false);
      setEditingCategory(null);
      await loadCategories();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to save project category');
    }
  };

  // Handle edit
  const handleEdit = (category: ProjectCategoryItem) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: category.description || ''
    });
    setShowCreateModal(true);
  };

  // Handle delete
  const handleDelete = async (category: ProjectCategoryItem) => {
    if (!window.confirm(`Are you sure you want to delete "${category.name}"?`)) {
      return;
    }

    try {
      await projectCategoriesAPI.deleteProjectCategory(category.id);
      setSuccess('Project category deleted successfully');
      await loadCategories();
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to delete project category');
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({ name: '', description: '' });
    setEditingCategory(null);
    setShowCreateModal(false);
    setError(null);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="w-full mx-auto py-2">
        {/* Header Section with 3D Card */}
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 border border-white/20 backdrop-blur-sm relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-indigo-600/5"></div>
          <div className="relative z-10 flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-1 flex items-center">
                <div className="relative bg-gradient-to-br from-white to-blue-100 rounded-lg p-2 mr-3 shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                  <Layers size={24} className="text-blue-600 drop-shadow-sm" />
                </div>
                Project Categories
              </h1>
              <p className="text-gray-600">
                Manage project categories and types for better organization
              </p>
            </div>
            {canManageCategories && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-2 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 flex items-center font-medium"
              >
                <Plus size={16} className="mr-2" />
                Add Category
              </button>
            )}
          </div>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl text-green-800 shadow-lg">
            {success}
          </div>
        )}
        {error && (
          <div className="mb-6 p-4 bg-gradient-to-r from-red-50 to-rose-50 border border-red-200 rounded-xl text-red-800 flex items-center shadow-lg">
            <AlertCircle size={20} className="mr-2" />
            {error}
          </div>
        )}

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm bg-white"
            />
          </div>
        </div>

        {/* Categories Table */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden w-full border border-white/20">
          <table className="w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-gray-50 to-blue-50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Created
                </th>
                {canManageCategories && (
                  <th className="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Actions
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {filteredCategories.map((category, index) => (
                <tr
                  key={category.id}
                  className="group hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue-100/50"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="bg-blue-100 rounded-lg p-2 mr-3 group-hover:bg-blue-200 transition-colors duration-200">
                        <Layers size={16} className="text-blue-600" />
                      </div>
                      <span className="text-sm font-semibold text-gray-900 group-hover:text-blue-700 transition-colors duration-200">
                        {category.name}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-5">
                    <span className="text-sm text-gray-600 group-hover:text-gray-700 transition-colors duration-200">
                      {category.description || 'No description'}
                    </span>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap text-sm text-gray-500 group-hover:text-gray-600 transition-colors duration-200">
                    {new Date(category.createdAt).toLocaleDateString()}
                  </td>
                  {canManageCategories && (
                    <td className="px-6 py-5 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleEdit(category)}
                          className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded-lg transition-all duration-200 transform hover:scale-105"
                          title="Edit category"
                        >
                          <Edit2 size={16} />
                        </button>
                        <button
                          onClick={() => handleDelete(category)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-lg transition-all duration-200 transform hover:scale-105"
                          title="Delete category"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  )}
                </tr>
              ))}
              {filteredCategories.length === 0 && (
                <tr>
                  <td colSpan={canManageCategories ? 4 : 3} className="text-center py-16">
                    <div className="flex flex-col items-center">
                      <div className="bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full w-20 h-20 flex items-center justify-center mb-6 shadow-inner">
                        <Layers size={40} className="text-blue-500" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">No categories found</h3>
                      <p className="text-gray-500 text-lg">
                        {searchTerm ? 'Try adjusting your search terms' : 'Get started by creating your first project category'}
                      </p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create/Edit Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              {editingCategory ? 'Edit Project Category' : 'Create Project Category'}
            </h2>

            <form onSubmit={handleSubmit}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Precision Projects"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Optional description..."
                    rows={3}
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  {editingCategory ? 'Update' : 'Create'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectCategoriesPage;
