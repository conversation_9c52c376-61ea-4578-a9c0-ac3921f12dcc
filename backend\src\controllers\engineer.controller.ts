import { Request, Response } from 'express';
import { prisma } from '../index';
import { v4 as uuidv4 } from 'uuid';
import { hashPassword } from '../utils/auth.utils';

// @desc    Get all engineers
// @route   GET /api/engineers
// @access  Private
export const getEngineers = async (req: Request, res: Response): Promise<void> => {
  try {
    const { department, role } = req.query;

    // Filter conditions
    const where: any = {};

    // Filter by department if provided
    if (department) {
      where.department = department as string;
    }

    // Filter by role if provided
    if (role) {
      where.role = role as string;
    }

    const engineers = await prisma.user.findMany({
      where: {
        ...where,
        role: {
          in: ['ENGINEER', 'TEAM_LEAD'], // Include both engineers and team leads
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    res.status(200).json({
      success: true,
      count: engineers.length,
      data: engineers,
    });
  } catch (error) {
    console.error('Get engineers error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single engineer
// @route   GET /api/engineers/:id
// @access  Private
export const getEngineer = async (req: Request, res: Response): Promise<void> => {
  try {
    const engineer = await prisma.user.findUnique({
      where: {
        id: req.params.id,
      },
    });

    if (!engineer) {
      res.status(404).json({
        success: false,
        message: 'Engineer not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: engineer,
    });
  } catch (error) {
    console.error('Get engineer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create engineer
// @route   POST /api/engineers
// @access  Private/Admin
export const createEngineer = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, code, department, role, email, skills, joinDate, password } = req.body;

    // Check if engineer already exists
    const engineerExists = await prisma.user.findFirst({
      where: {
        OR: [
          { code },
          { email },
        ],
      },
    });

    if (engineerExists) {
      res.status(400).json({
        success: false,
        message: 'Engineer with this code or email already exists',
      });
      return;
    }

    // Hash password
    const hashedPassword = await hashPassword(password || 'defaultPassword123');

    // Create engineer (user with ENGINEER or TEAM_LEAD role)
    const engineerId = uuidv4();
    const engineer = await prisma.user.create({
      data: {
        id: engineerId,
        name,
        code,
        department,
        role: role || 'ENGINEER', // Default to ENGINEER role
        email,
        password: hashedPassword,
        skills: skills ? skills.join(',') : null,
        joinDate: new Date(joinDate),
      },
    });

    res.status(201).json({
      success: true,
      data: engineer,
    });
  } catch (error) {
    console.error('Create engineer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update engineer
// @route   PUT /api/engineers/:id
// @access  Private/Admin
export const updateEngineer = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, department, role, email, skills, joinDate } = req.body;

    // Check if engineer exists
    const engineerExists = await prisma.user.findUnique({
      where: { id: req.params.id },
    });

    if (!engineerExists) {
      res.status(404).json({
        success: false,
        message: 'Engineer not found',
      });
      return;
    }

    // Update engineer
    const engineer = await prisma.user.update({
      where: { id: req.params.id },
      data: {
        name,
        department,
        role,
        email,
        skills: skills ? skills.join(',') : null,
        joinDate: joinDate ? new Date(joinDate) : undefined,
      },
    });

    res.status(200).json({
      success: true,
      data: engineer,
    });
  } catch (error) {
    console.error('Update engineer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete engineer
// @route   DELETE /api/engineers/:id
// @access  Private/Admin
export const deleteEngineer = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if engineer exists
    const engineerExists = await prisma.user.findUnique({
      where: { id: req.params.id },
    });

    if (!engineerExists) {
      res.status(404).json({
        success: false,
        message: 'Engineer not found',
      });
      return;
    }

    // Delete engineer
    await prisma.user.delete({
      where: { id: req.params.id },
    });

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete engineer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
