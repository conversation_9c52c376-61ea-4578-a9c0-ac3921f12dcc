const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function testPaymentSummary() {
  try {
    console.log('=== TESTING PAYMENT SUMMARY ENDPOINT ===\n');

    // Get all projects with PO values
    const projects = await prisma.project.findMany({
      where: {
        poValue: {
          not: null,
          gt: 0
        }
      },
      select: {
        id: true,
        name: true,
        poValue: true
      }
    });

    console.log(`Found ${projects.length} projects with PO Value:`);
    projects.forEach(project => {
      console.log(`- ${project.name}: ₹${Number(project.poValue).toLocaleString('en-IN')}`);
    });

    // Get all payment records
    const projectIds = projects.map(p => p.id);
    const paymentRecords = await prisma.payment.findMany({
      where: {
        projectId: {
          in: projectIds
        }
      },
      select: {
        projectId: true,
        amount: true,
        project: {
          select: {
            name: true
          }
        }
      }
    });

    console.log(`\nFound ${paymentRecords.length} payment records:`);
    paymentRecords.forEach(payment => {
      console.log(`- ${payment.project.name}: ₹${Number(payment.amount).toLocaleString('en-IN')}`);
    });

    // Group payments by project
    const paymentsByProject = paymentRecords.reduce((acc, payment) => {
      if (!acc[payment.projectId]) {
        acc[payment.projectId] = [];
      }
      acc[payment.projectId].push(payment);
      return acc;
    }, {});

    // Calculate totals
    let totalPoValue = 0;
    let totalPaid = 0;

    projects.forEach(project => {
      const poValue = Number(project.poValue) || 0;
      const projectPayments = paymentsByProject[project.id] || [];
      const paidAmount = projectPayments.reduce((sum, payment) => {
        return sum + Number(payment.amount);
      }, 0);

      totalPoValue += poValue;
      totalPaid += paidAmount;
    });

    const totalPending = totalPoValue - totalPaid;
    const paidPercentage = totalPoValue > 0 ? (totalPaid / totalPoValue) * 100 : 0;
    const pendingPercentage = totalPoValue > 0 ? (totalPending / totalPoValue) * 100 : 0;

    console.log('\n=== PAYMENT SUMMARY ===');
    console.log(`Total Projects: ${projects.length}`);
    console.log(`Total PO Value: ₹${totalPoValue.toLocaleString('en-IN')}`);
    console.log(`Total Paid: ₹${totalPaid.toLocaleString('en-IN')} (${paidPercentage.toFixed(1)}%)`);
    console.log(`Total Pending: ₹${totalPending.toLocaleString('en-IN')} (${pendingPercentage.toFixed(1)}%)`);

    const summary = {
      totalProjects: projects.length,
      totalPoValue: Math.round(totalPoValue * 100) / 100,
      totalPaid: Math.round(totalPaid * 100) / 100,
      totalPending: Math.round(totalPending * 100) / 100,
      paidPercentage: Math.round(paidPercentage * 100) / 100,
      pendingPercentage: Math.round(pendingPercentage * 100) / 100
    };

    console.log('\n=== API RESPONSE FORMAT ===');
    console.log(JSON.stringify(summary, null, 2));

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPaymentSummary();
