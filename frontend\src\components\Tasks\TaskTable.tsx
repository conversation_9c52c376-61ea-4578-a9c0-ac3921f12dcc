import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useAtom } from 'jotai';
import { useNavigate } from 'react-router-dom';
import {
  userTasks<PERSON><PERSON>,
  engineers<PERSON>tom,
  currentUser<PERSON>tom,
  normalizedProjectsAtom,
  projectsAtom
} from '../../store';
import { UserRole, TaskStatus, Task, TaskAssigneeType, Subtask, SubtaskAssigneeType } from '../../types';

import { tasksAPI, projectsAPI, subtasksAPI, momsAPI } from '../../services/api.ts';
import { dataService } from '../../services/dataServiceSingleton';
import {
  ChevronDown,
  ChevronRight,
  Filter,
  Search,
  MoreHorizontal,
  Eye,
  Plus,
  Edit,
  Trash2,
  MoreVertical,
  Maximize2,
  Minimize2,
  CheckSquare,
  RefreshCw,
  X,
  Check,
  XCircle,
  MessageSquare
} from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import TaskFormModal from './TaskFormModal';
import SubtaskFormModal from './SubtaskFormModal';
import { useNotification } from '../../contexts/NotificationContext';
import { toCamelCase } from '../../utils/textUtils';

// Debounce utility function for task operations
const useDebounce = (callback: Function, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback((...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

interface TaskTableProps {
  projectId?: string;
}

// Add interface for task with project name
interface TaskWithProjectName extends Task {
  projectName: string;
  sequence?: number;
}

// Add interface for new task state
interface NewTaskState {
  name: string;
  description: string;
  assigneeId: string;
  startDate: string;
  endDate: string;
  status: TaskStatus;
  priority: string;
}

// Add interface for new subtask state
interface NewSubtaskState {
  name: string;
  description: string;
  assigneeId: string;
  status: TaskStatus;
  startDate: string;
  endDate: string;
  department: string;
  priority: string;
}

const TaskTable: React.FC<TaskTableProps> = ({ projectId }) => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  // Get tasks from the store
  const [userTasks, setUserTasks] = useAtom(userTasksAtom);
  const [engineers] = useAtom(engineersAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [projects, setProjects] = useAtom(projectsAtom);

  // Debounced refresh function to reduce API calls
  const debouncedRefreshTasks = useDebounce(async () => {
    try {
      console.log('🔄 Refreshing tasks data...');
      await dataService.loadProjects(); // This will also load tasks
      console.log('✅ Tasks data refreshed successfully');
    } catch (error) {
      console.error('❌ Error refreshing tasks:', error);
    }
  }, 1000); // 1 second debounce

  const [normalizedProjects] = useAtom(normalizedProjectsAtom);

  // Filter users for task assignment - only team leads
  const teamLeads = engineers.filter(user => user.role === 'TEAM_LEAD');
  // Filter users for subtask assignment - only engineers
  const engineersOnly = engineers.filter(user => user.role === 'ENGINEER');

  // Get the current project if projectId is provided
  const project = projectId ? normalizedProjects.find(p => p.id === projectId) : undefined;



  // Local state for tasks to allow immediate UI updates
  const [localTasks, setLocalTasks] = useState<TaskWithProjectName[]>([]);
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [priorityFilter, setPriorityFilter] = useState<string>('ALL');
  const [assigneeFilter, setAssigneeFilter] = useState<string>('ALL');

  // Inline editing state
  const [editingTaskId, setEditingTaskId] = useState<string | null>(null);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editPosition, setEditPosition] = useState({ top: 0, left: 0, width: 0 });
  const [editValue, setEditValue] = useState<string>('');
  const [newTaskMode, setNewTaskMode] = useState(false);
  const [newTask, setNewTask] = useState<NewTaskState>({
    name: '',
    description: '',
    assigneeId: '',
    startDate: project?.startDate || '',
    endDate: project?.endDate || '',
    status: TaskStatus.NOT_STARTED,
    priority: 'Low'
  });

  // Task action menu state
  const [actionMenuTask, setActionMenuTask] = useState<string | null>(null);

  // Fullscreen state
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Task modal state
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);

  // Subtask modal state
  const [showSubtaskModal, setShowSubtaskModal] = useState(false);
  const [selectedTaskForSubtask, setSelectedTaskForSubtask] = useState<Task | null>(null);

  // New subtask inline creation state
  const [showNewSubtaskRow, setShowNewSubtaskRow] = useState(false);
  const [newSubtaskParentId, setNewSubtaskParentId] = useState<string>('');
  const initialNewSubtaskState: NewSubtaskState = {
    name: '',
    description: '',
    assigneeId: '',
    status: TaskStatus.NOT_STARTED,
    startDate: '',
    endDate: '',
    department: project?.department || '',
    priority: 'Low'
  };
  const [newSubtask, setNewSubtask] = useState<NewSubtaskState>(initialNewSubtaskState);

  // Calculate task status based on subtasks
  const calculateTaskStatus = (task: any) => {
    // IMPORTANT: Never override manually set COMPLETED or ON_HOLD statuses
    // These should be preserved regardless of subtask changes
    if (task.status === TaskStatus.COMPLETED || task.status === TaskStatus.ON_HOLD) {
      return task.status; // Preserve manually set status
    }

    if (!task.subtasks || task.subtasks.length === 0) {
      return task.status; // No subtasks, keep original status
    }

    const subtasks = task.subtasks;
    const statusCounts = {
      COMPLETED: 0,
      IN_PROGRESS: 0,
      NOT_STARTED: 0,
      DELAYED: 0,
      ON_HOLD: 0
    };

    subtasks.forEach((subtask: any) => {
      statusCounts[subtask.status as keyof typeof statusCounts]++;
    });

    const total = subtasks.length;

    // Business logic for task status based on subtasks:
    // 1. If ANY subtask is DELAYED -> Task is DELAYED (highest priority)
    // 2. If ALL subtasks are COMPLETED -> Task is COMPLETED
    // 3. If ANY subtask is IN_PROGRESS -> Task is IN_PROGRESS
    // 4. If ANY subtask is ON_HOLD and none are IN_PROGRESS/DELAYED -> Task is ON_HOLD
    // 5. If ALL subtasks are NOT_STARTED -> Task is NOT_STARTED

    if (statusCounts.DELAYED > 0) {
      return 'DELAYED';
    } else if (statusCounts.COMPLETED === total) {
      return 'COMPLETED';
    } else if (statusCounts.IN_PROGRESS > 0) {
      return 'IN_PROGRESS';
    } else if (statusCounts.ON_HOLD > 0) {
      return 'ON_HOLD';
    } else {
      return 'NOT_STARTED';
    }
  };

  // Initialize local tasks from userTasks or directly from project.tasks with calculated status
  useEffect(() => {
    // If projectId is provided and project exists with tasks, use those tasks directly
    if (projectId && project && project.tasks) {
      console.log('[Project Tasks] Loading tasks from project:', {
        projectId,
        projectName: project.name,
        taskCount: project.tasks.length,
        tasks: project.tasks
      });
      // Convert project tasks to the expected format with projectName and calculated status
      const projectTasks = project.tasks.map(task => ({
        ...task,
        projectName: project.name,
        status: calculateTaskStatus(task)
      }));
      setLocalTasks(projectTasks);
    } else {

      // Otherwise use filtered userTasks with calculated status
      const updatedUserTasks = userTasks.map(task => ({
        ...task,
        status: calculateTaskStatus(task)
      }));

      if (projectId) {
        setLocalTasks(updatedUserTasks.filter(task => task.projectId === projectId));
      } else {
        setLocalTasks(updatedUserTasks);
      }
    }
  }, [userTasks, projectId, project]);

  // Auto-expand only tasks that have subtasks (hierarchical tree view)
  useEffect(() => {
    if (localTasks.length > 0) {
      const tasksWithSubtasks = localTasks
        .filter(task => task.subtasks && task.subtasks.length > 0)
        .map(task => task.id);
      setExpandedTasks(new Set(tasksWithSubtasks));
    }
  }, [localTasks]);

  // Enhanced filter tasks function with multiple criteria
  const filterTasks = (tasks: TaskWithProjectName[]) => {
    return tasks.filter(task => {
      // Check if the task matches the search term (enhanced search)
      const matchesSearch = searchTerm === '' ||
        task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (task.displayId && task.displayId.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (task.id && task.id.toLowerCase().includes(searchTerm.toLowerCase()));

      // Check if the task matches the status filter
      const matchesStatus = statusFilter === 'ALL' || task.status === statusFilter;

      // Check if the task matches the priority filter
      const matchesPriority = priorityFilter === 'ALL' || task.priority === priorityFilter;

      // Check if the task matches the assignee filter
      let matchesAssignee = true;
      if (assigneeFilter !== 'ALL') {
        if (assigneeFilter === 'ME' && currentUser) {
          matchesAssignee = task.assigneeId === currentUser.id;
        } else {
          matchesAssignee = task.assigneeId === assigneeFilter;
        }
      }

      // If the task has subtasks, we need to check if any of them match the filters
      if (task.subtasks && task.subtasks.length > 0) {
        const hasMatchingSubtask = task.subtasks.some(subtask => {
          const subtaskMatchesSearch = searchTerm === '' ||
            subtask.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            subtask.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (subtask.displayId && subtask.displayId.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (subtask.id && subtask.id.toLowerCase().includes(searchTerm.toLowerCase()));

          const subtaskMatchesStatus = statusFilter === 'ALL' || subtask.status === statusFilter;
          const subtaskMatchesPriority = priorityFilter === 'ALL' || subtask.priority === priorityFilter;

          let subtaskMatchesAssignee = true;
          if (assigneeFilter !== 'ALL') {
            if (assigneeFilter === 'ME' && currentUser) {
              subtaskMatchesAssignee = subtask.assigneeId === currentUser.id;
            } else {
              subtaskMatchesAssignee = subtask.assigneeId === assigneeFilter;
            }
          }

          return subtaskMatchesSearch && subtaskMatchesStatus && subtaskMatchesPriority && subtaskMatchesAssignee;
        });

        // Include the task if it matches all criteria OR if any of its subtasks match
        return (matchesSearch && matchesStatus && matchesPriority && matchesAssignee) || hasMatchingSubtask;
      }

      return matchesSearch && matchesStatus && matchesPriority && matchesAssignee;
    });
  };

  // Organize tasks into a tree structure with parent tasks and subtasks
  const organizeTasksIntoTree = (tasks: Task[]): TaskWithProjectName[] => {
    console.log('Organizing tasks into tree structure');
    console.log('Tasks:', tasks);

    // Create a map of task IDs to their subtasks
    const subtaskMap: Record<string, Subtask[]> = {};

    // Group subtasks by their parent task ID
    tasks.forEach(task => {
      if (task.subtasks) {
        task.subtasks.forEach(subtask => {
          if (!subtaskMap[task.id]) {
            subtaskMap[task.id] = [];
          }
          subtaskMap[task.id].push(subtask);
        });
      }
    });

    console.log('Subtask map:', subtaskMap);

    // Attach subtasks to their parent tasks and add projectName
    return tasks.map(task => ({
      ...task,
      subtasks: (subtaskMap[task.id] || []).sort((a, b) => (a.sequence || 0) - (b.sequence || 0)),
      projectName: project?.name || ''
    }));
  };



  // Filter tasks based on search and status
  const filteredTasks = useMemo(() => {

    // First, separate tasks and subtasks
    // Tasks can have IDs like "T1", "PROJ1T1", "MOM-xxx", etc.
    // Subtasks have IDs like "S1", "PROJ1S1", etc.
    const mainTasks = localTasks.filter(task =>
      task.id && (
        task.id.startsWith('T') || // Regular tasks starting with T
        task.id.startsWith('MOM-') || // MOM tasks
        (!task.id.startsWith('T') && !task.id.startsWith('S') && !task.id.startsWith('MOM-')) // Other task formats
      )
    );
    const subtasks = localTasks.filter(task => task.id && task.id.startsWith('S'));

    console.log('Main tasks count:', mainTasks.length);
    console.log('Subtasks count:', subtasks.length);

    // Sort main tasks by sequence number to ensure proper order (T1, T2, T3, etc.)
    const sortedMainTasks = mainTasks.sort((a, b) => {
      // Handle MOM tasks separately - they should appear after regular tasks
      if (a.id.startsWith('MOM-') && !b.id.startsWith('MOM-')) return 1;
      if (!a.id.startsWith('MOM-') && b.id.startsWith('MOM-')) return -1;
      if (a.id.startsWith('MOM-') && b.id.startsWith('MOM-')) {
        return a.createdAt.localeCompare(b.createdAt);
      }

      // For regular tasks, sort by sequence number
      return (a.sequence || 0) - (b.sequence || 0);
    });

    // Organize into tree structure
    const organizedTasks = organizeTasksIntoTree(sortedMainTasks);

    // Apply filters
    const filtered = filterTasks(organizedTasks);
    return filtered;
  }, [localTasks, searchTerm, statusFilter, priorityFilter, assigneeFilter, currentUser]);

  // Toggle task expansion (for hierarchical tree view)
  const toggleTaskExpansion = (taskId: string) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
      } else {
        newSet.add(taskId);
      }
      return newSet;
    });
  };

  // Get engineer name by ID (for subtasks)
  const getEngineerName = (id: string) => {
    const engineer = engineers.find(e => e.id === id);
    return engineer ? engineer.name : 'Unassigned';
  };

  // Get team lead name by ID (for tasks)
  const getTeamLeadName = (id: string) => {
    const teamLead = engineers.find(e => e.id === id && e.role === 'TEAM_LEAD');
    return teamLead ? teamLead.name : 'Unassigned';
  };

  // Get status badge class
  const getStatusBadgeClass = (status: TaskStatus): string => {
    switch (status) {
      case TaskStatus.NOT_STARTED:
        return "badge badge-outline";
      case TaskStatus.IN_PROGRESS:
        return "badge badge-primary";
      case TaskStatus.COMPLETED:
        return "badge badge-success";
      case TaskStatus.DELAYED:
        return "badge badge-error";
      case TaskStatus.ON_HOLD:
        return "badge badge-warning";
      default:
        return "badge badge-outline";
    }
  };

  // Get status badge
  const getStatusBadge = (status: TaskStatus) => {
    const className = getStatusBadgeClass(status);
    return <span className={className}>{status.replace(/_/g, ' ')}</span>;
  };

  // Calculate duration in days
  const calculateDuration = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return `${diffDays} days`;
  };

  // Check if user can manage tasks (create/delete)
  const canManageTasks = (): boolean => {
    if (!currentUser) return false;

    return currentUser.role === UserRole.DIRECTOR || currentUser.role === UserRole.PROJECT_MANAGER;
  };

  // Check if user can edit a specific field
  const canEditField = (field: string): boolean => {
    if (!currentUser) return false;

    // Directors and Project Managers can edit all fields
    if ([UserRole.DIRECTOR, UserRole.PROJECT_MANAGER].includes(currentUser.role)) {
      return true;
    }

    // Team leads can only edit status and end date
    if (currentUser.role === UserRole.TEAM_LEAD) {
      return ['status', 'endDate'].includes(field);
    }

    // Engineers cannot edit any fields in this view
    return false;
  };

  // Start adding a new task
  const handleAddTask = () => {
    setNewTask({
      name: '',
      description: '',
      assigneeId: '',
      startDate: project?.startDate || '',
      endDate: project?.endDate || '',
      status: TaskStatus.NOT_STARTED,
      priority: 'Low'
    });
    setNewTaskMode(true);
    setEditingTaskId(null);
  };

  // Handle editing a specific field of a task or subtask
  const handleEditField = (item: Task | Subtask, field: string, event: React.MouseEvent) => {
    // Prevent event bubbling to avoid conflicts
    event.preventDefault();
    event.stopPropagation();

    // Check if user has permission to edit this field
    if (!canEditField(field)) {
      showNotification('You do not have permission to edit this field', 'error');
      return;
    }

    console.log('[Edit Field] Starting edit for:', { itemId: item.id, field, itemName: item.name });

    // Set the editing state
    setEditingTaskId(item.id);
    setEditingField(field);

    // Set the current value based on the field
    switch (field) {
      case 'name':
        setEditValue(item.name);
        break;
      case 'assigneeId':
        setEditValue(item.assigneeId);
        break;
      case 'status':
        setEditValue(item.status);
        break;
      case 'startDate':
        // Convert date to YYYY-MM-DD format for date input
        setEditValue(item.startDate ? new Date(item.startDate).toISOString().split('T')[0] : '');
        break;
      case 'endDate':
        // Convert date to YYYY-MM-DD format for date input
        setEditValue(item.endDate ? new Date(item.endDate).toISOString().split('T')[0] : '');
        break;
      case 'priority':
        setEditValue(item.priority || 'Low');
        break;
      default:
        setEditValue('');
    }

    // Clear other states
    setActionMenuTask(null);

    console.log('[Edit Field] Edit state set:', {
      editingTaskId: item.id,
      editingField: field,
      editValue: field === 'name' ? item.name : 'other value'
    });
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingTaskId(null);
    setEditingField(null);
    setEditValue('');
    setNewTaskMode(false);
  };

  // DONE BUTTON APPROACH: Save changes when user is ready
  const handleDoneClick = () => {
    console.log('[Done Button] Saving changes:', {
      taskId: editingTaskId,
      field: editingField,
      newValue: editValue
    });

    // Validate name field if editing name
    if (editingField === 'name' && editValue.trim() === '') {
      console.log('[Done Button] Empty name - canceling');
      showError('Validation Error', 'Name cannot be empty');
      return;
    }

    // Save the task - this will trigger the API call
    handleUpdateTask();
  };

  const handleCancelClick = () => {
    console.log('[Cancel Button] Clicked - canceling edit');
    handleCancelEdit();
  };

  // Keyboard handler - Handle Enter and Escape keys
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      e.stopPropagation();
      console.log('[Enter] Saving changes');
      handleDoneClick();
      return;
    }

    if (e.key === 'Escape') {
      e.preventDefault();
      e.stopPropagation();
      console.log('[Escape] Canceling edit');
      handleCancelEdit();
      return;
    }

    // For all other keys, let them work normally (don't prevent default)
    // This allows normal typing, backspace, delete, arrow keys, etc.
  };

  // Handle input change events - allow normal typing, apply camelCase on save
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const newValue = e.target.value;
    console.log('[Input Change] Local value updated to:', newValue);
    // Allow normal typing - camelCase conversion will happen on save
    setEditValue(newValue);
  };

  // Handle input click to prevent event bubbling
  const handleInputClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  // Handle task/subtask deletion
  const handleDeleteTask = async (itemId: string) => {
    // Determine if this is a task or subtask based on ID pattern
    const isSubtask = itemId.includes('T') && itemId.includes('S') && itemId.indexOf('S') > itemId.indexOf('T');
    const itemType = isSubtask ? 'subtask' : 'task';

    // Confirmation dialog removed as requested
    {
      try {
        console.log(`[${itemType} Delete] Starting ${itemType} deletion:`, {
          itemId,
          projectId,
          isSubtask
        });

        // Find the actual item to get its real database ID
        let itemToDelete = null;

        if (isSubtask) {
          // Find subtask in the nested structure
          for (const task of localTasks) {
            if (task.subtasks) {
              const subtask = task.subtasks.find(sub => sub.id === itemId);
              if (subtask) {
                itemToDelete = subtask;
                break;
              }
            }
          }
        } else {
          // Find main task
          itemToDelete = localTasks.find(task => task.id === itemId);
        }

        if (!itemToDelete) {
          console.error(`[${itemType} Delete] ${itemType} not found in local state:`, itemId);
          showError('Delete Error', `${itemType} not found`);
          return;
        }

        console.log(`[${itemType} Delete] Found item to delete:`, {
          itemId,
          databaseId: itemToDelete.id,
          displayId: itemToDelete.displayId,
          name: itemToDelete.name
        });

        // Remove from local state first for immediate UI update
        let updatedTasks;
        if (isSubtask) {
          // Remove subtask from its parent task
          updatedTasks = localTasks.map(task => ({
            ...task,
            subtasks: (task.subtasks || []).filter(sub => sub.id !== itemId)
          }));
        } else {
          // Remove main task
          updatedTasks = localTasks.filter(task => task.id !== itemId);
        }
        setLocalTasks(updatedTasks);

        // Call appropriate API to delete item using the database ID
        if (isSubtask) {
          await subtasksAPI.deleteSubtask(itemToDelete.id);
          console.log('[Subtask Delete] Subtask deleted successfully');
        } else {
          await tasksAPI.deleteTask(itemToDelete.id);
          console.log('[Task Delete] Task deleted successfully');
        }

        // Refresh the project data to get the updated tasks
        if (projectId) {
          try {
            console.log('[Task Delete] Refreshing project data...', { projectId });
            const response = await projectsAPI.getProject(projectId);
            if (response.data) {
              console.log('[Task Delete] Project data refreshed successfully:', {
                projectId,
                taskCount: response.data.tasks?.length
              });
            }
          } catch (refreshError: any) {
            console.error('[Task Delete] Error refreshing project data:', {
              error: refreshError,
              message: refreshError.message,
              response: refreshError.response?.data,
              status: refreshError.response?.status,
              projectId
            });
          }
        }
      } catch (error: any) {
        console.error(`[${itemType} Delete] Error deleting ${itemType}:`, {
          error,
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
          itemId,
          projectId
        });
        // Restore tasks if API call fails
        setLocalTasks(localTasks);
        showError('Delete Failed', `Failed to delete ${itemType}: ${error.message || 'Unknown error'}`);
      }
    }
    setActionMenuTask(null);
  };

  // Handle saving a new task
  const handleSaveNewTask = async () => {
    try {
      if (!newTask.name || !newTask.startDate || !newTask.endDate) {
        showError('Validation Error', 'Please fill in all required fields');
        return;
      }

      // Prepare the new task data
      const taskData: Partial<Task> = {
        id: '', // Will be set by backend
        projectId: projectId || '',
        displayId: '', // Will be set by backend
        name: toCamelCase(newTask.name || ''),
        description: newTask.description || '',
        assigneeId: newTask.assigneeId || '',
        assigneeType: TaskAssigneeType.ENGINEER,
        department: project?.department || '',
        startDate: newTask.startDate || '',
        endDate: newTask.endDate || '',
        status: newTask.status || TaskStatus.NOT_STARTED,
        subtasks: [],
        createdBy: currentUser?.id || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        priority: newTask.priority
      };

      // Call API to create task and get the created task with its real ID
      const response = await tasksAPI.createTask(taskData);
      const createdTask = response.data;

      // Add the new task to localTasks immediately
      setLocalTasks(prevTasks => [
        ...prevTasks,
        { ...createdTask, projectName: project?.name || '' }
      ]);

      // IMPORTANT: Update the global projects store to ensure consistency
      if (projectId && project) {
        setProjects(prevProjects =>
          prevProjects.map(p =>
            p.id === projectId
              ? {
                  ...p,
                  tasks: [...(p.tasks || []), createdTask]
                }
              : p
          )
        );
        console.log('✅ Global projects store updated with new task');
      }

      // Use debounced refresh to ensure all stores are in sync
      debouncedRefreshTasks();
      console.log('✅ Data refresh initiated after task creation');

      // Reset new task mode and clear the input row
      setNewTaskMode(false);
      setNewTask({
        name: '',
        description: '',
        assigneeId: '',
        startDate: project?.startDate || '',
        endDate: project?.endDate || '',
        status: TaskStatus.NOT_STARTED,
        priority: 'Low'
      });
    } catch (error) {
      console.error('Error saving new task:', error);
    }
  };

  // Handle updating a MOM entry
  const handleMOMUpdate = async () => {
    if (!editingTaskId || !editingField) {
      console.log('[MOM Update] No MOM being edited');
      return;
    }

    // Extract MOM ID from the task ID (format: "MOM-{momId}")
    const momId = editingTaskId.replace('MOM-', '');

    // Store current editing state before clearing it
    const currentField = editingField;
    const currentValue = editValue;

    console.log('[MOM Update] Starting update for MOM:', {
      momId,
      field: currentField,
      value: currentValue
    });

    // Exit edit mode immediately
    setEditingTaskId(null);
    setEditingField(null);
    setEditValue('');

    try {
      // Find the MOM task in local state to get current data
      const momTask = localTasks.find(task => task.id === editingTaskId);
      if (!momTask) {
        console.error('[MOM Update] MOM task not found in local state');
        return;
      }

      // Get MOM data from the virtual task
      console.log('[MOM Update] Full momTask object:', momTask);
      const momData = (momTask as any).momData;
      console.log('[MOM Update] Extracted momData:', momData);

      // Try to get customerId from multiple possible sources
      let customerId = '';
      if (momData && momData.customerId) {
        customerId = momData.customerId;
        console.log('[MOM Update] Using customerId from momData:', customerId);
      } else {
        // Try to get customerId from the project
        const project = projects.find(p => p.id === momTask.projectId);
        if (project && project.customerId) {
          customerId = project.customerId;
          console.log('[MOM Update] Using customerId from project:', customerId);
        } else {
          console.error('[MOM Update] No customerId found in momData or project');
          showError('Update Error', 'Unable to update MOM: Missing customer information');
          return;
        }
      }

      // Create update payload with current MOM data and the changed field
      const updatePayload: any = {
        date: momTask.startDate, // MOM date is stored in startDate
        customerId: customerId,
        projectId: momTask.projectId,
        engineerId: momTask.assigneeId,
        agenda: currentField === 'name' ? currentValue : (momData?.agenda || momTask.name),
        points: momData?.points || '',
        attendees: momData?.attendees || [],
        actionItems: momData?.actionItems || []
      };

      // Update specific field based on what was edited
      if (currentField === 'name') {
        // Extract agenda from the MOM task name format: "Minutes of Meeting - {agenda}"
        let agendaValue = currentValue;
        if (currentValue.startsWith('Minutes of Meeting - ')) {
          agendaValue = currentValue.replace('Minutes of Meeting - ', '');
        }
        updatePayload.agenda = agendaValue;
      } else if (currentField === 'assigneeId') {
        updatePayload.engineerId = currentValue;
      } else if (currentField === 'startDate' || currentField === 'endDate') {
        updatePayload.date = currentValue;
      }

      console.log('[MOM Update] Sending update request to MOM API:', {
        momId,
        field: currentField,
        value: currentValue,
        payload: updatePayload
      });

      // Update local state first for immediate UI feedback
      const updatedLocalTasks = localTasks.map(task => {
        if (task.id === editingTaskId) {
          if (currentField === 'name') {
            // For MOM tasks, just use the agenda directly (no prefix)
            return { ...task, name: currentValue };
          } else {
            return { ...task, [currentField]: currentValue };
          }
        }
        return task;
      });
      setLocalTasks(updatedLocalTasks);

      // Send the update to the MOM API
      await momsAPI.updateMOM(momId, updatePayload);

      console.log('[MOM Update] Update completed successfully - local state updated');
    } catch (error: any) {
      console.error('[MOM Update] Error:', {
        error,
        message: error.message,
        momId,
        field: currentField,
        value: currentValue
      });
      // Restore original local state on error
      setLocalTasks(localTasks);
      showError('Update Failed', `Failed to update MOM: ${error.message || 'Unknown error'}`);
    }
  };

  // Handle updating an existing task
  const handleUpdateTask = async () => {
    if (!editingTaskId) {
      console.warn('[Task Update] No editing task ID found');
      return;
    }

    // Handle MOM entries differently - route to MOM API
    if (editingTaskId.startsWith('MOM-')) {
      console.log('[Task Update] Detected MOM entry, routing to MOM update');
      await handleMOMUpdate();
      return;
    }

    // Find the task or subtask in the local state
    let taskToUpdate = null;
    let isSubtask = false;
    let parentTask = null;

    // First, try to find it as a main task
    taskToUpdate = localTasks.find(task => task.id === editingTaskId);

    // If not found, search in subtasks
    if (!taskToUpdate) {
      for (const task of localTasks) {
        if (task.subtasks) {
          const subtask = task.subtasks.find(sub => sub.id === editingTaskId);
          if (subtask) {
            taskToUpdate = subtask;
            isSubtask = true;
            parentTask = task;
            break;
          }
        }
      }
    }

    if (!taskToUpdate) {
      console.error('[Task Update] Task/Subtask not found in local state:', {
        taskId: editingTaskId,
        availableTasks: localTasks.map(t => t.id),
        availableSubtasks: localTasks.flatMap(t => (t.subtasks || []).map(s => s.id))
      });
      return;
    }

    // Store current editing state before clearing it
    const currentTaskId = editingTaskId;
    const currentField = editingField;
    const currentValue = editValue;

    console.log('[Task Update] Starting update for task:', {
      taskId: currentTaskId,
      field: currentField,
      value: currentValue,
      originalTask: taskToUpdate,
      projectId,
      projectName: project?.name
    });

    // Exit edit mode immediately
    setEditingTaskId(null);
    setEditingField(null);
    setEditValue('');

    try {
      // Create a minimal update payload with only the changed field
      const updatePayload: Record<string, any> = {};

      // Add only the specific field that was edited, apply camelCase for name fields
      if (currentField === 'name') updatePayload.name = toCamelCase(currentValue);
      else if (currentField === 'assigneeId') updatePayload.assigneeId = currentValue;
      else if (currentField === 'status') updatePayload.status = currentValue;
      else if (currentField === 'startDate') updatePayload.startDate = currentValue;
      else if (currentField === 'endDate') updatePayload.endDate = currentValue;
      else if (currentField === 'priority') updatePayload.priority = currentValue;

      console.log('[Task Update] Sending update request to API with minimal payload:', {
        taskId: currentTaskId,
        field: currentField,
        value: currentValue,
        payload: updatePayload,
        isSubtask: isSubtask,
        parentTaskId: parentTask?.id
      });

      // Update local state first for immediate UI feedback
      let updatedLocalTasks;
      if (isSubtask && parentTask) {
        // Update subtask within its parent task
        updatedLocalTasks = localTasks.map(task => {
          if (task.id === parentTask.id) {
            return {
              ...task,
              subtasks: (task.subtasks || []).map(subtask =>
                subtask.id === currentTaskId ? { ...subtask, ...updatePayload } : subtask
              )
            };
          }
          return task;
        });
      } else {
        // Update main task
        updatedLocalTasks = localTasks.map(task =>
          task.id === currentTaskId ? { ...task, ...updatePayload } : task
        );
      }
      setLocalTasks(updatedLocalTasks);

      // Send the update to the correct API
      // Check if this is a subtask (IDs like "1234T1S1", "1234T1S2", etc.)
      if (currentTaskId.includes('T') && currentTaskId.includes('S') && currentTaskId.indexOf('S') > currentTaskId.indexOf('T')) {
        console.log('[Task Update] Detected subtask ID, using subtasks API');
        await subtasksAPI.updateSubtask(currentTaskId, updatePayload);

        // If this was a status update, update parent task status locally
        if (currentField === 'status') {
          console.log('[Task Update] Updating parent task status locally after subtask status update');

          // Find the parent task and update its status based on subtasks
          const parentTaskId = currentTaskId.split('S')[0]; // Extract parent task ID
          setLocalTasks(prevTasks => {
            return prevTasks.map(task => {
              if (task.id.includes(parentTaskId)) {
                const updatedTask = { ...task };

                // IMPORTANT: Only recalculate if parent task is not manually set to COMPLETED or ON_HOLD
                if (updatedTask.status !== TaskStatus.COMPLETED && updatedTask.status !== TaskStatus.ON_HOLD) {
                  const calculatedStatus = calculateTaskStatus(updatedTask);
                  return { ...updatedTask, status: calculatedStatus as any };
                } else {
                  // Preserve manually set status
                  console.log(`[Task Update] Preserving parent task status: ${updatedTask.status}`);
                  return updatedTask;
                }
              }
              return task;
            });
          });
        }
      } else {
        console.log('[Task Update] Detected task ID, using tasks API');
        await tasksAPI.updateTask(currentTaskId, updatePayload);
      }

      // Don't immediately refresh - let the local state update persist
      // The local state has already been updated above for immediate UI feedback
      console.log('[Task Update] Update completed successfully - local state updated');
    } catch (error: any) {
      console.error('[Task Update] Error:', {
        error,
        message: error.message,
        taskId: currentTaskId,
        field: currentField,
        value: currentValue,
        projectId
      });
      setLocalTasks(localTasks);
      showError('Update Failed', `Failed to update: ${error.message || 'Unknown error'}`);
    }
  };

  // Close action menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setActionMenuTask(null);
    };

    if (actionMenuTask) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [actionMenuTask]);

  // Toggle action menu for a task
  const toggleActionMenu = (taskId: string) => {
    setActionMenuTask(actionMenuTask === taskId ? null : taskId);
  };

  // Navigate to task view
  const handleViewTask = (taskId: string) => {
    navigate(`/tasks/${taskId}`);
  };

  // Handle opening the task modal for editing
  const handleEditTask = (task: Task) => {
    setSelectedTask(task);
    setShowTaskModal(true);
  };

  // Handle opening the task modal for adding a new task
  const handleOpenTaskModal = () => {
    setSelectedTask(null);
    setShowTaskModal(true);
  };

  // Handle task form submission
  const handleTaskSubmit = async (taskData: Task) => {
    try {
      console.log('Task form submitted:', taskData);

      let createdOrUpdatedTask: Task;

      if (taskData.id) {
        // Update existing task
        const response = await tasksAPI.updateTask(taskData.id, taskData);
        createdOrUpdatedTask = response.data;
        console.log('Task updated successfully');
      } else {
        // Create new task
        const response = await tasksAPI.createTask(taskData);
        createdOrUpdatedTask = response.data;
        console.log('Task created successfully');

        // IMPORTANT: Update the global projects store for new tasks
        if (projectId && project) {
          setProjects(prevProjects =>
            prevProjects.map(p =>
              p.id === projectId
                ? {
                    ...p,
                    tasks: [...(p.tasks || []), createdOrUpdatedTask]
                  }
                : p
            )
          );
          console.log('✅ Global projects store updated with new task from modal');
        }
      }

      // Refresh the project data to get the updated tasks and ensure consistency
      if (projectId) {
        try {
          const response = await projectsAPI.getProject(projectId);
          if (response.data) {
            console.log('Project data refreshed after task update');

            // Update local tasks with the refreshed data
            if (response.data.tasks) {
              const refreshedTasks = response.data.tasks.map((task: Task) => ({
                ...task,
                projectName: response.data.name || ''
              }));

              setLocalTasks(refreshedTasks);
            }

            // Update global projects store with fresh data
            setProjects(prevProjects =>
              prevProjects.map(p =>
                p.id === projectId ? response.data : p
              )
            );
          }
        } catch (refreshError) {
          console.error('Error refreshing project data after task update:', refreshError);
        }
      }

      // Use debounced refresh to ensure all stores are in sync
      debouncedRefreshTasks();
      console.log('✅ Data refresh initiated after task submission');

      // Close the modal
      setShowTaskModal(false);
    } catch (error) {
      console.error('Error submitting task:', error);
    }
  };

  // Handle opening the subtask modal
  const handleAddSubtask = (task: Task) => {
    setSelectedTaskForSubtask(task);
    setShowSubtaskModal(true);
  };

  // Handle submitting a new subtask from the modal
  const handleSubtaskSubmit = async (subtask: Subtask) => {
    try {
      if (!selectedTaskForSubtask) return;

      console.log('Creating subtask:', subtask);

      // Create the subtask via subtasks API
      const response = await subtasksAPI.createSubtask({
        taskId: selectedTaskForSubtask.id,
        name: subtask.name,
        description: subtask.description || '',
        assigneeId: subtask.assigneeId || '',
        assigneeType: subtask.assigneeType || SubtaskAssigneeType.ENGINEER,
        startDate: subtask.startDate,
        endDate: subtask.endDate,
        status: subtask.status,
        createdBy: currentUser?.id || ''
      });

      console.log('Subtask created successfully:', response);

      // Close the modal
      setShowSubtaskModal(false);

      // Show success message
      showSuccess('Subtask Created', 'Subtask created successfully!');

      // Immediately update local state with the new subtask
      if (response.data) {
        const newSubtask = {
          ...response.data,
          displayId: response.data.displayId || `S${(response.data as any).sequence || '1'}`,
          taskName: selectedTaskForSubtask.name,
          projectName: project?.name || ''
        };

        // Update the local tasks to include the new subtask
        setLocalTasks(prevTasks =>
          prevTasks.map(task =>
            task.id === selectedTaskForSubtask.id
              ? {
                  ...task,
                  subtasks: [...(task.subtasks || []), newSubtask]
                }
              : task
          )
        );

        // IMPORTANT: Update the global projects store with the new subtask
        if (projectId && project) {
          setProjects(prevProjects =>
            prevProjects.map(p =>
              p.id === projectId
                ? {
                    ...p,
                    tasks: (p.tasks || []).map(t =>
                      t.id === selectedTaskForSubtask.id
                        ? {
                            ...t,
                            subtasks: [...(t.subtasks || []), newSubtask]
                          }
                        : t
                    )
                  }
                : p
            )
          );
          console.log('✅ Global projects store updated with new subtask');
        }
      }

      // Use debounced refresh to ensure all stores are in sync
      debouncedRefreshTasks();
      console.log('✅ Data refresh initiated after subtask creation');

      console.log('Subtask added successfully with optimistic update');
    } catch (error) {
      console.error('Error adding subtask:', error);
    }
  };

  // Handle saving a new subtask from the inline form
  const handleSaveNewSubtask = async (parentTaskId: string) => {
    try {
      if (!newSubtask.name || !newSubtask.assigneeId || !newSubtask.startDate || !newSubtask.endDate) {
        console.error('Missing required fields for subtask creation');
        return;
      }

      // Find the parent task
      const parentTask = localTasks.find(t => t.id === parentTaskId);
      if (!parentTask) {
        console.error('Parent task not found');
        return;
      }

      console.log('Creating new subtask for task:', parentTaskId);

      // Get existing subtasks for this task to determine the next subtask number
      const existingSubtasks = parentTask.subtasks || [];
      const nextSubtaskNumber = existingSubtasks.length + 1;

      // Generate the subtask ID and displayId
      const subtaskId = `${parentTask.projectId}T${parentTask.displayId.substring(1)}S${nextSubtaskNumber}`;
      const displayId = `S${nextSubtaskNumber}`;

      // Create the subtask data
      const subtaskData = {
        id: subtaskId,
        displayId: displayId,
        taskId: parentTaskId,
        name: newSubtask.name,
        description: newSubtask.description || '',
        assigneeId: newSubtask.assigneeId,
        assigneeType: TaskAssigneeType.ENGINEER,
        startDate: newSubtask.startDate,
        endDate: newSubtask.endDate,
        status: newSubtask.status,
        createdBy: currentUser?.id || '',
        department: parentTask.department,
        priority: newSubtask.priority,
        isSubtask: true, // Flag to indicate this is a subtask
        parentTaskId: parentTaskId // Include parent task ID
      };

      console.log('Creating subtask with data:', subtaskData);

      // Create the subtask via the subtasks API
      const response = await subtasksAPI.createSubtask({
        taskId: parentTaskId,
        name: newSubtask.name,
        description: newSubtask.description || '',
        assigneeId: newSubtask.assigneeId,
        assigneeType: SubtaskAssigneeType.ENGINEER,
        startDate: newSubtask.startDate,
        endDate: newSubtask.endDate,
        status: newSubtask.status,
        createdBy: currentUser?.id || ''
      });

      if (response.data) {
        console.log('Subtask created successfully:', response.data);

        // Reset the form and hide it
        setNewSubtask({
          name: '',
          description: '',
          assigneeId: '',
          status: TaskStatus.NOT_STARTED,
          startDate: '',
          endDate: '',
          department: project?.department || '',
          priority: 'Low'
        });
        setShowNewSubtaskRow(false);
        setNewSubtaskParentId('');

        // Show success message
        showSuccess('Subtask Created', 'Subtask created successfully!');

        // Immediately update local state with the new subtask
        const newSubtask = {
          ...response.data,
          displayId: response.data.displayId || `S${(response.data as any).sequence || '1'}`,
          taskName: parentTask.name,
          projectName: project?.name || ''
        };

        // Update the local tasks to include the new subtask
        setLocalTasks(prevTasks =>
          prevTasks.map(task =>
            task.id === parentTaskId
              ? {
                  ...task,
                  subtasks: [...(task.subtasks || []), newSubtask]
                }
              : task
          )
        );

        // Update parent task status locally using our calculation logic
        setLocalTasks(prevTasks => {
          return prevTasks.map(task => {
            if (task.id === parentTaskId) {
              const updatedTask = { ...task };
              // Recalculate parent task status based on updated subtasks
              const calculatedStatus = calculateTaskStatus(updatedTask);
              return { ...updatedTask, status: calculatedStatus as any };
            }
            return task;
          });
        });

        // IMPORTANT: Update the global projects store with the new subtask
        if (projectId && project) {
          setProjects(prevProjects =>
            prevProjects.map(p =>
              p.id === projectId
                ? {
                    ...p,
                    tasks: (p.tasks || []).map(t =>
                      t.id === parentTaskId
                        ? {
                            ...t,
                            subtasks: [...(t.subtasks || []), newSubtask]
                          }
                        : t
                    )
                  }
                : p
            )
          );
          console.log('✅ Global projects store updated with new subtask');
        }

        // Use debounced refresh to ensure all stores are in sync
        debouncedRefreshTasks();
        console.log('✅ Data refresh initiated after inline subtask creation');

        console.log('Subtask added successfully with local status calculation');
      }
    } catch (error) {
      console.error('Error adding subtask:', error);
    }
  };

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Reset button click handlers
  const handleResetNewTask = () => {
    setNewTask({
      name: '',
      description: '',
      assigneeId: '',
      startDate: project?.startDate || '',
      endDate: project?.endDate || '',
      status: TaskStatus.NOT_STARTED,
      priority: 'Low'
    });
  };

  const handleResetNewSubtask = () => {
    setNewSubtask({
      name: '',
      description: '',
      assigneeId: '',
      status: TaskStatus.NOT_STARTED,
      startDate: '',
      endDate: '',
      department: project?.department || '',
      priority: 'Low'
    });
  };

  return (
    <>
      <div className={`space-y-4 ${isFullscreen ? 'fixed inset-0 bg-white z-50 p-6 overflow-auto' : ''}`}>
        {/* Fullscreen header */}
        {isFullscreen && (
          <div className="flex justify-between items-center mb-4 border-b pb-4">
            <h2 className="text-xl font-semibold">Project Tasks</h2>
            <button
              className="btn btn-sm btn-outline"
              onClick={toggleFullscreen}
              title="Exit Fullscreen"
            >
              <Minimize2 size={16} className="mr-1" />
              Exit Fullscreen
            </button>
          </div>
        )}



      {/* Enhanced Filters and search */}
      <div className="flex flex-col space-y-3 mb-4">
        {/* First row - Search and main filters */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <input
                type="text"
                placeholder="Search tasks, IDs, descriptions..."
                className="form-input pl-10 py-2 text-sm w-64"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <select
              className="form-select py-2 text-sm"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="ALL">All Statuses</option>
              <option value={TaskStatus.NOT_STARTED}>Not Started</option>
              <option value={TaskStatus.IN_PROGRESS}>In Progress</option>
              <option value={TaskStatus.COMPLETED}>Completed</option>
              <option value={TaskStatus.DELAYED}>Delayed</option>
              <option value={TaskStatus.ON_HOLD}>On Hold</option>
            </select>
          </div>
        </div>

        {/* Second row - Additional filters */}
        <div className="flex items-center space-x-2">
          <select
            className="form-select py-2 text-sm"
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
          >
            <option value="ALL">All Priorities</option>
            <option value="High">High Priority</option>
            <option value="Medium">Medium Priority</option>
            <option value="Low">Low Priority</option>
          </select>

          <select
            className="form-select py-2 text-sm"
            value={assigneeFilter}
            onChange={(e) => setAssigneeFilter(e.target.value)}
          >
            <option value="ALL">All Assignees</option>
            <option value="ME">My Tasks</option>
            {teamLeads.map(teamLead => (
              <option key={teamLead.id} value={teamLead.id}>
                {teamLead.name}
              </option>
            ))}
          </select>

          {searchTerm || statusFilter !== 'ALL' || priorityFilter !== 'ALL' || assigneeFilter !== 'ALL' ? (
            <button
              className="btn btn-sm btn-outline text-gray-600 hover:text-gray-800"
              onClick={() => {
                setSearchTerm('');
                setStatusFilter('ALL');
                setPriorityFilter('ALL');
                setAssigneeFilter('ALL');
              }}
              title="Clear all filters"
            >
              Clear Filters
            </button>
          ) : null}
        </div>

        {/* Third row - Action buttons */}
        <div className="flex justify-end items-center space-x-2">
          {!isFullscreen && (
            <button
              className="btn btn-sm btn-outline flex items-center"
              onClick={toggleFullscreen}
              title="Fullscreen"
            >
              <Maximize2 size={16} />
            </button>
          )}
        </div>
      </div>

      {/* Task table */}
      <div className="w-full overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 border-separate border-spacing-0">
          <thead className="bg-gray-100">
            <tr>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8"></th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">ID</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Task Name</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Owner</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {/* Task rows with subtasks */}
            {filteredTasks.map((task) => (
              <React.Fragment key={task.id}>
                {/* Main task row */}
                <tr className={`${task.id.startsWith('MOM-') ? 'bg-purple-50 hover:bg-purple-100' : 'hover:bg-gray-50 bg-white'} ${editingTaskId === task.id ? 'bg-blue-50' : ''} ${task.subtasks && task.subtasks.length > 0 ? 'border-b-0' : ''} font-medium`}>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                  <input type="checkbox" className="form-checkbox" />
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                  <div className="flex items-center">
                    {/* Display expand/collapse icon if task has subtasks */}
                    {task.subtasks && task.subtasks.length > 0 ? (
                      <button
                        className="mr-2 p-1 hover:bg-gray-200 rounded transition-colors"
                        onClick={() => toggleTaskExpansion(task.id)}
                        title={expandedTasks.has(task.id) ? 'Collapse subtasks' : 'Expand subtasks'}
                      >
                        {expandedTasks.has(task.id) ? (
                          <ChevronDown size={16} className="text-blue-600" />
                        ) : (
                          <ChevronRight size={16} className="text-gray-400" />
                        )}
                      </button>
                    ) : (
                      <div className="w-6 mr-2"></div> // Spacer for alignment
                    )}
                    {/* Display the task displayId */}
                    <span className="font-medium text-blue-600">
                      {task.displayId}
                    </span>
                  </div>
                </td>
                <td className="px-4 py-2 whitespace-nowrap">
                  {editingTaskId === task.id && editingField === 'name' ? (
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        className="form-input flex-1 py-1 text-sm border-2 border-blue-500 focus:border-blue-600"
                        value={editValue}
                        onChange={handleInputChange}
                        onKeyDown={handleKeyDown}
                        onClick={handleInputClick}
                        autoFocus
                        placeholder="Enter task name..."
                      />
                      <button
                        onClick={handleDoneClick}
                        className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center"
                        title="Save changes"
                      >
                        <Check size={14} />
                      </button>
                      <button
                        onClick={handleCancelClick}
                        className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center"
                        title="Cancel changes"
                      >
                        <XCircle size={14} />
                      </button>
                    </div>
                  ) : (
                    <div
                      className={`text-sm font-medium cursor-pointer hover:text-primary ${task.id.startsWith('MOM-') ? 'text-purple-700' : 'text-gray-900'}`}
                      onClick={(e) => handleEditField(task, 'name', e)}
                      data-task-id={task.id}
                      data-field="name"
                      title={task.id.startsWith('MOM-') ? 'Click to edit MOM agenda' : 'Click to edit task name'}
                    >
                      {task.name}
                    </div>
                  )}
                </td>
                <td className="px-4 py-2 whitespace-nowrap">
                  {editingTaskId === task.id && editingField === 'assigneeId' ? (
                    <div className="flex items-center space-x-2">
                      <select
                        className="form-select flex-1 py-1 text-sm border-2 border-blue-500 focus:border-blue-600"
                        value={editValue}
                        onChange={handleInputChange}
                        onKeyDown={handleKeyDown}
                        onClick={handleInputClick}
                        autoFocus
                      >
                        <option value="">Select Team Lead</option>
                        {teamLeads.map(teamLead => (
                          <option key={teamLead.id} value={teamLead.id}>
                            {teamLead.name}
                          </option>
                        ))}
                      </select>
                      <button
                        onClick={handleDoneClick}
                        className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center justify-center"
                        title="Save changes"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M20 6 9 17l-5-5"/>
                        </svg>
                      </button>
                      <button
                        onClick={handleCancelClick}
                        className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center justify-center"
                        title="Cancel changes"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M18 6 6 18"/>
                          <path d="M6 6l12 12"/>
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <div
                      className="text-sm text-gray-500 cursor-pointer hover:text-primary"
                      onClick={(e) => handleEditField(task, 'assigneeId', e)}
                      data-task-id={task.id}
                      data-field="assigneeId"
                    >
                      {getTeamLeadName(task.assigneeId)}
                    </div>
                  )}
                </td>
                <td className="px-4 py-2 whitespace-nowrap">
                  {editingTaskId === task.id && editingField === 'status' ? (
                    <div className="flex items-center space-x-2">
                      <select
                        className="form-select flex-1 py-1 text-sm border-2 border-blue-500 focus:border-blue-600"
                        value={editValue}
                        onChange={handleInputChange}
                        onKeyDown={handleKeyDown}
                        onClick={handleInputClick}
                        autoFocus
                      >
                        {Object.values(TaskStatus).map(status => (
                          <option key={status} value={status}>
                            {status.replace(/_/g, ' ')}
                          </option>
                        ))}
                      </select>
                      <button
                        onClick={handleDoneClick}
                        className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center"
                        title="Save changes"
                      >
                        <Check size={14} />
                      </button>
                      <button
                        onClick={handleCancelClick}
                        className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center"
                        title="Cancel changes"
                      >
                        <XCircle size={14} />
                      </button>
                    </div>
                  ) : (
                    <div
                      className="cursor-pointer"
                      onClick={(e) => handleEditField(task, 'status', e)}
                      data-task-id={task.id}
                      data-field="status"
                    >
                      {getStatusBadge(task.status)}
                    </div>
                  )}
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                  {editingTaskId === task.id && editingField === 'startDate' ? (
                    <div className="flex items-center justify-between space-x-2">
                      <input
                        type="date"
                        className="form-input flex-1 py-1 text-sm border-2 border-blue-500 focus:border-blue-600"
                        value={editValue}
                        min={project?.startDate}
                        max={project?.endDate}
                        onChange={handleInputChange}
                        onKeyDown={handleKeyDown}
                        onClick={handleInputClick}
                        autoFocus
                      />
                      <div className="flex space-x-2 ml-auto">
                        <button
                          onClick={handleDoneClick}
                          className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center justify-center"
                          title="Save changes"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M20 6 9 17l-5-5"/>
                          </svg>
                        </button>
                        <button
                          onClick={handleCancelClick}
                          className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center justify-center"
                          title="Cancel changes"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M18 6 6 18"/>
                            <path d="M6 6l12 12"/>
                          </svg>
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="cursor-pointer hover:text-primary"
                      onClick={(e) => handleEditField(task, 'startDate', e)}
                      data-task-id={task.id}
                      data-field="startDate"
                    >
                      {new Date(task.startDate).toLocaleDateString()}
                    </div>
                  )}
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                  {editingTaskId === task.id && editingField === 'endDate' ? (
                    <div className="flex items-center justify-between space-x-2">
                      <input
                        type="date"
                        className="form-input flex-1 py-1 text-sm border-2 border-blue-500 focus:border-blue-600"
                        value={editValue}
                        min={project?.startDate}
                        max={project?.endDate}
                        onChange={handleInputChange}
                        onKeyDown={handleKeyDown}
                        onClick={handleInputClick}
                        autoFocus
                      />
                      <div className="flex space-x-2 ml-auto">
                        <button
                          onClick={handleDoneClick}
                          className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center justify-center"
                          title="Save changes"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M20 6 9 17l-5-5"/>
                          </svg>
                        </button>
                        <button
                          onClick={handleCancelClick}
                          className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center justify-center"
                          title="Cancel changes"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M18 6 6 18"/>
                            <path d="M6 6l12 12"/>
                          </svg>
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="cursor-pointer hover:text-primary"
                      onClick={(e) => handleEditField(task, 'endDate', e)}
                      data-task-id={task.id}
                      data-field="endDate"
                    >
                      {new Date(task.endDate).toLocaleDateString()}
                    </div>
                  )}
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                  {calculateDuration(task.startDate, task.endDate)}
                </td>
                <td className="px-4 py-2 whitespace-nowrap">
                  {editingTaskId === task.id && editingField === 'priority' ? (
                    <div className="flex items-center justify-between space-x-2">
                      <select
                        className="form-select flex-1 py-1 text-sm border-2 border-blue-500 focus:border-blue-600"
                        value={editValue}
                        onChange={handleInputChange}
                        onKeyDown={handleKeyDown}
                        onClick={handleInputClick}
                        autoFocus
                      >
                        <option value="High">High</option>
                        <option value="Medium">Medium</option>
                        <option value="Low">Low</option>
                      </select>
                      <div className="flex space-x-2 ml-auto">
                        <button
                          onClick={handleDoneClick}
                          className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center justify-center"
                          title="Save changes"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M20 6 9 17l-5-5"/>
                          </svg>
                        </button>
                        <button
                          onClick={handleCancelClick}
                          className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center justify-center"
                          title="Cancel changes"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M18 6 6 18"/>
                            <path d="M6 6l12 12"/>
                          </svg>
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="cursor-pointer"
                      onClick={(e) => handleEditField(task, 'priority', e)}
                      data-task-id={task.id}
                      data-field="priority"
                    >
                      {task.priority || 'Low'}
                    </div>
                  )}
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end space-x-2">
                    {/* Actions menu */}
                    {editingTaskId === task.id ? (
                      <button
                        className="text-success hover:text-success-dark"
                        onClick={() => setEditingTaskId(null)}
                      >
                        <CheckSquare size={16} />
                      </button>
                    ) : canManageTasks() && (
                      <div className="relative">
                        <button
                          className="text-gray-400 hover:text-gray-500"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleActionMenu(task.id);
                          }}
                        >
                          <MoreVertical size={16} />
                        </button>

                        {actionMenuTask === task.id && (
                          <div
                            className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div className="py-1">
                              <button
                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                onClick={() => handleViewTask(task.id)}
                              >
                                <MessageSquare size={16} className="mr-2" />
                                View Comments
                              </button>
                              <button
                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                onClick={(e) => handleEditTask(task)}
                              >
                                <Edit size={16} className="mr-2" />
                                Edit Task
                              </button>
                              <button
                                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                onClick={() => {
                                  setNewSubtaskParentId(task.id);
                                  setShowNewSubtaskRow(true);
                                  setActionMenuTask(null);
                                }}
                              >
                                <Plus size={16} className="mr-2" />
                                Add Subtask
                              </button>
                              <button
                                className="flex items-center w-full px-4 py-2 text-sm text-error hover:bg-gray-100"
                                onClick={() => handleDeleteTask(task.id)}
                              >
                                <Trash2 size={16} className="mr-2" />
                                Delete Task
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </td>
              </tr>

                {/* New Subtask Row - only show when parent task is expanded */}
                {showNewSubtaskRow && newSubtaskParentId === task.id && expandedTasks.has(task.id) && (
                  <tr className="bg-green-50 hover:bg-green-100 border-t border-dashed border-green-200">
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      <input type="checkbox" className="form-checkbox" disabled />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      {/* Hierarchical tree structure for new subtask */}
                      <div className="flex items-center pl-6">
                        <div className="flex items-center mr-2">
                          {/* Tree connector lines */}
                          <div className="border-l-2 border-gray-300 h-4 mr-2"></div>
                          <div className="border-b-2 border-gray-300 w-4 mr-2"></div>
                        </div>
                        <span className="text-green-600 font-medium">New</span>
                      </div>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap" style={{ minWidth: 200 }}>
                      <div className="pl-8" style={{ width: '100%' }}>
                        <input
                          type="text"
                          className="form-input w-full"
                          style={{ minWidth: 200 }}
                          placeholder="Enter subtask name"
                          value={newSubtask.name}
                          onChange={(e) => setNewSubtask({...newSubtask, name: e.target.value})}
                        />
                      </div>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <select
                        className="form-select w-full py-1 text-sm"
                        value={newSubtask.assigneeId}
                        onChange={(e) => setNewSubtask({...newSubtask, assigneeId: e.target.value})}
                      >
                        <option value="">Select Engineer</option>
                        {engineersOnly.map(engineer => (
                          <option key={engineer.id} value={engineer.id}>
                            {engineer.name} ({engineer.department})
                          </option>
                        ))}
                      </select>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <select
                        className="form-select w-full py-1 text-sm"
                        value={newSubtask.status}
                        onChange={(e) => setNewSubtask({...newSubtask, status: e.target.value as TaskStatus})}
                      >
                        {Object.values(TaskStatus).map(status => (
                          <option key={status} value={status}>
                            {status.replace(/_/g, ' ')}
                          </option>
                        ))}
                      </select>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      <input
                        type="date"
                        className="form-input w-full py-1 text-sm"
                        value={newSubtask.startDate}
                        min={new Date().toISOString().split('T')[0]}
                        onChange={(e) => setNewSubtask({...newSubtask, startDate: e.target.value})}
                      />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      <input
                        type="date"
                        className="form-input w-full py-1 text-sm"
                        value={newSubtask.endDate}
                        min={newSubtask.startDate || new Date().toISOString().split('T')[0]}
                        onChange={(e) => setNewSubtask({...newSubtask, endDate: e.target.value})}
                      />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      {newSubtask.startDate && newSubtask.endDate ?
                        calculateDuration(newSubtask.startDate, newSubtask.endDate) : '-'}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      <select
                        className="form-select w-full py-1 text-sm"
                        value={newSubtask.priority || 'Low'}
                        onChange={(e) => setNewSubtask({ ...newSubtask, priority: e.target.value })}
                      >
                        <option value="High">High</option>
                        <option value="Medium">Medium</option>
                        <option value="Low">Low</option>
                      </select>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          className="text-success hover:text-success-dark"
                          onClick={() => handleSaveNewSubtask(task.id)}
                          title="Save"
                          disabled={!newSubtask.name || !newSubtask.assigneeId || !newSubtask.startDate || !newSubtask.endDate}
                        >
                          <CheckSquare size={16} />
                        </button>
                        <button
                          className="text-error hover:text-error-dark"
                          onClick={() => {
                            setShowNewSubtaskRow(false);
                            setNewSubtask(initialNewSubtaskState);
                          }}
                          title="Cancel"
                        >
                          <X size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                )}

                {/* Subtask rows - only show when parent task is expanded */}
                {task.subtasks && task.subtasks.length > 0 && expandedTasks.has(task.id) && task.subtasks.map((subtask) => (
                  <tr key={subtask.id} className="bg-gray-50 hover:bg-gray-100 border-t border-gray-100">
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      <input type="checkbox" className="form-checkbox" />
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center pl-6">
                        <div className="flex items-center mr-2">
                          {/* Tree connector lines */}
                          <div className="border-l-2 border-gray-300 h-4 mr-2"></div>
                          <div className="border-b-2 border-gray-300 w-4 mr-2"></div>
                        </div>
                        <span className="text-blue-600 font-medium">{subtask.displayId}</span>
                      </div>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      {editingTaskId === subtask.id && editingField === 'name' ? (
                        <div className="flex items-center space-x-2 pl-8">
                          <input
                            type="text"
                            className="form-input flex-1 py-1 text-sm border-2 border-blue-500 focus:border-blue-600"
                            value={editValue}
                            onChange={handleInputChange}
                            onKeyDown={handleKeyDown}
                            autoFocus
                            placeholder="Enter subtask name..."
                          />
                          <button
                            onClick={handleDoneClick}
                            className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center"
                            title="Save changes"
                          >
                            <Check size={14} />
                          </button>
                          <button
                            onClick={handleCancelClick}
                            className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center"
                            title="Cancel changes"
                          >
                            <XCircle size={14} />
                          </button>
                        </div>
                      ) : (
                        <div
                          className="text-sm font-medium text-gray-700 cursor-pointer hover:text-primary pl-8"
                          onClick={(e) => handleEditField(subtask, 'name', e)}
                          data-task-id={subtask.id}
                          data-field="name"
                        >
                          {subtask.name}
                        </div>
                      )}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      {editingTaskId === subtask.id && editingField === 'assigneeId' ? (
                        <div className="flex items-center justify-between space-x-2">
                          <select
                            className="form-select flex-1 py-1 text-sm border-2 border-blue-500 focus:border-blue-600"
                            value={editValue}
                            onChange={(e) => setEditValue(e.target.value)}
                            onKeyDown={handleKeyDown}
                            autoFocus
                          >
                            <option value="">Select Engineer</option>
                            {engineersOnly.map(engineer => (
                              <option key={engineer.id} value={engineer.id}>
                                {engineer.name} ({engineer.department})
                              </option>
                            ))}
                          </select>
                          <div className="flex space-x-2 ml-auto">
                            <button
                              onClick={handleDoneClick}
                              className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center justify-center"
                              title="Save changes"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M20 6 9 17l-5-5"/>
                              </svg>
                            </button>
                            <button
                              onClick={handleCancelClick}
                              className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center justify-center"
                              title="Cancel changes"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M18 6 6 18"/>
                                <path d="M6 6l12 12"/>
                              </svg>
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div
                          className="text-sm text-gray-500 cursor-pointer hover:text-primary"
                          onClick={(e) => handleEditField(subtask, 'assigneeId', e)}
                          data-task-id={subtask.id}
                          data-field="assigneeId"
                        >
                          {getEngineerName(subtask.assigneeId)}
                        </div>
                      )}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      {editingTaskId === subtask.id && editingField === 'status' ? (
                        <div className="flex items-center space-x-2">
                          <select
                            className="form-select flex-1 py-1 text-sm border-2 border-blue-500 focus:border-blue-600"
                            value={editValue}
                            onChange={handleInputChange}
                            onKeyDown={handleKeyDown}
                            onClick={handleInputClick}
                            autoFocus
                          >
                            {Object.values(TaskStatus).map(status => (
                              <option key={status} value={status}>
                                {status.replace(/_/g, ' ')}
                              </option>
                            ))}
                          </select>
                          <button
                            onClick={handleDoneClick}
                            className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center"
                            title="Save changes"
                          >
                            <Check size={14} />
                          </button>
                          <button
                            onClick={handleCancelClick}
                            className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center"
                            title="Cancel changes"
                          >
                            <XCircle size={14} />
                          </button>
                        </div>
                      ) : (
                        <div
                          className="cursor-pointer"
                          onClick={(e) => handleEditField(subtask, 'status', e)}
                          data-task-id={subtask.id}
                          data-field="status"
                        >
                          {getStatusBadge(subtask.status)}
                        </div>
                      )}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      {editingTaskId === subtask.id && editingField === 'startDate' ? (
                        <div className="flex items-center justify-between space-x-2">
                          <input
                            type="date"
                            className="form-input flex-1 py-1 text-sm border-2 border-blue-500 focus:border-blue-600"
                            value={editValue}
                            min={project?.startDate}
                            max={project?.endDate}
                            onChange={handleInputChange}
                            onKeyDown={handleKeyDown}
                            onClick={handleInputClick}
                            autoFocus
                          />
                          <div className="flex space-x-2 ml-auto">
                            <button
                              onClick={handleDoneClick}
                              className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center justify-center"
                              title="Save changes"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M20 6 9 17l-5-5"/>
                              </svg>
                            </button>
                            <button
                              onClick={handleCancelClick}
                              className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center justify-center"
                              title="Cancel changes"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M18 6 6 18"/>
                                <path d="M6 6l12 12"/>
                              </svg>
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div
                          className="cursor-pointer hover:text-primary"
                          onClick={(e) => handleEditField(subtask, 'startDate', e)}
                          data-task-id={subtask.id}
                          data-field="startDate"
                        >
                          {new Date(subtask.startDate).toLocaleDateString()}
                        </div>
                      )}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      {editingTaskId === subtask.id && editingField === 'endDate' ? (
                        <div className="flex items-center justify-between space-x-2">
                          <input
                            type="date"
                            className="form-input flex-1 py-1 text-sm border-2 border-blue-500 focus:border-blue-600"
                            value={editValue}
                            min={project?.startDate}
                            max={project?.endDate}
                            onChange={handleInputChange}
                            onKeyDown={handleKeyDown}
                            onClick={handleInputClick}
                            autoFocus
                          />
                          <div className="flex space-x-2 ml-auto">
                            <button
                              onClick={handleDoneClick}
                              className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center justify-center"
                              title="Save changes"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M20 6 9 17l-5-5"/>
                              </svg>
                            </button>
                            <button
                              onClick={handleCancelClick}
                              className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center justify-center"
                              title="Cancel changes"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M18 6 6 18"/>
                                <path d="M6 6l12 12"/>
                              </svg>
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div
                          className="cursor-pointer hover:text-primary"
                          onClick={(e) => handleEditField(subtask, 'endDate', e)}
                          data-task-id={subtask.id}
                          data-field="endDate"
                        >
                          {new Date(subtask.endDate).toLocaleDateString()}
                        </div>
                      )}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      {calculateDuration(subtask.startDate, subtask.endDate)}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap">
                      {editingTaskId === subtask.id && editingField === 'priority' ? (
                        <div className="flex items-center justify-between space-x-2">
                          <select
                            className="form-select flex-1 py-1 text-sm border-2 border-blue-500 focus:border-blue-600"
                            value={editValue}
                            onChange={handleInputChange}
                            onKeyDown={handleKeyDown}
                            onClick={handleInputClick}
                            autoFocus
                          >
                            <option value="High">High</option>
                            <option value="Medium">Medium</option>
                            <option value="Low">Low</option>
                          </select>
                          <div className="flex space-x-2 ml-auto">
                            <button
                              onClick={handleDoneClick}
                              className="px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700 flex items-center justify-center"
                              title="Save changes"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M20 6 9 17l-5-5"/>
                              </svg>
                            </button>
                            <button
                              onClick={handleCancelClick}
                              className="px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 flex items-center justify-center"
                              title="Cancel changes"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M18 6 6 18"/>
                                <path d="M6 6l12 12"/>
                              </svg>
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div
                          className="cursor-pointer"
                          onClick={(e) => handleEditField(subtask, 'priority', e)}
                          data-task-id={subtask.id}
                          data-field="priority"
                        >
                          {subtask.priority || 'Low'}
                        </div>
                      )}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        {/* Actions menu */}
                        {canManageTasks() && (
                          <div className="relative">
                            <button
                              className="text-gray-400 hover:text-gray-500"
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleActionMenu(subtask.id);
                              }}
                            >
                              <MoreVertical size={16} />
                            </button>

                            {actionMenuTask === subtask.id && (
                              <div
                                className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <div className="py-1">
                                  <button
                                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    onClick={() => navigate(`/subtasks/${subtask.id}`)}
                                  >
                                    <MessageSquare size={16} className="mr-2" />
                                    View Comments
                                  </button>
                                  <button
                                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    onClick={(e) => handleEditField(subtask, 'name', e as React.MouseEvent)}
                                  >
                                    <Edit size={16} className="mr-2" />
                                    Edit Subtask
                                  </button>
                                  <button
                                    className="flex items-center w-full px-4 py-2 text-sm text-error hover:bg-gray-100"
                                    onClick={() => handleDeleteTask(subtask.id)}
                                  >
                                    <Trash2 size={16} className="mr-2" />
                                    Delete Subtask
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </React.Fragment>
            ))}
            {/* Add New Task Row */}
            {canManageTasks() && projectId && (
              <tr className="bg-blue-50 hover:bg-blue-100 border-t-2 border-dashed border-blue-200">
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                  <input type="checkbox" className="form-checkbox" disabled />
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                  <span className="text-blue-500 font-medium">New</span>
                </td>
                <td className="px-4 py-2 whitespace-nowrap">
                  <input
                    type="text"
                    className="form-input w-full py-1 text-sm"
                    placeholder="Enter task name"
                    value={newTask.name}
                    onChange={(e) => setNewTask({...newTask, name: e.target.value})}
                  />
                </td>
                <td className="px-4 py-2 whitespace-nowrap">
                  <select
                    className="form-select w-full py-1 text-sm"
                    value={newTask.assigneeId}
                    onChange={(e) => setNewTask({...newTask, assigneeId: e.target.value})}
                  >
                    <option value="">Select Team Lead</option>
                    {teamLeads.map(teamLead => (
                      <option key={teamLead.id} value={teamLead.id}>
                        {teamLead.name} ({teamLead.department})
                      </option>
                    ))}
                  </select>
                </td>
                <td className="px-4 py-2 whitespace-nowrap">
                  <select
                    className="form-select w-full py-1 text-sm"
                    value={newTask.status}
                    onChange={(e) => setNewTask({...newTask, status: e.target.value as TaskStatus})}
                  >
                    {Object.values(TaskStatus).map(status => (
                      <option key={status} value={status}>
                        {status.replace(/_/g, ' ')}
                      </option>
                    ))}
                  </select>
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                  <input
                    type="date"
                    className="form-input w-full py-1 text-sm"
                    value={newTask.startDate}
                    min={project?.startDate}
                    max={project?.endDate}
                    onChange={(e) => setNewTask({...newTask, startDate: e.target.value})}
                  />
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                  <input
                    type="date"
                    className="form-input w-full py-1 text-sm"
                    value={newTask.endDate}
                    min={project?.startDate}
                    max={project?.endDate}
                    onChange={(e) => setNewTask({...newTask, endDate: e.target.value})}
                  />
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                  {newTask.startDate && newTask.endDate ?
                    calculateDuration(newTask.startDate, newTask.endDate) : '-'}
                </td>
                <td className="px-4 py-2 whitespace-nowrap">
                  <select
                    className="form-select w-full py-1 text-sm"
                    value={newTask.priority || 'Low'}
                    onChange={(e) => setNewTask({ ...newTask, priority: e.target.value })}
                  >
                    <option value="High">High</option>
                    <option value="Medium">Medium</option>
                    <option value="Low">Low</option>
                  </select>
                </td>
                <td className="px-4 py-2 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      className="text-success hover:text-success-dark"
                      onClick={handleSaveNewTask}
                      title="Save"
                      disabled={!newTask.name || !newTask.assigneeId || !newTask.startDate || !newTask.endDate}
                    >
                      <CheckSquare size={16} />
                    </button>
                    <button
                      className="text-gray-400 hover:text-gray-500"
                      onClick={handleResetNewTask}
                      title="Reset"
                    >
                      <RefreshCw size={16} />
                    </button>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      </div>

      {/* Task form modal */}
      {showTaskModal && (
        <TaskFormModal
          isOpen={showTaskModal}
          onClose={() => setShowTaskModal(false)}
          onSubmit={handleTaskSubmit}
          projectId={projectId || ''}
          task={selectedTask || undefined}
          title={selectedTask ? 'Edit Task' : 'Add Task'}
        />
      )}

      {/* Subtask form modal */}
      {showSubtaskModal && selectedTaskForSubtask && (
        <SubtaskFormModal
          isOpen={showSubtaskModal}
          onClose={() => setShowSubtaskModal(false)}
          onSubmit={handleSubtaskSubmit}
          parentTask={selectedTaskForSubtask}
          title="Add Subtask"
        />
      )}
    </>
  );
};

export default TaskTable;
