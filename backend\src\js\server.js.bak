const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { PrismaClient } = require('@prisma/client');
const logger = require('./utils/logger');

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 5002;

// Initialize Prisma client
const prisma = new PrismaClient();

// Set log level based on environment
if (process.env.NODE_ENV === 'production') {
  logger.setLogLevel('WARNING'); // Only log warnings and errors in production
  logger.setConsoleOutput(false); // Disable console output in production
} else {
  logger.setLogLevel('INFO'); // Log info and above in development

  // Check if we should enable console output (default: false)
  const enableConsole = process.env.ENABLE_CONSOLE_LOGS === 'true';
  logger.setConsoleOutput(enableConsole);

  if (!enableConsole) {
    console.log('Console logging disabled. All logs are being written to log files.');
    console.log('To enable console logs, set ENABLE_CONSOLE_LOGS=true');
  }
}

logger.info('SYSTEM', 'Server starting up');

// Middleware
app.use(cors({
  origin: 'http://localhost:3004', // Vite default port
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();

  // Log the request
  logger.info('API', `${req.method} ${req.originalUrl}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // Log the response when it's sent
  res.on('finish', () => {
    const duration = Date.now() - start;
    const message = `${req.method} ${req.originalUrl} ${res.statusCode} ${duration}ms`;

    if (res.statusCode >= 500) {
      logger.error('API', message);
    } else if (res.statusCode >= 400) {
      logger.warning('API', message);
    } else {
      logger.info('API', message, { skipDuplicateCheck: true });
    }
  });

  next();
});

// Root route
app.get('/', (req, res) => {
  res.send('Project Management API is running');
});

// Auth routes
app.post('/api/auth/register', async (req, res) => {
  try {
    const { email, password, name, role, department } = req.body;

    // Check if user already exists
    const userExists = await prisma.user.findUnique({
      where: { email },
    });

    if (userExists) {
      return res.status(400).json({
        success: false,
        message: 'User already exists',
      });
    }

    // Create user (in a real app, you would hash the password)
    const user = await prisma.user.create({
      data: {
        email,
        password, // In a real app, this would be hashed
        name,
        role,
        department,
      },
    });

    res.status(201).json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department,
      },
    });
  } catch (error) {
    logger.error('AUTH', 'Register error', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
      });
    }

    // For simplicity in development, we'll accept plain text password comparison
    // In production, you should use bcrypt.compare(password, user.password)
    if (password !== 'password') { // Hardcoded for testing
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
      });
    }

    // In a real app, you would generate a JWT token
    // For now, we'll just use the user ID as the token for simplicity
    const token = user.id;

    res.status(200).json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department,
        profileImage: user.profileImage,
        token: token,
      },
    });
  } catch (error) {
    logger.error('AUTH', 'Login error', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Auth - Get current user
app.get('/api/auth/me', async (req, res) => {
  try {
    // In a real app, you would get the user ID from the JWT token
    // For now, we'll just use a mock token validation
    const authHeader = req.headers.authorization;

    // For development, we'll be more lenient with auth
    let userId;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.warn('Auth header missing or invalid, using fallback user');
      // Fallback to a default user for testing
      const defaultUser = await prisma.user.findFirst({
        where: { email: '<EMAIL>' }
      });

      if (defaultUser) {
        userId = defaultUser.id;
      } else {
        return res.status(401).json({
          success: false,
          message: 'Not authorized and no fallback user found',
        });
      }
    } else {
      // Get the token
      const token = authHeader.split(' ')[1];

      // In a real app, you would verify the token and extract the user ID
      // For now, we'll just use a mock user ID
      userId = token; // Assuming the token is the user ID for simplicity
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        department: true,
        profileImage: true,
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error('Get current user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// User routes
app.get('/api/users', async (req, res) => {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        department: true,
        profileImage: true,
        createdAt: true,
      },
    });

    res.status(200).json({
      success: true,
      count: users.length,
      data: users,
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Add a simple auth middleware for development
const simpleAuth = async (req, res, next) => {
  // For development, we'll skip strict auth checks
  next();
};

// Department routes
app.get('/api/departments', simpleAuth, async (req, res) => {
  try {
    const departments = await prisma.department.findMany();

    res.status(200).json({
      success: true,
      count: departments.length,
      data: departments,
    });
  } catch (error) {
    console.error('Get departments error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Engineers routes (now using User model with any role)
app.get('/api/engineers', simpleAuth, async (req, res) => {
  try {
    // Get all users with their engineer-specific fields
    const engineers = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        department: true,
        role: true,
        code: true,
        skills: true,
        joinDate: true,
        profileImage: true,
      },
    });

    // Process skills field to convert from string to array
    const processedEngineers = engineers.map(engineer => ({
      ...engineer,
      // Convert skills string to array if it exists
      skills: engineer.skills ? engineer.skills.split(',') : [],
      // Format joinDate if needed
      joinDate: engineer.joinDate ? engineer.joinDate.toISOString().split('T')[0] : null
    }));

    res.status(200).json({
      success: true,
      count: processedEngineers.length,
      data: processedEngineers,
    });
  } catch (error) {
    console.error('Get engineers error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Project routes
app.get('/api/projects', simpleAuth, async (req, res) => {
  try {
    const projects = await prisma.project.findMany({
      include: {
        tasks: true,
      },
    });

    res.status(200).json({
      success: true,
      count: projects.length,
      data: projects,
    });
  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.get('/api/projects/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const project = await prisma.project.findUnique({
      where: { id },
      include: {
        tasks: {
          include: {
            subtasks: true,
          },
        },
      },
    });

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found',
      });
    }

    res.status(200).json({
      success: true,
      data: project,
    });
  } catch (error) {
    console.error('Get project error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Create a new project
app.post('/api/projects', simpleAuth, async (req, res) => {
  try {
    const {
      id,
      name,
      code,
      customerId,
      poNumber,
      poDate,
      startDate,
      endDate,
      department,
      status,
      createdBy,
      createdAt
    } = req.body;

    logger.info('API', `Creating new project: ${name}`, { skipDuplicateCheck: true });

    // Format dates properly for Prisma
    // If date is in YYYY-MM-DD format, convert it to ISO string
    const formatDate = (dateStr) => {
      if (!dateStr) return null;
      // Check if it's already an ISO string
      if (dateStr.includes('T')) return dateStr;

      // Convert YYYY-MM-DD to ISO string
      const date = new Date(dateStr);
      return date.toISOString();
    };

    // Create the project
    const project = await prisma.project.create({
      data: {
        id,
        name,
        code,
        customerId,
        poNumber,
        poDate: formatDate(poDate),
        startDate: formatDate(startDate),
        endDate: formatDate(endDate),
        department,
        status,
        createdBy,
        createdAt: createdAt || new Date().toISOString()
      },
    });

    logger.success('API', `Project created successfully: ${name}`, {
      entityId: project.id,
      entityName: project.name,
      skipDuplicateCheck: true
    });

    res.status(201).json({
      success: true,
      data: project,
    });
  } catch (error) {
    logger.error('API', 'Create project error', {
      details: error.message,
      skipDuplicateCheck: true
    });
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Task routes
app.get('/api/tasks', simpleAuth, async (req, res) => {
  try {
    const tasks = await prisma.task.findMany({
      include: {
        subtasks: true,
        project: {
          select: {
            name: true,
            code: true,
          },
        },
      },
    });

    res.status(200).json({
      success: true,
      count: tasks.length,
      data: tasks,
    });
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.get('/api/tasks/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const task = await prisma.task.findUnique({
      where: { id },
      include: {
        subtasks: true,
        project: {
          select: {
            name: true,
            code: true,
            customer: true,
          },
        },
      },
    });

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found',
      });
    }

    res.status(200).json({
      success: true,
      data: task,
    });
  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Create a new task
app.post('/api/tasks', simpleAuth, async (req, res) => {
  try {
    const {
      id,
      projectId,
      name,
      description,
      assigneeId,
      assigneeType,
      department,
      startDate,
      endDate,
      status,
      subtasks,
      createdBy,
      createdAt
    } = req.body;

    logger.info('API', `Creating new task: ${name}`, { skipDuplicateCheck: true });

    // Format dates properly for Prisma
    // If date is in YYYY-MM-DD format, convert it to ISO string
    const formatDate = (dateStr) => {
      if (!dateStr) return null;
      // Check if it's already an ISO string
      if (dateStr.includes('T')) return dateStr;

      // Convert YYYY-MM-DD to ISO string
      const date = new Date(dateStr);
      return date.toISOString();
    };

    // Create the task
    const task = await prisma.task.create({
      data: {
        id,
        projectId,
        name,
        description,
        assigneeId,
        assigneeType,
        department,
        startDate: formatDate(startDate),
        endDate: formatDate(endDate),
        status,
        createdBy,
        createdAt: createdAt || new Date().toISOString()
      },
    });

    // Create subtasks if provided
    if (subtasks && subtasks.length > 0) {
      for (const subtask of subtasks) {
        await prisma.subtask.create({
          data: {
            id: subtask.id,
            taskId: task.id,
            name: subtask.name,
            description: subtask.description,
            assigneeId: subtask.assigneeId,
            assigneeType: subtask.assigneeType,
            startDate: formatDate(subtask.startDate),
            endDate: formatDate(subtask.endDate),
            status: subtask.status,
            totalTime: subtask.totalTime || 0,
            createdBy: subtask.createdBy || createdBy,
            createdAt: subtask.createdAt || createdAt
          }
        });
      }
    }

    logger.success('API', `Task created successfully: ${name}`, {
      entityId: task.id,
      entityName: task.name,
      skipDuplicateCheck: true
    });

    res.status(201).json({
      success: true,
      data: task,
    });
  } catch (error) {
    logger.error('API', 'Create task error', {
      details: error.message,
      skipDuplicateCheck: true
    });
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Customer routes
app.get('/api/customers', simpleAuth, async (req, res) => {
  try {
    const customers = await prisma.customer.findMany({
      include: {
        _count: {
          select: {
            projects: true,
            moms: true,
          },
        },
      },
    });

    res.status(200).json({
      success: true,
      count: customers.length,
      data: customers,
    });
  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.get('/api/customers/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        projects: true,
        moms: true,
      },
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    res.status(200).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('Get customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.post('/api/customers', simpleAuth, async (req, res) => {
  try {
    const { name, code, contactName, email, phone, address } = req.body;

    const customer = await prisma.customer.create({
      data: {
        name,
        code,
        contactName,
        email,
        phone,
        address,
      },
    });

    res.status(201).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('Create customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.put('/api/customers/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, contactName, email, phone, address } = req.body;

    const customer = await prisma.customer.update({
      where: { id },
      data: {
        name,
        code,
        contactName,
        email,
        phone,
        address,
      },
    });

    res.status(200).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('Update customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.delete('/api/customers/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if customer has related projects or MOMs
    const customerWithRelations = await prisma.customer.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            projects: true,
            moms: true,
          },
        },
      },
    });

    if (customerWithRelations._count.projects > 0 || customerWithRelations._count.moms > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete customer with associated projects or meetings',
      });
    }

    await prisma.customer.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: 'Customer deleted successfully',
    });
  } catch (error) {
    console.error('Delete customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// MOM routes
app.get('/api/moms', simpleAuth, async (req, res) => {
  try {
    const moms = await prisma.mOM.findMany({
      include: {
        attendees: true,
        actionItems: true,
      },
    });

    res.status(200).json({
      success: true,
      count: moms.length,
      data: moms,
    });
  } catch (error) {
    console.error('Get MOMs error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Logs routes
app.get('/api/logs', simpleAuth, async (req, res) => {
  try {
    const { level, category, search, maxLines } = req.query;

    const logs = logger.getLogs({
      level,
      category,
      search,
      maxLines: maxLines ? parseInt(maxLines) : 1000
    });

    res.status(200).json({
      success: true,
      count: logs.length,
      data: logs
    });
  } catch (error) {
    logger.error('API', 'Error fetching logs', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

app.delete('/api/logs', simpleAuth, async (req, res) => {
  try {
    // Only allow admin users to clear logs
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized'
      });
    }

    const success = logger.clearLogs();

    if (success) {
      logger.info('SYSTEM', 'Logs cleared by user request');
      res.status(200).json({
        success: true,
        message: 'Logs cleared successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to clear logs'
      });
    }
  } catch (error) {
    logger.error('API', 'Error clearing logs', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Start server
app.listen(PORT, () => {
  logger.success('SYSTEM', `Server is running on port ${PORT}`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('SYSTEM', 'Unhandled Rejection', err);
  // Close server & exit process
  process.exit(1);
});
