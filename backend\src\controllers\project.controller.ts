import { Request, Response } from 'express';
import { prisma } from '../index';
import { v4 as uuidv4 } from 'uuid';
import { AlertService } from '../services/alertService';
import { validateDates, validateNewProjectDates, validateTaskName, validateCode, validateProjectCode } from '../utils/auth.utils';

// @desc    Get all projects
// @route   GET /api/projects
// @access  Private
export const getProjects = async (req: Request, res: Response): Promise<void> => {
  try {
    const { projectManagerId } = req.query;

    // Filter conditions
    const where: any = {};

    // Filter by project manager if provided
    if (projectManagerId) {
      where.projectManagerId = projectManagerId as string;
    }

    // Filter by user role - project managers can see their own projects
    if (req.user.role === 'PROJECT_MANAGER') {
      where.projectManagerId = req.user.id;
    }

    const projects = await prisma.project.findMany({
      where,
      include: {
        task: {
          include: {
            subtask: true,
          },
        },
        section: {
          include: {
            task: {
              include: {
                subtask: true,
              },
            },
          },
          orderBy: {
            sequence: 'asc',
          },
        },
        customer: true,
        user_project_projectManagerIdTouser: {
          select: {
            id: true,
            name: true,
            role: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Normalize the response and add displayId to tasks and subtasks
    const normalizedProjects = projects.map((project: any) => {
      const normalizedProject: any = { ...project };

      // Debug customer data
      console.log('Project customer data:', {
        projectId: project.id,
        projectName: project.name,
        customerId: project.customerId,
        customer: project.customer
      });

      // Convert regular tasks (legacy support)
      const tasks = (project.task || []).map((task: any) => ({
        ...task,
        displayId: `T${task.sequence}`,
        type: 'TASK',
        subtasks: (task.subtask || []).map((subtask: any) => ({
          ...subtask,
          displayId: `S${task.sequence}${subtask.sequence}`
        }))
      }));

      // Convert sections with their tasks
      const sections = (project.section || []).map((section: any) => ({
        ...section,
        tasks: (section.task || []).map((task: any) => ({
          ...task,
          displayId: `T${task.sequence}`,
          type: 'TASK',
          subtasks: (task.subtask || []).map((subtask: any) => ({
            ...subtask,
            displayId: `S${task.sequence}${subtask.sequence}`
          }))
        }))
      }));

      // Set tasks, sections and clean up the original properties
      normalizedProject.tasks = tasks;
      normalizedProject.sections = sections;
      delete (normalizedProject as any).task;
      delete (normalizedProject as any).section;

      return normalizedProject;
    });

    res.status(200).json({
      success: true,
      count: normalizedProjects.length,
      data: normalizedProjects,
      timestamp: new Date().toISOString(), // Force fresh response
    });
  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single project
// @route   GET /api/projects/:id
// @access  Private
export const getProject = async (req: Request, res: Response): Promise<void> => {
  try {
    const project = await prisma.project.findUnique({
      where: { id: req.params.id },
      include: {
        task: {
          include: {
            subtask: true,
          },
        },
        section: {
          include: {
            task: {
              include: {
                subtask: true,
              },
            },
          },
          orderBy: {
            sequence: 'asc',
          },
        },
        customer: true,
        user_project_projectManagerIdTouser: {
          select: {
            id: true,
            name: true,
            role: true,
            email: true,
          },
        },
      },
    });

    if (!project) {
      res.status(404).json({
        success: false,
        message: 'Project not found',
      });
      return;
    }

    // Check if user has access to this project
    if (
      req.user.role === 'PROJECT_MANAGER' &&
      project.projectManagerId !== req.user.id
    ) {
      res.status(403).json({
        success: false,
        message: 'Not authorized to access this project',
      });
      return;
    }

    // Normalize the response and add displayId to tasks and subtasks
    const tasks = (project.task || []).map((task: any) => ({
      ...task,
      displayId: `T${task.sequence}`,
      type: 'TASK',
      subtasks: (task.subtask || []).map((subtask: any) => ({
        ...subtask,
        displayId: `S${task.sequence}${subtask.sequence}`
      }))
    }));

    // Convert sections with their tasks
    const sections = (project.section || []).map((section: any) => ({
      ...section,
      tasks: (section.task || []).map((task: any) => ({
        ...task,
        displayId: `T${task.sequence}`,
        type: 'TASK',
        subtasks: (task.subtask || []).map((subtask: any) => ({
          ...subtask,
          displayId: `S${task.sequence}${subtask.sequence}`
        }))
      }))
    }));

    const normalizedProject = {
      ...project,
      tasks: tasks,
      sections: sections
    };

    // Clean up the original properties
    delete (normalizedProject as any).task;
    delete (normalizedProject as any).section;

    res.status(200).json({
      success: true,
      data: normalizedProject,
    });
  } catch (error) {
    console.error('Get project error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create project
// @route   POST /api/projects
// @access  Private
export const createProject = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('Create project request body:', req.body);

    const {
      name,
      code,
      projectCategory,
      customerId,
      poNumber,
      poDate,
      poValue,
      startDate,
      endDate,
      projectManagerId,
      status,
      tasks,
    } = req.body;

    console.log('Extracted fields:', {
      name, code, projectCategory, customerId, poNumber, poDate, startDate, endDate, projectManagerId, status
    });

    // Validate project name
    const nameValidation = validateTaskName(name);
    if (!nameValidation.isValid) {
      res.status(400).json({
        success: false,
        message: nameValidation.message
      });
      return;
    }

    // Validate project code format
    const codeValidation = validateProjectCode(code);
    if (!codeValidation.isValid) {
      res.status(400).json({
        success: false,
        message: codeValidation.message
      });
      return;
    }

    // Validate dates (allow dates up to 30 days in the past)
    const dateValidation = validateDates(startDate, endDate);
    if (!dateValidation.isValid) {
      res.status(400).json({
        success: false,
        message: dateValidation.message
      });
      return;
    }

    // Check if project code already exists
    const projectExists = await prisma.project.findUnique({
      where: { code },
    });

    if (projectExists) {
      res.status(400).json({
        success: false,
        message: 'Project code already exists',
      });
      return;
    }

    // Validate required fields (endDate is now optional)
    if (!name || !code || !projectCategory || !customerId || !poNumber || !poDate || !startDate || !projectManagerId) {
      console.log('Missing required fields:', { name, code, projectCategory, customerId, poNumber, poDate, startDate, projectManagerId });
      res.status(400).json({
        success: false,
        message: 'Missing required fields',
      });
      return;
    }

    // Validate customer exists
    const customerExists = await prisma.customer.findUnique({
      where: { id: customerId },
    });

    if (!customerExists) {
      res.status(400).json({
        success: false,
        message: `Customer with ID '${customerId}' not found. Please select a valid customer.`,
      });
      return;
    }

    // Validate project manager exists
    const projectManagerExists = await prisma.user.findUnique({
      where: { id: projectManagerId },
    });

    if (!projectManagerExists) {
      res.status(400).json({
        success: false,
        message: `Project manager with ID '${projectManagerId}' not found. Please select a valid project manager.`,
      });
      return;
    }

    // Create project
    console.log('Creating project with data:', {
      name,
      code,
      projectCategory,
      customerId,
      poNumber,
      poDate: new Date(poDate),
      poValue,
      startDate: new Date(startDate),
      ...(endDate && { endDate: new Date(endDate) }),
      projectManagerId,
      status: status || 'NOT_STARTED',
      createdBy: req.user.id,
    });

    const projectId = uuidv4();
    const project = await prisma.project.create({
      data: {
        id: projectId,
        name,
        code,
        projectCategory,
        customerId,
        poNumber,
        poDate: new Date(poDate),
        ...(poValue && { poValue: parseFloat(poValue.toString()) }),
        startDate: new Date(startDate),
        ...(endDate && { endDate: new Date(endDate) }),
        department: 'GENERAL', // Add default department
        projectManagerId,
        status: status || 'NOT_STARTED',
        createdBy: req.user.id,
      },
    });

    console.log('Project created successfully:', project.id);

    // Create sections from milestone templates
    console.log('Loading milestone templates for project:', project.id);

    const milestoneTemplates = await prisma.milestone_template.findMany({
      where: { isActive: true },
      orderBy: { sequence: 'asc' }
    });

    console.log(`Found ${milestoneTemplates.length} active milestone templates`);

    if (milestoneTemplates.length > 0) {
      console.log('Creating sections from milestone templates for project:', project.id);

      for (const template of milestoneTemplates) {
        await prisma.section.create({
          data: {
            id: uuidv4(),
            projectId: project.id,
            name: template.name,
            sequence: template.sequence,
            description: template.description || '',
          },
        });
      }

      console.log('Sections created from milestone templates successfully');
    } else {
      // Fallback to default sections if no milestone templates exist
      console.log('No milestone templates found, creating default sections');

      const defaultSections = [
        { name: 'Design', description: 'Design and planning phase' },
        { name: 'Procurement', description: 'Procurement and sourcing phase' },
        { name: 'Assembly', description: 'Assembly and manufacturing phase' },
        { name: 'Testing', description: 'Testing and quality assurance phase' },
        { name: 'MQ1', description: 'First milestone and quality checkpoint' },
        { name: 'MQ2', description: 'Second milestone and quality checkpoint' }
      ];

      for (let i = 0; i < defaultSections.length; i++) {
        const section = defaultSections[i];
        await prisma.section.create({
          data: {
            id: uuidv4(),
            projectId: project.id,
            name: section.name,
            sequence: i + 1,
            description: section.description,
          },
        });
      }

      console.log('Default sections created successfully');
    }

    // Create project alert
    await AlertService.createProjectAlert(project.id, 'CREATED', req.user.id);

    // Create tasks if provided
    if (tasks && tasks.length > 0) {
      // Get the first section (Design) to assign tasks to
      const firstSection = await prisma.section.findFirst({
        where: { projectId: project.id },
        orderBy: { sequence: 'asc' }
      });

      if (firstSection) {
        for (let i = 0; i < tasks.length; i++) {
          const task = tasks[i];
          await prisma.task.create({
            data: {
              id: uuidv4(),
              projectId: project.id,
              sectionId: firstSection.id, // Assign to first section
              sequence: i + 1, // Add sequence
              name: task.name,
              description: task.description,
              assigneeId: task.assigneeId,
              assigneeType: task.assigneeType,
              department: task.department,
              startDate: new Date(task.startDate),
              endDate: new Date(task.endDate),
              status: task.status,
              createdBy: req.user.id,
              subtask: {
                create: task.subtasks?.map((subtask: any, index: number) => ({
                  id: uuidv4(),
                  sequence: index + 1, // Add sequence for subtasks
                  name: subtask.name,
                  description: subtask.description,
                  assigneeId: subtask.assigneeId,
                  assigneeType: subtask.assigneeType,
                  startDate: new Date(subtask.startDate),
                  endDate: new Date(subtask.endDate),
                  status: subtask.status,
                  createdBy: req.user.id,
                })),
              },
            },
          });
        }
      }
    }

    // Get the created project with tasks
    const createdProject = await prisma.project.findUnique({
      where: { id: project.id },
      include: {
        task: {
          include: {
            subtask: true,
          },
        },
        customer: true,
        user_project_projectManagerIdTouser: {
          select: {
            id: true,
            name: true,
            role: true,
            email: true,
          },
        },
      },
    });

    // Normalize the response and add displayId to tasks and subtasks
    const normalizedCreatedProject = {
      ...createdProject,
      tasks: (createdProject?.task || []).map((task: any) => ({
        ...task,
        displayId: `T${task.sequence}`,
        subtasks: (task.subtask || []).map((subtask: any) => ({
          ...subtask,
          displayId: `S${task.sequence}${subtask.sequence}`
        }))
      }))
    };

    res.status(201).json({
      success: true,
      data: normalizedCreatedProject,
    });
  } catch (error: any) {
    console.error('Create project error:', error);
    console.error('Error details:', error.message);
    console.error('Error stack:', error.stack);

    // Check if it's a Prisma validation error
    if (error.code === 'P2002') {
      res.status(400).json({
        success: false,
        message: 'Project code already exists',
      });
      return;
    }

    // Check if it's a foreign key constraint error
    if (error.code === 'P2003') {
      const constraint = error.meta?.constraint;
      if (constraint && constraint.includes('customerId')) {
        res.status(400).json({
          success: false,
          message: 'Invalid customer ID. Please select a valid customer.',
        });
        return;
      }
      if (constraint && constraint.includes('projectManagerId')) {
        res.status(400).json({
          success: false,
          message: 'Invalid project manager ID. Please select a valid project manager.',
        });
        return;
      }
      res.status(400).json({
        success: false,
        message: 'Invalid reference data. Please check your selections.',
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: 'Server error: ' + error.message,
    });
  }
};

// @desc    Update project
// @route   PUT /api/projects/:id
// @access  Private
export const updateProject = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('Update project request body:', req.body);

    const {
      name,
      code,
      projectCategory,
      customerId,
      poNumber,
      poDate,
      poValue,
      startDate,
      endDate,
      projectManagerId,
      status,
      description,
    } = req.body;

    // Validate project name if provided
    if (name) {
      const nameValidation = validateTaskName(name);
      if (!nameValidation.isValid) {
        res.status(400).json({
          success: false,
          message: nameValidation.message
        });
        return;
      }
    }

    // Validate project code format if provided
    if (code) {
      const codeValidation = validateProjectCode(code);
      if (!codeValidation.isValid) {
        res.status(400).json({
          success: false,
          message: codeValidation.message
        });
        return;
      }
    }

    // Validate dates if provided
    if (startDate || endDate) {
      const dateValidation = validateDates(startDate, endDate);
      if (!dateValidation.isValid) {
        res.status(400).json({
          success: false,
          message: dateValidation.message
        });
        return;
      }
    }

    // Check if project exists
    const projectExists = await prisma.project.findUnique({
      where: { id: req.params.id },
    });

    if (!projectExists) {
      res.status(404).json({
        success: false,
        message: 'Project not found',
      });
      return;
    }

    // Check if user has access to update this project
    if (
      req.user.role === 'PROJECT_MANAGER' &&
      projectExists.projectManagerId !== req.user.id
    ) {
      res.status(403).json({
        success: false,
        message: 'Not authorized to update this project',
      });
      return;
    }

    // Validate customer exists if customerId is being updated
    if (customerId !== undefined) {
      const customerExists = await prisma.customer.findUnique({
        where: { id: customerId },
      });

      if (!customerExists) {
        res.status(400).json({
          success: false,
          message: `Customer with ID '${customerId}' not found. Please select a valid customer.`,
        });
        return;
      }
    }

    // Validate project manager exists if projectManagerId is being updated
    if (projectManagerId !== undefined) {
      const projectManagerExists = await prisma.user.findUnique({
        where: { id: projectManagerId },
      });

      if (!projectManagerExists) {
        res.status(400).json({
          success: false,
          message: `Project manager with ID '${projectManagerId}' not found. Please select a valid project manager.`,
        });
        return;
      }
    }

    // Prepare update data - only include fields that are provided
    const updateData: any = {};

    if (name !== undefined) updateData.name = name;
    if (code !== undefined) updateData.code = code;
    if (projectCategory !== undefined) updateData.projectCategory = projectCategory;
    if (customerId !== undefined) updateData.customerId = customerId;
    if (poNumber !== undefined) updateData.poNumber = poNumber;
    if (poDate !== undefined) updateData.poDate = poDate ? new Date(poDate) : undefined;
    if (poValue !== undefined) updateData.poValue = poValue ? parseFloat(poValue.toString()) : null;
    if (startDate !== undefined) updateData.startDate = startDate ? new Date(startDate) : undefined;
    if (endDate !== undefined && endDate) updateData.endDate = new Date(endDate);
    if (projectManagerId !== undefined) updateData.projectManagerId = projectManagerId;
    if (status !== undefined) updateData.status = status;
    // Note: description field is not in the current database schema, so we skip it for now

    console.log('Update data prepared:', updateData);

    // Update project
    const project = await prisma.project.update({
      where: { id: req.params.id },
      data: updateData,
      include: {
        customer: true,
        user_project_projectManagerIdTouser: {
          select: {
            id: true,
            name: true,
            role: true,
            email: true,
          },
        },
      },
    });

    res.status(200).json({
      success: true,
      data: project,
    });
  } catch (error) {
    console.error('Update project error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete project
// @route   DELETE /api/projects/:id
// @access  Private
export const deleteProject = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if project exists
    const projectExists = await prisma.project.findUnique({
      where: { id: req.params.id },
    });

    if (!projectExists) {
      res.status(404).json({
        success: false,
        message: 'Project not found',
      });
      return;
    }

    // Check if user has access to delete this project
    if (
      req.user.role === 'PROJECT_MANAGER' &&
      projectExists.projectManagerId !== req.user.id
    ) {
      res.status(403).json({
        success: false,
        message: 'Not authorized to delete this project',
      });
      return;
    }

    // Delete project (tasks and subtasks will be deleted automatically due to cascade)
    await prisma.project.delete({
      where: { id: req.params.id },
    });

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete project error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
