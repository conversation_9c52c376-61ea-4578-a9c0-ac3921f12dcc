import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertTriangle } from 'lucide-react';

const TeamsPage: React.FC = () => {
  const navigate = useNavigate();

  // Automatically redirect to the User Management page after a short delay
  useEffect(() => {
    const redirectTimer = setTimeout(() => {
      navigate('/users');
    }, 5002);

    return () => clearTimeout(redirectTimer);
  }, [navigate]);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Teams & Engineers (Legacy URL)</h1>
        <p className="text-gray-600">
          This URL has been deprecated
        </p>
      </div>

      <div className="bg-amber-50 border-l-4 border-amber-500 p-4 rounded-md">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-amber-800">
              URL Change: This feature is now called "User Management"
            </h3>
            <div className="mt-2 text-sm text-amber-700">
              <p>
                The Teams & Engineers section has been renamed to User Management.
                You will be redirected to the new URL in 5 seconds.
              </p>
              <button
                onClick={() => navigate('/users')}
                className="mt-2 btn btn-sm btn-warning"
              >
                Go to User Management Now
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeamsPage;