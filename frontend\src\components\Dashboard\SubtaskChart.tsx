import React, { useState } from 'react';
import { useAtom } from 'jotai';
import { projectsAtom, currentUserAtom } from '../../store';
import { TaskStatus, Subtask } from '../../types';
import { Pie } from 'react-chartjs-2';
import { useNavigate } from 'react-router-dom';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  Title
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  Title
);

const SubtaskChart: React.FC = () => {
  const [projects] = useAtom(projectsAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const navigate = useNavigate();
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);

  // Filter projects based on user role and assignment
  const userProjects = projects.filter(project => {
    if (currentUser?.role === 'DIRECTOR') {
      return true; // Directors can see all projects
    } else if (currentUser?.role === 'PROJECT_MANAGER') {
      // Project managers can see projects they manage
      return project.projectManagerId === currentUser.id;
    } else if (currentUser?.role === 'TEAM_LEAD') {
      // Team leads can see projects where they are assigned tasks or projects in their department
      return project.department === currentUser.department ||
             project.tasks?.some(task => task.assigneeId === currentUser.id) || false;
    } else if (currentUser?.role === 'ENGINEER') {
      // Engineers only see projects where they have assigned subtasks
      return project.tasks?.some(task =>
        task.subtasks?.some(subtask => subtask.assigneeId === currentUser.id)
      ) || false;
    }
    return false;
  });

  // Get all subtasks from user projects
  const allSubtasks: Subtask[] = userProjects.flatMap(project =>
    project.tasks?.flatMap(task => task.subtasks || []) || []
  );

  // Filter subtasks based on user role
  const userSubtasks = currentUser?.role === 'ENGINEER'
    ? allSubtasks.filter(subtask => subtask.assigneeId === currentUser.id)
    : allSubtasks;

  // Count subtasks by status
  const notStarted = userSubtasks.filter(subtask => subtask.status === TaskStatus.NOT_STARTED).length;
  const inProgress = userSubtasks.filter(subtask => subtask.status === TaskStatus.IN_PROGRESS).length;
  const completed = userSubtasks.filter(subtask => subtask.status === TaskStatus.COMPLETED).length;
  const delayed = userSubtasks.filter(subtask => subtask.status === TaskStatus.DELAYED).length;
  const onHold = userSubtasks.filter(subtask => subtask.status === TaskStatus.ON_HOLD).length;

  const data = {
    labels: ['Not Started', 'In Progress', 'Completed', 'Delayed', 'On Hold'],
    datasets: [
      {
        data: [notStarted, inProgress, completed, delayed, onHold],
        backgroundColor: [
          'rgba(107, 114, 142, 0.9)',
          'rgba(247, 144, 30, 0.9)',
          'rgba(45, 182, 125, 0.9)',
          'rgba(231, 76, 60, 0.9)',
          'rgba(241, 196, 15, 0.9)'
        ],
        borderColor: [
          'rgba(107, 114, 142, 1)',
          'rgba(247, 144, 30, 1)',
          'rgba(45, 182, 125, 1)',
          'rgba(231, 76, 60, 1)',
          'rgba(241, 196, 15, 1)'
        ],
        borderWidth: 2,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.formattedValue || '';
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = Math.round((context.raw / total) * 100);
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      }
    },
    onClick: (event: any, elements: any) => {
      if (elements.length > 0) {
        const index = elements[0].index;
        const statusMap = ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'DELAYED', 'ON_HOLD'];
        const status = statusMap[index];
        navigate(`/subtasks?status=${status}`);
      }
    }
  };

  // Handle filter button clicks
  const handleFilterClick = (status: string) => {
    setSelectedStatus(status);
    navigate(`/subtasks?status=${status}`);
  };

  return (
    <div className="relative group h-full">
      {/* 3D Card Container */}
      <div className="relative bg-gradient-to-br from-white via-gray-50 to-gray-100
                      rounded-2xl p-8 h-full flex flex-col
                      shadow-[0_8px_30px_rgb(0,0,0,0.12)]
                      hover:shadow-[0_20px_60px_rgb(0,0,0,0.15)]
                      transition-all duration-500 ease-out
                      hover:-translate-y-2
                      border border-white/20
                      backdrop-blur-sm
                      before:absolute before:inset-0
                      before:bg-gradient-to-br before:from-white/10 before:to-transparent
                      before:rounded-2xl before:pointer-events-none
                      after:absolute after:inset-0
                      after:bg-gradient-to-tr after:from-transparent after:via-white/5 after:to-white/10
                      after:rounded-2xl after:pointer-events-none">

        {/* Inner glow effect */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-purple-50/30 via-transparent to-blue-50/30 pointer-events-none"></div>

        {/* Content */}
        <div className="relative z-10 flex flex-col h-full">
          {/* Header with enhanced styling */}
          <div className="flex items-center justify-between mb-6 flex-shrink-0">
            <div className="flex items-center">
              <div className="w-1 h-8 bg-gradient-to-b from-purple-500 to-blue-600 rounded-full mr-4
                              shadow-lg shadow-purple-500/30"></div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600
                             bg-clip-text text-transparent">
                Subtask Status Distribution
              </h3>
            </div>

            {/* Quick Filter Buttons */}
            <div className="flex space-x-2">
              <button
                onClick={() => handleFilterClick('DELAYED')}
                className="px-3 py-1 text-xs font-medium bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
                title="View Delayed Subtasks"
              >
                Delayed ({delayed})
              </button>
              <button
                onClick={() => handleFilterClick('IN_PROGRESS')}
                className="px-3 py-1 text-xs font-medium bg-orange-100 text-orange-700 rounded-lg hover:bg-orange-200 transition-colors"
                title="View In Progress Subtasks"
              >
                In Progress ({inProgress})
              </button>
            </div>
          </div>

          {/* Chart Container with inner shadow */}
          <div className="flex-1 relative min-h-0">
            <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-gray-50/80
                            rounded-xl shadow-inner border border-gray-200/50">
              <div className="p-4 h-full">
                <Pie data={data} options={options} />
              </div>
            </div>
          </div>
        </div>

        {/* Subtle animated background pattern */}
        <div className="absolute inset-0 opacity-5 pointer-events-none">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-500/10 via-transparent to-blue-500/10
                          animate-pulse rounded-2xl"></div>
        </div>
      </div>

      {/* Enhanced bottom shadow for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900/5 to-gray-900/10
                      rounded-2xl blur-xl translate-y-4 scale-95 -z-10
                      group-hover:translate-y-6 group-hover:blur-2xl
                      transition-all duration-500"></div>
    </div>
  );
};

export default SubtaskChart;
