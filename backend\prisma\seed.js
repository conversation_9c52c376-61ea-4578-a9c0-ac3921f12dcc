const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

// Hash password function
async function hashPassword(password) {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
}

const prisma = new PrismaClient();

async function main() {
  console.log('Seeding database...');

  // Create users with engineer data integrated
  const hashedPassword = await hashPassword('password');

  const users = [
    {
      id: uuidv4(),
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'John Director',
      role: 'DIRECTOR',
      department: 'Management',
      profileImage: 'https://placehold.co/100x100',
      code: 'D101',
      skills: 'Leadership,Strategy,Business Development',
      joinDate: new Date('2018-01-10'),
    },
  ];

  for (const user of users) {
    const result = await prisma.user.upsert({
      where: { email: user.email },
      update: {
        name: user.name,
        password: user.password,
        role: user.role,
        department: user.department,
        profileImage: user.profileImage,
        code: user.code,
        skills: user.skills,
        joinDate: user.joinDate,
      },
      create: {
        id: user.id,
        email: user.email,
        password: user.password,
        name: user.name,
        role: user.role,
        department: user.department,
        profileImage: user.profileImage,
        code: user.code,
        skills: user.skills,
        joinDate: user.joinDate,
      },
    });
    console.log(`✅ User created/updated: ${result.name} (${result.role}) - ${result.email}`);
  }

  console.log('\n🎉 Director seeded successfully!');
  console.log('\n📋 Login credentials:');
  console.log('Director: <EMAIL> / password');
}

main()
  .catch((e) => {
    console.error('Error seeding database:', e.message, e.stack);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
