/**
 * Centralized date formatting utilities
 * Default format: DD/MM/YYYY
 */

import { format, parseISO, isValid } from 'date-fns';

// Default date format for the application
export const DEFAULT_DATE_FORMAT = 'dd/MM/yyyy';
export const DEFAULT_DATETIME_FORMAT = 'dd/MM/yyyy HH:mm';
export const DEFAULT_DATE_TIME_FORMAT = 'dd/MM/yyyy h:mm a';

/**
 * Format a date to DD/MM/YYYY format
 */
export const formatDate = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    
    return format(dateObj, DEFAULT_DATE_FORMAT);
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Format a date to DD/MM/YYYY HH:mm format
 */
export const formatDateTime = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    
    return format(dateObj, DEFAULT_DATETIME_FORMAT);
  } catch (error) {
    console.error('Error formatting datetime:', error);
    return '';
  }
};

/**
 * Format a date to DD/MM/YYYY h:mm a format (with AM/PM)
 */
export const formatDateTimeAMPM = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    
    return format(dateObj, DEFAULT_DATE_TIME_FORMAT);
  } catch (error) {
    console.error('Error formatting datetime with AM/PM:', error);
    return '';
  }
};

/**
 * Format a date for HTML date input (YYYY-MM-DD)
 */
export const formatDateForInput = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    
    return format(dateObj, 'yyyy-MM-dd');
  } catch (error) {
    console.error('Error formatting date for input:', error);
    return '';
  }
};

/**
 * Format a date range to DD/MM/YYYY - DD/MM/YYYY
 */
export const formatDateRange = (
  startDate: string | Date | null | undefined,
  endDate: string | Date | null | undefined
): string => {
  const start = formatDate(startDate);
  const end = formatDate(endDate);
  
  if (!start && !end) return '';
  if (!start) return `Until ${end}`;
  if (!end) return `From ${start}`;
  
  return `${start} - ${end}`;
};

/**
 * Format a date for display with relative information
 */
export const formatDateWithRelative = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    
    const formatted = format(dateObj, DEFAULT_DATE_FORMAT);
    const now = new Date();
    const diffInDays = Math.floor((dateObj.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return `${formatted} (Today)`;
    if (diffInDays === 1) return `${formatted} (Tomorrow)`;
    if (diffInDays === -1) return `${formatted} (Yesterday)`;
    if (diffInDays > 0) return `${formatted} (In ${diffInDays} days)`;
    if (diffInDays < 0) return `${formatted} (${Math.abs(diffInDays)} days ago)`;
    
    return formatted;
  } catch (error) {
    console.error('Error formatting date with relative:', error);
    return '';
  }
};

/**
 * Get today's date in DD/MM/YYYY format
 */
export const getTodayFormatted = (): string => {
  return formatDate(new Date());
};

/**
 * Parse DD/MM/YYYY string to Date object
 */
export const parseDateFromDDMMYYYY = (dateString: string): Date | null => {
  if (!dateString) return null;
  
  try {
    // Handle DD/MM/YYYY format
    const parts = dateString.split('/');
    if (parts.length === 3) {
      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
      const year = parseInt(parts[2], 10);
      
      const date = new Date(year, month, day);
      return isValid(date) ? date : null;
    }
    
    // Fallback to standard parsing
    const date = new Date(dateString);
    return isValid(date) ? date : null;
  } catch (error) {
    console.error('Error parsing date from DD/MM/YYYY:', error);
    return null;
  }
};

/**
 * Convert DD/MM/YYYY to YYYY-MM-DD for HTML inputs
 */
export const convertDDMMYYYYToInputFormat = (dateString: string): string => {
  const date = parseDateFromDDMMYYYY(dateString);
  return date ? formatDateForInput(date) : '';
};

/**
 * Convert YYYY-MM-DD to DD/MM/YYYY for display
 */
export const convertInputFormatToDDMMYYYY = (dateString: string): string => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return isValid(date) ? formatDate(date) : '';
  } catch (error) {
    console.error('Error converting input format to DD/MM/YYYY:', error);
    return '';
  }
};
