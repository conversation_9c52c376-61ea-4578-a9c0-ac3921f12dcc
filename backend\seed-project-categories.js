const { PrismaClient } = require('@prisma/client');
const { v4: uuidv4 } = require('uuid');

const prisma = new PrismaClient();

async function seedProjectCategories() {
  try {
    console.log('🌱 Seeding project categories...');

    // Check if categories already exist
    const existingCategories = await prisma.project_category_item.findMany();
    console.log(`Found ${existingCategories.length} existing project categories`);

    if (existingCategories.length > 0) {
      console.log('✅ Project categories already exist:');
      existingCategories.forEach(cat => {
        console.log(`  - ${cat.name} (${cat.code})`);
      });
      return;
    }

    // Default project categories
    const defaultCategories = [
      {
        id: uuidv4(),
        name: 'Projects',
        code: 'PROJECTS',
        description: 'Standard projects and development work'
      },
      {
        id: uuidv4(),
        name: 'Precision Projects',
        code: 'PRECISION_PROJECTS',
        description: 'High-precision engineering and manufacturing projects'
      },
      {
        id: uuidv4(),
        name: 'Spare Parts',
        code: 'SPARE',
        description: 'Spare parts manufacturing and supply'
      },
      {
        id: uuidv4(),
        name: 'Service',
        code: 'SERVICE',
        description: 'Service and maintenance projects'
      }
    ];

    console.log('Creating default project categories...');

    for (const category of defaultCategories) {
      const created = await prisma.project_category_item.create({
        data: category
      });
      console.log(`✅ Created: ${created.name} (${created.code})`);
    }

    console.log('🎉 Project categories seeded successfully!');

  } catch (error) {
    console.error('❌ Error seeding project categories:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
if (require.main === module) {
  seedProjectCategories()
    .then(() => {
      console.log('✅ Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedProjectCategories };
