import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2, Calendar, User, FileText } from 'lucide-react';

interface MOMEditFormProps {
  mom: any;
  onSave: (updatedMOM: any) => void;
  onCancel: () => void;
}

interface MOMPointData {
  id?: string;
  slNo?: number;
  date?: string;
  discussionType?: string;
  station?: string;
  discussion: string;
  actionPlan?: string;
  responsibility?: string;
  plannedDate?: string;
  completionDate?: string;
  status: string;
  imageAttachment?: string;
  remarks?: string;
  isNew?: boolean;
}

const MOMEditForm: React.FC<MOMEditFormProps> = ({ mom, onSave, onCancel }) => {
  const [meetingDate, setMeetingDate] = useState(
    mom.date ? new Date(mom.date).toISOString().split('T')[0] : ''
  );
  const [momPoints, setMomPoints] = useState<MOMPointData[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [allUsers, setAllUsers] = useState<any[]>([]);
  const [fullMOMData, setFullMOMData] = useState<any>(null);

  // Fetch complete MOM data with discussion points
  useEffect(() => {
    const fetchMOMData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/moms/${mom.id}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          const momData = data.data;
          setFullMOMData(momData);

          // Update meeting date if available
          if (momData.date) {
            setMeetingDate(new Date(momData.date).toISOString().split('T')[0]);
          }

          // Initialize MOM points from fetched data
          if (momData.mompoint && momData.mompoint.length > 0) {
            const points = momData.mompoint.map((point: any) => ({
              id: point.id,
              slNo: point.slNo,
              date: point.date ? new Date(point.date).toISOString().split('T')[0] : '',
              discussionType: point.discussionType || '',
              station: point.station || '',
              discussion: point.discussion || '',
              actionPlan: point.actionPlan || '',
              responsibility: point.responsibility || '',
              plannedDate: point.plannedDate ? new Date(point.plannedDate).toISOString().split('T')[0] : '',
              completionDate: point.completionDate ? new Date(point.completionDate).toISOString().split('T')[0] : '',
              status: point.status || 'PENDING',
              imageAttachment: point.imageAttachment || '',
              remarks: point.remarks || '',
              isNew: false
            }));
            setMomPoints(points);
          } else {
            // Add one empty point if no existing points
            setMomPoints([{
              discussion: '',
              status: 'PENDING',
              isNew: true
            }]);
          }
        }
      } catch (error) {
        console.error('Error fetching MOM data:', error);
        // Fallback to empty point
        setMomPoints([{
          discussion: '',
          status: 'PENDING',
          isNew: true
        }]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMOMData();
  }, [mom.id]);

  // Load users for responsibility dropdown
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const response = await fetch('/api/users', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });
        if (response.ok) {
          const data = await response.json();
          setAllUsers(data.data || []);
        }
      } catch (error) {
        console.error('Error loading users:', error);
      }
    };

    loadUsers();
  }, []);

  // Add new MOM point
  const addMOMPoint = () => {
    setMomPoints([...momPoints, {
      discussion: '',
      status: 'PENDING',
      isNew: true
    }]);
  };

  // Remove MOM point
  const removeMOMPoint = (index: number) => {
    if (momPoints.length > 1) {
      setMomPoints(momPoints.filter((_, i) => i !== index));
    }
  };

  // Update MOM point
  const updateMOMPoint = (index: number, field: string, value: string) => {
    setMomPoints(momPoints.map((point, i) =>
      i === index ? { ...point, [field]: value } : point
    ));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Update MOM basic info
      if (meetingDate) {
        await fetch(`/api/moms/${mom.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            date: new Date(meetingDate).toISOString(),
          }),
        });
      }

      // Handle MOM points
      for (let i = 0; i < momPoints.length; i++) {
        const point = momPoints[i];

        if (!point.discussion.trim()) continue;

        if (point.isNew) {
          // Create new point
          await fetch(`/api/moms/${mom.id}/points`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
            body: JSON.stringify({
              date: point.date ? new Date(point.date).toISOString() : null,
              discussionType: point.discussionType || null,
              station: point.station || null,
              discussion: point.discussion,
              actionPlan: point.actionPlan || null,
              responsibility: point.responsibility || null,
              plannedDate: point.plannedDate ? new Date(point.plannedDate).toISOString() : null,
              completionDate: point.completionDate ? new Date(point.completionDate).toISOString() : null,
              status: point.status,
              imageAttachment: point.imageAttachment || null,
              remarks: point.remarks || null,
            }),
          });
        } else if (point.id) {
          // Update existing point
          await fetch(`/api/moms/${mom.id}/points/${point.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
            body: JSON.stringify({
              date: point.date ? new Date(point.date).toISOString() : null,
              discussionType: point.discussionType || null,
              station: point.station || null,
              discussion: point.discussion,
              actionPlan: point.actionPlan || null,
              responsibility: point.responsibility || null,
              plannedDate: point.plannedDate ? new Date(point.plannedDate).toISOString() : null,
              completionDate: point.completionDate ? new Date(point.completionDate).toISOString() : null,
              status: point.status,
              imageAttachment: point.imageAttachment || null,
              remarks: point.remarks || null,
            }),
          });
        }
      }

      // Refresh the MOM data
      const response = await fetch(`/api/moms/${mom.id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const updatedMOM = await response.json();
        onSave(updatedMOM.data);
      }

    } catch (error) {
      console.error('Error updating MOM:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
          <h3 className="text-2xl font-bold text-gray-900 flex items-center">
            <FileText size={24} className="mr-3 text-green-600" />
            Edit Minutes of Meeting
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="flex flex-col h-full">
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600"></div>
                <span className="ml-3 text-gray-600">Loading MOM data...</span>
              </div>
            ) : (
              <>
                {/* Project Info */}
                <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-2">Project Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-blue-800">Project:</span>
                      <span className="ml-2 text-blue-700">{fullMOMData?.project?.name || mom.project?.name || 'Unknown Project'}</span>
                    </div>
                    <div>
                      <span className="font-medium text-blue-800">Project Code:</span>
                      <span className="ml-2 text-blue-700">{fullMOMData?.project?.code || mom.project?.code || 'N/A'}</span>
                    </div>
                    <div>
                      <span className="font-medium text-blue-800">Customer:</span>
                      <span className="ml-2 text-blue-700">{fullMOMData?.project?.customer?.name || mom.project?.customer?.name || 'Unknown Customer'}</span>
                    </div>
                    <div>
                      <span className="font-medium text-blue-800">PO Number:</span>
                      <span className="ml-2 text-blue-700">{fullMOMData?.project?.poNumber || mom.project?.poNumber || 'N/A'}</span>
                    </div>
                  </div>
                </div>

            {/* Meeting Date */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="inline h-4 w-4 mr-1" />
                Meeting Date
              </label>
              <input
                type="date"
                value={meetingDate}
                onChange={(e) => setMeetingDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* MOM Points Table */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-900">Discussion Points</h4>
                <button
                  type="button"
                  onClick={addMOMPoint}
                  className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Point
                </button>
              </div>

              <div className="overflow-x-auto border border-gray-200 rounded-lg">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Station</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discussion</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action Plan</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Responsibility</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Planned Date</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Date</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Result Comments</th>
                      <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {momPoints.map((point, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-3 py-2">
                          <input
                            type="date"
                            value={point.date || ''}
                            onChange={(e) => updateMOMPoint(index, 'date', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="text"
                            value={point.discussionType || ''}
                            onChange={(e) => updateMOMPoint(index, 'discussionType', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                            placeholder="Discussion Type"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="text"
                            value={point.station || ''}
                            onChange={(e) => updateMOMPoint(index, 'station', e.target.value)}
                            placeholder="Station"
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <textarea
                            value={point.discussion}
                            onChange={(e) => updateMOMPoint(index, 'discussion', e.target.value)}
                            placeholder="Enter discussion point"
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                            rows={2}
                          />
                        </td>
                        <td className="px-3 py-2">
                          <textarea
                            value={point.actionPlan || ''}
                            onChange={(e) => updateMOMPoint(index, 'actionPlan', e.target.value)}
                            placeholder="Action plan"
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                            rows={2}
                          />
                        </td>
                        <td className="px-3 py-2">
                          <select
                            value={point.responsibility || ''}
                            onChange={(e) => updateMOMPoint(index, 'responsibility', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          >
                            <option value="">Select Assignee</option>
                            {allUsers.map((user) => (
                              <option key={user.id} value={user.id}>
                                {user.name}
                              </option>
                            ))}
                          </select>
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="date"
                            value={point.plannedDate || ''}
                            onChange={(e) => updateMOMPoint(index, 'plannedDate', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <input
                            type="date"
                            value={point.completionDate || ''}
                            onChange={(e) => updateMOMPoint(index, 'completionDate', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                        </td>
                        <td className="px-3 py-2">
                          <select
                            value={point.status}
                            onChange={(e) => updateMOMPoint(index, 'status', e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          >
                            <option value="PENDING">Pending</option>
                            <option value="IN_PROGRESS">In Progress</option>
                            <option value="COMPLETED">Completed</option>
                            <option value="DELAYED">Delayed</option>
                            <option value="ON_HOLD">On Hold</option>
                          </select>
                        </td>
                        <td className="px-3 py-2">
                          <textarea
                            value={point.remarks || ''}
                            onChange={(e) => updateMOMPoint(index, 'remarks', e.target.value)}
                            placeholder="Result comments"
                            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                            rows={2}
                          />
                        </td>
                        <td className="px-3 py-2">
                          {momPoints.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeMOMPoint(index)}
                              className="text-red-600 hover:text-red-800 transition-colors"
                              title="Remove point"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
              </>
            )}
          </div>

          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                disabled={isSubmitting || isLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                disabled={isSubmitting || isLoading}
              >
                {isSubmitting ? 'Saving...' : isLoading ? 'Loading...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MOMEditForm;
