import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Get payment summary and details for Directors
export const getPaymentData = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if user is Director
    if (req.user.role !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only Directors can view payment data.'
      });
      return;
    }

    // Get all projects with PO values
    const projects = await prisma.project.findMany({
      where: {
        poValue: {
          not: null,
          gt: 0
        }
      },
      include: {
        customer: true,
        user_project_projectManagerIdTouser: {
          select: {
            id: true,
            name: true,
            role: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Get payment records for all projects
    const projectIds = projects.map(p => p.id);
    const paymentRecords = await prisma.payment.findMany({
      where: {
        projectId: {
          in: projectIds
        }
      },
      select: {
        projectId: true,
        amount: true,
        paymentDate: true,
        paymentMethod: true,
        referenceNumber: true,
        description: true
      }
    });

    // Group payments by project
    const paymentsByProject = paymentRecords.reduce((acc, payment) => {
      if (!acc[payment.projectId]) {
        acc[payment.projectId] = [];
      }
      acc[payment.projectId].push(payment);
      return acc;
    }, {} as Record<string, typeof paymentRecords>);

    // Calculate payment data based on actual payment records
    const paymentData = projects.map(project => {
      const poValue = Number(project.poValue) || 0;
      const projectPayments = paymentsByProject[project.id] || [];

      // Calculate total paid amount from actual payment records
      const paidAmount = projectPayments.reduce((sum, payment) => {
        return sum + Number(payment.amount);
      }, 0);

      const pendingAmount = poValue - paidAmount;
      const paidPercentage = poValue > 0 ? (paidAmount / poValue) * 100 : 0;

      let paymentStatus: 'FULLY_PAID' | 'PARTIALLY_PAID' | 'PENDING';
      if (paidPercentage >= 100) {
        paymentStatus = 'FULLY_PAID';
      } else if (paidPercentage > 0) {
        paymentStatus = 'PARTIALLY_PAID';
      } else {
        paymentStatus = 'PENDING';
      }

      return {
        id: project.id,
        projectName: project.name,
        projectCode: project.code,
        customerName: project.customer?.name || 'Unknown',
        poValue,
        paidAmount: Math.round(paidAmount * 100) / 100, // Round to 2 decimal places
        pendingAmount: Math.round(pendingAmount * 100) / 100,
        paymentStatus,
        poDate: project.poDate,
        startDate: project.startDate,
        endDate: project.endDate,
        projectCategory: project.projectCategory,
        projectManagerName: project.user_project_projectManagerIdTouser?.name || 'Unassigned'
      };
    });

    // Calculate summary
    const totalProjects = paymentData.length;
    const totalPayment = paymentData.reduce((sum, p) => sum + p.poValue, 0);
    const totalPaid = paymentData.reduce((sum, p) => sum + p.paidAmount, 0);
    const totalPending = totalPayment - totalPaid;
    const paidPercentage = totalPayment > 0 ? (totalPaid / totalPayment) * 100 : 0;
    const pendingPercentage = 100 - paidPercentage;

    const summary = {
      totalProjects,
      totalPayment: Math.round(totalPayment * 100) / 100,
      totalPaid: Math.round(totalPaid * 100) / 100,
      totalPending: Math.round(totalPending * 100) / 100,
      paidPercentage: Math.round(paidPercentage * 100) / 100,
      pendingPercentage: Math.round(pendingPercentage * 100) / 100
    };

    res.json({
      success: true,
      data: {
        summary,
        payments: paymentData
      }
    });

  } catch (error) {
    console.error('Error fetching payment data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payment data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get payment statistics for dashboard
export const getPaymentStats = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if user is Director
    if (req.user.role !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only Directors can view payment statistics.'
      });
      return;
    }

    // Get payment statistics by category
    const projectsByCategory = await prisma.project.groupBy({
      by: ['projectCategory'],
      where: {
        poValue: {
          not: null,
          gt: 0
        }
      },
      _count: {
        id: true
      },
      _sum: {
        poValue: true
      }
    });

    // Get payment statistics by customer
    const projectsByCustomer = await prisma.project.groupBy({
      by: ['customerId'],
      where: {
        poValue: {
          not: null,
          gt: 0
        }
      },
      _count: {
        id: true
      },
      _sum: {
        poValue: true
      }
    });

    // Get customer names
    const customerIds = projectsByCustomer.map(p => p.customerId);
    const customers = await prisma.customer.findMany({
      where: {
        id: {
          in: customerIds
        }
      },
      select: {
        id: true,
        name: true
      }
    });

    // Map customer names to statistics
    const customerStats = projectsByCustomer.map(stat => {
      const customer = customers.find(c => c.id === stat.customerId);
      return {
        customerName: customer?.name || 'Unknown',
        projectCount: stat._count.id,
        totalValue: Number(stat._sum.poValue) || 0
      };
    });

    // Get monthly payment trends (mock data for now)
    const monthlyTrends = Array.from({ length: 12 }, (_, index) => {
      const month = new Date();
      month.setMonth(month.getMonth() - (11 - index));

      return {
        month: month.toISOString().slice(0, 7), // YYYY-MM format
        totalPayments: Math.random() * 1000000, // Mock data
        projectCount: Math.floor(Math.random() * 20) + 1
      };
    });

    res.json({
      success: true,
      data: {
        categoryStats: projectsByCategory.map(stat => ({
          category: stat.projectCategory,
          projectCount: stat._count.id,
          totalValue: Number(stat._sum.poValue) || 0
        })),
        customerStats,
        monthlyTrends
      }
    });

  } catch (error) {
    console.error('Error fetching payment statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payment statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Export payment data to Excel (placeholder)
export const exportPaymentData = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if user is Director
    if (req.user.role !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only Directors can export payment data.'
      });
      return;
    }

    // In a real implementation, this would generate an Excel file
    // For now, return a success message
    res.json({
      success: true,
      message: 'Excel export functionality will be implemented',
      data: {
        exportUrl: '/api/payments/export/excel',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error exporting payment data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export payment data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Create a new payment record
export const createPayment = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if user is Director
    if (req.user.role !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only Directors can create payment records.'
      });
      return;
    }

    const {
      projectId,
      amount,
      paymentDate,
      paymentMethod = 'BANK_TRANSFER',
      referenceNumber,
      description
    } = req.body;

    // Validate required fields
    if (!projectId || !amount || !paymentDate) {
      res.status(400).json({
        success: false,
        message: 'Project ID, amount, and payment date are required.'
      });
      return;
    }

    // Verify project exists
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      select: { id: true, name: true, poValue: true }
    });

    if (!project) {
      res.status(404).json({
        success: false,
        message: 'Project not found.'
      });
      return;
    }

    // Create payment record
    const payment = await prisma.payment.create({
      data: {
        projectId,
        amount: parseFloat(amount.toString()),
        paymentDate: new Date(paymentDate),
        paymentMethod,
        referenceNumber,
        description,
        createdBy: req.user.id
      },
      include: {
        project: {
          select: {
            name: true,
            code: true
          }
        }
      }
    });

    console.log(`Payment created: ₹${payment.amount} for project ${project.name} (${projectId})`);

    res.status(201).json({
      success: true,
      message: 'Payment record created successfully.',
      data: payment
    });
  } catch (error) {
    console.error('Error creating payment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create payment record.'
    });
  }
};

// Update a payment record
export const updatePayment = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if user is Director
    if (req.user.role !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only Directors can update payment records.'
      });
      return;
    }

    const { paymentId } = req.params;
    const {
      amount,
      paymentDate,
      paymentMethod,
      referenceNumber,
      description
    } = req.body;

    // Verify payment exists
    const existingPayment = await prisma.payment.findUnique({
      where: { id: paymentId }
    });

    if (!existingPayment) {
      res.status(404).json({
        success: false,
        message: 'Payment record not found.'
      });
      return;
    }

    // Update payment record
    const payment = await prisma.payment.update({
      where: { id: paymentId },
      data: {
        ...(amount && { amount: parseFloat(amount.toString()) }),
        ...(paymentDate && { paymentDate: new Date(paymentDate) }),
        ...(paymentMethod && { paymentMethod }),
        ...(referenceNumber !== undefined && { referenceNumber }),
        ...(description !== undefined && { description })
      },
      include: {
        project: {
          select: {
            name: true,
            code: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Payment record updated successfully.',
      data: payment
    });
  } catch (error) {
    console.error('Error updating payment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update payment record.'
    });
  }
};

// Delete a payment record
export const deletePayment = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if user is Director
    if (req.user.role !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only Directors can delete payment records.'
      });
      return;
    }

    const { paymentId } = req.params;

    // Verify payment exists
    const existingPayment = await prisma.payment.findUnique({
      where: { id: paymentId }
    });

    if (!existingPayment) {
      res.status(404).json({
        success: false,
        message: 'Payment record not found.'
      });
      return;
    }

    // Delete payment record
    await prisma.payment.delete({
      where: { id: paymentId }
    });

    res.json({
      success: true,
      message: 'Payment record deleted successfully.'
    });
  } catch (error) {
    console.error('Error deleting payment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete payment record.'
    });
  }
};

// Get all payments for a specific project
export const getProjectPayments = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if user is Director
    if (req.user.role !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only Directors can view payment records.'
      });
      return;
    }

    const { projectId } = req.params;

    // Verify project exists
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      select: {
        id: true,
        name: true,
        code: true,
        poValue: true,
        customer: {
          select: {
            name: true
          }
        }
      }
    });

    if (!project) {
      res.status(404).json({
        success: false,
        message: 'Project not found.'
      });
      return;
    }

    // Get all payments for the project
    const payments = await prisma.payment.findMany({
      where: { projectId },
      include: {
        user: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        paymentDate: 'desc'
      }
    });

    // Calculate totals
    const totalPaid = payments.reduce((sum, payment) => sum + Number(payment.amount), 0);
    const poValue = Number(project.poValue) || 0;
    const pendingAmount = poValue - totalPaid;

    res.json({
      success: true,
      data: {
        project: {
          id: project.id,
          name: project.name,
          code: project.code,
          poValue,
          customerName: project.customer?.name || 'Unknown'
        },
        payments,
        summary: {
          totalPaid: Math.round(totalPaid * 100) / 100,
          pendingAmount: Math.round(pendingAmount * 100) / 100,
          poValue
        }
      }
    });
  } catch (error) {
    console.error('Error fetching project payments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch project payments.'
    });
  }
};

// Get payment summary for dashboard
export const getPaymentSummary = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if user is Director
    if (req.user.role !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only Directors can view payment summary.'
      });
      return;
    }

    // Get all projects with PO values
    const projects = await prisma.project.findMany({
      where: {
        poValue: {
          not: null,
          gt: 0
        }
      },
      select: {
        id: true,
        poValue: true
      }
    });

    // Get all payment records
    const projectIds = projects.map(p => p.id);
    const paymentRecords = await prisma.payment.findMany({
      where: {
        projectId: {
          in: projectIds
        }
      },
      select: {
        projectId: true,
        amount: true
      }
    });

    // Group payments by project
    const paymentsByProject = paymentRecords.reduce((acc, payment) => {
      if (!acc[payment.projectId]) {
        acc[payment.projectId] = [];
      }
      acc[payment.projectId].push(payment);
      return acc;
    }, {} as Record<string, typeof paymentRecords>);

    // Calculate totals
    let totalPoValue = 0;
    let totalPaid = 0;

    projects.forEach(project => {
      const poValue = Number(project.poValue) || 0;
      const projectPayments = paymentsByProject[project.id] || [];
      const paidAmount = projectPayments.reduce((sum, payment) => {
        return sum + Number(payment.amount);
      }, 0);

      totalPoValue += poValue;
      totalPaid += paidAmount;
    });

    const totalPending = totalPoValue - totalPaid;
    const paidPercentage = totalPoValue > 0 ? (totalPaid / totalPoValue) * 100 : 0;
    const pendingPercentage = totalPoValue > 0 ? (totalPending / totalPoValue) * 100 : 0;

    res.json({
      success: true,
      data: {
        totalProjects: projects.length,
        totalPoValue: Math.round(totalPoValue * 100) / 100,
        totalPaid: Math.round(totalPaid * 100) / 100,
        totalPending: Math.round(totalPending * 100) / 100,
        paidPercentage: Math.round(paidPercentage * 100) / 100,
        pendingPercentage: Math.round(pendingPercentage * 100) / 100
      }
    });
  } catch (error) {
    console.error('Error fetching payment summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payment summary.'
    });
  }
};

// Get all payment records for payment list page
export const getPaymentRecords = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if user is Director
    if (req.user.role !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Only Directors can view payment records.'
      });
      return;
    }

    // Get all payment records with project and user details
    const paymentRecords = await prisma.payment.findMany({
      include: {
        project: {
          select: {
            name: true,
            code: true,
            customer: {
              select: {
                name: true
              }
            }
          }
        },
        user: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        paymentDate: 'desc'
      }
    });

    // Format the response
    const formattedRecords = paymentRecords.map(payment => ({
      id: payment.id,
      projectId: payment.projectId,
      projectName: payment.project.name,
      projectCode: payment.project.code,
      customerName: payment.project.customer?.name || 'Unknown',
      amount: Number(payment.amount),
      paymentDate: payment.paymentDate.toISOString(),
      paymentMethod: payment.paymentMethod,
      referenceNumber: payment.referenceNumber,
      description: payment.description,
      createdBy: payment.createdBy,
      createdAt: payment.createdAt.toISOString(),
      user: {
        name: payment.user.name,
        email: payment.user.email
      }
    }));

    res.json({
      success: true,
      data: {
        payments: formattedRecords,
        total: formattedRecords.length
      }
    });
  } catch (error) {
    console.error('Error fetching payment records:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payment records.'
    });
  }
};