// User types
export enum UserRole {
  DIRECTOR = "DIRECTOR",
  PROJECT_MANAGER = "PROJECT_MANAGER",
  TEAM_LEAD = "TEAM_LEAD",
  ENGINEER = "ENGINEER"
}

export interface User {
  id: string;
  email: string;
  password?: string; // Only used for creation/update, not stored in state
  name: string;
  role: UserRole;
  department: string;
  code?: string;
  skills?: string;
  joinDate?: string;
  profileImage?: string;
  createdAt?: string;
  updatedAt?: string;
  passwordChanged?: string; // "Y" if password has been changed, "N" if not
}

// Department types (legacy - keeping for backward compatibility)
export interface Department {
  id: string;
  name: string;
  code: string;
  description?: string;
}



// Customer types
export interface Customer {
  id: string;
  name: string;
  code: string;
  contactName?: string | null;
  email?: string | null;
  phone?: string | null;
  address?: string | null;
  createdAt: string;
  updatedAt: string;
  _count?: {
    project: number;
    mom: number;
  };
}

// Engineer types
export interface Engineer {
  id: string;
  name: string;
  code: string;
  department: string;
  role: UserRole;
  email: string;
  skills?: string[];
  joinDate: string;
}

// Project types
export enum TaskStatus {
  NOT_STARTED = "NOT_STARTED",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  DELAYED = "DELAYED",
  ON_HOLD = "ON_HOLD"
}

export enum ProjectCategory {
  PROJECTS = "PROJECTS",
  PRECISION_PROJECTS = "PRECISION_PROJECTS",
  SPARE = "SPARE",
  SERVICE = "SERVICE"
}

// Project Category Management Interface
export interface ProjectCategoryItem {
  id: string;
  name: string;
  code: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  _count?: {
    projects: number;
  };
}

export interface MilestoneTemplate {
  id: string;
  name: string;
  description?: string;
  sequence: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export enum TaskAssigneeType {
  ENGINEER = "ENGINEER",
  OUTSIDE_VENDOR = "OUTSIDE_VENDOR",
  CUSTOMER = "CUSTOMER"
}

export enum SubtaskAssigneeType {
  ENGINEER = "ENGINEER",
  OUTSIDE_VENDOR = "OUTSIDE_VENDOR",
  CUSTOMER = "CUSTOMER"
}

export interface ProjectManager {
  id: string;
  projectId: string;
  userId: string;
  role: string;
  user: User;
  createdAt: string;
  updatedAt: string;
}

export interface Project {
  id?: string; // Optional because it will be generated by the backend (P1, P2, P3)
  name: string;
  code: string;
  projectCategory: ProjectCategory;
  customerId: string;
  customer?: Customer;
  poNumber: string;
  poDate: string;
  poValue?: number; // PO Value field for Project Managers
  startDate: string;
  endDate: string;
  projectManagerId?: string; // Optional for backward compatibility
  projectManager?: User; // Single project manager for backward compatibility
  projectManagerIds?: string[]; // Array of manager IDs for multiple managers
  projectManagers?: ProjectManager[]; // Array of project managers with details
  tasks: Task[];
  sections?: Section[]; // Project sections
  status: TaskStatus;
  createdBy: string;
  createdAt: string;
  updatedAt?: string;
  description?: string; // Optional description field
}

// Payment-related interfaces for Directors
export interface PaymentSummary {
  totalProjects: number;
  totalPayment: number;
  totalPaid: number;
  totalPending: number;
  paidPercentage: number;
  pendingPercentage: number;
}

export interface ProjectPayment {
  id: string;
  projectName: string;
  projectCode: string;
  customerName: string;
  poValue: number;
  paidAmount: number;
  pendingAmount: number;
  paymentStatus: 'FULLY_PAID' | 'PARTIALLY_PAID' | 'PENDING';
  poDate: string;
  startDate: string;
  endDate?: string;
  projectCategory: ProjectCategory;
}

export interface Section {
  id: string;
  projectId: string;
  name: string;
  sequence: number;
  description: string;
  createdAt: string;
  updatedAt: string;
  tasks?: Task[];
  task?: Task[];  // Backend relation property
}

export interface Task {
  id: string;
  displayId: string;
  projectId: string;
  sectionId: string;
  sequence?: number;
  name: string;
  description: string;
  assigneeId: string;
  assigneeType: TaskAssigneeType;
  department: string;
  startDate: string;
  endDate: string;
  status: TaskStatus;
  subtasks: Subtask[];
  section?: Section;
  createdBy: string;
  createdAt: string;
  updatedAt?: string;
  priority?: string;
}

export interface Subtask {
  id: string;
  displayId: string;
  taskId: string;
  sequence?: number;
  name: string;
  description: string;
  assigneeId: string;
  assigneeType: SubtaskAssigneeType;
  startDate: string;
  endDate: string;
  status: TaskStatus;
  totalTime?: number;
  createdBy: string;
  createdAt: string;
  priority?: string;
  department?: string;
}

// MOM (Minutes of Meeting) types
export interface MOMAttendee {
  id: string;
  name: string;
  company: string; // "Mekhos Technology" or customer name
  designation?: string;
  email?: string;
}

export interface MOM {
  id: string;
  projectId?: string;
  customerId?: string;
  engineerId?: string;
  date: string;
  agenda?: string;
  attendees?: MOMAttendee[];
  actionItems?: MOMActionItem[];
  points?: string[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  updatedBy?: string;
  project?: {
    id: string;
    name: string;
    code: string;
    customer: Customer;
  };
  customer?: Customer;
  user_mom_createdByTouser?: {
    id: string;
    name: string;
    email: string;
  };
  user_mom_updatedByTouser?: {
    id: string;
    name: string;
    email: string;
  };
  mompoint?: MOMPoint[];
}

export interface MOMPoint {
  id: string;
  momId: string;
  slNo: number;
  date?: string;
  discussionType?: string; // Changed from MOMDiscussionType to string to allow free text
  station?: string;
  discussion: string;
  actionPlan?: string;
  responsibility?: string;
  plannedDate?: string;
  completionDate?: string;
  status: MOMPointStatus;
  imageAttachment?: string;
  remarks?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
  user_mompoint_createdByTouser?: {
    id: string;
    name: string;
    email: string;
  };
  user_mompoint_updatedByTouser?: {
    id: string;
    name: string;
    email: string;
  };
}

export enum MOMDiscussionType {
  DESIGN = "DESIGN",
  PROCUREMENT = "PROCUREMENT",
  ASSEMBLY = "ASSEMBLY",
  TESTING = "TESTING",
  QUALITY = "QUALITY",
  DELIVERY = "DELIVERY",
  OTHER = "OTHER"
}

export enum MOMPointStatus {
  PENDING = "PENDING",
  IN_PROGRESS = "IN_PROGRESS",
  COMPLETED = "COMPLETED",
  DELAYED = "DELAYED",
  ON_HOLD = "ON_HOLD"
}

export interface MOMActionItem {
  id: string;
  description: string;
  assigneeId?: string;
  dueDate: string;
  status: string;
  projectId?: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  project?: {
    id: string;
    name: string;
    code: string;
  };
}

// Alert types
export enum AlertPriority {
  CRITICAL = "CRITICAL",
  HIGH = "HIGH",
  MEDIUM = "MEDIUM",
  LOW = "LOW"
}

export enum AlertStatus {
  NEW = "NEW",
  IN_PROGRESS = "IN_PROGRESS",
  RESOLVED = "RESOLVED",
  DISMISSED = "DISMISSED"
}

export enum AlertType {
  SYSTEM = "SYSTEM",
  PROJECT = "PROJECT",
  TASK = "TASK",
  DEADLINE = "DEADLINE",
  SECURITY = "SECURITY",
  MAINTENANCE = "MAINTENANCE"
}

export interface Alert {
  id: string;
  type: string;
  message: string;
  priority?: string;
  status?: string;
  read?: boolean;
  createdAt: string;
  updatedAt: string;
}

