import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useAtom } from 'jotai';
import { currentUserAtom, userSubtasksAtom } from '../store';
import { UserRole, TaskStatus } from '../types';
import { useDataService } from '../services/dataService';
import { useManualStatusUpdate } from '../hooks/useStatusUpdater';
import {
  CheckCircle2,
  Clock,
  AlertCircle,
  Play,
  Pause,
  Calendar,
  User,
  FolderOpen,
  Search,
  ChevronDown,
  Target,
  TrendingUp
} from 'lucide-react';

const SubtasksPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentUser] = useAtom(currentUserAtom);
  const [userSubtasks] = useAtom(userSubtasksAtom);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [isLoading, setIsLoading] = useState(false);

  // Data service for refreshing data
  const { refreshData } = useDataService();

  // Status updater for manual triggers
  const { triggerSubtaskUpdate } = useManualStatusUpdate();

  // Read initial filter from URL parameters
  useEffect(() => {
    const statusParam = searchParams.get('status');
    if (statusParam && Object.values(TaskStatus).includes(statusParam as TaskStatus)) {
      setStatusFilter(statusParam);
    }
  }, [searchParams]);

  // Refresh data when component mounts
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        await refreshData();
      } catch (error) {
        console.error('Error refreshing subtasks data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [refreshData]);

  // Filter subtasks based on search and status
  const filteredSubtasks = userSubtasks.filter(subtask => {
    const matchesSearch = searchTerm === '' ||
      subtask.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      subtask.taskName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      subtask.projectName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'ALL' || subtask.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Get status badge styling
  const getStatusBadge = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.NOT_STARTED:
        return 'bg-slate-100 text-slate-800 border-slate-200';
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case TaskStatus.COMPLETED:
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case TaskStatus.DELAYED:
        return 'bg-red-100 text-red-800 border-red-200';
      case TaskStatus.ON_HOLD:
        return 'bg-amber-100 text-amber-800 border-amber-200';
      default:
        return 'bg-slate-100 text-slate-800 border-slate-200';
    }
  };

  // Get status icon
  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.NOT_STARTED:
        return <Clock size={16} className="text-slate-400" />;
      case TaskStatus.IN_PROGRESS:
        return <Play size={16} className="text-blue-600" />;
      case TaskStatus.COMPLETED:
        return <CheckCircle2 size={16} className="text-emerald-600" />;
      case TaskStatus.DELAYED:
        return <AlertCircle size={16} className="text-red-600" />;
      case TaskStatus.ON_HOLD:
        return <Pause size={16} className="text-amber-600" />;
      default:
        return <Clock size={16} className="text-slate-400" />;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'high':
        return 'text-red-600';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  // Check if user has access to subtasks page
  const hasSubtaskAccess = () => {
    if (!currentUser) return false;
    return [
      UserRole.DIRECTOR,
      UserRole.PROJECT_MANAGER,
      UserRole.TEAM_LEAD,
      UserRole.ENGINEER
    ].includes(currentUser.role);
  };

  if (!hasSubtaskAccess()) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center">
        <div className="bg-white p-12 rounded-3xl shadow-2xl border border-slate-200 transform hover:scale-105 transition-all duration-300">
          <div className="text-center">
            <div className="w-24 h-24 bg-gradient-to-br from-red-100 to-red-200 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
              <AlertCircle size={48} className="text-red-500" />
            </div>
            <h2 className="text-2xl font-bold text-slate-900 mb-4">Access Restricted</h2>
            <p className="text-slate-600 max-w-md mx-auto">You don't have permission to access the subtasks management system. Please contact your administrator.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-xl border-b border-slate-200 backdrop-blur-lg bg-white/95">
        <div className="max-w-full mx-auto px-2">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg transform hover:scale-110 transition-all duration-300">
                  <Target className="text-white" size={24} />
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                    {currentUser?.role === UserRole.ENGINEER ? 'My Subtasks' : 'Subtasks'}
                  </h1>
                  <p className="text-slate-600 mt-1">
                    {currentUser?.role === UserRole.ENGINEER
                      ? 'View and manage all subtasks assigned to you'
                      : 'View and manage subtasks based on your role and permissions'
                    }
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="hidden md:flex items-center space-x-2 bg-gradient-to-r from-emerald-100 to-green-100 px-4 py-2 rounded-xl border border-emerald-200">
                  <TrendingUp size={16} className="text-emerald-600" />
                  <span className="text-sm font-medium text-emerald-700">
                    {Math.round((userSubtasks.filter(t => t.status === TaskStatus.COMPLETED).length / Math.max(userSubtasks.length, 1)) * 100)}% Complete
                  </span>
                </div>
                <div className="w-10 h-10 bg-gradient-to-br from-slate-100 to-slate-200 rounded-xl flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer">
                  <User size={20} className="text-slate-600" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-full mx-auto py-8 space-y-8 px-2">
        {/* Search and Filters */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-slate-200/50 w-full">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="relative flex-grow">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10">
                <Search size={20} className="text-slate-400" />
              </div>
              <input
                type="text"
                placeholder="Search subtasks, tasks, or projects..."
                className="w-full pl-12 pr-4 py-4 bg-white/80 backdrop-blur-sm rounded-xl border border-slate-200 focus:border-blue-400 focus:ring-4 focus:ring-blue-100 transition-all duration-300 shadow-sm hover:shadow-md text-slate-900 placeholder-slate-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="relative">
              <select
                className="appearance-none bg-white/80 backdrop-blur-sm border border-slate-200 rounded-xl px-6 py-4 pr-12 focus:border-blue-400 focus:ring-4 focus:ring-blue-100 transition-all duration-300 shadow-sm hover:shadow-md cursor-pointer"
                value={statusFilter}
                onChange={(e) => {
                  const newStatus = e.target.value;
                  setStatusFilter(newStatus);
                  if (newStatus === 'ALL') {
                    searchParams.delete('status');
                  } else {
                    searchParams.set('status', newStatus);
                  }
                  setSearchParams(searchParams);
                }}
              >
                <option value="ALL">All Status</option>
                {Object.values(TaskStatus).map(status => (
                  <option key={status} value={status}>
                    {status.replace(/_/g, ' ')}
                  </option>
                ))}
              </select>
              <ChevronDown size={20} className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 pointer-events-none" />
            </div>
          </div>
        </div>

        {/* Subtasks Table */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-2xl border border-slate-200/50 w-full overflow-hidden">
          {isLoading ? (
            <div className="text-center py-16">
              <div className="relative w-16 h-16 mx-auto mb-6">
                <div className="absolute inset-0 rounded-full border-4 border-slate-200"></div>
                <div className="absolute inset-0 rounded-full border-4 border-blue-500 border-t-transparent animate-spin"></div>
              </div>
              <p className="text-slate-600 text-lg">Loading subtasks...</p>
            </div>
          ) : filteredSubtasks.length === 0 ? (
            <div className="text-center py-16">
              <h3 className="text-xl font-semibold text-slate-900 mb-3">No subtasks found</h3>
              <p className="text-slate-600 max-w-md mx-auto">
                {searchTerm || statusFilter !== 'ALL'
                  ? 'Try adjusting your search or filter criteria.'
                  : currentUser?.role === UserRole.ENGINEER
                    ? 'You don\'t have any subtasks assigned yet.'
                    : 'No subtasks found for your current permissions.'
                }
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200">
                    <th className="px-6 py-6 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">Subtask</th>
                    <th className="px-6 py-6 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">Task & Project</th>
                    <th className="px-6 py-6 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-6 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">Priority</th>
                    <th className="px-6 py-6 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider">Dates</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-100">
                  {filteredSubtasks.map((subtask) => (
                    <tr key={subtask.id} className="hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 transition-all duration-200 group">
                      <td className="px-6 py-6">
                        <div className="max-w-sm">
                          <div className="text-sm font-semibold text-slate-900 mb-1 group-hover:text-blue-700 transition-colors">
                            {subtask.name}
                          </div>
                          {subtask.description && (
                            <div className="text-sm text-slate-500 mb-2 line-clamp-2">
                              {subtask.description}
                            </div>
                          )}
                          <div className="text-xs text-slate-400 font-mono bg-slate-50 px-2 py-1 rounded-md inline-block">
                            ID: {subtask.displayId || subtask.id}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-6">
                        <div>
                          <div className="text-sm font-medium text-slate-900">
                            {subtask.taskName}
                          </div>
                          <div className="text-sm text-slate-500">
                            {subtask.projectName}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-6">
                        <span className={`inline-flex items-center px-3 py-2 rounded-xl text-xs font-semibold border ${getStatusBadge(subtask.status)} shadow-sm`}>
                          {getStatusIcon(subtask.status)}
                          <span className="ml-2">{subtask.status.replace(/_/g, ' ')}</span>
                        </span>
                      </td>
                      <td className="px-6 py-6">
                        <span className={`text-sm font-medium ${getPriorityColor(subtask.priority || 'medium')}`}>
                          {subtask.priority || 'Medium'}
                        </span>
                      </td>
                      <td className="px-6 py-6">
                        <div className="space-y-2">
                          <div className="flex items-center text-sm text-slate-600">
                            <Calendar size={14} className="mr-2 text-slate-400" />
                            <span className="font-medium">Start:</span>
                            <span className="ml-1">{formatDate(subtask.startDate)}</span>
                          </div>
                          <div className="flex items-center text-sm text-slate-600">
                            <Calendar size={14} className="mr-2 text-slate-400" />
                            <span className="font-medium">End:</span>
                            <span className="ml-1">{formatDate(subtask.endDate)}</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubtasksPage;