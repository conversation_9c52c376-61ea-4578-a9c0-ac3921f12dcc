import { use<PERSON>tom } from 'jotai';
import {
  users<PERSON><PERSON>,
  departments<PERSON><PERSON>,
  engineers<PERSON><PERSON>,
  projects<PERSON><PERSON>,
  moms<PERSON><PERSON>,
  customers<PERSON>tom,
  alerts<PERSON><PERSON>
} from '../store';
import {
  usersAPI,
  departmentsAPI,
  engineersAPI,
  projectsAPI,
  momsAPI,
  customersAPI,
  alertsAPI
} from './api';
import { useCallback, useEffect, useMemo } from 'react';
import { dataService } from './dataServiceSingleton';

// React hook to fetch and update data from the API
export const useDataService = () => {
  // Get atom setters
  const [, setUsers] = useAtom(usersAtom);
  const [, setDepartments] = useAtom(departmentsAtom);
  const [, setEngineers] = useAtom(engineersAtom);
  const [, setProjects] = useAtom(projectsAtom);
  const [, setMOMs] = useAtom(momsAtom);
  const [, setCustomers] = useAtom(customersAtom);
  const [, setAlerts] = useAtom(alertsAtom);

  // Load users
  const loadUsers = useCallback(async () => {
    try {
      console.log('Loading users data');
      const response = await usersAPI.getUsers();
      setUsers(response.data);
      console.log(`Successfully loaded ${response.data.length} users`);
      return true;
    } catch (error) {
      console.error('Error loading users data:', error);
      return false;
    }
  }, [setUsers]);



  // Load departments (legacy - keeping for backward compatibility)
  const loadDepartments = useCallback(async () => {
    try {
      console.log('Loading departments data');
      const response = await departmentsAPI.getDepartments();
      setDepartments(response.data);
      console.log(`Successfully loaded ${response.data.length} departments`);
      return true;
    } catch (error) {
      console.error('Error loading departments data:', error);
      return false;
    }
  }, [setDepartments]);

  // Load engineers
  const loadEngineers = useCallback(async () => {
    try {
      console.log('Loading engineers data');
      const response = await engineersAPI.getEngineers();
      setEngineers(response.data);
      console.log(`Successfully loaded ${response.data.length} engineers`);
      return true;
    } catch (error) {
      console.error('Error loading engineers data:', error);
      return false;
    }
  }, [setEngineers]);

  // Load projects
  const loadProjects = useCallback(async () => {
    try {
      console.log('Loading projects data');
      const response = await projectsAPI.getProjects();
      setProjects(response.data);
      console.log(`Successfully loaded ${response.data.length} projects`);
      return true;
    } catch (error) {
      console.error('Error loading projects data:', error);
      return false;
    }
  }, [setProjects]);

  // Load MOMs
  const loadMOMs = useCallback(async () => {
    try {
      console.log('Loading MOMs data');
      const response = await momsAPI.getMOMs();
      setMOMs(response.data);
      console.log(`Successfully loaded ${response.data.length} MOMs`);
      return true;
    } catch (error) {
      console.error('Error loading MOMs data:', error);
      return false;
    }
  }, [setMOMs]);

  // Load customers
  const loadCustomers = useCallback(async () => {
    try {
      console.log('Loading customers data');
      const response = await customersAPI.getCustomers();
      setCustomers(response.data);
      console.log(`Successfully loaded ${response.data.length} customers`);
      return true;
    } catch (error) {
      console.error('Error loading customers data:', error);
      return false;
    }
  }, [setCustomers]);

  // Load alerts
  const loadAlerts = useCallback(async () => {
    try {
      console.log('Loading alerts data');
      const response = await alertsAPI.getAlerts();
      setAlerts(response.data);
      console.log(`Successfully loaded ${response.data.length} alerts`);
      return true;
    } catch (error) {
      console.error('Error loading alerts data:', error);
      return false;
    }
  }, [setAlerts]);

  // Load all data
  const loadAllData = useCallback(async () => {
    try {
      console.log('Loading all application data');

      // Load data sequentially to avoid too many simultaneous connections
      // This helps prevent ERR_INSUFFICIENT_RESOURCES errors
      await loadUsers();
      await loadDepartments(); // Legacy support
      await loadEngineers();
      await loadProjects();
      await loadMOMs();
      await loadCustomers();
      await loadAlerts();

      console.log('Successfully loaded all application data');
      return true;
    } catch (error) {
      console.error('Error loading all application data:', error);
      return false;
    }
  }, [loadUsers, loadDepartments, loadEngineers, loadProjects, loadMOMs, loadCustomers, loadAlerts]);

  // Refresh data function
  const refreshData = useCallback(async () => {
    return loadAllData();
  }, [loadAllData]);

  // Load project categories using singleton
  const loadProjectCategories = useCallback(async () => {
    try {
      console.log('Loading project categories data via singleton');
      const result = await dataService.loadProjectCategories();
      console.log(`Project categories loading result: ${result}`);
      return result;
    } catch (error) {
      console.error('Error loading project categories data:', error);
      return false;
    }
  }, []);

  return useMemo(() => ({
    loadAllData,
    loadUsers,
    loadDepartments,
    loadEngineers,
    loadProjects,
    loadMOMs,
    loadCustomers,
    loadAlerts,
    loadProjectCategories,
    refreshData
  }), [loadAllData, loadUsers, loadDepartments, loadEngineers, loadProjects, loadMOMs, loadCustomers, loadAlerts, loadProjectCategories, refreshData]);
};
