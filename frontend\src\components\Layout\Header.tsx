import React, { useCallback } from 'react';
import { useAtom } from 'jotai';
import { currentUserAtom, alertsAtom, unreadAlertsCountStandaloneAtom } from '../../store';
import { Bell, Search, Menu } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';
import { markAllUnreadAlertsAsRead } from '../../utils/alertUtils';
import { alertsCountService } from '../../services/alertsCountService';

interface HeaderProps {
  toggleMobileSidebar: () => void;
}

const Header: React.FC<HeaderProps> = ({ toggleMobileSidebar }) => {
  const [currentUser] = useAtom(currentUserAtom);
  const [alerts, setAlerts] = useAtom(alertsAtom);
  const [unreadAlertsCount] = useAtom(unreadAlertsCountStandaloneAtom);
  const navigate = useNavigate();
  const location = useLocation();

  // Handle notification bell click
  const handleNotificationClick = useCallback(async () => {
    // Navigate to alerts page first for immediate feedback
    navigate('/alerts');

    // Reset the unread count immediately for better UX
    alertsCountService.resetCount();

    // Mark all unread alerts as read in the background
    try {
      await markAllUnreadAlertsAsRead(alerts, setAlerts);
    } catch (error) {
      console.error('Error marking alerts as read:', error);
      // If marking as read fails, refresh the count from server
      alertsCountService.fetchUnreadCount();
    }
  }, [alerts, setAlerts, navigate]);

  // Determine the page title based on the current route
  const getPageTitle = (): string => {
    const path = location.pathname;

    switch (path) {
      case '/dashboard':
        return 'Dashboard';
      case '/tasks':
        return 'Tasks';
      case '/assigned-tasks':
        return 'Tasks';
      case '/subtasks':
        return 'My Subtasks';
      case '/mom':
        return 'Minutes of Meeting';
      case '/alerts':
        return 'Alerts';
      case '/teams':
        return 'Teams & Engineers';
      case '/managers':
        return 'Managers';
      case '/customers':
        return 'Customers';
      case '/projects':
        return 'Projects';
      case '/users':
        return 'User Management';
      case '/departments':
        return 'Departments';
      case '/settings':
        return 'Settings';
      default:
        return 'Mekhos Project Management';
    }
  };

  const pageTitle = getPageTitle();

  // Get user's initials for avatar
  const getUserInitials = (name: string): string => {
    if (!name) return 'U';
    const names = name.trim().split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    }
    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  };

  return (
    <header className="bg-gradient-to-r from-slate-50 to-blue-50 border-b border-slate-200/50 sticky top-0 z-30 backdrop-blur-sm shadow-lg shadow-slate-200/20">
      <div className="flex items-center justify-between px-6 py-3">
        {/* Left Side - Mobile Menu Button */}
        <div className="flex items-center lg:hidden">
          <button
            type="button"
            className="p-2.5 rounded-xl text-slate-600 hover:text-slate-900 hover:bg-white/70 hover:shadow-md transform hover:scale-105 transition-all duration-200 border border-transparent hover:border-slate-200"
            onClick={toggleMobileSidebar}
            aria-label="Toggle mobile menu"
            title="Toggle mobile menu"
          >
            <Menu size={20} />
          </button>
        </div>

        {/* Spacer */}
        <div className="flex-1"></div>

        {/* Right Side Controls - Fixed Position */}
        <div className="flex items-center space-x-4 flex-shrink-0">
          {/* Notification Bell */}
          <div className="relative">
            <button
              onClick={handleNotificationClick}
              className="p-2.5 text-slate-600 hover:text-slate-900 bg-white/60 hover:bg-white/90 rounded-xl relative transition-all duration-200 border border-slate-200/50 hover:border-slate-300 shadow-sm hover:shadow-md transform hover:scale-105 backdrop-blur-sm"
              title={unreadAlertsCount > 0 ? `${unreadAlertsCount} unread notification${unreadAlertsCount > 1 ? 's' : ''}` : 'View notifications'}
            >
              <Bell size={18} />
              {unreadAlertsCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs min-w-[18px] h-[18px] flex items-center justify-center rounded-full shadow-lg border-2 border-white animate-pulse font-medium">
                  {unreadAlertsCount > 99 ? '99+' : unreadAlertsCount}
                </span>
              )}
            </button>
          </div>

          {/* User Profile Section */}
          <div className="flex items-center space-x-3 bg-white/60 backdrop-blur-sm rounded-xl px-3 py-2 border border-slate-200/50 shadow-sm hover:shadow-md hover:bg-white/80 transition-all duration-200">
            {/* User Info */}
            <div className="hidden md:block">
              <div className="text-sm text-right">
                <p className="font-semibold text-slate-800 leading-tight">
                  {currentUser?.name}
                </p>
                <p className="text-xs text-slate-500 capitalize font-medium">
                  {currentUser?.role.replace(/_/g, ' ')}
                </p>
              </div>
            </div>

            {/* Avatar */}
            <div className="relative">
              <div className="h-9 w-9 rounded-xl bg-gradient-to-br from-slate-200 to-slate-300 overflow-hidden border-2 border-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105">
                {currentUser?.profileImage ? (
                  <img
                    src={currentUser.profileImage}
                    alt={currentUser.name}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="h-full w-full flex items-center justify-center bg-gradient-to-br from-blue-500 to-blue-600 text-white font-bold text-sm">
                    {getUserInitials(currentUser?.name || '')}
                  </div>
                )}
              </div>
              {/* Online Status Indicator */}
              <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full shadow-sm"></div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;