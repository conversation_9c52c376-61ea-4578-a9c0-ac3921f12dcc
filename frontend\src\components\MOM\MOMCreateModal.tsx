import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { projectsAtom, customersAtom, currentUserAtom, momsAtom } from '../../store';
import { X, Plus, Calendar, Save, Trash2 } from 'lucide-react';
import { MOMDiscussionType, MOMPointStatus } from '../../types';
import { formatDateForInput } from '../../utils/dateFormatter';

interface MOMCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (momData: any) => void;
}

interface MOMPointFormData {
  date?: string;
  discussionType?: string; // Changed from MOMDiscussionType to string
  station?: string;
  discussion: string;
  actionPlan?: string;
  responsibility?: string;
  plannedDate?: string;
  completionDate?: string;
  status: MOMPointStatus;
  remarks?: string;
}

const MOMCreateModal: React.FC<MOMCreateModalProps> = ({ isOpen, onClose, onSave }) => {
  const [projects] = useAtom(projectsAtom);
  const [customers] = useAtom(customersAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [moms] = useAtom(momsAtom);

  // Form state
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [meetingDate, setMeetingDate] = useState<string>(formatDateForInput(new Date()));
  const [mekhosAttendees, setMekhosAttendees] = useState<string[]>(['']);
  const [customerAttendees, setCustomerAttendees] = useState<string[]>(['']);
  const [discussionPoints, setDiscussionPoints] = useState<MOMPointFormData[]>([{
    discussion: '',
    status: MOMPointStatus.PENDING
  }]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Get selected project and customer info
  const selectedProject = projects.find(p => p.id === selectedProjectId);
  const selectedCustomer = customers.find(c => c.id === selectedProject?.customerId);

  const addDiscussionPoint = () => {
    setDiscussionPoints([...discussionPoints, {
      discussion: '',
      status: MOMPointStatus.PENDING
    }]);
  };

  const removeDiscussionPoint = (index: number) => {
    if (discussionPoints.length > 1) {
      setDiscussionPoints(discussionPoints.filter((_, i) => i !== index));
    }
  };

  const updateDiscussionPoint = (index: number, field: keyof MOMPointFormData, value: any) => {
    const updated = [...discussionPoints];
    updated[index] = { ...updated[index], [field]: value };
    setDiscussionPoints(updated);
  };

  const addAttendee = (type: 'mekhos' | 'customer') => {
    if (type === 'mekhos') {
      setMekhosAttendees([...mekhosAttendees, '']);
    } else {
      setCustomerAttendees([...customerAttendees, '']);
    }
  };

  const removeAttendee = (type: 'mekhos' | 'customer', index: number) => {
    if (type === 'mekhos' && mekhosAttendees.length > 1) {
      setMekhosAttendees(mekhosAttendees.filter((_, i) => i !== index));
    } else if (type === 'customer' && customerAttendees.length > 1) {
      setCustomerAttendees(customerAttendees.filter((_, i) => i !== index));
    }
  };

  const updateAttendee = (type: 'mekhos' | 'customer', index: number, value: string) => {
    if (type === 'mekhos') {
      const updated = [...mekhosAttendees];
      updated[index] = value;
      setMekhosAttendees(updated);
    } else {
      const updated = [...customerAttendees];
      updated[index] = value;
      setCustomerAttendees(updated);
    }
  };

  const validate = (): boolean => {
    const newErrors: {[key: string]: string} = {};

    if (!selectedProjectId) {
      newErrors.project = 'Project is required';
    } else {
      // Check if MOM already exists for this project
      const existingMOM = moms.find(mom => mom.projectId === selectedProjectId);
      if (existingMOM) {
        newErrors.project = `MOM already exists for this project. Please edit the existing MOM instead.`;
      }
    }

    if (!meetingDate) newErrors.date = 'Meeting date is required';

    // Check if at least one discussion point has content
    if (!discussionPoints.some(point => point.discussion.trim())) {
      newErrors.points = 'At least one discussion point is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validate()) return;

    setIsSubmitting(true);
    try {
      const momData = {
        projectId: selectedProjectId,
        date: new Date(meetingDate).toISOString(),
        mekhosAttendees: mekhosAttendees.filter(name => name.trim()),
        customerAttendees: customerAttendees.filter(name => name.trim()),
        discussionPoints: discussionPoints.filter(point => point.discussion.trim())
      };

      onSave(momData);
    } catch (error) {
      console.error('Error creating MOM:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setSelectedProjectId('');
    setMeetingDate(formatDateForInput(new Date()));
    setMekhosAttendees(['']);
    setCustomerAttendees(['']);
    setDiscussionPoints([{
      discussion: '',
      status: MOMPointStatus.PENDING
    }]);
    setErrors({});
  };

  useEffect(() => {
    if (isOpen) {
      resetForm();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-7xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Calendar className="h-5 w-5 text-green-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900">Create Minutes of Meeting</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Project Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Project *
            </label>
            <select
              value={selectedProjectId}
              onChange={(e) => {
                const projectId = e.target.value;
                setSelectedProjectId(projectId);

                // Clear existing project errors
                setErrors(prev => ({ ...prev, project: '' }));

                // Check for duplicate MOM in real-time
                if (projectId) {
                  const existingMOM = moms.find(mom => mom.projectId === projectId);
                  if (existingMOM) {
                    setErrors(prev => ({
                      ...prev,
                      project: 'MOM already exists for this project. Please edit the existing MOM instead.'
                    }));
                  }
                }
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select a project...</option>
              {projects.map(project => (
                <option key={project.id} value={project.id}>
                  {project.name} ({project.code})
                </option>
              ))}
            </select>
            {errors.project && <p className="mt-1 text-sm text-red-600">{errors.project}</p>}
          </div>

          {/* Project Information */}
          {selectedProject && (
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Project Information</h3>
              <div className="grid grid-cols-2 gap-6 bg-blue-50 p-4 rounded-lg">
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">Project:</label>
                  <p className="text-blue-900 font-medium">{selectedProject.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">Project Code:</label>
                  <p className="text-blue-900 font-medium">{selectedProject.code}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">Customer:</label>
                  <p className="text-blue-900 font-medium">{selectedCustomer?.name || 'N/A'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-blue-700 mb-1">PO Number:</label>
                  <p className="text-blue-900 font-medium">{selectedProject.poNumber || 'N/A'}</p>
                </div>
              </div>
            </div>
          )}

          {/* Meeting Date */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="inline h-4 w-4 mr-1" />
              Meeting Date *
            </label>
            <input
              type="date"
              value={meetingDate}
              onChange={(e) => setMeetingDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {errors.date && <p className="mt-1 text-sm text-red-600">{errors.date}</p>}
          </div>

          {/* Attendees */}
          <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Mekhos Attendees */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-700">Mekhos Attendees</label>
                <button
                  onClick={() => addAttendee('mekhos')}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  <Plus className="h-4 w-4 inline mr-1" />
                  Add
                </button>
              </div>
              {mekhosAttendees.map((attendee, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={attendee}
                    onChange={(e) => updateAttendee('mekhos', index, e.target.value)}
                    placeholder="Attendee name"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  {mekhosAttendees.length > 1 && (
                    <button
                      onClick={() => removeAttendee('mekhos', index)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded transition-colors"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>

            {/* Customer Attendees */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-700">Customer Attendees</label>
                <button
                  onClick={() => addAttendee('customer')}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  <Plus className="h-4 w-4 inline mr-1" />
                  Add
                </button>
              </div>
              {customerAttendees.map((attendee, index) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <input
                    type="text"
                    value={attendee}
                    onChange={(e) => updateAttendee('customer', index, e.target.value)}
                    placeholder="Attendee name"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  {customerAttendees.length > 1 && (
                    <button
                      onClick={() => removeAttendee('customer', index)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded transition-colors"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Discussion Points */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Discussion Points</h3>
              <button
                onClick={addDiscussionPoint}
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Point
              </button>
            </div>
            {errors.points && <p className="mb-3 text-sm text-red-600">{errors.points}</p>}

            {/* Table */}
            <div className="overflow-x-auto">
              <table className="w-full border border-gray-300 rounded-lg overflow-hidden">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Date</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Type</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Station</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Discussion</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Action Plan</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Responsibility</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Planned Date</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Completion Date</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Status</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Result/Comments</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {discussionPoints.map((point, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-2 py-2 border-r border-gray-300">
                        <input
                          type="date"
                          value={point.date || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'date', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="dd-mm-yyyy"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <input
                          type="text"
                          value={point.discussionType || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'discussionType', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="Discussion Type"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <input
                          type="text"
                          value={point.station || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'station', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="Station"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <textarea
                          value={point.discussion}
                          onChange={(e) => updateDiscussionPoint(index, 'discussion', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                          rows={2}
                          placeholder="Enter discussion"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <textarea
                          value={point.actionPlan || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'actionPlan', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                          rows={2}
                          placeholder="Action plan"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <input
                          type="text"
                          value={point.responsibility || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'responsibility', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="Select Assignee"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <input
                          type="date"
                          value={point.plannedDate || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'plannedDate', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="dd-mm-yyyy"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <input
                          type="date"
                          value={point.completionDate || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'completionDate', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="dd-mm-yyyy"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <select
                          value={point.status}
                          onChange={(e) => updateDiscussionPoint(index, 'status', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          {Object.values(MOMPointStatus).map(status => (
                            <option key={status} value={status}>{status}</option>
                          ))}
                        </select>
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <textarea
                          value={point.remarks || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'remarks', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                          rows={2}
                          placeholder="Result/comments"
                        />
                      </td>
                      <td className="px-2 py-2">
                        {discussionPoints.length > 1 && (
                          <button
                            onClick={() => removeDiscussionPoint(index)}
                            className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors"
                            title="Remove point"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSubmitting}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MOMCreateModal;
