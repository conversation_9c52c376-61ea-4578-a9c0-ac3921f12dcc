import { Request, Response, NextFunction } from 'express';
import { prisma } from '../index';
import { generateToken, comparePassword, hashPassword, normalizeEmail } from '../utils/auth.utils';
import { v4 as uuidv4 } from 'uuid';

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
export const register = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password, name, role, department } = req.body;

    // Normalize email to lowercase
    const normalizedEmail = normalizeEmail(email);

    // Check if user already exists
    const userExists = await prisma.user.findUnique({
      where: { email: normalizedEmail },
    });

    if (userExists) {
      res.status(400).json({
        success: false,
        message: 'User already exists',
      });
      return;
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Generate a unique ID for the user
    const userId = uuidv4();
    const now = new Date();

    // Create user
    const user = await prisma.user.create({
      data: {
        id: userId,
        email: normalizedEmail,
        password: hashedPassword,
        name,
        role,
        department,
        updatedAt: now,
      },
    });

    // Generate token
    const token = generateToken(user.id);

    res.status(201).json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department,
        token,
      },
    });
  } catch (error) {
    console.error('Register error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
export const login = async (req: Request, res: Response): Promise<void> => {
  try {
    console.log('🔐 Login attempt:', req.body);
    const { email, password } = req.body;

    // Normalize email to lowercase
    const normalizedEmail = normalizeEmail(email);

    console.log(`🔍 Looking for user with email: ${normalizedEmail}`);
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email: normalizedEmail },
    });

    if (!user) {
      console.log(`❌ User not found with email: ${email}`);
      res.status(401).json({
        success: false,
        message: 'Invalid credentials',
      });
      return;
    }

    console.log(`✅ User found: ${user.name} (${user.role})`);
    console.log(`🔑 Comparing password for user: ${email}`);

    // Check if password matches
    const isMatch = await comparePassword(password, user.password);

    if (!isMatch) {
      console.log(`❌ Password mismatch for user: ${email}`);
      res.status(401).json({
        success: false,
        message: 'Invalid credentials',
      });
      return;
    }

    console.log(`✅ Password match successful for user: ${email}`);

    // Generate token
    const token = generateToken(user.id);

    res.status(200).json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department,
        profileImage: user.profileImage,
        token,
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get current user
// @route   GET /api/auth/me
// @access  Private
export const getMe = async (req: Request, res: Response): Promise<void> => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });

    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department,
        profileImage: user.profileImage,
      },
    });
  } catch (error) {
    console.error('Get me error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Change user password
// @route   POST /api/auth/change-password
// @access  Private
export const changePassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({
        success: false,
        message: 'Not authorized',
      });
      return;
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found',
      });
      return;
    }

    // Verify current password
    const isMatch = await comparePassword(currentPassword, user.password);

    if (!isMatch) {
      res.status(401).json({
        success: false,
        message: 'Current password is incorrect',
      });
      return;
    }

    // Hash new password
    const hashedPassword = await hashPassword(newPassword);

    // Update user password and set passwordChanged to 'Y'
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
        passwordChanged: 'Y',
      },
    });

    res.status(200).json({
      success: true,
      message: 'Password changed successfully',
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
