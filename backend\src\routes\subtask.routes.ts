import express from 'express';
import {
  getSubtasks,
  getSubtask,
  createSubtask,
  updateSubtask,
  deleteSubtask,
} from '../controllers/subtask.controller';
import { protect, authorize } from '../middleware/auth.middleware';
import subtaskCommentRoutes from './subtaskComment.routes';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(getSubtasks)
  .post(
    authorize('DIRECTOR', 'PROJECT_MANAGER'),
    createSubtask
  );

router.route('/:id')
  .get(getSubtask)
  .put(updateSubtask) // All authenticated users can update their assigned subtasks
  .delete(
    authorize('DIRECTOR', 'PROJECT_MANAGER'),
    deleteSubtask
  );

// Comment routes
router.use('/:subtaskId/comments', subtaskCommentRoutes);

export default router;
