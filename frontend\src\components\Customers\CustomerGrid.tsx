import React, { useState, useRef, useEffect, KeyboardEvent } from 'react';
import { Plus, Trash2, Check, X, FileText, Users } from 'lucide-react';
import { Customer } from '../../types';
import { customersAPI } from '../../services/api';
import { normalizeEmail } from '../../utils/dateValidation';
import { validateName, validateEmail, validateCode, validatePhone } from '../../utils/validation';

interface CustomerGridProps {
  customers: Customer[];
  onCustomersChange: (customers: Customer[]) => void;
  canManage: boolean;
  onError: (error: string | null) => void;
  onSuccess: (message: string | null) => void;
  projects: any[];
  moms: any[];
  showNewRow?: boolean;
  onAddCustomer?: () => void;
  onHideNewRow?: () => void;
}

interface EditingCell {
  customerId: string;
  field: string;
}

interface NewCustomerRow {
  name: string;
  code: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
}

const CustomerGrid: React.FC<CustomerGridProps> = ({
  customers,
  onCustomersChange,
  canManage,
  onError,
  onSuccess,
  projects,
  moms,
  showNewRow = false,
  onAddCustomer,
  onHideNewRow
}) => {
  const [editingCell, setEditingCell] = useState<EditingCell | null>(null);
  const [editValue, setEditValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [newCustomerRow, setNewCustomerRow] = useState<NewCustomerRow>({
    name: '',
    code: '',
    contactName: '',
    email: '',
    phone: '',
    address: ''
  });
  const [fieldErrors, setFieldErrors] = useState<{ [key in keyof NewCustomerRow]?: string }>({});
  const [editingNewRow, setEditingNewRow] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Focus input when editing starts
  useEffect(() => {
    if (editingCell && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [editingCell]);

  // Get customer stats
  const getCustomerStats = (customerId: string) => {
    const customerProjects = projects.filter(project => project.customerId === customerId);
    const customerMOMs = moms.filter(mom => mom.customerId === customerId);
    return {
      projectCount: customerProjects.length,
      momCount: customerMOMs.length
    };
  };

  // Start editing a cell
  const startEditing = (customerId: string, field: string, currentValue: string) => {
    if (!canManage) return;
    setEditingCell({ customerId, field });
    setEditValue(currentValue || '');
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingCell(null);
    setEditValue('');
  };

  // Validate field value
  const validateField = (field: string, value: string): { isValid: boolean; message?: string } => {
    switch (field) {
      case 'name':
        return validateName(value);
      case 'code':
        return validateCode(value);
      case 'email':
        if (!value || value.trim().length === 0) {
          return { isValid: false, message: 'Email address is required' };
        }
        return validateEmail(value);
      case 'phone':
        if (!value || value.trim().length === 0) {
          return { isValid: false, message: 'Phone number is required' };
        }
        const digitsOnly = value.replace(/\D/g, '');
        if (digitsOnly.length !== 10) {
          return { isValid: false, message: 'Phone number must be exactly 10 digits' };
        }
        return { isValid: true };
      case 'contactName':
        return value ? validateName(value) : { isValid: true };
      default:
        return { isValid: true };
    }
  };

  // Save cell value
  const saveCellValue = async () => {
    if (!editingCell) return;

    const { customerId, field } = editingCell;
    const customer = customers.find(c => c.id === customerId);
    if (!customer) return;

    // Validate the value
    const validation = validateField(field, editValue);
    if (!validation.isValid) {
      onError(validation.message || 'Invalid value');
      return;
    }

    // Check for duplicates for name and code
    if (field === 'name' || field === 'code') {
      const duplicate = customers.find(c => 
        c.id !== customerId && 
        c[field as keyof Customer]?.toString().toLowerCase() === editValue.toLowerCase()
      );
      if (duplicate) {
        onError(`A customer with this ${field} already exists`);
        return;
      }
    }

    setIsSubmitting(true);
    onError(null);

    try {
      const updatedData = {
        ...customer,
        [field]: field === 'email' && editValue ? normalizeEmail(editValue) : editValue || null
      };

      const response = await customersAPI.updateCustomer(customerId, updatedData);
      
      // Update local state
      onCustomersChange(customers.map(c => 
        c.id === customerId ? response.data : c
      ));

      onSuccess(`Customer ${field} updated successfully!`);
      cancelEditing();
    } catch (error: any) {
      console.error('Error updating customer:', error);
      onError(error.response?.data?.message || 'Failed to update customer');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle key press in input
  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      saveCellValue();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      cancelEditing();
    }
  };

  // Handle new customer row field change
  const handleNewRowChange = (field: keyof NewCustomerRow, value: string) => {
    setNewCustomerRow(prev => ({
      ...prev,
      [field]: value
    }));

    // Real-time validation - only show errors if user has typed something
    if (value.length > 0) {
      const validation = validateField(field, value);
      setFieldErrors(prev => ({
        ...prev,
        [field]: validation.isValid ? undefined : validation.message
      }));
    } else {
      // Clear error when field is empty
      setFieldErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  // Start editing new row field
  const startEditingNewRow = (field: string) => {
    if (!canManage) return;
    setEditingNewRow(field);
  };

  // Save new customer
  const saveNewCustomer = async () => {
    if (!canManage) return;

    // Check if required fields are provided - but don't show general error
    if (!newCustomerRow.name.trim() || !newCustomerRow.code.trim() || !newCustomerRow.email.trim() || !newCustomerRow.phone.trim()) {
      // Don't show general error - let individual field validation handle it
      return;
    }

    // Validate fields - errors are already shown in individual fields
    const nameValidation = validateName(newCustomerRow.name);
    if (!nameValidation.isValid) return;

    const codeValidation = validateCode(newCustomerRow.code);
    if (!codeValidation.isValid) return;

    const emailValidation = validateEmail(newCustomerRow.email);
    if (!emailValidation.isValid) return;

    const phoneValidation = validateField('phone', newCustomerRow.phone);
    if (!phoneValidation.isValid) return;

    // Check for duplicates
    const duplicateName = customers.find(c =>
      c.name.toLowerCase() === newCustomerRow.name.toLowerCase()
    );
    if (duplicateName) {
      onError('A customer with this name already exists');
      return;
    }

    const duplicateCode = customers.find(c =>
      c.code.toLowerCase() === newCustomerRow.code.toLowerCase()
    );
    if (duplicateCode) {
      onError('A customer with this code already exists');
      return;
    }

    setIsSubmitting(true);
    onError(null);

    try {
      const customerData = {
        name: newCustomerRow.name.trim(),
        code: newCustomerRow.code.trim(),
        contactName: newCustomerRow.contactName.trim() || null,
        email: newCustomerRow.email ? normalizeEmail(newCustomerRow.email) : null,
        phone: newCustomerRow.phone.trim() || null,
        address: newCustomerRow.address.trim() || null
      };

      const response = await customersAPI.createCustomer(customerData);
      onCustomersChange([...customers, response.data]);
      onSuccess('New customer added successfully!');

      // Reset the new row
      setNewCustomerRow({
        name: '',
        code: '',
        contactName: '',
        email: '',
        phone: '',
        address: ''
      });
      setFieldErrors({});
      setEditingNewRow(null);
    } catch (error: any) {
      console.error('Error creating customer:', error);
      onError(error.response?.data?.message || 'Failed to create customer');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle key press in new row
  const handleNewRowKeyPress = (e: KeyboardEvent<HTMLInputElement>, field: string) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      // If this is the last required field and both name and code are filled, save
      if ((field === 'name' || field === 'code') && newCustomerRow.name.trim() && newCustomerRow.code.trim()) {
        saveNewCustomer();
      } else {
        // Move to next field or save if all required fields are filled
        setEditingNewRow(null);
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setEditingNewRow(null);
    }
  };

  // Clear/cancel new customer row
  const clearNewCustomerRow = () => {
    setNewCustomerRow({
      name: '',
      code: '',
      contactName: '',
      email: '',
      phone: '',
      address: ''
    });
    setFieldErrors({});
    setEditingNewRow(null);
    if (onHideNewRow) {
      onHideNewRow();
    }
  };

  // Delete customer
  const deleteCustomer = async (customerId: string) => {
    if (!canManage) return;

    const stats = getCustomerStats(customerId);
    if (stats.projectCount > 0 || stats.momCount > 0) {
      onError(`Cannot delete customer. It has ${stats.projectCount} projects and ${stats.momCount} MOMs associated with it.`);
      return;
    }

    setDeletingId(customerId);
    onError(null);

    try {
      await customersAPI.deleteCustomer(customerId);
      onCustomersChange(customers.filter(c => c.id !== customerId));
      onSuccess('Customer deleted successfully!');
    } catch (error: any) {
      console.error('Error deleting customer:', error);
      onError(error.response?.data?.message || 'Failed to delete customer');
    } finally {
      setDeletingId(null);
    }
  };

  // Render editable cell
  const renderEditableCell = (customer: Customer, field: keyof Customer, value: string) => {
    const isEditing = editingCell?.customerId === customer.id && editingCell?.field === field;

    if (isEditing) {
      return (
        <div className="flex items-center space-x-2">
          <input
            ref={inputRef}
            type={field === 'email' ? 'email' : 'text'}
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyPress}
            className="flex-1 px-2 py-1 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isSubmitting}
          />
          <button
            onClick={saveCellValue}
            disabled={isSubmitting}
            className="text-green-600 hover:text-green-800 p-1"
            title="Save"
          >
            <Check size={16} />
          </button>
          <button
            onClick={cancelEditing}
            disabled={isSubmitting}
            className="text-red-600 hover:text-red-800 p-1"
            title="Cancel"
          >
            <X size={16} />
          </button>
        </div>
      );
    }

    return (
      <div
        className={`cursor-pointer hover:bg-blue-50 p-2 rounded ${canManage ? 'hover:bg-blue-50' : ''}`}
        onClick={() => canManage && startEditing(customer.id, field, value)}
        title={canManage ? 'Click to edit' : 'Read-only'}
      >
        {value || '-'}
      </div>
    );
  };

  // Render new customer row cell
  const renderNewRowCell = (field: keyof NewCustomerRow) => {
    const isEditing = editingNewRow === field;
    const value = newCustomerRow[field];
    const error = fieldErrors[field];
    const isRequired = ['name', 'code', 'email', 'phone'].includes(field);

    if (isEditing) {
      return (
        <div className="w-full">
          <input
            ref={inputRef}
            type={field === 'email' ? 'email' : 'text'}
            value={value}
            onChange={(e) => handleNewRowChange(field, e.target.value)}
            onKeyDown={(e) => handleNewRowKeyPress(e, field)}
            onBlur={() => setEditingNewRow(null)}
            className={`w-full px-2 py-1 border rounded focus:outline-none focus:ring-2 ${
              error
                ? 'border-red-300 focus:ring-red-500'
                : 'border-blue-300 focus:ring-blue-500'
            }`}
            placeholder={`Enter ${field === 'contactName' ? 'contact name' : field}${isRequired ? ' *' : ''}`}
            disabled={isSubmitting}
            autoFocus
          />
          {error && (
            <div className="text-red-500 text-xs mt-1 px-1">
              {error}
            </div>
          )}
        </div>
      );
    }

    return (
      <div
        className={`cursor-pointer hover:bg-blue-50 p-2 rounded min-h-[2rem] flex items-center ${canManage ? 'hover:bg-blue-50' : ''}`}
        onClick={() => canManage && startEditingNewRow(field)}
        title={canManage ? 'Click to add' : 'Read-only'}
      >
        {value || (canManage ? `Add ${field === 'contactName' ? 'contact name' : field}${isRequired ? ' *' : ''}...` : '-')}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100">
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="bg-gradient-to-r from-gray-50 to-slate-50 border-b border-gray-200">
              <th className="pl-6 pr-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Code *</th>
              <th className="px-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Company Name *</th>
              <th className="px-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Contact Person</th>
              <th className="px-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Email *</th>
              <th className="px-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Phone *</th>
              <th className="px-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Address</th>
              <th className="px-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Projects</th>
              <th className="px-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">Meetings</th>
              {canManage && (
                <th className="pr-6 pl-2 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {customers.map((customer, index) => {
              const stats = getCustomerStats(customer.id);

              return (
                <tr key={customer.id} className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 group`}>
                  <td className="pl-6 pr-2 py-2">
                    {renderEditableCell(customer, 'code', customer.code)}
                  </td>
                  <td className="px-2 py-2">
                    {renderEditableCell(customer, 'name', customer.name)}
                  </td>
                  <td className="px-2 py-2">
                    {renderEditableCell(customer, 'contactName', customer.contactName || '')}
                  </td>
                  <td className="px-2 py-2">
                    {renderEditableCell(customer, 'email', customer.email || '')}
                  </td>
                  <td className="px-2 py-2">
                    {renderEditableCell(customer, 'phone', customer.phone || '')}
                  </td>
                  <td className="px-2 py-2">
                    {renderEditableCell(customer, 'address', customer.address || '')}
                  </td>
                  <td className="px-2 py-4">
                    <div className="flex items-center bg-blue-50 rounded-lg px-3 py-2 w-fit">
                      <div className="bg-blue-100 rounded-full p-1 mr-2">
                        <FileText size={14} className="text-blue-600" />
                      </div>
                      <span className="font-semibold text-blue-700">{stats.projectCount}</span>
                    </div>
                  </td>
                  <td className="px-2 py-4">
                    <div className="flex items-center bg-green-50 rounded-lg px-3 py-2 w-fit">
                      <div className="bg-green-100 rounded-full p-1 mr-2">
                        <Users size={14} className="text-green-600" />
                      </div>
                      <span className="font-semibold text-green-700">{stats.momCount}</span>
                    </div>
                  </td>
                  {canManage && (
                    <td className="pr-6 pl-2 py-4">
                      <button
                        className="bg-red-100 hover:bg-red-200 text-red-700 p-2 rounded-lg hover:shadow-lg transform hover:scale-110 transition-all duration-200"
                        onClick={() => deleteCustomer(customer.id)}
                        disabled={deletingId === customer.id}
                        title="Delete Customer"
                      >
                        {deletingId === customer.id ? (
                          <svg className="animate-spin h-4 w-4 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        ) : (
                          <Trash2 size={14} />
                        )}
                      </button>
                    </td>
                  )}
                </tr>
              );
            })}

            {/* New customer row - only show when showNewRow is true */}
            {canManage && showNewRow && (
              <tr className={`border-t-2 border-blue-200 ${customers.length % 2 === 0 ? 'bg-blue-50/30' : 'bg-blue-100/30'} hover:bg-blue-50/50 transition-all duration-300`}>
                <td className="pl-6 pr-2 py-2">
                  {renderNewRowCell('code')}
                </td>
                <td className="px-2 py-2">
                  {renderNewRowCell('name')}
                </td>
                <td className="px-2 py-2">
                  {renderNewRowCell('contactName')}
                </td>
                <td className="px-2 py-2">
                  {renderNewRowCell('email')}
                </td>
                <td className="px-2 py-2">
                  {renderNewRowCell('phone')}
                </td>
                <td className="px-2 py-2">
                  {renderNewRowCell('address')}
                </td>
                <td className="px-2 py-4">
                  <div className="flex items-center bg-gray-100 rounded-lg px-3 py-2 w-fit">
                    <div className="bg-gray-200 rounded-full p-1 mr-2">
                      <FileText size={14} className="text-gray-500" />
                    </div>
                    <span className="font-semibold text-gray-500">0</span>
                  </div>
                </td>
                <td className="px-2 py-4">
                  <div className="flex items-center bg-gray-100 rounded-lg px-3 py-2 w-fit">
                    <div className="bg-gray-200 rounded-full p-1 mr-2">
                      <Users size={14} className="text-gray-500" />
                    </div>
                    <span className="font-semibold text-gray-500">0</span>
                  </div>
                </td>
                <td className="pr-6 pl-2 py-4">
                  <div className="flex items-center space-x-2">
                    {(newCustomerRow.name.trim() && newCustomerRow.code.trim()) ? (
                      <button
                        onClick={saveNewCustomer}
                        disabled={isSubmitting}
                        className="bg-green-100 hover:bg-green-200 text-green-700 p-2 rounded-lg hover:shadow-lg transform hover:scale-110 transition-all duration-200"
                        title="Save New Customer"
                      >
                        {isSubmitting ? (
                          <svg className="animate-spin h-4 w-4 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        ) : (
                          <Check size={14} />
                        )}
                      </button>
                    ) : (
                      <span className="text-gray-400 text-sm">Fill name & code</span>
                    )}
                    <button
                      onClick={clearNewCustomerRow}
                      disabled={isSubmitting}
                      className="bg-red-100 hover:bg-red-200 text-red-700 p-2 rounded-lg hover:shadow-lg transform hover:scale-110 transition-all duration-200"
                      title="Cancel/Clear"
                    >
                      <X size={14} />
                    </button>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CustomerGrid;
