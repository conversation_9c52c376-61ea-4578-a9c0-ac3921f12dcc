import React from 'react';
import { use<PERSON>tom } from 'jotai';
import { currentUserAtom } from '../../store';
import { UserRole } from '../../types';
import { Navigate } from 'react-router-dom';
import { Shield, AlertTriangle } from 'lucide-react';

interface DirectorOnlyRouteProps {
  children: React.ReactNode;
}

const DirectorOnlyRoute: React.FC<DirectorOnlyRouteProps> = ({ children }) => {
  const [currentUser] = useAtom(currentUserAtom);

  // If no user is logged in, redirect to login
  if (!currentUser) {
    return <Navigate to="/login" replace />;
  }

  // If user is not a Director, show access denied page
  if (currentUser.role !== UserRole.DIRECTOR) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
        <div className="max-w-md w-full">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
              <Shield className="h-8 w-8 text-red-600" />
            </div>
            
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Access Restricted
            </h1>
            
            <div className="flex items-center justify-center mb-4">
              <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
              <span className="text-amber-700 font-medium">Director Access Only</span>
            </div>
            
            <p className="text-gray-600 mb-6">
              This page is restricted to Directors only. You don't have the necessary permissions to access this content.
            </p>
            
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-700">
                <strong>Your Role:</strong> {currentUser.role.replace('_', ' ')}
              </p>
              <p className="text-sm text-gray-700 mt-1">
                <strong>Required Role:</strong> Director
              </p>
            </div>
            
            <button
              onClick={() => window.history.back()}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
            >
              Go Back
            </button>
            
            <p className="text-xs text-gray-500 mt-4">
              If you believe this is an error, please contact your system administrator.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // If user is a Director, render the children
  return <>{children}</>;
};

export default DirectorOnlyRoute;
