import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { currentUserAtom } from '../../store';
import { paymentsAPI } from '../../services/api';
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip
} from 'recharts';
import {
  DollarSign
} from 'lucide-react';

interface PaymentData {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

interface PaymentSummary {
  totalProjects: number;
  totalPoValue: number;
  totalPaid: number;
  totalPending: number;
  paidPercentage: number;
  pendingPercentage: number;
}

const PaymentChart: React.FC = () => {
  const [currentUser] = useAtom(currentUserAtom);
  const [paymentSummary, setPaymentSummary] = useState<PaymentSummary>({
    totalProjects: 0,
    totalPoValue: 0,
    totalPaid: 0,
    totalPending: 0,
    paidPercentage: 0,
    pendingPercentage: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if user is Director
  const isDirector = currentUser?.role === 'DIRECTOR';

  useEffect(() => {
    if (isDirector) {
      fetchPaymentData();
    }
  }, [isDirector]);

  const fetchPaymentData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await paymentsAPI.getPaymentSummary();

      if (response.success) {
        setPaymentSummary(response.data);
      } else {
        setError('Failed to fetch payment data');
      }
    } catch (err) {
      console.error('Error fetching payment data:', err);
      setError('Failed to load payment data');
    } finally {
      setLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Prepare chart data
  const chartData: PaymentData[] = [
    {
      name: 'Paid',
      value: paymentSummary.totalPaid,
      percentage: paymentSummary.paidPercentage,
      color: '#10B981' // Green
    },
    {
      name: 'Pending',
      value: paymentSummary.totalPending,
      percentage: paymentSummary.pendingPercentage,
      color: '#F59E0B' // Orange
    }
  ];

  // Custom tooltip for the pie chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2 mb-1">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: data.color }}
            ></div>
            <span className="font-medium text-gray-900">{data.name}</span>
          </div>
          <div className="text-sm text-gray-600">
            <div>Amount: {formatCurrency(data.value)}</div>
            <div>Percentage: {data.percentage.toFixed(1)}%</div>
          </div>
        </div>
      );
    }
    return null;
  };

  // Don't render for non-directors
  if (!isDirector) {
    return null;
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden h-full flex flex-col">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center">
          <DollarSign className="w-5 h-5 text-gray-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">Payment Status Distribution</h3>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 flex-1 flex flex-col min-h-0">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent mx-auto mb-3"></div>
              <p className="text-gray-600">Loading...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500 mb-3">{error}</p>
              <button
                onClick={fetchPaymentData}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Try Again
              </button>
            </div>
          </div>
        ) : paymentSummary.totalPoValue === 0 ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-500">No payment data available</p>
            </div>
          </div>
        ) : (
          <div className="flex items-center space-x-6 h-full">
            {/* Pie Chart */}
            <div className="flex-1 h-full min-h-[200px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    outerRadius="80%"
                    paddingAngle={2}
                    dataKey="value"
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </div>

            {/* Simple Legend */}
            <div className="flex-1 space-y-4 flex flex-col justify-center">
              {chartData.map((entry, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: entry.color }}
                  ></div>
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-gray-700">{entry.name}</span>
                    <span className="text-xs text-gray-500">{entry.percentage.toFixed(1)}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentChart;
