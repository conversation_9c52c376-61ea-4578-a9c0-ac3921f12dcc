import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAtom } from 'jotai';
import { normalizedProjectsAtom, customersAtom, currentUserAtom } from '../store';
import { format } from 'date-fns';
import { ArrowLeft, Calendar, Clock, Building, FileText, Edit, Trash2, ArrowUpRight, ArrowDownRight, Minus, Target, TrendingUp, CheckCircle, PlayCircle, PauseCircle, AlertTriangle, FileSpreadsheet } from 'lucide-react';
import TaskTable from '../components/Tasks/TaskTable';
import SectionTaskView from '../components/Tasks/SectionTaskView';
import ProjectEditModal from '../components/Projects/ProjectEditModal';
import ProjectExcelManager from '../components/Projects/ProjectExcelManager';

import { TaskStatus, ProjectCategory } from '../types';
import { dataService } from '../services/dataServiceSingleton';

const ProjectDetailsPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const [projects] = useAtom(normalizedProjectsAtom);
  const [customers] = useAtom(customersAtom);
  const [currentUser] = useAtom(currentUserAtom);

  // Edit modal state
  const [showEditModal, setShowEditModal] = useState(false);
  const [showExcelManager, setShowExcelManager] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(Date.now());

  // Animation trigger
  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Refresh project data when page becomes visible or when needed
  useEffect(() => {
    const refreshProjectData = async () => {
      try {
        console.log('🔄 Refreshing project data for ProjectDetailsPage...');
        await dataService.loadProjects();
        setLastRefresh(Date.now());
        console.log('✅ Project data refreshed successfully');
      } catch (error) {
        console.error('❌ Error refreshing project data:', error);
      }
    };

    // Refresh on mount
    refreshProjectData();

    // Add visibility change listener to refresh when page becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('📱 Page became visible, refreshing project data...');
        refreshProjectData();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Refresh every 30 seconds when page is visible
    const refreshInterval = setInterval(() => {
      if (!document.hidden) {
        console.log('⏰ Periodic refresh of project data...');
        refreshProjectData();
      }
    }, 30000);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(refreshInterval);
    };
  }, [projectId]);

  // Find the project by ID
  const project = projects.find(p => p.id === projectId);

  // Calculate days remaining based on project status and dates
  const getDaysRemaining = (startDate: string, endDate: string, status: string): { days: number; type: 'start' | 'end' | 'overdue' } => {
    const today = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);

    // If project hasn't started yet, show days until start
    if (status === 'NOT_STARTED' && today < start) {
      const diffTime = start.getTime() - today.getTime();
      return {
        days: Math.ceil(diffTime / (1000 * 60 * 60 * 24)),
        type: 'start'
      };
    }

    // If project is active or completed, show days until end (or overdue)
    const diffTime = end.getTime() - today.getTime();
    const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return {
      days: Math.abs(days),
      type: days < 0 ? 'overdue' : 'end'
    };
  };

  // Calculate project progress
  const getProjectProgress = (): {
    taskProgress: number;
    timeProgress: number;
    status: 'ahead' | 'behind' | 'on-track';
    difference: number;
  } => {
    const tasks = project?.tasks || [];

    // Calculate task progress
    const taskProgress = tasks.length === 0 ? 0 :
      Math.round((tasks.filter(task => task.status === TaskStatus.COMPLETED).length / tasks.length) * 100);

    // Calculate time progress
    const today = new Date();
    const startDate = new Date(project?.startDate || today);
    const endDate = new Date(project?.endDate || today);

    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const daysPassed = Math.ceil((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    let timeProgress = Math.round((daysPassed / totalDays) * 100);
    timeProgress = Math.max(0, Math.min(100, timeProgress)); // Ensure between 0-100

    // Determine progress status
    let status: 'ahead' | 'behind' | 'on-track' = 'on-track';
    let difference = taskProgress - timeProgress;

    if (difference >= 10) {
      status = 'ahead';
    } else if (difference <= -10) {
      status = 'behind';
    }

    return { taskProgress, timeProgress, status, difference };
  };

  // Get customer name
  const getCustomerName = (): string => {
    if (!project) return 'Unknown Customer';
    const customer = customers.find(c => c.id === project.customerId);
    return customer ? customer.name : 'Unknown Customer';
  };

  // Get project category display name
  const getProjectCategoryDisplayName = (projectCategory: ProjectCategory): string => {
    switch (projectCategory) {
      case ProjectCategory.PROJECTS:
        return 'Projects';
      case ProjectCategory.PRECISION_PROJECTS:
        return 'Precision Projects';
      case ProjectCategory.SPARE:
        return 'Spare';
      case ProjectCategory.SERVICE:
        return 'Service';
      default:
        return 'Unknown Category';
    }
  };

  // Check if user can edit project - Only DIRECTOR role allowed
  const canEditProject = (): boolean => {
    if (!currentUser || !project) return false;
    return currentUser.role === 'DIRECTOR';
  };

  // Check if user can manage Excel - DIRECTOR and PROJECT_MANAGER allowed
  const canManageExcel = (): boolean => {
    if (!currentUser || !project) return false;
    return currentUser.role === 'DIRECTOR' ||
           (currentUser.role === 'PROJECT_MANAGER' && project.projectManagerId === currentUser.id);
  };

  // Handle edit project
  const handleEditProject = () => {
    setShowEditModal(true);
  };

  // Handle Excel manager
  const handleExcelManager = () => {
    setShowExcelManager(true);
  };

  // Handle project save
  const handleProjectSave = (updatedProject: any) => {
    console.log('Project updated:', updatedProject);
    setShowEditModal(false);
  };

  // Handle Excel data import
  const handleExcelDataImported = async (sections: any[]) => {
    try {
      console.log('📥 handleExcelDataImported called with sections:', sections);
      console.log('📥 Project ID:', project.id);
      console.log('📥 Number of sections:', sections.length);

      // Call API to import data
      console.log('📡 Calling dataService.importProjectExcelData...');
      await dataService.importProjectExcelData(project.id, sections);

      console.log('✅ Import successful, refreshing project data...');
      // Refresh project data
      await dataService.loadProjects();
      setLastRefresh(Date.now());
      setShowExcelManager(false);
      console.log('✅ Project data refreshed and modal closed');

      // Debug: Check if tasks were actually created
      setTimeout(() => {
        const refreshedProject = projects.find(p => p.id === project.id);
        console.log('🔍 Refreshed project data:', refreshedProject);
        console.log('🔍 Number of sections:', refreshedProject?.sections?.length);
        refreshedProject?.sections?.forEach((section, index) => {
          console.log(`🔍 Section ${index + 1}: ${section.name} - ${section.tasks?.length || 0} tasks`);
          section.tasks?.forEach((task, taskIndex) => {
            console.log(`  📋 Task ${taskIndex + 1}: ${task.name} (${task.displayId})`);
          });
        });
      }, 1000);
    } catch (error) {
      console.error('❌ Error importing Excel data:', error);
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    setShowEditModal(false);
  };

  // Get status icon and color
  const getStatusIconAndColor = (status: string) => {
    switch (status) {
      case TaskStatus.COMPLETED:
        return { icon: CheckCircle, color: 'text-green-600', bgColor: 'bg-green-100' };
      case TaskStatus.IN_PROGRESS:
        return { icon: PlayCircle, color: 'text-blue-600', bgColor: 'bg-blue-100' };
      case TaskStatus.ON_HOLD:
        return { icon: PauseCircle, color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
      case TaskStatus.DELAYED:
        return { icon: AlertTriangle, color: 'text-red-600', bgColor: 'bg-red-100' };
      default:
        return { icon: Target, color: 'text-gray-600', bgColor: 'bg-gray-100' };
    }
  };

  // If project not found, show error
  if (!project) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <div className="w-full space-y-6">
          <div className={`flex items-center transform transition-all duration-500 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-2 opacity-0'}`}>
            <button
              onClick={() => navigate('/projects')}
              className="mr-4 p-3 rounded-xl bg-white border border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <ArrowLeft size={20} />
            </button>
            <h1 className="text-2xl font-semibold text-gray-900">Project Not Found</h1>
          </div>
          <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-xl shadow-lg">
            The project you're looking for doesn't exist or you don't have permission to view it.
          </div>
        </div>
      </div>
    );
  }

  const { taskProgress, timeProgress, status, difference } = getProjectProgress();
  const daysRemainingInfo = getDaysRemaining(project.startDate, project.endDate, project.status);
  const customerName = getCustomerName();
  const statusInfo = getStatusIconAndColor(project.status);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-gray-100">
      <div className="w-full space-y-6 p-4">
        {/* Header */}
        <div className={`flex items-center transform transition-all duration-500 delay-75 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-2 opacity-0'}`}>
          <button
            onClick={() => navigate('/projects')}
            className="mr-4 p-3 rounded-xl bg-white/80 backdrop-blur-sm border border-gray-200/60 text-gray-600 hover:bg-white hover:border-gray-300 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 group"
            aria-label="Back to projects"
            title="Back to projects"
          >
            <ArrowLeft size={20} className="group-hover:-translate-x-1 transition-transform duration-200" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-1 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text">
              {project.name} - {getProjectCategoryDisplayName(project.projectCategory)}
            </h1>
            <p className="text-gray-600">
              Project Code: <span className="font-semibold text-blue-600">{project.code}</span>
            </p>
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-6">
          {/* Project Details & Progress Card */}
          <div className={`transform transition-all duration-500 delay-150 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-2 opacity-0'}`}>
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 p-6 hover:shadow-2xl transition-all duration-300 hover:scale-[1.002]">
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 gap-4">
                <h2 className="text-xl font-bold text-gray-900 flex items-center">
                  <TrendingUp className="mr-2 text-blue-600" size={24} />
                  Project Overview
                </h2>
                <div className="flex gap-3">
                  {canManageExcel() && (
                    <button
                      className="px-5 py-2.5 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-xl font-semibold hover:from-green-700 hover:to-green-800 transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 group"
                      onClick={handleExcelManager}
                    >
                      <FileSpreadsheet size={16} className="mr-2 group-hover:scale-110 transition-transform duration-200" />
                      Excel Import/Export
                    </button>
                  )}
                  {canEditProject() && (
                    <button
                      className="px-5 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 group"
                      onClick={handleEditProject}
                    >
                      <Edit size={16} className="mr-2 group-hover:rotate-12 transition-transform duration-200" />
                      Edit Project
                    </button>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                {/* Project Details Section */}
                <div className="lg:col-span-3 space-y-4">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl p-4 hover:from-blue-100 hover:to-blue-200/50 transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-0.5">
                    <div className="flex items-start">
                      <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl mr-3 shadow-lg transform transition-transform duration-200 hover:scale-110">
                        <Building className="text-white" size={18} />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 mb-1 font-medium">Customer</p>
                        <p className="font-bold text-gray-900">{customerName}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-emerald-50 to-emerald-100/50 rounded-xl p-4 hover:from-emerald-100 hover:to-emerald-200/50 transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-0.5">
                    <div className="flex items-start">
                      <div className="p-3 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl mr-3 shadow-lg transform transition-transform duration-200 hover:scale-110">
                        <FileText className="text-white" size={18} />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 mb-1 font-medium">PO Number</p>
                        <p className="font-bold text-gray-900">{project.poNumber || 'N/A'}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl p-4 hover:from-purple-100 hover:to-purple-200/50 transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-0.5">
                    <div className="flex items-start">
                      <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl mr-3 shadow-lg transform transition-transform duration-200 hover:scale-110">
                        <Calendar className="text-white" size={18} />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 mb-1 font-medium">Timeline</p>
                        <p className="font-bold text-gray-900 text-sm">
                          {format(new Date(project.startDate), 'MMM d, yyyy')} - {format(new Date(project.endDate), 'MMM d, yyyy')}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-orange-50 to-orange-100/50 rounded-xl p-4 hover:from-orange-100 hover:to-orange-200/50 transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-0.5">
                    <div className="flex items-start">
                      <div className={`p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl mr-3 shadow-lg transform transition-transform duration-200 hover:scale-110`}>
                        <statusInfo.icon className="text-white" size={18} />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 mb-1 font-medium">Status</p>
                        <div className="flex items-center mt-1">
                          <span className={`px-3 py-1.5 inline-flex text-xs leading-5 font-bold rounded-full shadow-sm
                            ${project.status === TaskStatus.COMPLETED ? 'bg-green-100 text-green-800' :
                              project.status === TaskStatus.IN_PROGRESS ? 'bg-blue-100 text-blue-800' :
                              project.status === TaskStatus.DELAYED ? 'bg-red-100 text-red-800' :
                              project.status === TaskStatus.ON_HOLD ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'}`}>
                            {project.status.replace(/_/g, ' ')}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Progress Section */}
                <div className="lg:col-span-6 space-y-4">
                  <h3 className="font-bold text-gray-800 mb-4 text-lg">Project Progress</h3>

                  {/* Task Progress */}
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100/50 rounded-xl p-5 shadow-sm">
                    <div className="flex justify-between items-center mb-4">
                      <span className="text-sm font-bold text-gray-700">Task Completion</span>
                      <div className="flex items-center">
                        <span className="text-2xl font-bold text-gray-900 mr-3">{taskProgress}%</span>
                        {status === 'ahead' && (
                          <span className="text-green-600 flex items-center text-sm font-bold bg-green-100 px-3 py-1 rounded-full shadow-sm">
                            <ArrowUpRight size={14} />
                            +{difference}%
                          </span>
                        )}
                        {status === 'behind' && (
                          <span className="text-red-600 flex items-center text-sm font-bold bg-red-100 px-3 py-1 rounded-full shadow-sm">
                            <ArrowDownRight size={14} />
                            -{Math.abs(difference)}%
                          </span>
                        )}
                        {status === 'on-track' && (
                          <span className="text-gray-500 flex items-center text-sm font-bold bg-gray-100 px-3 py-1 rounded-full shadow-sm">
                            <Minus size={14} />
                            On track
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="w-full bg-gray-300 rounded-full h-4 shadow-inner">
                      <div
                        className={`h-4 rounded-full transition-all duration-1000 ease-out shadow-sm ${
                          status === 'ahead'
                            ? 'bg-gradient-to-r from-green-400 to-green-500'
                            : status === 'behind'
                              ? 'bg-gradient-to-r from-red-400 to-red-500'
                              : 'bg-gradient-to-r from-blue-400 to-blue-500'
                        }`}
                        style={{ width: `${taskProgress}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Time Progress */}
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100/50 rounded-xl p-5 shadow-sm">
                    <div className="flex justify-between mb-4">
                      <span className="text-sm font-bold text-gray-700">Time Elapsed</span>
                      <span className="text-2xl font-bold text-gray-900">{timeProgress}%</span>
                    </div>
                    <div className="w-full bg-gray-300 rounded-full h-4 shadow-inner">
                      <div
                        className="bg-gradient-to-r from-gray-400 to-gray-500 h-4 rounded-full transition-all duration-1000 ease-out shadow-sm"
                        style={{ width: `${timeProgress}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Timeline */}
                  <div className="bg-gradient-to-r from-indigo-50 to-indigo-100/50 rounded-xl p-5 shadow-sm">
                    <div className="flex items-center">
                      <div className="p-3 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl mr-4 shadow-lg">
                        <Clock size={20} className="text-white" />
                      </div>
                      <span className={`font-bold text-lg ${
                        daysRemainingInfo.type === 'overdue' ? 'text-red-600' :
                        daysRemainingInfo.type === 'start' ? 'text-blue-600' : 'text-gray-900'
                      }`}>
                        {daysRemainingInfo.type === 'overdue'
                          ? `${daysRemainingInfo.days} days overdue`
                          : daysRemainingInfo.type === 'start'
                          ? `${daysRemainingInfo.days} days until start`
                          : `${daysRemainingInfo.days} days remaining`}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Task Stats Section */}
                <div className="lg:col-span-3 space-y-4">
                  <h3 className="font-bold text-gray-800 mb-4 text-lg">Statistics</h3>

                  <div className="grid grid-cols-3 lg:grid-cols-2 gap-3 text-sm">
                    <div className="bg-gradient-to-br from-gray-100 to-gray-200/50 p-4 rounded-xl text-center hover:from-gray-200 hover:to-gray-300/50 transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-1">
                      <p className="text-gray-600 text-xs mb-2 font-medium">Total</p>
                      <p className="text-2xl font-bold text-gray-900">{project.tasks?.length || 0}</p>
                    </div>
                    <div className="bg-gradient-to-br from-green-100 to-green-200/50 p-4 rounded-xl text-center hover:from-green-200 hover:to-green-300/50 transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-1">
                      <p className="text-green-700 text-xs mb-2 font-medium">Completed</p>
                      <p className="text-2xl font-bold text-green-800">{project.tasks?.filter(t => t.status === TaskStatus.COMPLETED).length || 0}</p>
                    </div>
                    <div className="bg-gradient-to-br from-blue-100 to-blue-200/50 p-4 rounded-xl text-center hover:from-blue-200 hover:to-blue-300/50 transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-1">
                      <p className="text-blue-700 text-xs mb-2 font-medium">Active</p>
                      <p className="text-2xl font-bold text-blue-800">{project.tasks?.filter(t => t.status === TaskStatus.IN_PROGRESS).length || 0}</p>
                    </div>
                    <div className="bg-gradient-to-br from-gray-50 to-gray-100/50 p-4 rounded-xl text-center hover:from-gray-100 hover:to-gray-200/50 transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-1">
                      <p className="text-gray-700 text-xs mb-2 font-medium">Not Started</p>
                      <p className="text-2xl font-bold text-gray-800">{project.tasks?.filter(t => t.status === TaskStatus.NOT_STARTED).length || 0}</p>
                    </div>
                    <div className="bg-gradient-to-br from-red-100 to-red-200/50 p-4 rounded-xl text-center hover:from-red-200 hover:to-red-300/50 transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-1">
                      <p className="text-red-700 text-xs mb-2 font-medium">Delayed</p>
                      <p className="text-2xl font-bold text-red-700">{project.tasks?.filter(t => t.status === TaskStatus.DELAYED).length || 0}</p>
                    </div>
                    <div className="bg-gradient-to-br from-yellow-100 to-yellow-200/50 p-4 rounded-xl text-center hover:from-yellow-200 hover:to-yellow-300/50 transition-all duration-300 shadow-sm hover:shadow-md transform hover:-translate-y-1">
                      <p className="text-yellow-700 text-xs mb-2 font-medium">On Hold</p>
                      <p className="text-2xl font-bold text-yellow-700">{project.tasks?.filter(t => t.status === TaskStatus.ON_HOLD).length || 0}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Project Sections and Tasks */}
          <div className={`transform transition-all duration-500 delay-225 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-2 opacity-0'}`}>
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 p-6 hover:shadow-2xl transition-all duration-300">
              <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <Target className="mr-2 text-blue-600" size={24} />
                Project Tasks by Section
              </h2>
              <SectionTaskView projectId={project.id} />
            </div>
          </div>
        </div>

        {/* Project Edit Modal */}
        <ProjectEditModal
          project={project}
          isOpen={showEditModal}
          onClose={handleModalClose}
          onSave={handleProjectSave}
        />

        {/* Excel Import/Export Manager */}
        {showExcelManager && (
          <ProjectExcelManager
            project={project}
            onDataImported={handleExcelDataImported}
            onClose={() => setShowExcelManager(false)}
          />
        )}
      </div>
    </div>
  );
};

export default ProjectDetailsPage;