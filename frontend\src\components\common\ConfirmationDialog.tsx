import React from 'react';
import { AlertTriangle, CheckCircle, Info, Trash2, Edit, Plus, Eye } from 'lucide-react';

export type ConfirmationType = 'create' | 'update' | 'delete' | 'read' | 'info' | 'warning';

interface ConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  type?: ConfirmationType;
  confirmText?: string;
  cancelText?: string;
  isLoading?: boolean;
  details?: string[];
}

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  type = 'info',
  confirmText,
  cancelText = 'Cancel',
  isLoading = false,
  details = []
}) => {
  if (!isOpen) return null;

  const getIcon = () => {
    switch (type) {
      case 'create':
        return <Plus className="w-6 h-6 text-green-600" />;
      case 'update':
        return <Edit className="w-6 h-6 text-blue-600" />;
      case 'delete':
        return <Trash2 className="w-6 h-6 text-red-600" />;
      case 'read':
        return <Eye className="w-6 h-6 text-gray-600" />;
      case 'warning':
        return <AlertTriangle className="w-6 h-6 text-yellow-600" />;
      default:
        return <Info className="w-6 h-6 text-blue-600" />;
    }
  };

  const getColors = () => {
    switch (type) {
      case 'create':
        return {
          bg: 'from-green-50 to-green-100',
          border: 'border-green-200',
          button: 'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
          text: 'text-green-800'
        };
      case 'update':
        return {
          bg: 'from-blue-50 to-blue-100',
          border: 'border-blue-200',
          button: 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
          text: 'text-blue-800'
        };
      case 'delete':
        return {
          bg: 'from-red-50 to-red-100',
          border: 'border-red-200',
          button: 'from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',
          text: 'text-red-800'
        };
      case 'warning':
        return {
          bg: 'from-yellow-50 to-yellow-100',
          border: 'border-yellow-200',
          button: 'from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700',
          text: 'text-yellow-800'
        };
      default:
        return {
          bg: 'from-gray-50 to-gray-100',
          border: 'border-gray-200',
          button: 'from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700',
          text: 'text-gray-800'
        };
    }
  };

  const colors = getColors();
  const defaultConfirmText = confirmText || (type === 'delete' ? 'Delete' : type === 'create' ? 'Create' : type === 'update' ? 'Update' : 'Confirm');

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Dialog */}
        <div className="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
          <div className={`mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gradient-to-br ${colors.bg} ${colors.border} border-2`}>
            {getIcon()}
          </div>
          
          <div className="mt-3 text-center sm:mt-5">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              {title}
            </h3>
            <div className="mt-2">
              <p className="text-sm text-gray-500">
                {message}
              </p>
              
              {/* Details */}
              {details.length > 0 && (
                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <ul className="text-sm text-gray-600 space-y-1">
                    {details.map((detail, index) => (
                      <li key={index} className="flex items-center">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          {/* Buttons */}
          <div className="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
            <button
              type="button"
              className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-gradient-to-r ${colors.button} text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm transition-all duration-200 ${
                isLoading ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              onClick={onConfirm}
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </div>
              ) : (
                defaultConfirmText
              )}
            </button>
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:col-start-1 sm:text-sm transition-all duration-200"
              onClick={onClose}
              disabled={isLoading}
            >
              {cancelText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationDialog;
