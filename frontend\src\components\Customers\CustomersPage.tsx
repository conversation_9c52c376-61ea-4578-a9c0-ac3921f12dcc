import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useAtom } from 'jotai';
import { customersAtom, projectsAtom, momsAtom, currentUserAtom } from '../../store';
import { Customer, UserRole } from '../../types';
import { Building, Users, AlertCircle, Shield, Plus } from 'lucide-react';
import CustomerGrid from './CustomerGrid';
import { dataService } from '../../services/dataServiceSingleton';
import { useNotification } from '../../contexts/NotificationContext';

// Debounce utility function for customer operations
const useDebounce = (callback: Function, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback((...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

const CustomersPage: React.FC = () => {
  const { showSuccess, showError } = useNotification();

  const [customers, setCustomers] = useAtom(customersAtom);
  const [projects] = useAtom(projectsAtom);
  const [moms] = useAtom(momsAtom);
  const [currentUser] = useAtom(currentUserAtom);

  const [error, setError] = useState<string | null>(null);
  const [showNewRow, setShowNewRow] = useState(false);

  // Debounced refresh function to reduce API calls
  const debouncedRefreshCustomers = useDebounce(async () => {
    try {
      console.log('🔄 Refreshing customers data...');
      await dataService.loadCustomers();
      console.log('✅ Customers data refreshed successfully');
    } catch (error) {
      console.error('❌ Error refreshing customers:', error);
      setError('Failed to refresh customers. Please try again.');
    }
  }, 1000); // 1 second debounce

  // Role-based permission functions
  const canManageCustomers = (): boolean => {
    return currentUser?.role === UserRole.DIRECTOR;
  };

  const canViewCustomers = (): boolean => {
    if (!currentUser) return false;
    // All authenticated users can view customers
    return true;
  };

  // Handle Add Customer button click
  const handleAddCustomer = () => {
    setShowNewRow(true);
    setError(null);
  };

  // Auto-refresh mechanism for real-time updates
  useEffect(() => {
    // Add visibility change listener to refresh when page becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('📱 Customers page became visible, refreshing data...');
        debouncedRefreshCustomers();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Refresh every 30 seconds when page is visible
    const refreshInterval = setInterval(() => {
      if (!document.hidden) {
        console.log('⏰ Periodic refresh of customers data...');
        debouncedRefreshCustomers();
      }
    }, 30000);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(refreshInterval);
    };
  }, [debouncedRefreshCustomers]);



  // Check URL params for auto-show new row (from MOM page)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('addCustomer') === 'true') {
      setShowNewRow(true);
      // Clean up URL
      window.history.replaceState({}, '', window.location.pathname);
    }
  }, []);

  // Early return if user doesn't have permission to view customers
  if (!canViewCustomers()) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6">
        <div className="container mx-auto">
          <div className="flex justify-center items-center min-h-[60vh]">
            <div className="bg-white rounded-2xl shadow-2xl p-12 text-center transform perspective-1000 hover:scale-105 transition-all duration-300 border border-gray-100">
              <div className="bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6 shadow-inner">
                <Shield size={48} className="text-gray-400" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Access Restricted</h3>
              <p className="text-gray-600 mb-2 text-lg">
                You don't have permission to view customer management.
              </p>
              <p className="text-gray-500">
                Please contact your administrator for access.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="w-full mx-auto py-2">
        {/* Header Section with 3D Card */}
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 border border-white/20 backdrop-blur-sm relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-indigo-600/5"></div>
          <div className="relative z-10 flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-1 flex items-center">
                <div className="relative bg-gradient-to-br from-white to-blue-100 rounded-lg p-2 mr-3 shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                  <Users size={24} className="text-blue-600 drop-shadow-sm" />
                </div>
                Customers
              </h1>
              <p className="text-gray-600">
                {canManageCustomers()
                  ? "Manage customers and their information"
                  : "View customers (read-only access)"}
              </p>
            </div>
            {canManageCustomers() && (
              <button
                onClick={handleAddCustomer}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-2 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 flex items-center font-medium"
              >
                <Plus size={16} className="mr-2" />
                Add Customer
              </button>
            )}
          </div>
        </div>

        {/* Read-Only Warning */}
        {!canManageCustomers() && (
          <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-xl p-4 mb-6 shadow-lg">
            <div className="flex items-center">
              <div className="bg-amber-100 rounded-full p-2 mr-3">
                <Shield size={18} className="text-amber-600" />
              </div>
              <div>
                <p className="font-bold text-amber-800">Read-Only Access</p>
                <p className="text-amber-700 text-sm">
                  Only Directors can create, edit, or delete customers. You can view the customer list below.
                </p>
              </div>
            </div>
          </div>
        )}



        {/* Error Message */}
        {error && (
          <div className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl p-4 mb-6 shadow-lg">
            <div className="flex items-start">
              <div className="bg-red-100 rounded-full p-2 mr-3 mt-0.5">
                <AlertCircle size={18} className="text-red-600" />
              </div>
              <span className="text-red-800 font-medium">{error}</span>
            </div>
          </div>
        )}

        {/* Main Content */}
        {customers.length === 0 && !canManageCustomers() ? (
          <div className="flex justify-center items-center min-h-[50vh]">
            <div className="bg-white rounded-3xl shadow-2xl p-16 text-center transform hover:scale-105 transition-all duration-300 border border-gray-100 max-w-2xl">
              <div className="bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full w-32 h-32 flex items-center justify-center mx-auto mb-8 shadow-inner">
                <Building size={64} className="text-blue-500" />
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">No Customers Yet</h2>
              <p className="text-gray-600 mb-8 text-lg leading-relaxed">
                No customers have been added yet. Contact your Director to add customers.
              </p>
            </div>
          </div>
        ) : (
          <CustomerGrid
            customers={customers}
            onCustomersChange={(updatedCustomers) => {
              setCustomers(updatedCustomers);
              // Use debounced refresh to ensure data consistency
              debouncedRefreshCustomers();
            }}
            canManage={canManageCustomers()}
            onError={setError}
            onSuccess={(message) => {
              showSuccess('Customer Operation', message);
              setShowNewRow(false); // Hide new row after successful creation
              // Use debounced refresh after successful operations
              debouncedRefreshCustomers();
            }}
            projects={projects}
            moms={moms}
            showNewRow={showNewRow}
            onAddCustomer={handleAddCustomer}
            onHideNewRow={() => setShowNewRow(false)}
          />
        )}
      </div>
    </div>
  );
};

export default CustomersPage;
