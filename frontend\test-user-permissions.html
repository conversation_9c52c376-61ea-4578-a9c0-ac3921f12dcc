<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Permissions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .debug { background-color: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>User Permissions Test</h1>
    
    <div class="test-section">
        <h3>Current User Info</h3>
        <div id="currentUserInfo" class="debug">Loading...</div>
    </div>

    <div class="test-section">
        <h3>Test User Update Button</h3>
        <button onclick="testUserUpdate()">Test Update User</button>
        <button onclick="testPermissions()">Test Permissions</button>
        <div id="testResults" class="debug"></div>
    </div>

    <div class="test-section">
        <h3>API Test</h3>
        <button onclick="testAPI()">Test API Connection</button>
        <div id="apiResults" class="debug"></div>
    </div>

    <script>
        // Get token from localStorage
        const token = localStorage.getItem('token');
        const user = JSON.parse(localStorage.getItem('user') || 'null');

        // Display current user info
        document.getElementById('currentUserInfo').innerHTML = `
            <strong>Token:</strong> ${token ? 'Present' : 'Missing'}<br>
            <strong>User:</strong> ${user ? `${user.name} (${user.role}) - ${user.department}` : 'Not logged in'}<br>
            <strong>User ID:</strong> ${user ? user.id : 'N/A'}
        `;

        async function testAPI() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = 'Testing API...';

            try {
                // Test basic API connection
                const response = await fetch('http://localhost:5002/api/users', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            ✅ API Connection: Success<br>
                            📊 Users found: ${data.data.length}<br>
                            🔑 Authentication: Working
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error">
                            ❌ API Error: ${data.message}
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        ❌ Network Error: ${error.message}
                    </div>
                `;
            }
        }

        async function testUserUpdate() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = 'Testing user update...';

            if (!user || !token) {
                resultsDiv.innerHTML = '<div class="error">❌ Not logged in</div>';
                return;
            }

            try {
                // Get users first
                const usersResponse = await fetch('http://localhost:5002/api/users', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const usersData = await usersResponse.json();
                
                if (!usersData.success) {
                    resultsDiv.innerHTML = `<div class="error">❌ Failed to get users: ${usersData.message}</div>`;
                    return;
                }

                // Find a user to test with (not the current user)
                const testUser = usersData.data.find(u => u.id !== user.id);
                
                if (!testUser) {
                    resultsDiv.innerHTML = '<div class="error">❌ No other users found to test with</div>';
                    return;
                }

                // Test update
                const updateResponse = await fetch(`http://localhost:5002/api/users/${testUser.id}`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: testUser.name + ' (Test Update)',
                        skills: 'Test,Skills,Update'
                    })
                });

                const updateData = await updateResponse.json();
                
                if (updateData.success) {
                    resultsDiv.innerHTML = `
                        <div class="success">
                            ✅ User Update: Success<br>
                            👤 Updated: ${testUser.name}<br>
                            📝 New name: ${updateData.data.name}
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="error">
                            ❌ Update Failed: ${updateData.message}<br>
                            📊 Status: ${updateResponse.status}
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="error">
                        ❌ Error: ${error.message}
                    </div>
                `;
            }
        }

        function testPermissions() {
            const resultsDiv = document.getElementById('testResults');
            
            if (!user) {
                resultsDiv.innerHTML = '<div class="error">❌ Not logged in</div>';
                return;
            }

            // Simulate permission checks
            const UserRole = {
                DIRECTOR: 'DIRECTOR',
                PROJECT_MANAGER: 'PROJECT_MANAGER',
                TEAM_LEAD: 'TEAM_LEAD',
                ENGINEER: 'ENGINEER'
            };

            function canEditUser(targetUser) {
                if (!user) return false;
                if (user.id === targetUser.id) return true;

                switch (user.role) {
                    case UserRole.DIRECTOR:
                        return true;
                    case UserRole.PROJECT_MANAGER:
                        return [UserRole.TEAM_LEAD, UserRole.ENGINEER].includes(targetUser.role);
                    case UserRole.TEAM_LEAD:
                        return targetUser.role === UserRole.ENGINEER && targetUser.department === user.department;
                    default:
                        return false;
                }
            }

            // Test with different user types
            const testUsers = [
                { id: '1', name: 'Test Director', role: 'DIRECTOR', department: 'GENERAL' },
                { id: '2', name: 'Test PM', role: 'PROJECT_MANAGER', department: 'DEVELOPMENT' },
                { id: '3', name: 'Test TL', role: 'TEAM_LEAD', department: user.department },
                { id: '4', name: 'Test Engineer', role: 'ENGINEER', department: user.department },
                { id: '5', name: 'Test Engineer Other', role: 'ENGINEER', department: 'OTHER' }
            ];

            let results = `<strong>Permission Test Results for ${user.name} (${user.role}):</strong><br><br>`;
            
            testUsers.forEach(testUser => {
                const canEdit = canEditUser(testUser);
                results += `${canEdit ? '✅' : '❌'} Can edit ${testUser.name} (${testUser.role}): ${canEdit}<br>`;
            });

            resultsDiv.innerHTML = `<div class="debug">${results}</div>`;
        }

        // Auto-run API test on load
        if (token && user) {
            testAPI();
        }
    </script>
</body>
</html>
