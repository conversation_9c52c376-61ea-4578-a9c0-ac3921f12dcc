import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { customersAtom, usersAtom, projectsAtom, projectCategoriesAtom } from '../../store';
import { Project, TaskStatus, ProjectCategory } from '../../types';
import { X, Save, AlertCircle, Calendar, User, FileText, Building2, Hash, Target, PlusCircle, Layers, DollarSign } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { projectsAPI } from '../../services/api';
import { useConfirmationContext } from '../../contexts/ConfirmationContext';
import { formatDateForInput } from '../../utils/dateFormatter';
import { ErrorDialog } from '../common/ErrorDialog';
import { SuccessDialog } from '../common/SuccessDialog';
import { dataService } from '../../services/dataServiceSingleton';
import { validateDates, getMinDate } from '../../utils/dateValidation';
import { formatProjectCode, validateProjectCode } from '../../utils/validation';

interface ProjectEditModalProps {
  project: Project | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedProject: Project) => void;
}

const ProjectEditModal: React.FC<ProjectEditModalProps> = ({
  project,
  isOpen,
  onClose,
  onSave
}) => {
  const navigate = useNavigate();
  const [customers] = useAtom(customersAtom);
  const [users] = useAtom(usersAtom);
  const [projects, setProjects] = useAtom(projectsAtom);
  const [projectCategories] = useAtom(projectCategoriesAtom);

  // Import confirmation context
  const { confirmAction, showError } = useConfirmationContext();

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    projectCategory: ProjectCategory.PROJECTS,
    customerId: '',
    poNumber: '',
    poDate: '',
    poValue: '',
    startDate: '',
    endDate: '',
    projectManagerId: '',
    status: TaskStatus.NOT_STARTED,
    description: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  // Dialog states
  const [showErrorDialog, setShowErrorDialog] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [errorDetails, setErrorDetails] = useState<string[]>([]);
  const [successDetails, setSuccessDetails] = useState<string[]>([]);
  const [updatedProjectData, setUpdatedProjectData] = useState<Project | null>(null);



  // Initialize form data when project changes
  useEffect(() => {
    if (project) {
      console.log('Initializing form with project data:', project);
      console.log('Original dates:', {
        poDate: project.poDate,
        startDate: project.startDate,
        endDate: project.endDate
      });

      const formattedPoDate = formatDateForInput(project.poDate || '');
      const formattedStartDate = formatDateForInput(project.startDate || '');
      const formattedEndDate = formatDateForInput(project.endDate || '');

      console.log('Formatted dates:', {
        poDate: formattedPoDate,
        startDate: formattedStartDate,
        endDate: formattedEndDate
      });

      setFormData({
        name: project.name || '',
        code: project.code || '',
        projectCategory: project.projectCategory || ProjectCategory.PROJECTS,
        customerId: project.customerId || '',
        poNumber: project.poNumber || '',
        poDate: formattedPoDate,
        poValue: project.poValue?.toString() || '',
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        projectManagerId: project.projectManagerId || '',
        status: project.status || TaskStatus.NOT_STARTED,
        description: project.description || ''
      });
    }
  }, [project]);

  // Handle modal animation
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => setIsVisible(true), 10);
    } else {
      setIsVisible(false);
    }
  }, [isOpen]);

  // Load project categories when modal opens
  useEffect(() => {
    const loadProjectCategories = async () => {
      if (isOpen && projectCategories.length === 0) {
        console.log('🔄 Loading project categories for edit modal...');
        try {
          // First try to load just project categories
          const result = await dataService.loadProjectCategories();
          if (!result) {
            // If that fails, try loading all data
            console.log('🔄 Fallback: Loading all data for edit modal...');
            await dataService.refreshData();
          }
          console.log('✅ Project categories loaded for edit modal');
        } catch (error) {
          console.error('❌ Failed to load project categories for edit modal:', error);
        }
      }
    };

    loadProjectCategories();
  }, [isOpen, projectCategories.length]);

  // Get project managers - only PROJECT_MANAGER role
  const projectManagers = users.filter(user =>
    user.role === 'PROJECT_MANAGER'
  );

  // Debug project categories
  console.log('🔍 ProjectEditModal - Project Categories:', {
    count: projectCategories.length,
    categories: projectCategories
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError(null);
  };

  const handleProjectCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatProjectCode(e.target.value);
    setFormData(prev => ({
      ...prev,
      code: formatted
    }));
    setError(null);
  };

  const handleFocus = (fieldName: string) => {
    setFocusedField(fieldName);
  };

  const handleBlur = () => {
    setFocusedField(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!project) return;

    // Get customer and project manager names for confirmation
    const selectedCustomer = customers.find(c => c.id === formData.customerId);
    const selectedManager = projectManagers.find(pm => pm.id === formData.projectManagerId);

    const confirmationDetails = [
      `Project Name: ${formData.name}`,
      `Project Code: ${formData.code}`,
      `Customer: ${selectedCustomer?.name || 'Unknown'}`,
      `Project Manager: ${selectedManager?.name || 'Unknown'}`,
      `Start Date: ${formData.startDate}`,
      `End Date: ${formData.endDate}`,
      `Status: ${formData.status}`
    ];

    confirmAction({
      title: 'Update Project',
      message: 'Are you sure you want to update this project?',
      type: 'update',
      confirmText: 'Update',
      details: confirmationDetails,
      onConfirm: async () => {
        setLoading(true);
        setError(null);

        try {
          // Validate required fields (endDate is now optional)
          if (!formData.name || !formData.customerId || !formData.startDate) {
            const errorDetailsList = [
              'Missing required fields:',
              !formData.name ? '• Project name is required' : '',
              !formData.customerId ? '• Customer selection is required' : '',
              !formData.startDate ? '• Start date is required' : '',
              'Please fill in all required fields and try again.'
            ].filter(Boolean);

            setErrorDetails(errorDetailsList);
            setShowErrorDialog(true);
            setLoading(false);
            return;
          }

          // Validate project code format if provided
          if (formData.code) {
            const codeValidation = validateProjectCode(formData.code);
            if (!codeValidation.isValid) {
              const errorDetailsList = [
                'Project code validation failed:',
                `• ${codeValidation.message}`,
                'Please check the project code format and try again.'
              ];

              setErrorDetails(errorDetailsList);
              setShowErrorDialog(true);
              setLoading(false);
              return;
            }
          }

          // Validate dates using the new validation function
          const dateValidation = validateDates(formData.startDate, formData.endDate);
          if (!dateValidation.isValid) {
            const errorDetailsList = [
              'Date validation failed:',
              `• ${dateValidation.message}`,
              'Please check your dates and try again.'
            ];

            setErrorDetails(errorDetailsList);
            setShowErrorDialog(true);
            setLoading(false);
            return;
          }

          // Additional validation: end date must be after start date (if provided)
          if (formData.endDate && new Date(formData.startDate) >= new Date(formData.endDate)) {
            const errorDetailsList = [
              'Date validation failed:',
              '• End date must be after start date',
              'Please check your dates and try again.'
            ];

            setErrorDetails(errorDetailsList);
            setShowErrorDialog(true);
            setLoading(false);
            return;
          }

          // Update project via API
          const response = await projectsAPI.updateProject(project.id, formData);

          if (response.data) {
            // Update projects atom
            const updatedProjects = projects.map(p =>
              p.id === project.id ? response.data : p
            );
            setProjects(updatedProjects);

            // Prepare success details
            const selectedCustomer = customers.find(c => c.id === formData.customerId);
            const selectedManager = projectManagers.find(pm => pm.id === formData.projectManagerId);

            const successDetailsList = [
              `Project Name: ${formData.name}`,
              `Project Code: ${formData.code}`,
              `Customer: ${selectedCustomer?.name || 'Unknown'}`,
              `Project Manager: ${selectedManager?.name || 'Unknown'}`,
              `Start Date: ${formData.startDate}`,
              `End Date: ${formData.endDate || 'Not specified'}`,
              `PO Number: ${formData.poNumber}`,
              `PO Value: ${formData.poValue ? `₹${Number(formData.poValue).toLocaleString('en-IN')}` : 'Not specified'}`,
              `Status: ${formData.status.replace(/_/g, ' ')}`
            ];

            setSuccessDetails(successDetailsList);
            setUpdatedProjectData(response.data);
            setShowSuccessDialog(true);

            // Don't call onSave immediately - let the success dialog handle it
          }
        } catch (error: any) {
          console.error('Error updating project:', error);
          const errorMessage = error.response?.data?.message || 'Failed to update project';

          const errorDetailsList = [
            `Error Type: ${error.name || 'Unknown Error'}`,
            `Error Message: ${errorMessage}`,
            `Time: ${new Date().toLocaleString()}`,
            'Please check your input and try again.'
          ];

          setErrorDetails(errorDetailsList);
          setShowErrorDialog(true);
        } finally {
          setLoading(false);
        }
      }
    });
  };

  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.NOT_STARTED:
        return 'from-slate-400 to-slate-500';
      case TaskStatus.IN_PROGRESS:
        return 'from-sky-400 to-sky-500';
      case TaskStatus.COMPLETED:
        return 'from-emerald-400 to-emerald-500';
      case TaskStatus.ON_HOLD:
        return 'from-amber-400 to-amber-500';
      default:
        return 'from-slate-400 to-slate-500';
    }
  };

  if (!isOpen || !project) return null;

  return (
    <>
    <div className={`fixed inset-0 z-50 transition-all duration-300 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/40 backdrop-blur-sm" onClick={onClose} />

      {/* Modal Container */}
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className={`
          relative w-full max-w-4xl max-h-[90vh] overflow-hidden
          transform transition-all duration-300 ease-out
          ${isVisible ? 'scale-100 translate-y-0' : 'scale-95 translate-y-4'}
        `}>
          {/* Main container */}
          <div className="
            backdrop-blur-md bg-white/95 border border-white/30
            rounded-2xl shadow-xl overflow-hidden
          ">
            {/* Header */}
            <div className="relative bg-gradient-to-r from-slate-600 to-slate-700 p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                    <FileText className="text-white" size={20} />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-white">Edit Project</h2>
                    <p className="text-slate-200 text-sm">{project.name}</p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="
                    w-10 h-10 bg-white/10 hover:bg-white/20 rounded-lg
                    flex items-center justify-center transition-all duration-200
                    hover:rotate-90
                  "
                >
                  <X className="text-white" size={20} />
                </button>
              </div>
            </div>

            {/* Form Container */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="space-y-6">

                {/* Form Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Project Name */}
                  <div className="lg:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <Target className="mr-2 text-slate-500" size={16} />
                      Project Name *
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        onFocus={() => handleFocus('name')}
                        onBlur={handleBlur}
                        className={`
                          w-full h-12 px-4 bg-white border-2 rounded-lg
                          transition-all duration-200 text-gray-900
                          ${focusedField === 'name'
                            ? 'border-slate-400 shadow-md'
                            : 'border-gray-200 hover:border-gray-300'
                          }
                          focus:outline-none focus:ring-0
                        `}
                        required
                      />
                    </div>
                  </div>

                  {/* Project Code */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <Hash className="mr-2 text-slate-500" size={16} />
                      Project Code
                    </label>
                    <input
                      type="text"
                      name="code"
                      value={formData.code}
                      onChange={handleProjectCodeChange}
                      onFocus={() => handleFocus('code')}
                      onBlur={handleBlur}
                      maxLength={11}
                      className={`
                        w-full h-11 px-4 bg-white border-2 rounded-lg
                        transition-all duration-200 text-gray-900
                        ${focusedField === 'code'
                          ? 'border-slate-400 shadow-md'
                          : 'border-gray-200 hover:border-gray-300'
                        }
                        focus:outline-none focus:ring-0
                      `}
                      placeholder="ABCD-12-345"
                    />
                  </div>

                  {/* Project Category */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <Layers className="mr-2 text-slate-500" size={16} />
                      Project Category *
                    </label>
                    <select
                      name="projectCategory"
                      value={formData.projectCategory}
                      onChange={handleInputChange}
                      onFocus={() => handleFocus('projectCategory')}
                      onBlur={handleBlur}
                      className={`
                        w-full h-11 px-4 bg-white border-2 rounded-lg
                        transition-all duration-200 text-gray-900
                        ${focusedField === 'projectCategory'
                          ? 'border-slate-400 shadow-md'
                          : 'border-gray-200 hover:border-gray-300'
                        }
                        focus:outline-none focus:ring-0
                      `}
                      required
                      disabled={projectCategories.length === 0}
                    >
                      <option value="">
                        {projectCategories.length === 0 ? 'Loading categories...' : 'Select Project Category'}
                      </option>
                      {projectCategories.map(category => (
                        <option key={category.id} value={category.code}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Customer */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <Building2 className="mr-2 text-slate-500" size={16} />
                      Customer *
                    </label>
                    <div className="flex space-x-2">
                      <div className="flex-1">
                        <select
                          name="customerId"
                          value={formData.customerId}
                          onChange={handleInputChange}
                          onFocus={() => handleFocus('customerId')}
                          onBlur={handleBlur}
                          className={`
                            w-full h-11 px-4 bg-white border-2 rounded-lg
                            transition-all duration-200 text-gray-900
                            ${focusedField === 'customerId'
                              ? 'border-slate-400 shadow-md'
                              : 'border-gray-200 hover:border-gray-300'
                            }
                            focus:outline-none focus:ring-0
                          `}
                          required
                        >
                          <option value="">Select Customer</option>
                          {customers.map(customer => (
                            <option key={customer.id} value={customer.id}>
                              {customer.name}
                            </option>
                          ))}
                        </select>
                      </div>
                      <button
                        type="button"
                        className="px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 flex items-center shadow-md hover:shadow-lg transform hover:scale-105"
                        onClick={() => {
                          onClose(); // Close the modal first
                          navigate('/customers?addCustomer=true');
                        }}
                      >
                        <PlusCircle size={16} className="mr-1" />
                        <span className="text-sm">New</span>
                      </button>
                    </div>
                  </div>

                  {/* Project Manager */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <User className="mr-2 text-slate-500" size={16} />
                      Project Manager
                    </label>
                    <select
                      name="projectManagerId"
                      value={formData.projectManagerId}
                      onChange={handleInputChange}
                      onFocus={() => handleFocus('projectManagerId')}
                      onBlur={handleBlur}
                      className={`
                        w-full h-11 px-4 bg-white border-2 rounded-lg
                        transition-all duration-200 text-gray-900
                        ${focusedField === 'projectManagerId'
                          ? 'border-slate-400 shadow-md'
                          : 'border-gray-200 hover:border-gray-300'
                        }
                        focus:outline-none focus:ring-0
                      `}
                    >
                      <option value="">Select Project Manager</option>
                      {projectManagers.map(manager => (
                        <option key={manager.id} value={manager.id}>
                          {manager.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* PO Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <FileText className="mr-2 text-slate-500" size={16} />
                      PO Number
                    </label>
                    <input
                      type="text"
                      name="poNumber"
                      value={formData.poNumber}
                      onChange={handleInputChange}
                      onFocus={() => handleFocus('poNumber')}
                      onBlur={handleBlur}
                      className={`
                        w-full h-11 px-4 bg-white border-2 rounded-lg
                        transition-all duration-200 text-gray-900
                        ${focusedField === 'poNumber'
                          ? 'border-slate-400 shadow-md'
                          : 'border-gray-200 hover:border-gray-300'
                        }
                        focus:outline-none focus:ring-0
                      `}
                      placeholder="Purchase Order Number"
                    />
                  </div>

                  {/* PO Value */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <DollarSign className="mr-2 text-slate-500" size={16} />
                      PO Value (₹)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      name="poValue"
                      value={formData.poValue}
                      onChange={handleInputChange}
                      onFocus={() => handleFocus('poValue')}
                      onBlur={handleBlur}
                      className={`
                        w-full h-11 px-4 bg-white border-2 rounded-lg
                        transition-all duration-200 text-gray-900
                        ${focusedField === 'poValue'
                          ? 'border-slate-400 shadow-md'
                          : 'border-gray-200 hover:border-gray-300'
                        }
                        focus:outline-none focus:ring-0
                      `}
                      placeholder="Enter PO value"
                    />
                  </div>

                  {/* Date Fields */}
                  {[
                    { name: 'poDate', label: 'PO Date', required: false },
                    { name: 'startDate', label: 'Start Date', required: true },
                    { name: 'endDate', label: 'End Date (Optional)', required: false }
                  ].map(({ name, label, required }) => (
                    <div key={name}>
                      <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        <Calendar className="mr-2 text-slate-500" size={16} />
                        {label} {required && '*'}
                      </label>
                      <input
                        type="date"
                        name={name}
                        value={formData[name as keyof typeof formData] as string}
                        onChange={handleInputChange}
                        onFocus={() => handleFocus(name)}
                        onBlur={handleBlur}
                        min={getMinDate()}
                        className={`
                          w-full h-11 px-4 bg-white border-2 rounded-lg
                          transition-all duration-200 text-gray-900
                          ${focusedField === name
                            ? 'border-slate-400 shadow-md'
                            : 'border-gray-200 hover:border-gray-300'
                          }
                          focus:outline-none focus:ring-0
                        `}
                        required={required}
                      />
                      {name === 'startDate' && (
                        <p className="text-xs text-gray-500 mt-1">
                          Start date can be up to 30 days in the past
                        </p>
                      )}
                      {name === 'endDate' && (
                        <p className="text-xs text-gray-500 mt-1">
                          End date can be up to 30 days in the past (optional)
                        </p>
                      )}
                    </div>
                  ))}

                  {/* Status */}
                  <div className="lg:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${getStatusColor(formData.status)} mr-2`} />
                      Status
                    </label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      onFocus={() => handleFocus('status')}
                      onBlur={handleBlur}
                      className={`
                        w-full h-11 px-4 bg-white border-2 rounded-lg
                        transition-all duration-200 text-gray-900
                        ${focusedField === 'status'
                          ? 'border-slate-400 shadow-md'
                          : 'border-gray-200 hover:border-gray-300'
                        }
                        focus:outline-none focus:ring-0
                      `}
                    >
                      {Object.values(TaskStatus).map(status => (
                        <option key={status} value={status}>
                          {status.replace(/_/g, ' ')}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <FileText className="mr-2 text-slate-500" size={16} />
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    onFocus={() => handleFocus('description')}
                    onBlur={handleBlur}
                    rows={4}
                    className={`
                      w-full p-4 bg-white border-2 rounded-lg resize-none
                      transition-all duration-200 text-gray-900
                      ${focusedField === 'description'
                        ? 'border-slate-400 shadow-md'
                        : 'border-gray-200 hover:border-gray-300'
                      }
                      focus:outline-none focus:ring-0
                    `}
                    placeholder="Project description..."
                  />
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={onClose}
                    disabled={loading}
                    className="
                      px-6 py-2.5 bg-gray-100 hover:bg-gray-200 text-gray-700
                      rounded-lg transition-all duration-200 font-medium
                      hover:shadow-sm
                      disabled:opacity-50 disabled:cursor-not-allowed
                    "
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleSubmit}
                    disabled={loading}
                    className="
                      px-6 py-2.5 bg-slate-600 hover:bg-slate-700 text-white rounded-lg
                      transition-all duration-200 font-medium
                      hover:shadow-md disabled:opacity-50
                      disabled:cursor-not-allowed flex items-center
                    "
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/30 border-t-white mr-2" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save size={16} className="mr-2" />
                        Save Changes
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Success Dialog - Outside modal container with higher z-index */}
    <SuccessDialog
      isOpen={showSuccessDialog}
      onClose={() => {
        setShowSuccessDialog(false);
      }}
      onAction={() => {
        setShowSuccessDialog(false);
        // Call onSave callback to update the parent component
        if (updatedProjectData) {
          onSave(updatedProjectData);
        }
        onClose(); // Close the modal after success
      }}
      title="Project Updated Successfully!"
      message="Your project has been updated with the new information."
      details={successDetails}
      actionText="OK"
    />

    {/* Error Dialog - Outside modal container with higher z-index */}
    <ErrorDialog
      isOpen={showErrorDialog}
      onClose={() => setShowErrorDialog(false)}
      title="Failed to Update Project"
      message="There was an error updating your project. Please check the details below and try again."
      details={errorDetails}
      actionText="OK"
    />
    </>
  );
};

export default ProjectEditModal;