const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkMOMData() {
  try {
    console.log('=== CHECKING MOM DATA ===');

    // Get all MOMs with related data
    const moms = await prisma.mom.findMany({
      include: {
        project: {
          include: {
            customer: true
          }
        },
        mompoint: true,
        user_mom_createdByTouser: true,
        user_mom_updatedByTouser: true
      }
    });

    console.log(`Found ${moms.length} MOMs in database`);

    moms.forEach((mom, index) => {
      console.log(`\n--- MOM ${index + 1} ---`);
      console.log(`ID: ${mom.id}`);
      console.log(`Date: ${mom.date}`);
      console.log(`Project: ${mom.project?.name || 'No project'} (${mom.project?.code || 'No code'})`);
      console.log(`Customer: ${mom.project?.customer?.name || 'No customer'}`);
      console.log(`Agenda: ${mom.agenda || 'No agenda'}`);
      console.log(`Created by: ${mom.user_mom_createdByTouser?.name || 'Unknown'}`);
      console.log(`Created at: ${mom.createdAt}`);
      console.log(`Updated at: ${mom.updatedAt}`);
      console.log(`MOM Points: ${mom.mompoint?.length || 0}`);

      if (mom.mompoint && mom.mompoint.length > 0) {
        console.log('  MOM Points:');
        mom.mompoint.forEach((point, idx) => {
          console.log(`    ${idx + 1}. ${point.discussion} (Status: ${point.status})`);
        });
      }

      // Check if there are attendees or action items stored as JSON
      if (mom.attendees) {
        try {
          const attendees = typeof mom.attendees === 'string' ? JSON.parse(mom.attendees) : mom.attendees;
          console.log(`Attendees: ${Array.isArray(attendees) ? attendees.length : 0}`);
        } catch (e) {
          console.log('Attendees: Invalid JSON');
        }
      }

      if (mom.actionItems) {
        try {
          const actionItems = typeof mom.actionItems === 'string' ? JSON.parse(mom.actionItems) : mom.actionItems;
          console.log(`Action Items: ${Array.isArray(actionItems) ? actionItems.length : 0}`);
        } catch (e) {
          console.log('Action Items: Invalid JSON');
        }
      }
    });

  } catch (error) {
    console.error('Error checking MOM data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkMOMData();
