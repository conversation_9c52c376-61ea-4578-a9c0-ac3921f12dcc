.task-hierarchy {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.project-container {
  margin-bottom: 30px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.project-title {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #eee;
}

.tasks-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.task-group {
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
  border: 1px solid #e9ecef;
}

.task-group:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-color: #dee2e6;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 12px;
}

.task-id-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.task-id {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  min-width: 35px;
  text-align: center;
}

.task-title {
  font-weight: 500;
  color: #2c3e50;
  font-size: 1rem;
  word-break: break-word;
}

.task-status {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
}

.status-completed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-in-progress {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-delayed {
  background-color: #ffebee;
  color: #d32f2f;
}

.status-on-hold {
  background-color: #fff3e0;
  color: #f57c00;
}

.task-details {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  gap: 12px;
}

.detail-label {
  color: #6c757d;
  font-size: 0.85rem;
  min-width: 80px;
}

.detail-value {
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 500;
}

.priority-tag {
  background: #f1f3f5;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.date-row {
  display: flex;
  gap: 16px;
  margin-top: 12px;
}

.date-item {
  flex: 1;
}

.date-label {
  color: #6c757d;
  font-size: 0.85rem;
  display: block;
  margin-bottom: 4px;
}

.date-value {
  color: #2c3e50;
  font-size: 0.9rem;
  font-weight: 500;
}

.subtasks-container {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #dee2e6;
}

.subtasks-title {
  color: #2c3e50;
  font-size: 1rem;
  margin-bottom: 12px;
}

.subtask-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
}

.subtask-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 8px;
}

.subtask-id {
  background-color: #f1f3f5;
  color: #495057;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.85rem;
  min-width: 35px;
  text-align: center;
}

.subtask-title {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.95rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tasks-container {
    grid-template-columns: 1fr;
  }
  
  .project-container {
    padding: 16px;
  }
  
  .task-header,
  .subtask-header {
    flex-direction: column;
  }
  
  .task-status {
    align-self: flex-start;
  }
  
  .detail-row {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .date-row {
    flex-direction: column;
    gap: 8px;
  }
} 