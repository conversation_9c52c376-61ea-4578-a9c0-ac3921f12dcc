import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { momsAtom, projectsAtom, customersAtom, currentUserAtom } from '../../store';
import { MOM, MOMPoint, MOMDiscussionType, MOMPointStatus } from '../../types';
import { Plus, Save, X, FileText, Filter } from 'lucide-react';
import { formatDate, formatDateForInput } from '../../utils/dateFormatter';
import { momsAPI } from '../../services/api';
import { useNotification } from '../../contexts/NotificationContext';
import { v4 as uuidv4 } from 'uuid';

const MOMExcelGrid: React.FC = () => {
  const [moms] = useAtom(momsAtom);
  const [projects] = useAtom(projectsAtom);
  const [customers] = useAtom(customersAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const { showSuccess, showError } = useNotification();

  // Filter states
  const [selectedProject, setSelectedProject] = useState<string>('');
  const [selectedCustomer, setSelectedCustomer] = useState<string>('');

  // Get all MOM points from all MOMs
  const getAllMOMPoints = (): (MOMPoint & { mom: MOM })[] => {
    if (!moms) return [];

    let allPoints: (MOMPoint & { mom: MOM })[] = [];

    moms.forEach(mom => {
      if (mom.mompoint) {
        mom.mompoint.forEach(point => {
          allPoints.push({ ...point, mom });
        });
      }
    });

    // Apply filters
    if (selectedProject) {
      allPoints = allPoints.filter(point => point.mom.projectId === selectedProject);
    }
    if (selectedCustomer) {
      allPoints = allPoints.filter(point => {
        const project = projects?.find(p => p.id === point.mom.projectId);
        return project?.customerId === selectedCustomer;
      });
    }

    // Sort by serial number descending (newest first)
    return allPoints.sort((a, b) => b.slNo - a.slNo);
  };

  const filteredPoints = getAllMOMPoints();

  // Get unique customers from projects that have MOMs
  const getCustomersWithMOMs = () => {
    if (!moms || !customers || !projects) return [];

    const customerIds = new Set<string>();
    moms.forEach(mom => {
      const project = projects.find(p => p.id === mom.projectId);
      if (project) {
        customerIds.add(project.customerId);
      }
    });

    return customers.filter(c => customerIds.has(c.id));
  };

  // Get projects for selected customer that have MOMs
  const getProjectsWithMOMs = () => {
    if (!moms || !projects) return [];

    const projectIds = new Set(moms.map(mom => mom.projectId).filter(Boolean));
    let filteredProjects = projects.filter(p => projectIds.has(p.id));

    if (selectedCustomer) {
      filteredProjects = filteredProjects.filter(p => p.customerId === selectedCustomer);
    }

    return filteredProjects;
  };

  // This is a view-only component, no creation functionality

  const updatePoint = async (pointId: string, updates: Partial<MOMPoint>) => {
    try {
      const point = filteredPoints.find(p => p.id === pointId);
      if (!point) return;

      await momsAPI.updateMOMPoint(point.mom.id, pointId, updates);
      showSuccess('MOM point updated successfully');
    } catch (error) {
      console.error('Error updating MOM point:', error);
      showError('Failed to update MOM point');
    }
  };

  const deletePoint = async (pointId: string) => {
    try {
      const point = filteredPoints.find(p => p.id === pointId);
      if (!point) return;

      await momsAPI.deleteMOMPoint(point.mom.id, pointId);
      showSuccess('MOM point deleted successfully');
    } catch (error) {
      console.error('Error deleting MOM point:', error);
      showError('Failed to delete MOM point');
    }
  };

  return (
    <div className="space-y-6">
      {/* Excel-like Header */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        {/* Header Row */}
        <div className="bg-purple-100 px-6 py-3">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold text-gray-900">MOM</h1>
            <div className="flex items-center space-x-6 text-sm">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-gray-700">Total Queries:</span>
                <span className="bg-white px-2 py-1 rounded border">{filteredPoints.length}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="font-medium text-gray-700">Completed:</span>
                <span className="bg-white px-2 py-1 rounded border">{filteredPoints.filter(p => p.status === MOMPointStatus.COMPLETED).length}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="font-medium text-gray-700">Pending:</span>
                <span className="bg-white px-2 py-1 rounded border">{filteredPoints.filter(p => p.status === MOMPointStatus.PENDING).length}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Project Info Row */}
        <div className="bg-green-100 px-6 py-2">
          <div className="grid grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Project Name:</span>
              <span className="ml-2">
                {selectedProject ?
                  getProjectsWithMOMs().find(p => p.id === selectedProject)?.name || 'Unknown Project' :
                  'All Projects'
                }
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Company Name:</span>
              <span className="ml-2">
                {selectedCustomer ?
                  getCustomersWithMOMs().find(c => c.id === selectedCustomer)?.name || 'Unknown Company' :
                  'All Companies'
                }
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Date:</span>
              <span className="ml-2">{formatDate(new Date())}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Status:</span>
              <span className="ml-2">Active</span>
            </div>
          </div>
        </div>

        {/* Filters Row */}
        <div className="bg-gray-50 px-6 py-3 border-b">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Filters:</span>
            </div>

            <select
              value={selectedCustomer}
              onChange={(e) => {
                setSelectedCustomer(e.target.value);
                setSelectedProject(''); // Reset project filter
              }}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="">All Customers</option>
              {getCustomersWithMOMs().map(customer => (
                <option key={customer.id} value={customer.id}>{customer.name}</option>
              ))}
            </select>

            <select
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="">All Projects</option>
              {getProjectsWithMOMs().map(project => (
                <option key={project.id} value={project.id}>
                  {project.code}: {project.name}
                </option>
              ))}
            </select>

            {(selectedCustomer || selectedProject) && (
              <button
                onClick={() => {
                  setSelectedCustomer('');
                  setSelectedProject('');
                }}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
              >
                Clear Filters
              </button>
            )}
          </div>
        </div>
      </div>

      {/* MOM Grid */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                  Sl No
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Project
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Customer
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                  Date
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Discussion Type
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                  Station
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Discussion
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action Plan
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Responsibility
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Planned Date
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Completion Date
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                  Status
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Image/Attachment
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Remarks
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPoints.map((point) => (
                <MOMPointRow
                  key={point.id}
                  point={point}
                  onUpdate={(updates) => updatePoint(point.id, updates)}
                  onDelete={() => deletePoint(point.id)}
                />
              ))}

              {/* Empty state */}
              {filteredPoints.length === 0 && (
                <tr>
                  <td colSpan={15} className="px-3 py-8 text-center text-gray-500">
                    No MOM points found. Create a new MOM to add discussion points.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// MOM Point Row Component
interface MOMPointRowProps {
  point: MOMPoint & { mom: MOM };
  onUpdate: (updates: Partial<MOMPoint>) => void;
  onDelete: () => void;
}

const MOMPointRow: React.FC<MOMPointRowProps> = ({ point, onUpdate, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<Partial<MOMPoint>>(point);
  const [projects] = useAtom(projectsAtom);
  const [customers] = useAtom(customersAtom);

  const handleSave = () => {
    onUpdate(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData(point);
    setIsEditing(false);
  };

  const getStatusColor = (status: MOMPointStatus) => {
    switch (status) {
      case MOMPointStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case MOMPointStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case MOMPointStatus.DELAYED:
        return 'bg-red-100 text-red-800';
      case MOMPointStatus.ON_HOLD:
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const project = projects?.find(p => p.id === point.mom.projectId);
  const customer = customers?.find(c => c.id === project?.customerId);

  if (isEditing) {
    return (
      <tr className="bg-blue-50">
        <td className="px-3 py-2 text-sm text-gray-900">{point.slNo}</td>
        <td className="px-3 py-2 text-sm text-gray-900">
          {project ? `${project.code}: ${project.name}` : 'No Project'}
        </td>
        <td className="px-3 py-2 text-sm text-gray-900">
          {customer?.name || 'Unknown Customer'}
        </td>
        <td className="px-3 py-2">
          <input
            type="date"
            value={editData.date ? formatDateForInput(editData.date) : ''}
            onChange={(e) => setEditData({ ...editData, date: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <input
            type="text"
            value={editData.discussionType || ''}
            onChange={(e) => setEditData({ ...editData, discussionType: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Discussion Type"
          />
        </td>
        <td className="px-3 py-2">
          <input
            type="text"
            value={editData.station || ''}
            onChange={(e) => setEditData({ ...editData, station: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <textarea
            value={editData.discussion || ''}
            onChange={(e) => setEditData({ ...editData, discussion: e.target.value })}
            rows={2}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <textarea
            value={editData.actionPlan || ''}
            onChange={(e) => setEditData({ ...editData, actionPlan: e.target.value })}
            rows={2}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <input
            type="text"
            value={editData.responsibility || ''}
            onChange={(e) => setEditData({ ...editData, responsibility: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <input
            type="date"
            value={editData.plannedDate ? formatDateForInput(editData.plannedDate) : ''}
            onChange={(e) => setEditData({ ...editData, plannedDate: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <input
            type="date"
            value={editData.completionDate ? formatDateForInput(editData.completionDate) : ''}
            onChange={(e) => setEditData({ ...editData, completionDate: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <select
            value={editData.status || MOMPointStatus.PENDING}
            onChange={(e) => setEditData({ ...editData, status: e.target.value as MOMPointStatus })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            {Object.values(MOMPointStatus).map(status => (
              <option key={status} value={status}>{status.replace('_', ' ')}</option>
            ))}
          </select>
        </td>
        <td className="px-3 py-2">
          <input
            type="text"
            value={editData.imageAttachment || ''}
            onChange={(e) => setEditData({ ...editData, imageAttachment: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <textarea
            value={editData.remarks || ''}
            onChange={(e) => setEditData({ ...editData, remarks: e.target.value })}
            rows={2}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <div className="flex space-x-1">
            <button
              onClick={handleSave}
              className="p-1 text-green-600 hover:text-green-800"
            >
              <Save className="h-4 w-4" />
            </button>
            <button
              onClick={handleCancel}
              className="p-1 text-gray-600 hover:text-gray-800"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
    );
  }

  return (
    <tr className="hover:bg-gray-50" onDoubleClick={() => setIsEditing(true)}>
      <td className="px-3 py-4 text-sm text-gray-900">{point.slNo}</td>
      <td className="px-3 py-4 text-sm text-gray-900">
        <div className="max-w-xs truncate" title={project ? `${project.code}: ${project.name}` : 'No Project'}>
          {project ? `${project.code}: ${project.name}` : 'No Project'}
        </div>
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {customer?.name || 'Unknown Customer'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.date ? formatDate(point.date) : '-'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.discussionType || '-'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.station || '-'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        <div className="max-w-xs truncate" title={point.discussion}>
          {point.discussion}
        </div>
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        <div className="max-w-xs truncate" title={point.actionPlan || ''}>
          {point.actionPlan || '-'}
        </div>
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.responsibility || '-'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.plannedDate ? formatDate(point.plannedDate) : '-'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.completionDate ? formatDate(point.completionDate) : '-'}
      </td>
      <td className="px-3 py-4 text-sm">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(point.status)}`}>
          {point.status.replace('_', ' ')}
        </span>
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.imageAttachment || '-'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        <div className="max-w-xs truncate" title={point.remarks || ''}>
          {point.remarks || '-'}
        </div>
      </td>
      <td className="px-3 py-4 text-sm">
        <div className="flex space-x-1">
          <button
            onClick={() => setIsEditing(true)}
            className="p-1 text-blue-600 hover:text-blue-800"
            title="Edit"
          >
            <FileText className="h-4 w-4" />
          </button>
          <button
            onClick={onDelete}
            className="p-1 text-red-600 hover:text-red-800"
            title="Delete"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </td>
    </tr>
  );
};

export default MOMExcelGrid;
