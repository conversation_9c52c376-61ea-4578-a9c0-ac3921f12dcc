import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { customers<PERSON><PERSON>, engineers<PERSON><PERSON>, moms<PERSON>tom, currentUserAtom } from '../../store';
import { M<PERSON>, MOMAttendee, MOMActionItem, Customer } from '../../types';
import { v4 as uuidv4 } from 'uuid';
import { CheckCircle2, XCircle, PlusCircle } from 'lucide-react';
import CustomerDialog from '../Customers/CustomerDialog';
import EditableGrid, { GridColumn, GridRow } from '../common/EditableGrid';
import { useNavigate } from 'react-router-dom';

// Define row types for the unified grid
enum RowType {
  MEKHOS_ATTENDEE = 'Mekhos Attendee',
  CUSTOMER_ATTENDEE = 'Customer Attendee',
  DISCUSSION_POINT = 'Discussion Point',
  ACTION_ITEM = 'Action Item'
}

// Define the unified row structure
interface UnifiedRow extends GridRow {
  type: RowType;
  name: string;        // Name for attendees, point text for discussion points, description for action items
  detail1?: string;    // Designation for attendees, empty for points, assignee for action items
  detail2?: string;    // Email for attendees, empty for points, due date for action items
}

const MOMUnifiedGrid: React.FC = () => {
  const navigate = useNavigate();
  const [customers, setCustomers] = useAtom(customersAtom);
  const [engineers] = useAtom(engineersAtom);
  const [, setMoms] = useAtom(momsAtom);
  const [currentUser] = useAtom(currentUserAtom);

  const [date, setDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [customerId, setCustomerId] = useState<string>('');
  const [engineerId, setEngineerId] = useState<string>('');
  const [agenda, setAgenda] = useState<string>('');

  // Unified grid rows
  const [rows, setRows] = useState<UnifiedRow[]>([
    { id: uuidv4(), type: RowType.MEKHOS_ATTENDEE, name: '', detail1: '', detail2: '' },
    { id: uuidv4(), type: RowType.CUSTOMER_ATTENDEE, name: '', detail1: '', detail2: '' },
    { id: uuidv4(), type: RowType.DISCUSSION_POINT, name: '' },
    { id: uuidv4(), type: RowType.ACTION_ITEM, name: '', detail1: '', detail2: '' }
  ]);

  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState(false);
  const [newCustomerName, setNewCustomerName] = useState('');
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Column definitions for the unified grid
  const columns: GridColumn[] = [
    {
      id: 'type',
      header: 'Type',
      type: 'select',
      width: '15%',
      options: [
        { value: RowType.MEKHOS_ATTENDEE, label: 'Mekhos Attendee' },
        { value: RowType.CUSTOMER_ATTENDEE, label: 'Customer Attendee' },
        { value: RowType.DISCUSSION_POINT, label: 'Discussion Point' },
        { value: RowType.ACTION_ITEM, label: 'Action Item' }
      ],
      required: true
    },
    {
      id: 'name',
      header: 'Name / Description / Point',
      width: '35%',
      required: true
    },
    {
      id: 'detail1',
      header: 'Designation / Assignee',
      width: '25%',
      type: 'text' // Will be dynamically changed to select for action items
    },
    {
      id: 'detail2',
      header: 'Email / Due Date',
      width: '25%',
      type: 'text' // Will be dynamically changed to date for action items
    }
  ];

  // Handler for adding a new customer
  const handleAddCustomer = (newCustomer: Customer) => {
    setCustomers((prevCustomers) => [...prevCustomers, newCustomer]);
    setCustomerId(newCustomer.id);
    setIsCustomerDialogOpen(false);
  };

  // Add a new row based on type
  const addRow = () => {
    setRows([...rows, { id: uuidv4(), type: RowType.DISCUSSION_POINT, name: '' }]);
  };

  // Handle row changes, including dynamic field types
  const handleRowsChange = (newRows: GridRow[]) => {
    // Cast to UnifiedRow[] and handle any special logic
    const typedRows = newRows as UnifiedRow[];
    setRows(typedRows);
  };

  // Validation function
  const validate = (): boolean => {
    const newErrors: {[key: string]: string} = {};

    if (!date) newErrors.date = 'Meeting date is required';
    if (!customerId) newErrors.customerId = 'Customer is required';
    if (!engineerId) newErrors.engineerId = 'Engineer is required';
    if (!agenda.trim()) newErrors.agenda = 'Agenda is required';

    // Check if at least one attendee from Mekhos has a name
    if (!rows.some(r => r.type === RowType.MEKHOS_ATTENDEE && r.name?.trim())) {
      newErrors.mekhosAttendees = 'At least one Mekhos attendee is required';
    }

    // Check if at least one attendee from customer has a name
    if (!rows.some(r => r.type === RowType.CUSTOMER_ATTENDEE && r.name?.trim())) {
      newErrors.customerAttendees = 'At least one customer attendee is required';
    }

    // Check if at least one point is entered
    if (!rows.some(r => r.type === RowType.DISCUSSION_POINT && r.name?.trim())) {
      newErrors.points = 'At least one discussion point is required';
    }

    // Check action items
    const invalidActionItems = rows.filter(item =>
      item.type === RowType.ACTION_ITEM &&
      item.name?.trim() &&
      (!item.detail1 || !item.detail2)
    );

    if (invalidActionItems.length > 0) {
      newErrors.actionItems = 'All action items must have an assignee and due date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Form submission handler
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validate()) {
      return;
    }

    setIsSubmitting(true);

    // Simulate API call delay
    setTimeout(() => {
      try {
        // Extract Mekhos attendees
        const mekhosAttendees: MOMAttendee[] = rows
          .filter(row => row.type === RowType.MEKHOS_ATTENDEE && row.name?.trim())
          .map(row => ({
            id: row.id,
            name: row.name,
            company: "Mekhos Technology",
            designation: row.detail1 || '',
            email: row.detail2 || ''
          }));

        // Extract customer attendees
        const customerAttendees: MOMAttendee[] = rows
          .filter(row => row.type === RowType.CUSTOMER_ATTENDEE && row.name?.trim())
          .map(row => ({
            id: row.id,
            name: row.name,
            company: customers.find(c => c.id === customerId)?.name || "Unknown",
            designation: row.detail1 || '',
            email: row.detail2 || ''
          }));

        // Extract discussion points
        const discussionPoints: string[] = rows
          .filter(row => row.type === RowType.DISCUSSION_POINT && row.name?.trim())
          .map(row => row.name);

        // Extract action items
        const actionItems: MOMActionItem[] = rows
          .filter(row => row.type === RowType.ACTION_ITEM && row.name?.trim())
          .map(row => ({
            id: row.id,
            description: row.name,
            assigneeId: row.detail1 || '',
            dueDate: row.detail2 || '',
            status: "PENDING"
          }));

        // Create new MOM entry
        const newMOM: MOM = {
          id: uuidv4(),
          date,
          customerId,
          engineerId,
          agenda,
          attendees: [...mekhosAttendees, ...customerAttendees],
          points: discussionPoints,
          actionItems,
          createdBy: currentUser?.id || '',
          createdAt: new Date().toISOString()
        };

        // Add the new MOM to the state
        setMoms((prevMoms) => [...prevMoms, newMOM]);

        // Show success message
        setSuccess('Minutes of Meeting created successfully!');

        // Reset the form
        resetForm();

        // Clear success message after 5 seconds
        setTimeout(() => {
          setSuccess(null);
        }, 5002);
      } catch (error) {
        console.error('Error creating MOM:', error);
      } finally {
        setIsSubmitting(false);
      }
    }, 1000);
  };

  // Reset form to initial state
  const resetForm = () => {
    setDate(new Date().toISOString().split('T')[0]);
    setCustomerId('');
    setEngineerId('');
    setAgenda('');
    setRows([
      { id: uuidv4(), type: RowType.MEKHOS_ATTENDEE, name: '', detail1: '', detail2: '' },
      { id: uuidv4(), type: RowType.CUSTOMER_ATTENDEE, name: '', detail1: '', detail2: '' },
      { id: uuidv4(), type: RowType.DISCUSSION_POINT, name: '' },
      { id: uuidv4(), type: RowType.ACTION_ITEM, name: '', detail1: '', detail2: '' }
    ]);
    setErrors({});
  };

  // Render the cell based on row type
  const renderCell = (row: UnifiedRow, columnId: string) => {
    if (columnId === 'detail1' && row.type === RowType.ACTION_ITEM) {
      return (
        <select
          value={row.detail1 || ''}
          onChange={(e) => {
            const updatedRows = rows.map(r =>
              r.id === row.id ? { ...r, detail1: e.target.value } : r
            );
            setRows(updatedRows);
          }}
          className="w-full bg-transparent focus:outline-none"
        >
          <option value="">Select Assignee</option>
          {engineers.map(eng => (
            <option key={eng.id} value={eng.id}>{eng.name}</option>
          ))}
        </select>
      );
    }

    if (columnId === 'detail2' && row.type === RowType.ACTION_ITEM) {
      return (
        <input
          type="date"
          value={row.detail2 || ''}
          onChange={(e) => {
            const updatedRows = rows.map(r =>
              r.id === row.id ? { ...r, detail2: e.target.value } : r
            );
            setRows(updatedRows);
          }}
          className="w-full bg-transparent focus:outline-none"
        />
      );
    }

    return null; // Use default rendering
  };

  return (
    <div className="card p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Create Minutes of Meeting</h2>
      </div>

      {success && (
        <div className="bg-success/10 text-success-dark p-4 rounded-md flex items-center justify-between mb-6 fade-in">
          <div className="flex items-center">
            <CheckCircle2 size={18} className="mr-2" />
            <span>{success}</span>
          </div>
          <button
            onClick={() => setSuccess(null)}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle size={18} />
          </button>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label htmlFor="date" className="form-label">Meeting Date</label>
            <input
              id="date"
              type="date"
              className={`form-input ${errors.date ? 'border-error' : ''}`}
              value={date}
              onChange={(e) => setDate(e.target.value)}
              required
            />
            {errors.date && (
              <p className="mt-1 text-xs text-error">{errors.date}</p>
            )}
          </div>

          <div>
            <label htmlFor="customer" className="form-label">Customer</label>
            <div className="flex space-x-2">
              <div className="flex-1">
                <select
                  id="customer"
                  className={`form-select ${errors.customerId ? 'border-error' : ''}`}
                  value={customerId}
                  onChange={(e) => setCustomerId(e.target.value)}
                  required
                >
                  <option value="">Select Customer</option>
                  {customers.map((customer) => (
                    <option key={customer.id} value={customer.id}>{customer.name}</option>
                  ))}
                </select>
              </div>
              <button
                type="button"
                className="btn btn-outline flex items-center py-2"
                onClick={() => navigate('/customers?addCustomer=true')}
              >
                <PlusCircle size={16} className="mr-1" />
                New
              </button>
            </div>
            {errors.customerId && (
              <p className="mt-1 text-xs text-error">{errors.customerId}</p>
            )}
          </div>

          <div>
            <label htmlFor="engineerId" className="form-label">Lead Engineer</label>
            <select
              id="engineerId"
              className={`form-select ${errors.engineerId ? 'border-error' : ''}`}
              value={engineerId}
              onChange={(e) => setEngineerId(e.target.value)}
              required
            >
              <option value="">Select Engineer</option>
              {engineers.map((engineer) => (
                <option key={engineer.id} value={engineer.id}>
                  {engineer.name} ({engineer.department})
                </option>
              ))}
            </select>
            {errors.engineerId && (
              <p className="mt-1 text-xs text-error">{errors.engineerId}</p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="agenda" className="form-label">Meeting Agenda</label>
          <textarea
            id="agenda"
            className={`form-input ${errors.agenda ? 'border-error' : ''}`}
            rows={3}
            placeholder="Enter the meeting agenda..."
            value={agenda}
            onChange={(e) => setAgenda(e.target.value)}
            required
          />
          {errors.agenda && (
            <p className="mt-1 text-xs text-error">{errors.agenda}</p>
          )}
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-800">Meeting Details</h3>

          <EditableGrid
            columns={columns}
            rows={rows}
            onRowsChange={handleRowsChange}
            onAddRow={addRow}
            onRemoveRow={(rowId) => setRows(rows.filter(row => row.id !== rowId))}
            addRowButtonText="Add Row"
            emptyMessage="No data available. Add rows to enter meeting details."
            renderCustomCell={renderCell}
          />

          {(errors.mekhosAttendees || errors.customerAttendees || errors.points || errors.actionItems) && (
            <div className="mt-2 text-xs text-error">
              {errors.mekhosAttendees && <p>{errors.mekhosAttendees}</p>}
              {errors.customerAttendees && <p>{errors.customerAttendees}</p>}
              {errors.points && <p>{errors.points}</p>}
              {errors.actionItems && <p>{errors.actionItems}</p>}
            </div>
          )}
        </div>

        <div className="flex justify-end mt-6">
          <button
            type="submit"
            className="btn btn-primary flex items-center"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </span>
            ) : (
              <span className="flex items-center">
                <CheckCircle2 size={18} className="mr-2" />
                Create MOM
              </span>
            )}
          </button>
        </div>
      </form>

      {/* Customer Dialog */}
      <CustomerDialog
        isOpen={isCustomerDialogOpen}
        onClose={() => setIsCustomerDialogOpen(false)}
        onSave={handleAddCustomer}
        initialData={{ name: newCustomerName }}
      />
    </div>
  );
};

export default MOMUnifiedGrid;
