import { getDefaultStore } from 'jotai';
import {
  <PERSON><PERSON><PERSON>,
  departments<PERSON>tom,
  engineers<PERSON>tom,
  projectsAtom,
  moms<PERSON><PERSON>,
  customersAtom,
  alerts<PERSON>tom,
  projectCategoriesAtom,
  milestoneTemplatesAtom
} from '../store';
import {
  usersAPI,
  departmentsAPI,
  engineersAPI,
  projectsAPI,
  momsAPI,
  customersAPI,
  alertsAP<PERSON>,
  projectCategoriesAPI,
  milestoneTemplatesAPI
} from './api';

// Singleton data service that doesn't use hooks
class DataService {
  private store = getDefaultStore();
  private isLoading = false;
  private lastLoadTime = 0;
  private readonly CACHE_DURATION = 60000; // 1 minute cache (reduced for better responsiveness after login)
  private loadingPromises: Map<string, Promise<boolean>> = new Map(); // Track individual loading promises
  private dataLoaded = false; // Track if initial data load is complete

  // Helper method to handle individual data loading with deduplication and retry
  private async loadDataType<T>(
    key: string,
    apiCall: () => Promise<{ data: T[] }>,
    atomSetter: (data: T[]) => void,
    maxRetries: number = 3
  ): Promise<boolean> {
    // Check if already loading this data type
    if (this.loadingPromises.has(key)) {
      console.log(`${key} loading already in progress, waiting...`);
      return this.loadingPromises.get(key)!;
    }

    // Create loading promise with retry logic
    const loadingPromise = (async () => {
      let lastError: any;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`Loading ${key} data (attempt ${attempt}/${maxRetries})`);

          // Check if token is still valid before making request
          const token = localStorage.getItem('token');
          if (!token) {
            throw new Error('No authentication token found');
          }

          const response = await apiCall();
          atomSetter(response.data);
          console.log(`✅ Successfully loaded ${response.data.length} ${key}`);
          return true;
        } catch (error: any) {
          lastError = error;
          console.warn(`⚠️ Attempt ${attempt}/${maxRetries} failed for ${key}:`, error.message);

          // If it's an auth error, don't retry
          if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {
            console.error(`❌ Authentication error loading ${key}, stopping retries`);
            break;
          }

          // Wait before retrying (exponential backoff)
          if (attempt < maxRetries) {
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Max 5 seconds
            console.log(`⏳ Waiting ${delay}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      console.error(`❌ Failed to load ${key} after ${maxRetries} attempts:`, lastError);
      return false;
    })();

    // Store the promise
    this.loadingPromises.set(key, loadingPromise);

    try {
      return await loadingPromise;
    } finally {
      // Remove from loading promises when done
      this.loadingPromises.delete(key);
    }
  }

  // Load users
  async loadUsers(): Promise<boolean> {
    return this.loadDataType('users', usersAPI.getUsers, (data) => this.store.set(usersAtom, data));
  }

  // Load departments (legacy - keeping for backward compatibility)
  async loadDepartments(): Promise<boolean> {
    return this.loadDataType('departments', departmentsAPI.getDepartments, (data) => this.store.set(departmentsAtom, data));
  }

  // Load engineers
  async loadEngineers(): Promise<boolean> {
    return this.loadDataType('engineers', engineersAPI.getEngineers, (data) => this.store.set(engineersAtom, data));
  }

  // Load projects
  async loadProjects(): Promise<boolean> {
    return this.loadDataType('projects', projectsAPI.getProjects, (data) => this.store.set(projectsAtom, data));
  }

  // Load MOMs
  async loadMOMs(): Promise<boolean> {
    return this.loadDataType('moms', momsAPI.getMOMs, (data) => this.store.set(momsAtom, data));
  }

  // Load customers
  async loadCustomers(): Promise<boolean> {
    return this.loadDataType('customers', customersAPI.getCustomers, (data) => this.store.set(customersAtom, data));
  }

  // Load project categories (Director and Project Manager)
  async loadProjectCategories(): Promise<boolean> {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');

    if (!token) {
      console.warn('⚠️ No authentication token found, skipping project categories load');
      return false;
    }

    // Check if user is Director or Project Manager
    if (user) {
      const userData = JSON.parse(user);
      if (userData.role !== 'DIRECTOR' && userData.role !== 'PROJECT_MANAGER') {
        console.log('⚠️ User is not Director or Project Manager, skipping project categories load');
        return false;
      }
    }

    return this.loadDataType('projectCategories', projectCategoriesAPI.getProjectCategories, (data) => this.store.set(projectCategoriesAtom, data));
  }

  // Load milestone templates (Director only)
  async loadMilestoneTemplates(): Promise<boolean> {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');

    console.log('🔐 Loading milestone templates with token:', token ? 'Present' : 'Missing');

    if (!token) {
      console.warn('⚠️ No authentication token found, skipping milestone templates load');
      return false;
    }

    // Check if user is Director
    if (user) {
      const userData = JSON.parse(user);
      if (userData.role !== 'DIRECTOR') {
        console.log('⚠️ User is not Director, skipping milestone templates load');
        return false;
      }
    }

    return this.loadDataType('milestoneTemplates', milestoneTemplatesAPI.getMilestoneTemplates, (data) => this.store.set(milestoneTemplatesAtom, data));
  }

  // Load alerts
  async loadAlerts(): Promise<boolean> {
    console.log('🔄 DataService.loadAlerts() called');
    try {
      const result = await this.loadDataType('alerts', alertsAPI.getAlerts, (data) => {
        console.log('📊 Setting alerts data in store:', data.length, 'alerts');
        this.store.set(alertsAtom, data);
      });
      console.log('✅ DataService.loadAlerts() completed successfully:', result);
      return result;
    } catch (error) {
      console.error('❌ DataService.loadAlerts() failed:', error);
      throw error;
    }
  }

  // Helper method to check if cache is valid
  private isCacheValid(): boolean {
    const now = Date.now();
    return this.lastLoadTime > 0 && (now - this.lastLoadTime) < this.CACHE_DURATION;
  }

  // Load all data with caching and optimized loading strategy
  async loadAllData(): Promise<boolean> {
    const now = Date.now();

    // Check authentication first
    const token = localStorage.getItem('token');
    if (!token) {
      console.warn('⚠️ No authentication token found, cannot load data');
      return false;
    }

    // Check if data was loaded recently
    if (this.lastLoadTime > 0 && (now - this.lastLoadTime) < this.CACHE_DURATION) {
      console.log(`Data loaded recently (${Math.round((now - this.lastLoadTime) / 1000)}s ago), using cache...`);
      return true;
    }

    if (this.isLoading) {
      console.log('Data loading already in progress, waiting for completion...');
      // Wait for current loading to complete
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.dataLoaded;
    }

    try {
      this.isLoading = true;
      console.log('🚀 Loading all application data with optimized strategy...');

      // Load critical data first (users and engineers are needed for most operations)
      console.log('📊 Phase 1: Loading critical user data...');
      const criticalResults = await Promise.allSettled([
        this.loadUsers(),
        this.loadEngineers()
      ]);

      // Load main application data in parallel
      console.log('📊 Phase 2: Loading main application data...');
      const mainResults = await Promise.allSettled([
        this.loadProjects(),
        this.loadCustomers(),
        this.loadMOMs(),
        this.loadDepartments() // Legacy support
      ]);

      // Load optional data (role-specific)
      console.log('📊 Phase 3: Loading role-specific data...');
      const optionalResults = await Promise.allSettled([
        this.loadProjectCategories(),
        this.loadMilestoneTemplates()
        // this.loadAlerts() - Removed: Only load when alerts page is accessed
      ]);

      // Combine all results
      const allResults = [...criticalResults, ...mainResults, ...optionalResults];
      const failures = allResults.filter(result => result.status === 'rejected');
      const successes = allResults.filter(result => result.status === 'fulfilled').length;

      // Log results
      console.log(`📊 Data loading completed: ${successes}/${allResults.length} successful`);

      if (failures.length > 0) {
        const promiseNames = ['users', 'engineers', 'projects', 'customers', 'moms', 'departments', 'projectCategories', 'milestoneTemplates'];
        console.warn(`⚠️ ${failures.length} data loading operations failed:`);
        failures.forEach((failure, index) => {
          console.error(`❌ Failed to load ${promiseNames[index]}:`, failure.reason);
        });

        // If critical data failed, consider it a failure
        if (criticalResults.some(result => result.status === 'rejected')) {
          console.error('❌ Critical data loading failed, marking as unsuccessful');
          return false;
        }
      }

      this.lastLoadTime = now;
      this.dataLoaded = true; // Mark data as loaded
      console.log('✅ Successfully completed data loading process');
      return true;
    } catch (error) {
      console.error('❌ Error loading all application data:', error);
      return false;
    } finally {
      this.isLoading = false;
    }
  }

  // Refresh data function - force reload by clearing cache
  async refreshData(): Promise<boolean> {
    console.log('Force refreshing all data...');
    this.lastLoadTime = 0; // Clear cache
    this.dataLoaded = false; // Reset data loaded flag
    this.loadingPromises.clear(); // Clear any pending loading promises
    return this.loadAllData();
  }

  // Excel import/export methods
  async importProjectExcelData(projectId: string, sections: any[]): Promise<any> {
    try {
      console.log('🚀 DataService: Importing Excel data for project:', projectId);
      console.log('🚀 DataService: Sections data:', sections);
      console.log('🚀 DataService: Calling projectsAPI.importExcelData...');

      const response = await projectsAPI.importExcelData(projectId, sections);

      console.log('✅ DataService: Excel import successful:', response);
      return response;
    } catch (error) {
      console.error('❌ DataService: Error importing Excel data:', error);
      throw error;
    }
  }

  async exportProjectExcelData(projectId: string): Promise<any> {
    try {
      console.log('Exporting Excel data for project:', projectId);
      const response = await projectsAPI.exportExcelData(projectId);
      console.log('Excel export successful:', response);
      return response;
    } catch (error) {
      console.error('Error exporting Excel data:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const dataService = new DataService();
