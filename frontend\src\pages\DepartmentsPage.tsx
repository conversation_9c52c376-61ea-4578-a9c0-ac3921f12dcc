import React, { useState, useEffect, useCallback, useRef } from 'react';
import { BuildingOfficeIcon } from '@heroicons/react/24/solid';
import { Plus, Shield, AlertCircle } from 'lucide-react';
import { useAtom } from 'jotai';
import { currentUserAtom, departmentsAtom } from '../store';
import { UserRole, Department } from '../types';
import DepartmentGrid from '../components/Departments/DepartmentGrid';
import { dataService } from '../services/dataServiceSingleton';
import { useNotification } from '../contexts/NotificationContext';

// Debounce utility function for department operations
const useDebounce = (callback: Function, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback((...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

const DepartmentsPage: React.FC = () => {
  const { showSuccess, showError } = useNotification();

  const [currentUser] = useAtom(currentUserAtom);
  const [departments, setDepartments] = useAtom(departmentsAtom);

  const [error, setError] = useState<string | null>(null);
  const [showNewRow, setShowNewRow] = useState(false);

  // Debounced refresh function to reduce API calls
  const debouncedRefreshDepartments = useDebounce(async () => {
    try {
      console.log('🔄 Refreshing departments data...');
      await dataService.loadDepartments();
      console.log('✅ Departments data refreshed successfully');
    } catch (error) {
      console.error('❌ Error refreshing departments:', error);
      setError('Failed to refresh departments. Please try again.');
    }
  }, 1000); // 1 second debounce

  // Role-based permission functions
  const canManageDepartments = (): boolean => {
    return currentUser?.role === UserRole.DIRECTOR;
  };

  const canViewDepartments = (): boolean => {
    if (!currentUser) return false;
    return [UserRole.DIRECTOR, UserRole.PROJECT_MANAGER, UserRole.TEAM_LEAD].includes(currentUser.role as UserRole);
  };

  // Handle Add Department button click
  const handleAddDepartment = () => {
    setShowNewRow(true);
    setError(null);
  };

  // Auto-refresh mechanism for real-time updates
  useEffect(() => {
    // Add visibility change listener to refresh when page becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('📱 Departments page became visible, refreshing data...');
        debouncedRefreshDepartments();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Refresh every 30 seconds when page is visible
    const refreshInterval = setInterval(() => {
      if (!document.hidden) {
        console.log('⏰ Periodic refresh of departments data...');
        debouncedRefreshDepartments();
      }
    }, 30000);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(refreshInterval);
    };
  }, [debouncedRefreshDepartments]);



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="w-full mx-auto py-2">
        {/* Header Section with 3D Card */}
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 border border-white/20 backdrop-blur-sm relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-indigo-600/5"></div>
          <div className="relative z-10 flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-1 flex items-center">
                <div className="relative bg-gradient-to-br from-white to-blue-100 rounded-lg p-2 mr-3 shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                  <BuildingOfficeIcon className="w-6 h-6 text-blue-600 drop-shadow-sm" />
                </div>
                Departments
              </h1>
              <p className="text-gray-600">
                {canManageDepartments()
                  ? "Manage company departments"
                  : "View departments (read-only access)"}
              </p>
            </div>
            {canManageDepartments() && (
              <button
                type="button"
                onClick={handleAddDepartment}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-2 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 flex items-center font-medium"
              >
                <Plus size={16} className="mr-2" />
                Add Department
              </button>
            )}
          </div>
        </div>

        {/* Read-Only Warning */}
        {!canManageDepartments() && canViewDepartments() && (
          <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-xl p-4 mb-6 shadow-lg">
            <div className="flex items-center">
              <div className="bg-amber-100 rounded-full p-2 mr-3">
                <Shield size={18} className="text-amber-600" />
              </div>
              <div>
                <p className="font-bold text-amber-800">Read-Only Access</p>
                <p className="text-amber-700 text-sm">
                  Only Directors can create, edit, or delete departments. You can view the department list below.
                </p>
              </div>
            </div>
          </div>
        )}



        {/* Error Message */}
        {error && (
          <div className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl p-4 mb-6 shadow-lg">
            <div className="flex items-start">
              <div className="bg-red-100 rounded-full p-2 mr-3 mt-0.5">
                <AlertCircle size={18} className="text-red-600" />
              </div>
              <span className="text-red-800 font-medium">{error}</span>
            </div>
          </div>
        )}

        {/* Main Content */}
        {!canViewDepartments() ? (
          <div className="flex justify-center items-center min-h-[60vh]">
            <div className="bg-white rounded-2xl shadow-2xl p-12 text-center transform perspective-1000 hover:scale-105 transition-all duration-300 border border-gray-100">
              <div className="bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6 shadow-inner">
                <Shield size={48} className="text-gray-400" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Access Restricted</h3>
              <p className="text-gray-600 mb-2 text-lg">
                You don't have permission to view department management.
              </p>
              <p className="text-gray-500">
                Please contact your administrator for access.
              </p>
            </div>
          </div>
        ) : departments.length === 0 && !canManageDepartments() ? (
          <div className="flex justify-center items-center min-h-[50vh]">
            <div className="bg-white rounded-3xl shadow-2xl p-16 text-center transform hover:scale-105 transition-all duration-300 border border-gray-100 max-w-2xl">
              <div className="bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full w-32 h-32 flex items-center justify-center mx-auto mb-8 shadow-inner">
                <BuildingOfficeIcon className="w-16 h-16 text-blue-500" />
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">No Departments Yet</h2>
              <p className="text-gray-600 mb-8 text-lg leading-relaxed">
                No departments have been added yet. Contact your Director to add departments.
              </p>
            </div>
          </div>
        ) : (
          <DepartmentGrid
            departments={departments}
            onDepartmentsChange={(updatedDepartments) => {
              setDepartments(updatedDepartments);
              // Use debounced refresh to ensure data consistency
              debouncedRefreshDepartments();
            }}
            canManage={canManageDepartments()}
            onError={setError}
            onSuccess={(message) => {
              showSuccess('Department Operation', message);
              setShowNewRow(false); // Hide new row after successful creation
              // Use debounced refresh after successful operations
              debouncedRefreshDepartments();
            }}
            showNewRow={showNewRow}
            onAddDepartment={handleAddDepartment}
            onHideNewRow={() => setShowNewRow(false)}
          />
        )}
      </div>
    </div>
  );
};

export default DepartmentsPage;