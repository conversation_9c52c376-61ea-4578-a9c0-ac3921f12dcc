const axios = require('axios');

const BASE_URL = 'http://localhost:5002';

async function testCompleteUserManagement() {
  try {
    console.log('🧪 Testing Complete User Management System...\n');

    // Step 1: Login as Director
    console.log('1. Logging in as Director...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'password'
    });

    if (!loginResponse.data.success) {
      throw new Error('Login failed: ' + loginResponse.data.message);
    }

    const token = loginResponse.data.data.token;
    const currentUser = loginResponse.data.data;
    console.log('✅ Login successful as:', currentUser.name, '(', currentUser.role, ')');

    // Step 2: Test Get Users
    console.log('\n2. Testing Get Users...');
    const usersResponse = await axios.get(`${BASE_URL}/api/users`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (!usersResponse.data.success) {
      throw new Error('Get users failed: ' + usersResponse.data.message);
    }

    const users = usersResponse.data.data;
    console.log(`✅ Found ${users.length} users`);

    // Step 3: Test User Update (Director updating Engineer)
    console.log('\n3. Testing User Update (Director -> Engineer)...');
    const engineerUser = users.find(user => user.role === 'ENGINEER');
    
    if (!engineerUser) {
      throw new Error('No engineer found to test with');
    }

    console.log(`📝 Updating engineer: ${engineerUser.name} (${engineerUser.id})`);

    const updateData = {
      name: engineerUser.name + ' (Updated by Director)',
      skills: 'JavaScript,React,Node.js,Testing,Updated'
    };

    const updateResponse = await axios.put(`${BASE_URL}/api/users/${engineerUser.id}`, updateData, {
      headers: { 
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!updateResponse.data.success) {
      throw new Error('Update failed: ' + updateResponse.data.message);
    }

    console.log('✅ User updated successfully!');
    console.log('📊 Updated name:', updateResponse.data.data.name);
    console.log('📊 Updated skills:', updateResponse.data.data.skills);

    // Step 4: Test Permission Restrictions (Engineer trying to update Director)
    console.log('\n4. Testing Permission Restrictions...');
    
    // Login as Engineer
    const engineerLoginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: engineerUser.email,
      password: 'password'
    });

    if (engineerLoginResponse.data.success) {
      const engineerToken = engineerLoginResponse.data.data.token;
      const directorUser = users.find(user => user.role === 'DIRECTOR');

      try {
        await axios.put(`${BASE_URL}/api/users/${directorUser.id}`, {
          name: 'Should Not Work'
        }, {
          headers: { 
            Authorization: `Bearer ${engineerToken}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log('❌ Permission restriction failed - Engineer was able to update Director');
      } catch (error) {
        if (error.response && error.response.status === 403) {
          console.log('✅ Permission restriction working - Engineer cannot update Director');
        } else {
          console.log('⚠️ Unexpected error:', error.response?.data?.message || error.message);
        }
      }
    }

    // Step 5: Test Self Update
    console.log('\n5. Testing Self Update...');
    const selfUpdateResponse = await axios.put(`${BASE_URL}/api/users/${currentUser.id}`, {
      name: currentUser.name + ' (Self Updated)',
      skills: 'Leadership,Management,Strategy'
    }, {
      headers: { 
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (selfUpdateResponse.data.success) {
      console.log('✅ Self update successful');
      console.log('📊 Updated name:', selfUpdateResponse.data.data.name);
    } else {
      console.log('❌ Self update failed:', selfUpdateResponse.data.message);
    }

    // Step 6: Test Validation
    console.log('\n6. Testing Validation...');
    
    try {
      await axios.put(`${BASE_URL}/api/users/${engineerUser.id}`, {
        name: '123Invalid@Name!',
        email: 'invalid-email'
      }, {
        headers: { 
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('❌ Validation failed - Invalid data was accepted');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ Validation working - Invalid data rejected');
        console.log('📝 Error message:', error.response.data.message);
      } else {
        console.log('⚠️ Unexpected validation error:', error.response?.data?.message || error.message);
      }
    }

    // Step 7: Test Frontend API Integration
    console.log('\n7. Testing Frontend API Integration...');
    
    // Simulate frontend API call
    const frontendUpdateResponse = await axios.put(`${BASE_URL}/api/users/${engineerUser.id}`, {
      name: 'Frontend Test Update',
      email: engineerUser.email,
      role: engineerUser.role,
      department: engineerUser.department,
      skills: 'Frontend,Testing,Integration'
    }, {
      headers: { 
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (frontendUpdateResponse.data.success) {
      console.log('✅ Frontend API integration working');
      console.log('📊 Response format correct:', {
        success: frontendUpdateResponse.data.success,
        hasData: !!frontendUpdateResponse.data.data,
        dataFields: Object.keys(frontendUpdateResponse.data.data)
      });
    } else {
      console.log('❌ Frontend API integration failed');
    }

    console.log('\n🎉 Complete User Management System Test Completed!');
    console.log('\n📋 Summary:');
    console.log('✅ Authentication: Working');
    console.log('✅ Get Users: Working');
    console.log('✅ User Update: Working');
    console.log('✅ Permission Checks: Working');
    console.log('✅ Self Update: Working');
    console.log('✅ Validation: Working');
    console.log('✅ Frontend Integration: Working');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('📊 Response data:', error.response.data);
      console.error('📊 Response status:', error.response.status);
    }
    process.exit(1);
  }
}

// Run the test
testCompleteUserManagement();
