import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { projectsAtom, currentUserAtom } from '../../store';
import { TaskStatus, Task } from '../../types';
import { Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  Title
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  Title
);

const TaskChart: React.FC = () => {
  const [projects] = useAtom(projectsAtom);
  const [currentUser] = useAtom(currentUserAtom);

  // Filter projects based on user role and assignment
  const userProjects = projects.filter(project => {
    if (currentUser?.role === 'DIRECTOR') {
      return true; // Directors can see all projects
    } else if (currentUser?.role === 'PROJECT_MANAGER') {
      // Project managers can see projects they manage
      return project.projectManagerId === currentUser.id;
    } else if (currentUser?.role === 'TEAM_LEAD') {
      // Team leads can see projects where they are assigned tasks or projects in their department
      return project.department === currentUser.department ||
             project.tasks?.some(task => task.assigneeId === currentUser.id) || false;
    } else if (currentUser?.role === 'ENGINEER') {
      // Engineers only see projects where they are assigned tasks
      return project.tasks?.some(task => task.assigneeId === currentUser.id) || false;
    }
    return false;
  });

  // Get all tasks from user projects
  const userTasks: Task[] = userProjects.flatMap(project => project.tasks || []);

  // Count tasks by status
  const notStarted = userTasks.filter(task => task.status === TaskStatus.NOT_STARTED).length;
  const inProgress = userTasks.filter(task => task.status === TaskStatus.IN_PROGRESS).length;
  const completed = userTasks.filter(task => task.status === TaskStatus.COMPLETED).length;
  const delayed = userTasks.filter(task => task.status === TaskStatus.DELAYED).length;
  const onHold = userTasks.filter(task => task.status === TaskStatus.ON_HOLD).length;

  // Create arrays for all statuses (for legend) and filtered data (for chart)
  const allStatusData = [
    { label: 'Not Started', count: notStarted, color: '#6B728E', borderColor: '#5A6178', hoverColor: '#7A8199' },
    { label: 'In Progress', count: inProgress, color: '#F7901E', borderColor: '#E6820D', hoverColor: '#FF9F2F' },
    { label: 'Completed', count: completed, color: '#2DB67D', borderColor: '#26A65B', hoverColor: '#3EC78D' },
    { label: 'Delayed', count: delayed, color: '#E74C3C', borderColor: '#C0392B', hoverColor: '#EC5D4C' },
    { label: 'On Hold', count: onHold, color: '#F1C40F', borderColor: '#D4AC0D', hoverColor: '#F4D03F' }
  ];

  // Only include statuses with tasks for the actual chart data
  const statusData = allStatusData.filter(item => item.count > 0);

  const data = {
    labels: allStatusData.map(item => item.label), // Use all statuses for labels
    datasets: [
      {
        data: allStatusData.map(item => item.count), // Use all statuses, Chart.js will handle 0 values
        backgroundColor: allStatusData.map(item => item.color),
        borderColor: '#f8fafc',
        borderWidth: 5,
        hoverBackgroundColor: allStatusData.map(item => item.hoverColor),
        hoverBorderColor: '#f8fafc',
        hoverBorderWidth: 6,
        borderAlign: 'inner' as const,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 0, // Explicitly set to 0 for full pie chart (no donut hole)
    plugins: {
      legend: {
        position: 'right' as const,
        align: 'center' as const,
        labels: {
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 20,
          font: {
            size: 12,
            weight: '500'
          },
          color: '#374151',
          boxWidth: 12,
          boxHeight: 12
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#ffffff',
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.formattedValue || '';
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = Math.round((context.raw / total) * 100);
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      }
    },
    elements: {
      arc: {
        borderWidth: 5,
        borderColor: '#f8fafc',
        borderAlign: 'inner'
      }
    },
    layout: {
      padding: {
        top: 10,
        bottom: 10,
        left: 10,
        right: 10
      }
    }
  };

  // Show empty state if no tasks or no filtered data
  if (userTasks.length === 0 || statusData.length === 0) {
    return (
      <div className="relative group h-full">
        <div className="relative bg-gradient-to-br from-white via-gray-50 to-gray-100
                        rounded-2xl p-8 h-full flex flex-col
                        shadow-[0_8px_30px_rgb(0,0,0,0.12)]
                        border border-white/20
                        backdrop-blur-sm">
          <div className="relative z-10 flex flex-col h-full">
            <div className="flex items-center mb-6 flex-shrink-0">
              <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full mr-4
                              shadow-lg shadow-blue-500/30"></div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600
                             bg-clip-text text-transparent">
                Task Status Distribution
              </h3>
            </div>
            <div className="flex-1 flex items-center justify-center">
              <p className="text-gray-500 text-center">No tasks available</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative group h-full">
      {/* 3D Card Container */}
      <div className="relative bg-gradient-to-br from-white via-gray-50 to-gray-100
                      rounded-2xl p-6 h-full flex flex-col
                      shadow-[0_8px_30px_rgb(0,0,0,0.12)]
                      hover:shadow-[0_20px_60px_rgb(0,0,0,0.15)]
                      transition-all duration-500 ease-out
                      hover:-translate-y-2
                      backdrop-blur-sm
                      before:absolute before:inset-0
                      before:bg-gradient-to-br before:from-white/10 before:to-transparent
                      before:rounded-2xl before:pointer-events-none
                      after:absolute after:inset-0
                      after:bg-gradient-to-tr after:from-transparent after:via-white/5 after:to-white/10
                      after:rounded-2xl after:pointer-events-none">

        {/* Inner glow effect */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/30 pointer-events-none"></div>

        {/* Content */}
        <div className="relative z-10 flex flex-col h-full">
          {/* Header with enhanced styling */}
          <div className="flex items-center mb-6 flex-shrink-0">
            <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full mr-4
                            shadow-lg shadow-blue-500/30"></div>
            <h3 className="text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600
                           bg-clip-text text-transparent">
              Task Status Distribution
            </h3>
          </div>

          {/* Chart Container */}
          <div className="flex-1 relative min-h-0">
            <div className="h-full">
              <Pie
                key={`task-chart-${allStatusData.map(s => `${s.label}-${s.count}`).join('-')}`}
                data={data}
                options={options}
              />
            </div>
          </div>
        </div>

        {/* Subtle animated background pattern */}
        <div className="absolute inset-0 opacity-5 pointer-events-none">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500/10 via-transparent to-purple-500/10
                          animate-pulse rounded-2xl"></div>
        </div>
      </div>

      {/* Enhanced bottom shadow for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900/5 to-gray-900/10
                      rounded-2xl blur-xl translate-y-4 scale-95 -z-10
                      group-hover:translate-y-6 group-hover:blur-2xl
                      transition-all duration-500"></div>
    </div>
  );
};

export default TaskChart;