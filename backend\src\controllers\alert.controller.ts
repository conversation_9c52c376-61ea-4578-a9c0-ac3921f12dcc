import { Request, Response } from 'express';
import { prisma } from '../index';
import { AlertService } from '../services/alertService';

// @desc    Get unread alerts count for a user
// @route   GET /api/alerts/unread-count
// @access  Private
export const getUnreadAlertsCount = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    // Get user's notification priority preferences
    const userSettings = await prisma.usersettings.findUnique({
      where: { userId },
      select: {
        notifyLowPriority: true,
        notifyMediumPriority: true,
        notifyHighPriority: true,
        notifyCriticalPriority: true,
      },
    });

    // Determine allowed priorities based on user settings
    const allowedPriorities: string[] = [];
    if (userSettings?.notifyLowPriority) allowedPriorities.push('LOW');
    if (userSettings?.notifyMediumPriority) allowedPriorities.push('MEDIUM');
    if (userSettings?.notifyHighPriority) allowedPriorities.push('HIGH');
    if (userSettings?.notifyCriticalPriority) allowedPriorities.push('CRITICAL');

    // Build where clause for unread alerts
    let where: any = { read: false };

    // Role-based filtering
    if (userRole === 'DIRECTOR') {
      // Directors can see all alerts
    } else if (userRole === 'PROJECT_MANAGER') {
      // Project managers see their assigned alerts and department alerts
      where.OR = [
        { assigneeId: userId },
        { department: req.user.department },
        { assigneeId: null, department: req.user.department }, // Department-wide alerts
      ];
    } else {
      // Other roles see only their assigned alerts
      where.assigneeId = userId;
    }

    // Priority filtering based on user preferences
    if (allowedPriorities.length > 0) {
      where.priority = { in: allowedPriorities };
    } else {
      // If no priorities are enabled, return 0
      res.status(200).json({
        success: true,
        data: { count: 0 },
      });
      return;
    }

    // Get count of unread alerts
    const unreadCount = await prisma.alert.count({ where });

    res.status(200).json({
      success: true,
      data: { count: unreadCount },
    });
  } catch (error) {
    console.error('Get unread alerts count error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get all alerts for a user
// @route   GET /api/alerts
// @access  Private
export const getAlerts = async (req: Request, res: Response) => {
  try {
    const { type, priority, read, resolved, department } = req.query;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Get user's notification priority preferences
    const userSettings = await prisma.usersettings.findUnique({
      where: { userId },
      select: {
        notifyLowPriority: true,
        notifyMediumPriority: true,
        notifyHighPriority: true,
        notifyCriticalPriority: true,
      },
    });

    // Build allowed priorities based on user settings
    const allowedPriorities: string[] = [];
    if (userSettings?.notifyLowPriority !== false) allowedPriorities.push('LOW');
    if (userSettings?.notifyMediumPriority !== false) allowedPriorities.push('MEDIUM');
    if (userSettings?.notifyHighPriority !== false) allowedPriorities.push('HIGH');
    if (userSettings?.notifyCriticalPriority !== false) allowedPriorities.push('CRITICAL');

    // Build filter conditions
    const where: any = {};

    // Role-based filtering
    if (userRole === 'DIRECTOR') {
      // Directors can see all alerts
    } else if (userRole === 'PROJECT_MANAGER') {
      // Project managers see their assigned alerts and department alerts
      where.OR = [
        { assigneeId: userId },
        { department: req.user.department },
        { assigneeId: null, department: req.user.department }, // Department-wide alerts
      ];
    } else {
      // Other roles see only their assigned alerts
      where.assigneeId = userId;
    }

    // Priority filtering based on user preferences
    if (allowedPriorities.length > 0) {
      where.priority = { in: allowedPriorities };
    } else {
      // If no priorities are enabled, return empty result
      where.id = 'non-existent-id';
    }

    // Additional filters
    if (type) where.type = type;
    if (priority) {
      // Override user preference if specific priority is requested
      where.priority = priority;
    }
    if (read !== undefined) where.read = read === 'true';
    if (resolved !== undefined) where.resolved = resolved === 'true';
    if (department) where.department = department;

    // Get alerts without sorting first (we'll sort manually)
    const alertsUnsorted = await prisma.alert.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    // Sort alerts by read status and date
    const alerts = alertsUnsorted.sort((a, b) => {
      // First sort by read status (unread first)
      if (a.read !== b.read) {
        return a.read ? 1 : -1;
      }

      // Then by creation date (newest first)
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    res.status(200).json({
      success: true,
      count: alerts.length,
      data: alerts,
    });
  } catch (error) {
    console.error('Get alerts error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single alert
// @route   GET /api/alerts/:id
// @access  Private
export const getAlert = async (req: Request, res: Response) => {
  try {
    const alert = await prisma.alert.findUnique({
      where: { id: req.params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    if (!alert) {
      res.status(404).json({
        success: false,
        message: 'Alert not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: alert,
    });
  } catch (error) {
    console.error('Get alert error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create alert
// @route   POST /api/alerts
// @access  Private
export const createAlert = async (req: Request, res: Response) => {
  try {
    const {
      type,
      priority = 'MEDIUM',
      title,
      message,
      relatedTo,
      relatedType,
      assigneeId,
      department,
      dueDate,
    } = req.body;

    const alert = await AlertService.createAlert({
      type,
      priority,
      title,
      message,
      relatedTo,
      relatedType,
      assigneeId,
      department,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      createdBy: req.user.id,
    });

    res.status(201).json({
      success: true,
      data: alert,
    });
  } catch (error) {
    console.error('Create alert error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update alert
// @route   PUT /api/alerts/:id
// @access  Private
export const updateAlert = async (req: Request, res: Response) => {
  try {
    const { title, message, priority, resolved } = req.body;

    const alert = await prisma.alert.update({
      where: { id: req.params.id },
      data: {
        title,
        message,
        priority,
        resolved,
      },
    });

    res.status(200).json({
      success: true,
      data: alert,
    });
  } catch (error) {
    console.error('Update alert error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Mark alert as read
// @route   PUT /api/alerts/:id/read
// @access  Private
export const markAsRead = async (req: Request, res: Response) => {
  try {
    const alert = await AlertService.markAsRead(req.params.id);

    res.status(200).json({
      success: true,
      data: alert,
    });
  } catch (error) {
    console.error('Mark alert as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete alert
// @route   DELETE /api/alerts/:id
// @access  Private
export const deleteAlert = async (req: Request, res: Response) => {
  try {
    await prisma.alert.delete({
      where: { id: req.params.id },
    });

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete alert error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create system alert (Admin only)
// @route   POST /api/alerts/system
// @access  Private (Director only)
export const createSystemAlert = async (req: Request, res: Response) => {
  try {
    // Check if user is director
    if (req.user.role !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Director role required.',
      });
      return;
    }

    const { title, message, priority = 'MEDIUM', department } = req.body;

    await AlertService.createSystemAlert(title, message, priority, department);

    res.status(201).json({
      success: true,
      message: 'System alert created successfully',
    });
  } catch (error) {
    console.error('Create system alert error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Clean up old alert titles (Admin only)
// @route   POST /api/alerts/cleanup
// @access  Private (Director only)
export const cleanupOldAlerts = async (req: Request, res: Response) => {
  try {
    // Check if user is director
    if (req.user.role !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Access denied. Director role required.',
      });
      return;
    }

    // Get all MOM alerts with old titles and update them with proper information
    const oldMOMAlerts = await prisma.alert.findMany({
      where: {
        title: 'New MOM Created',
        type: 'MOM'
      }
    });

    let updatedCount = 0;

    // Update each alert with proper title
    for (const alert of oldMOMAlerts) {
      if (alert.relatedTo) {
        try {
          // Get the MOM data using the relatedTo field
          const mom = await prisma.mom.findUnique({
            where: { id: alert.relatedTo },
            include: {
              project: {
                include: {
                  customer: true,
                },
              },
            }
          });

          if (mom) {
            const newTitle = `Minutes of Meeting created for ${mom.project?.customer?.name || 'Unknown Customer'}${mom.project ? ` - ${mom.project.name}` : ''}`;
            const newMessage = `Meeting document created for project ${mom.project?.code || 'Unknown Project'}`;

            await prisma.alert.update({
              where: { id: alert.id },
              data: {
                title: newTitle,
                message: newMessage
              }
            });

            updatedCount++;
          }
        } catch (error) {
          console.error(`Error updating alert ${alert.id}:`, error);
          // Continue with other alerts even if one fails
        }
      }
    }

    // Also update any other old alert titles by removing "New " prefix
    const taskUpdates = await prisma.alert.updateMany({
      where: { title: 'New Task Created' },
      data: { title: 'Task Created' }
    });

    const projectUpdates = await prisma.alert.updateMany({
      where: { title: 'New Project Created' },
      data: { title: 'Project Created' }
    });

    const userUpdates = await prisma.alert.updateMany({
      where: { title: 'New User Created' },
      data: { title: 'User Created' }
    });

    const genericUpdatesCount = taskUpdates.count + projectUpdates.count + userUpdates.count;

    const totalUpdated = updatedCount + genericUpdatesCount;

    res.status(200).json({
      success: true,
      message: `Updated ${totalUpdated} alert titles`,
      data: { updatedCount: totalUpdated }
    });
  } catch (error) {
    console.error('Cleanup alerts error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Check deadline alerts (Cron job endpoint)
// @route   POST /api/alerts/check-deadlines
// @access  Private (System only)
export const checkDeadlineAlerts = async (req: Request, res: Response) => {
  try {
    await AlertService.checkDeadlineAlerts();

    res.status(200).json({
      success: true,
      message: 'Deadline alerts checked successfully',
    });
  } catch (error) {
    console.error('Check deadline alerts error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};


