import { Request, Response } from 'express';
import { prisma } from '../index';
import { v4 as uuidv4 } from 'uuid';

// @desc    Get user profile
// @route   GET /api/settings/profile
// @access  Private
export const getUserProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user.id;

    // Get user profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        department: true,
        role: true
      }
    });

    if (!user) {
      res.status(404).json({
        success: false,
        message: 'User not found'
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get user profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update user profile
// @route   PUT /api/settings/profile
// @access  Private
export const updateUserProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user.id;
    const { name, email } = req.body;

    // Update user profile
    const user = await prisma.user.update({
      where: { id: userId },
      data: {
        name,
        email
      },
      select: {
        id: true,
        name: true,
        email: true,
        department: true,
        role: true
      }
    });

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Update user profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get user settings
// @route   GET /api/settings
// @access  Private
export const getUserSettings = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;

    let userSettings = await prisma.usersettings.findUnique({
      where: { userId },
    });

    // If no settings exist, create default settings
    if (!userSettings) {
      userSettings = await prisma.usersettings.create({
        data: {
          id: uuidv4(),
          userId,
          emailNotifications: true,
          pushNotifications: true,
          taskReminders: true,
          projectUpdates: true,
          securityAlerts: true,
          notifyLowPriority: true,
          notifyMediumPriority: true,
          notifyHighPriority: true,
          notifyCriticalPriority: true,
          language: 'en',
          timezone: 'UTC',
          dateFormat: 'MM/DD/YYYY',
          timeFormat: '12h',
          updatedAt: new Date(),
        },
      });
    }

    res.status(200).json({
      success: true,
      data: userSettings,
    });
  } catch (error) {
    console.error('Get user settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update user settings
// @route   PUT /api/settings
// @access  Private
export const updateUserSettings = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const {
      emailNotifications,
      pushNotifications,
      taskReminders,
      projectUpdates,
      securityAlerts,
      notifyLowPriority,
      notifyMediumPriority,
      notifyHighPriority,
      notifyCriticalPriority,
      language,
      timezone,
      dateFormat,
      timeFormat,
    } = req.body;

    // Check if settings exist
    let userSettings = await prisma.usersettings.findUnique({
      where: { userId },
    });

    if (!userSettings) {
      // Create new settings
      userSettings = await prisma.usersettings.create({
        data: {
          id: uuidv4(),
          userId,
          emailNotifications: emailNotifications ?? true,
          pushNotifications: pushNotifications ?? true,
          taskReminders: taskReminders ?? true,
          projectUpdates: projectUpdates ?? true,
          securityAlerts: securityAlerts ?? true,
          notifyLowPriority: notifyLowPriority ?? true,
          notifyMediumPriority: notifyMediumPriority ?? true,
          notifyHighPriority: notifyHighPriority ?? true,
          notifyCriticalPriority: notifyCriticalPriority ?? true,
          language: language || 'en',
          timezone: timezone || 'UTC',
          dateFormat: dateFormat || 'MM/DD/YYYY',
          timeFormat: timeFormat || '12h',
          updatedAt: new Date(),
        },
      });
    } else {
      // Update existing settings
      userSettings = await prisma.usersettings.update({
        where: { userId },
        data: {
          ...(emailNotifications !== undefined && { emailNotifications }),
          ...(pushNotifications !== undefined && { pushNotifications }),
          ...(taskReminders !== undefined && { taskReminders }),
          ...(projectUpdates !== undefined && { projectUpdates }),
          ...(securityAlerts !== undefined && { securityAlerts }),
          ...(notifyLowPriority !== undefined && { notifyLowPriority }),
          ...(notifyMediumPriority !== undefined && { notifyMediumPriority }),
          ...(notifyHighPriority !== undefined && { notifyHighPriority }),
          ...(notifyCriticalPriority !== undefined && { notifyCriticalPriority }),
          ...(language !== undefined && { language }),
          ...(timezone !== undefined && { timezone }),
          ...(dateFormat !== undefined && { dateFormat }),
          ...(timeFormat !== undefined && { timeFormat }),
        },
      });
    }

    res.status(200).json({
      success: true,
      data: userSettings,
    });
  } catch (error) {
    console.error('Update user settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};





// @desc    Reset user settings to defaults
// @route   POST /api/settings/reset
// @access  Private
export const resetUserSettings = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;

    const userSettings = await prisma.usersettings.upsert({
      where: { userId },
      update: {
        emailNotifications: true,
        pushNotifications: true,
        taskReminders: true,
        projectUpdates: true,
        securityAlerts: true,
        notifyLowPriority: true,
        notifyMediumPriority: true,
        notifyHighPriority: true,
        notifyCriticalPriority: true,
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
      },
      create: {
        id: uuidv4(),
        userId,
        emailNotifications: true,
        pushNotifications: true,
        taskReminders: true,
        projectUpdates: true,
        securityAlerts: true,
        notifyLowPriority: true,
        notifyMediumPriority: true,
        notifyHighPriority: true,
        notifyCriticalPriority: true,
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        updatedAt: new Date(),
      },
    });

    res.status(200).json({
      success: true,
      data: userSettings,
    });
  } catch (error) {
    console.error('Reset user settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get notification preferences only
// @route   GET /api/settings/notifications
// @access  Private
export const getNotificationSettings = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;

    let userSettings = await prisma.usersettings.findUnique({
      where: { userId },
      select: {
        emailNotifications: true,
        pushNotifications: true,
        taskReminders: true,
        projectUpdates: true,
        securityAlerts: true,
        notifyLowPriority: true,
        notifyMediumPriority: true,
        notifyHighPriority: true,
        notifyCriticalPriority: true,
      },
    });

    // If no settings exist, return defaults
    if (!userSettings) {
      userSettings = {
        emailNotifications: true,
        pushNotifications: true,
        taskReminders: true,
        projectUpdates: true,
        securityAlerts: true,
        notifyLowPriority: true,
        notifyMediumPriority: true,
        notifyHighPriority: true,
        notifyCriticalPriority: true,
      };
    }

    res.status(200).json({
      success: true,
      data: userSettings,
    });
  } catch (error) {
    console.error('Get notification settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update notification preferences only
// @route   PUT /api/settings/notifications
// @access  Private
export const updateNotificationSettings = async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const {
      emailNotifications,
      pushNotifications,
      taskReminders,
      projectUpdates,
      securityAlerts,
      notifyLowPriority,
      notifyMediumPriority,
      notifyHighPriority,
      notifyCriticalPriority,
    } = req.body;

    // Check if settings exist
    let userSettings = await prisma.usersettings.findUnique({
      where: { userId },
    });

    if (!userSettings) {
      // Create new settings with notification data
      userSettings = await prisma.usersettings.create({
        data: {
          id: uuidv4(),
          userId,
          emailNotifications: emailNotifications ?? true,
          pushNotifications: pushNotifications ?? true,
          taskReminders: taskReminders ?? true,
          projectUpdates: projectUpdates ?? true,
          securityAlerts: securityAlerts ?? true,
          notifyLowPriority: notifyLowPriority ?? true,
          notifyMediumPriority: notifyMediumPriority ?? true,
          notifyHighPriority: notifyHighPriority ?? true,
          notifyCriticalPriority: notifyCriticalPriority ?? true,
          updatedAt: new Date(),
        },
      });
    } else {
      // Update existing settings
      userSettings = await prisma.usersettings.update({
        where: { userId },
        data: {
          ...(emailNotifications !== undefined && { emailNotifications }),
          ...(pushNotifications !== undefined && { pushNotifications }),
          ...(taskReminders !== undefined && { taskReminders }),
          ...(projectUpdates !== undefined && { projectUpdates }),
          ...(securityAlerts !== undefined && { securityAlerts }),
          ...(notifyLowPriority !== undefined && { notifyLowPriority }),
          ...(notifyMediumPriority !== undefined && { notifyMediumPriority }),
          ...(notifyHighPriority !== undefined && { notifyHighPriority }),
          ...(notifyCriticalPriority !== undefined && { notifyCriticalPriority }),
        },
      });
    }

    res.status(200).json({
      success: true,
      data: {
        emailNotifications: userSettings.emailNotifications,
        pushNotifications: userSettings.pushNotifications,
        taskReminders: userSettings.taskReminders,
        projectUpdates: userSettings.projectUpdates,
        securityAlerts: userSettings.securityAlerts,
        notifyLowPriority: userSettings.notifyLowPriority,
        notifyMediumPriority: userSettings.notifyMediumPriority,
        notifyHighPriority: userSettings.notifyHighPriority,
        notifyCriticalPriority: userSettings.notifyCriticalPriority,
      },
    });
  } catch (error) {
    console.error('Update notification settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};