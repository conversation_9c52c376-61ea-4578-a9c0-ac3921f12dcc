import React, { useEffect, useCallback, useState, useRef } from 'react';
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { useAtom } from 'jotai';
import { isAuthenticated<PERSON>tom, currentUser<PERSON>tom, isAuthLoading<PERSON>tom, isDataLoading<PERSON>tom } from './store';
import { authAPI } from './services/api';
import { dataService } from './services/dataServiceSingleton';
import { alertsCountService } from './services/alertsCountService';
import { useStatusUpdater } from './hooks/useStatusUpdater';

import DashboardPage from './pages/DashboardPage';

// Import pages
import LoginPage from './pages/LoginPage';
import MOMPage from './pages/MOMPage';
import MOMCreatePage from './pages/MOMCreatePage';
import MOMEditPage from './pages/MOMEditPage';
import MOMAssignedPage from './pages/MOMAssignedPage';

import DepartmentsPage from './pages/DepartmentsPage';
import TeamsPage from './pages/TeamsPage';
import ProjectCategoriesPage from './pages/ProjectCategoriesPage';
import MilestonesPage from './pages/MilestonesPage';
import ProjectsPage from './pages/ProjectsPage';
import ProjectDetailsPage from './pages/ProjectDetailsPage';

import CreateProjectPage from './pages/CreateProjectPage';
import AddTaskPage from './pages/AddTaskPage';
import TestProjectCodePage from './pages/TestProjectCodePage';
import UsersPage from './pages/UsersPage';
import PasswordChangePage from './pages/PasswordChangePage';
import AlertsPage from './pages/AlertsPage';
import SettingsPage from './pages/SettingsPage';
import SubtasksPage from './pages/SubtasksPage';
import TasksPage from './pages/TasksPage';
import PaymentsPage from './pages/PaymentsPage';
import PaymentListPage, { TransactionHistoryPage } from './pages/PaymentListPage';
import POValueDetailsPage from './pages/POValueDetailsPage';
import PendingPaymentsPage from './pages/PendingPaymentsPage';

// Import components
import Layout from './components/Layout/Layout';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import DirectorOnlyRoute from './components/common/DirectorOnlyRoute';
import CustomersPage from './components/Customers/CustomersPage';
import TaskView from './components/Tasks/TaskView';
import SubtaskView from './components/Subtasks/SubtaskView';
import { ConfirmationProvider } from './contexts/ConfirmationContext';
import { NotificationProvider } from './contexts/NotificationContext';
// import LoadingOverlay from './components/common/LoadingOverlay'; // Removed blur loading overlay

const App: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useAtom(isAuthenticatedAtom);
  const [currentUser, setCurrentUser] = useAtom(currentUserAtom);
  const [isAuthLoading, setIsAuthLoading] = useAtom(isAuthLoadingAtom);
  const [isDataLoading, setIsDataLoading] = useAtom(isDataLoadingAtom);
  const [passwordChangeRequired, setPasswordChangeRequired] = useState(false);
  const navigate = useNavigate();
  const dataLoadedRef = useRef(false);

  // Initialize automatic status updater
  useStatusUpdater();

  // Add debugging - only log on mount, not on every change
  useEffect(() => {
    console.log('App mounted');
    console.log('isAuthenticated:', isAuthenticated);
    console.log('currentUser:', currentUser);
    console.log('isAuthLoading:', isAuthLoading);
    console.log('localStorage token:', localStorage.getItem('token'));
    console.log('localStorage user:', localStorage.getItem('user'));
  }, []); // Empty dependency array to run only once

  // Handle logout
  const handleLogout = useCallback(() => {
    // Stop alerts count service when logging out
    console.log('🛑 Stopping alerts count service on logout...');
    alertsCountService.stopPeriodicRefresh();
    alertsCountService.resetCount();

    setCurrentUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    navigate('/login');
  }, [setCurrentUser, setIsAuthenticated, navigate]);

  // Data loading is now handled directly in the authentication effect

  // Check for authentication token and get current user - run only once on mount
  useEffect(() => {
    let isMounted = true; // Track if component is still mounted

    // Authentication logic
    const token = localStorage.getItem('token');
    const storedUser = localStorage.getItem('user');

    if (token && storedUser) {
      try {
        // We already have the user data in localStorage, use it immediately
        const userData = JSON.parse(storedUser);
        if (isMounted) {
          setCurrentUser(userData);
          setIsAuthenticated(true);
          setIsAuthLoading(false); // Set loading to false immediately

          // Check if password change is required
          if (userData.passwordChanged === 'N') {
            setPasswordChangeRequired(true);
            navigate('/change-password');
          } else {
            setPasswordChangeRequired(false);
          }
        }

        // Verify token in the background without disrupting the user experience
        authAPI.getCurrentUser()
          .then(response => {
            if (!isMounted) return; // Don't update if component unmounted

            // Update user data if it's different
            if (JSON.stringify(response.data) !== JSON.stringify(userData)) {
              setCurrentUser(response.data);
              localStorage.setItem('user', JSON.stringify(response.data));

              // Check if password change is required (in case it changed)
              if (response.data.passwordChanged === 'N' && userData.passwordChanged !== 'N') {
                setPasswordChangeRequired(true);
                navigate('/change-password');
              }
            }
          })
          .catch(error => {
            if (!isMounted) return; // Don't update if component unmounted

            console.error('Token verification error:', error);
            // Only log out if it's a clear authentication error (401)
            // Don't log out on network errors or other temporary issues
            if (error.message && (error.message.includes('401') || error.message.includes('Authentication error'))) {
              console.log('Authentication error detected, logging out user');
              handleLogout();
            } else {
              console.log('Non-authentication error, keeping user logged in:', error.message);
            }
          });
      } catch (error) {
        console.error('Error parsing stored user data:', error);
        if (isMounted) {
          // Handle invalid stored data by clearing it
          localStorage.removeItem('user');
          localStorage.removeItem('token');
          setIsAuthenticated(false);
          setCurrentUser(null);
          setIsAuthLoading(false);
        }
      }
    } else {
      if (isMounted) {
        // No token or user data
        setIsAuthenticated(false);
        setCurrentUser(null);
        setIsAuthLoading(false);
      }
    }

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array ensures this runs only once

  // Separate effect to handle data loading when authentication state changes
  useEffect(() => {
    // Only load data if user is authenticated and doesn't need password change
    if (isAuthenticated && currentUser && !passwordChangeRequired) {
      console.log('🔄 Authentication state changed, loading data...', {
        isAuthenticated,
        currentUser: currentUser?.name,
        passwordChangeRequired
      });

      const startTime = Date.now();
      setIsDataLoading(true);

      // Use the singleton data service for optimized loading
      dataService.loadAllData()
        .then((success) => {
          const endTime = Date.now();
          console.log(`✅ Data loading completed after authentication in ${endTime - startTime}ms, success: ${success}`);

          if (success) {
            // Start periodic alerts count refresh after successful authentication
            console.log('🚀 Starting alerts count service...');
            alertsCountService.startPeriodicRefresh(60000); // Refresh every 60 seconds
          } else {
            console.warn('⚠️ Some data failed to load, but continuing...');
          }
        })
        .catch(error => {
          console.error('❌ Error loading data after authentication:', error);
        })
        .finally(() => {
          setIsDataLoading(false);
        });
    } else {
      console.log('⏸️ Skipping data load:', {
        isAuthenticated,
        hasCurrentUser: !!currentUser,
        passwordChangeRequired
      });
      setIsDataLoading(false);
    }
  }, [isAuthenticated, currentUser, passwordChangeRequired, setIsDataLoading]);

  return (
    <NotificationProvider>
      <ConfirmationProvider>
        {/* <LoadingOverlay /> */}
        <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route
          path="/change-password"
          element={
            <ProtectedRoute>
              <PasswordChangePage />
            </ProtectedRoute>
          }
        />

        {/* Protected routes */}
        <Route element={<ProtectedRoute />}>
          <Route path="/" element={<Layout />}>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<DashboardPage />} />
            <Route path="create-project" element={<CreateProjectPage />} />
            <Route path="add-task" element={<AddTaskPage />} />
            <Route path="add-task/:projectId" element={<AddTaskPage />} />
            <Route path="test-project-code" element={<TestProjectCodePage />} />
            <Route path="mom" element={<MOMPage />} />
            <Route path="mom/create" element={<MOMCreatePage />} />
            <Route path="mom/edit/:id" element={<MOMEditPage />} />
            <Route path="mom/assigned" element={<MOMAssignedPage />} />
            <Route path="departments" element={<DepartmentsPage />} />
            <Route path="teams" element={<TeamsPage />} />
            <Route path="customers" element={<CustomersPage />} />
            <Route path="project-categories" element={<ProjectCategoriesPage />} />
            <Route path="milestones" element={
              <DirectorOnlyRoute>
                <MilestonesPage />
              </DirectorOnlyRoute>
            } />
            <Route path="payments" element={
              <DirectorOnlyRoute>
                <PaymentsPage />
              </DirectorOnlyRoute>
            } />
            <Route path="payment-list" element={
              <DirectorOnlyRoute>
                <PaymentListPage />
              </DirectorOnlyRoute>
            } />
            <Route path="po-details" element={
              <DirectorOnlyRoute>
                <POValueDetailsPage />
              </DirectorOnlyRoute>
            } />
            <Route path="transaction-history" element={
              <DirectorOnlyRoute>
                <TransactionHistoryPage />
              </DirectorOnlyRoute>
            } />
            <Route path="pending-payments" element={
              <DirectorOnlyRoute>
                <PendingPaymentsPage />
              </DirectorOnlyRoute>
            } />
            <Route path="projects" element={<ProjectsPage />} />
            <Route path="projects/:projectId" element={<ProjectDetailsPage />} />
            <Route path="tasks" element={<TasksPage />} />
            <Route path="tasks/:taskId" element={<TaskView />} />
            <Route path="subtasks" element={<SubtasksPage />} />
            <Route path="subtasks/:subtaskId" element={<SubtaskView />} />
            <Route path="users" element={<UsersPage />} />
            <Route path="alerts" element={<AlertsPage />} />
            <Route path="settings" element={<SettingsPage />} />
          </Route>
        </Route>

        <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </ConfirmationProvider>
    </NotificationProvider>
  );
};

export default App;