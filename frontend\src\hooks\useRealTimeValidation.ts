import { useState, useCallback, useEffect } from 'react';
import {
  validateName,
  validateTaskName,
  validateNumber,
  validateCode,
  validateEmail,
  validatePhone,
  validateDates,
  validateMOMDate,
  validateDueDate
} from '../utils/dateValidation';
import { validateProjectCode } from '../utils/validation';

export interface ValidationRule {
  type: 'name' | 'taskName' | 'number' | 'code' | 'projectCode' | 'email' | 'phone' | 'date' | 'momDate' | 'dueDate' | 'required' | 'minLength' | 'maxLength' | 'custom';
  message?: string;
  min?: number;
  max?: number;
  customValidator?: (value: string) => { isValid: boolean; message?: string };
  dateType?: 'start' | 'end' | 'due' | 'mom';
  compareWith?: string; // For date comparisons
}

export interface FieldValidation {
  value: string;
  error: string | null;
  isValid: boolean;
  isDirty: boolean;
  isTouched: boolean;
}

export interface ValidationConfig {
  [fieldName: string]: ValidationRule[];
}

export const useRealTimeValidation = (config: ValidationConfig) => {
  const [fields, setFields] = useState<{ [key: string]: FieldValidation }>(() => {
    const initialFields: { [key: string]: FieldValidation } = {};
    Object.keys(config).forEach(fieldName => {
      initialFields[fieldName] = {
        value: '',
        error: null,
        isValid: true,
        isDirty: false,
        isTouched: false
      };
    });
    return initialFields;
  });

  const validateField = useCallback((fieldName: string, value: string, allValues?: { [key: string]: string }) => {
    const rules = config[fieldName];
    if (!rules) return { isValid: true, error: null };

    for (const rule of rules) {
      let result = { isValid: true, message: '' };

      switch (rule.type) {
        case 'required':
          if (!value || value.trim() === '') {
            result = { isValid: false, message: rule.message || `${fieldName} is required` };
          }
          break;

        case 'name':
          result = validateName(value);
          if (!result.isValid && rule.message) {
            result.message = rule.message;
          }
          break;

        case 'taskName':
          result = validateTaskName(value);
          if (!result.isValid && rule.message) {
            result.message = rule.message;
          }
          break;

        case 'number':
          result = validateNumber(value, fieldName);
          if (!result.isValid && rule.message) {
            result.message = rule.message;
          }
          break;

        case 'code':
          result = validateCode(value, fieldName);
          if (!result.isValid && rule.message) {
            result.message = rule.message;
          }
          break;

        case 'projectCode':
          result = validateProjectCode(value);
          if (!result.isValid && rule.message) {
            result.message = rule.message;
          }
          break;

        case 'email':
          result = validateEmail(value);
          if (!result.isValid && rule.message) {
            result.message = rule.message;
          }
          break;

        case 'phone':
          result = validatePhone(value);
          if (!result.isValid && rule.message) {
            result.message = rule.message;
          }
          break;

        case 'date':
          if (rule.compareWith && allValues) {
            const compareValue = allValues[rule.compareWith];
            if (rule.dateType === 'start') {
              result = validateDates(value, compareValue);
            } else if (rule.dateType === 'end') {
              result = validateDates(compareValue, value);
            }
          } else {
            result = validateDates(value);
          }
          if (!result.isValid && rule.message) {
            result.message = rule.message;
          }
          break;

        case 'momDate':
          result = validateMOMDate(value);
          if (!result.isValid && rule.message) {
            result.message = rule.message;
          }
          break;

        case 'dueDate':
          result = validateDueDate(value);
          if (!result.isValid && rule.message) {
            result.message = rule.message;
          }
          break;

        case 'minLength':
          if (value.length < (rule.min || 0)) {
            result = {
              isValid: false,
              message: rule.message || `${fieldName} must be at least ${rule.min} characters`
            };
          }
          break;

        case 'maxLength':
          if (value.length > (rule.max || Infinity)) {
            result = {
              isValid: false,
              message: rule.message || `${fieldName} must be no more than ${rule.max} characters`
            };
          }
          break;

        case 'custom':
          if (rule.customValidator) {
            result = rule.customValidator(value);
            if (!result.isValid && rule.message) {
              result.message = rule.message;
            }
          }
          break;
      }

      if (!result.isValid) {
        return { isValid: false, error: result.message };
      }
    }

    return { isValid: true, error: null };
  }, [config]);

  const updateField = useCallback((fieldName: string, value: string) => {
    setFields(prev => {
      const allValues = { ...prev };
      Object.keys(allValues).forEach(key => {
        if (key === fieldName) {
          allValues[key] = { ...allValues[key], value };
        }
      });

      const validation = validateField(fieldName, value,
        Object.fromEntries(Object.entries(allValues).map(([k, v]) => [k, k === fieldName ? value : v.value]))
      );

      return {
        ...prev,
        [fieldName]: {
          value,
          error: validation.error,
          isValid: validation.isValid,
          isDirty: true,
          isTouched: prev[fieldName]?.isTouched || false
        }
      };
    });
  }, [validateField]);

  const touchField = useCallback((fieldName: string) => {
    setFields(prev => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        isTouched: true
      }
    }));
  }, []);

  const resetField = useCallback((fieldName: string) => {
    setFields(prev => ({
      ...prev,
      [fieldName]: {
        value: '',
        error: null,
        isValid: true,
        isDirty: false,
        isTouched: false
      }
    }));
  }, []);

  const resetAllFields = useCallback(() => {
    setFields(prev => {
      const resetFields: { [key: string]: FieldValidation } = {};
      Object.keys(prev).forEach(fieldName => {
        resetFields[fieldName] = {
          value: '',
          error: null,
          isValid: true,
          isDirty: false,
          isTouched: false
        };
      });
      return resetFields;
    });
  }, []);

  const setFieldValue = useCallback((fieldName: string, value: string) => {
    updateField(fieldName, value);
  }, [updateField]);

  const setFieldValues = useCallback((values: { [key: string]: string }) => {
    Object.entries(values).forEach(([fieldName, value]) => {
      updateField(fieldName, value);
    });
  }, [updateField]);

  const validateAllFields = useCallback(() => {
    const allValues = Object.fromEntries(
      Object.entries(fields).map(([key, field]) => [key, field.value])
    );

    let isFormValid = true;
    const updatedFields = { ...fields };

    Object.keys(config).forEach(fieldName => {
      const validation = validateField(fieldName, fields[fieldName]?.value || '', allValues);
      updatedFields[fieldName] = {
        ...updatedFields[fieldName],
        error: validation.error,
        isValid: validation.isValid,
        isTouched: true
      };
      if (!validation.isValid) {
        isFormValid = false;
      }
    });

    setFields(updatedFields);
    return isFormValid;
  }, [fields, config, validateField]);

  const getFieldProps = useCallback((fieldName: string) => {
    const field = fields[fieldName];
    const shouldShowError = field?.isDirty && (field?.isTouched || field?.value?.length > 0);
    return {
      value: field?.value || '',
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        updateField(fieldName, e.target.value);
      },
      onBlur: () => touchField(fieldName),
      error: shouldShowError ? field.error : null,
      isValid: field?.isValid ?? true,
      isDirty: field?.isDirty ?? false,
      isTouched: field?.isTouched ?? false
    };
  }, [fields, updateField, touchField]);

  const isFormValid = Object.values(fields).every(field => field.isValid);
  const hasErrors = Object.values(fields).some(field => field.error && field.isTouched);
  const isDirty = Object.values(fields).some(field => field.isDirty);

  return {
    fields,
    updateField,
    touchField,
    resetField,
    resetAllFields,
    setFieldValue,
    setFieldValues,
    validateAllFields,
    getFieldProps,
    isFormValid,
    hasErrors,
    isDirty
  };
};
