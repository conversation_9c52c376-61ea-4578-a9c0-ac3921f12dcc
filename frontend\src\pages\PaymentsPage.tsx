import React, { useState, useEffect, useMemo } from 'react';
import { useAtom } from 'jotai';
import { projectsAtom, customersAtom, currentUserAtom } from '../store';
import { ProjectPayment, PaymentSummary, UserRole } from '../types';
import { paymentsAPI } from '../services/api';
import PaymentModal from '../components/Payments/PaymentModal';
import PaymentStatusDistribution from '../components/Payments/PaymentStatusDistribution';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  PieChart,
  Filter,
  Download,
  Search,
  Calendar,
  Building2,
  CreditCard,
  AlertCircle,
  CheckCircle,
  Clock,
  Plus,
  Edit,
  MoreHorizontal
} from 'lucide-react';
import { PieChart as RechartsPieChart, Cell, ResponsiveContainer, Tooltip, Legend, Pie } from 'recharts';

const PaymentsPage: React.FC = () => {
  const [projects] = useAtom(projectsAtom);
  const [customers] = useAtom(customersAtom);
  const [currentUser] = useAtom(currentUserAtom);

  // State for filters
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });

  // State for API data
  const [paymentData, setPaymentData] = useState<ProjectPayment[]>([]);
  const [paymentSummary, setPaymentSummary] = useState<PaymentSummary>({
    totalProjects: 0,
    totalPayment: 0,
    totalPaid: 0,
    totalPending: 0,
    paidPercentage: 0,
    pendingPercentage: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Payment modal state
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedProject, setSelectedProject] = useState<ProjectPayment | null>(null);

  // Check if user is Director
  const isDirector = currentUser?.role === UserRole.DIRECTOR;

  // Load payment data
  useEffect(() => {
    const loadPaymentData = async () => {
      if (!isDirector) return;

      try {
        setLoading(true);
        setError(null);

        const response = await paymentsAPI.getPaymentData();
        if (response.success) {
          setPaymentData(response.data.payments);
          setPaymentSummary(response.data.summary);
        } else {
          setError('Failed to load payment data');
        }
      } catch (err) {
        console.error('Error loading payment data:', err);
        setError('Failed to load payment data');
      } finally {
        setLoading(false);
      }
    };

    loadPaymentData();
  }, [isDirector]);

  // Redirect if not Director
  useEffect(() => {
    if (currentUser && !isDirector) {
      // Could redirect or show access denied
      console.log('Access denied: Only Directors can view payments');
    }
  }, [currentUser, isDirector]);

  // Filter payment data
  const filteredPayments = useMemo(() => {
    return paymentData.filter(payment => {
      const matchesSearch =
        payment.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.projectCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.customerName.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCustomer = !selectedCustomer || payment.customerName === selectedCustomer;
      const matchesStatus = !selectedStatus || payment.paymentStatus === selectedStatus;

      // Date range filter
      let matchesDateRange = true;
      if (dateRange.start || dateRange.end) {
        const projectDate = new Date(payment.poDate);
        if (dateRange.start) {
          matchesDateRange = matchesDateRange && projectDate >= new Date(dateRange.start);
        }
        if (dateRange.end) {
          matchesDateRange = matchesDateRange && projectDate <= new Date(dateRange.end);
        }
      }

      return matchesSearch && matchesCustomer && matchesStatus && matchesDateRange;
    });
  }, [paymentData, searchTerm, selectedCustomer, selectedStatus, dateRange]);

  // Pie chart data with percentages
  const pieChartData = useMemo(() => {
    const total = paymentSummary.totalPayment;
    if (total === 0) {
      return [
        { name: 'No Data', value: 0, percentage: 100, color: '#9CA3AF' }
      ];
    }

    const paidPercentage = (paymentSummary.totalPaid / total) * 100;
    const pendingPercentage = (paymentSummary.totalPending / total) * 100;

    return [
      {
        name: 'Paid',
        value: paymentSummary.totalPaid,
        percentage: paidPercentage,
        color: '#10B981'
      },
      {
        name: 'Pending',
        value: paymentSummary.totalPending,
        percentage: pendingPercentage,
        color: '#F59E0B'
      }
    ].filter(item => item.percentage > 0);
  }, [paymentSummary]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Get status icon and color
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'FULLY_PAID':
        return { icon: CheckCircle, color: 'text-green-600 bg-green-100', text: 'Fully Paid' };
      case 'PARTIALLY_PAID':
        return { icon: Clock, color: 'text-yellow-600 bg-yellow-100', text: 'Partially Paid' };
      case 'PENDING':
        return { icon: AlertCircle, color: 'text-red-600 bg-red-100', text: 'Pending' };
      default:
        return { icon: AlertCircle, color: 'text-gray-600 bg-gray-100', text: 'Unknown' };
    }
  };

  // Export to Excel functionality
  const handleExport = () => {
    // In real app, implement Excel export
    console.log('Exporting payment data to Excel...');
  };

  // Clear filters
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCustomer('');
    setSelectedStatus('');
    setDateRange({ start: '', end: '' });
  };

  // Handle add payment
  const handleAddPayment = (project: ProjectPayment) => {
    setSelectedProject(project);
    setShowPaymentModal(true);
  };

  // Handle save payment
  const handleSavePayment = async (paymentData: any) => {
    try {
      if (!selectedProject) {
        alert('No project selected');
        return;
      }

      // Call the API to create the payment
      const response = await fetch('/api/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          projectId: selectedProject.id,
          ...paymentData
        })
      });

      const result = await response.json();

      if (result.success) {
        alert('Payment saved successfully!');

        // Reload payment data to show updated amounts
        const paymentResponse = await paymentsAPI.getPaymentData();
        if (paymentResponse.success) {
          setPaymentData(paymentResponse.data.payments);
          setPaymentSummary(paymentResponse.data.summary);
        }
      } else {
        alert(`Failed to save payment: ${result.message}`);
      }
    } catch (error) {
      console.error('Error saving payment:', error);
      alert('Failed to save payment');
    }
  };

  if (!isDirector) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">Only Directors can access the Payments section.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-500 border-t-transparent mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Loading Payment Data</h2>
          <p className="text-gray-600">Please wait while we fetch the payment information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Data</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-blue-50 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Payments Dashboard</h1>
            <p className="text-gray-600">Track project payments and financial metrics</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleExport}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <Download className="w-4 h-4 mr-2" />
              Export Excel
            </button>
          </div>
        </div>
      </div>

      {/* 3D Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Total Projects Card */}
        <div
          className="bg-white rounded-xl shadow-lg p-6 border border-gray-200"
          style={{
            transform: 'perspective(1000px) rotateX(2deg)',
            boxShadow: '0 20px 40px -12px rgba(0, 0, 0, 0.15)'
          }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Projects</p>
              <p className="text-2xl font-bold text-gray-900">{paymentSummary.totalProjects}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Building2 className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        {/* Total Payment Card */}
        <div
          className="bg-white rounded-xl shadow-lg p-6 border border-gray-200"
          style={{
            transform: 'perspective(1000px) rotateX(2deg)',
            boxShadow: '0 20px 40px -12px rgba(0, 0, 0, 0.15)'
          }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Payment</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(paymentSummary.totalPayment)}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <DollarSign className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>

        {/* Total Paid Card */}
        <div
          className="bg-white rounded-xl shadow-lg p-6 border border-gray-200"
          style={{
            transform: 'perspective(1000px) rotateX(2deg)',
            boxShadow: '0 20px 40px -12px rgba(0, 0, 0, 0.15)'
          }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Paid</p>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(paymentSummary.totalPaid)}</p>
              <p className="text-xs text-gray-500">{paymentSummary.paidPercentage.toFixed(1)}% of total</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        {/* Total Pending Card */}
        <div
          className="bg-white rounded-xl shadow-lg p-6 border border-gray-200"
          style={{
            transform: 'perspective(1000px) rotateX(2deg)',
            boxShadow: '0 20px 40px -12px rgba(0, 0, 0, 0.15)'
          }}
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Pending</p>
              <p className="text-2xl font-bold text-orange-600">{formatCurrency(paymentSummary.totalPending)}</p>
              <p className="text-xs text-gray-500">{paymentSummary.pendingPercentage.toFixed(1)}% of total</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <TrendingDown className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Excel-style Payment Table */}
      <div
        className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden"
        style={{
          transform: 'perspective(1000px) rotateX(0.5deg)',
          boxShadow: '0 20px 40px -12px rgba(0, 0, 0, 0.15)'
        }}
      >
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Payment Details</h3>
            <span className="text-sm text-gray-500">
              Showing {filteredPayments.length} of {paymentData.length} projects
            </span>
          </div>

          {/* Filters Section */}
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <Filter className="w-4 h-4 text-gray-600 mr-2" />
                <h4 className="text-sm font-medium text-gray-900">Filters</h4>
              </div>
              <button
                onClick={clearFilters}
                className="text-xs text-blue-600 hover:text-blue-800 font-medium"
              >
                Clear All
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
              {/* Search */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Search</label>
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Project, code, customer..."
                    className="w-full pl-7 pr-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Customer Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Customer</label>
                <select
                  value={selectedCustomer}
                  onChange={(e) => setSelectedCustomer(e.target.value)}
                  className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Customers</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.name}>
                      {customer.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Status Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Payment Status</label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Status</option>
                  <option value="FULLY_PAID">Fully Paid</option>
                  <option value="PARTIALLY_PAID">Partially Paid</option>
                  <option value="PENDING">Pending</option>
                </select>
              </div>

              {/* Date Range */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Date Range</label>
                <div className="flex space-x-1">
                  <input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                    className="flex-1 px-1 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                  />
                  <input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                    className="flex-1 px-1 py-1.5 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px]">
                  Project
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Customer
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  PO Value
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Paid Amount
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Pending Amount
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  PO Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Category
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPayments.length === 0 ? (
                <tr>
                  <td colSpan={9} className="px-4 py-8 text-center text-gray-500">
                    No payment data found matching your filters.
                  </td>
                </tr>
              ) : (
                filteredPayments.map((payment) => {
                  const statusDisplay = getStatusDisplay(payment.paymentStatus);
                  const StatusIcon = statusDisplay.icon;

                  return (
                    <tr key={payment.id} className="hover:bg-gray-50">
                      {/* Project */}
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div>
                          <div className="font-medium">{payment.projectName}</div>
                          <div className="text-xs text-gray-500">{payment.projectCode}</div>
                        </div>
                      </td>

                      {/* Customer */}
                      <td className="px-4 py-4 text-sm text-gray-900">
                        {payment.customerName}
                      </td>

                      {/* PO Value */}
                      <td className="px-4 py-4 text-sm text-gray-900 font-medium">
                        {formatCurrency(payment.poValue)}
                      </td>

                      {/* Paid Amount */}
                      <td className="px-4 py-4 text-sm text-green-600 font-medium">
                        {formatCurrency(payment.paidAmount)}
                      </td>

                      {/* Pending Amount */}
                      <td className="px-4 py-4 text-sm text-orange-600 font-medium">
                        {formatCurrency(payment.pendingAmount)}
                      </td>

                      {/* Status */}
                      <td className="px-4 py-4 text-sm">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusDisplay.color}`}>
                          <StatusIcon className="w-3 h-3 mr-1" />
                          {statusDisplay.text}
                        </span>
                      </td>

                      {/* PO Date */}
                      <td className="px-4 py-4 text-sm text-gray-900">
                        {new Date(payment.poDate).toLocaleDateString()}
                      </td>

                      {/* Category */}
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                          {payment.projectCategory.replace('_', ' ')}
                        </span>
                      </td>

                      {/* Actions */}
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <button
                          onClick={() => handleAddPayment(payment)}
                          className="inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors"
                          title="Add Payment"
                        >
                          <Plus className="w-3 h-3 mr-1" />
                          Add Payment
                        </button>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>

        {/* Summary Footer */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>
              Total: {formatCurrency(paymentSummary.totalPayment)} |
              Paid: {formatCurrency(paymentSummary.totalPaid)} |
              Pending: {formatCurrency(paymentSummary.totalPending)}
            </span>
            <span>Last updated: {new Date().toLocaleDateString()}</span>
          </div>
        </div>
      </div>

      {/* Payment Status Distribution */}
      <div className="mt-8">
        <PaymentStatusDistribution />
      </div>

      {/* Payment Modal */}
      {selectedProject && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => {
            setShowPaymentModal(false);
            setSelectedProject(null);
          }}
          onSave={handleSavePayment}
          projectId={selectedProject.id}
          projectName={selectedProject.projectName}
          poValue={selectedProject.poValue}
          currentPaid={selectedProject.paidAmount}
        />
      )}
    </div>
  );
};

export default PaymentsPage;