import express, { Request, Response } from 'express';
import {
  getPaymentData,
  getPaymentSummary,
  getPaymentRecords,
  getPaymentStats,
  exportPaymentData,
  createPayment,
  updatePayment,
  deletePayment,
  getProjectPayments
} from '../controllers/payments.controller';
import { protect } from '../middleware/auth.middleware';

const router = express.Router();

// Apply authentication middleware to all payment routes
router.use(protect);

// Routes
router.route('/')
  .get(getPaymentData)
  .post(createPayment);

router.route('/summary')
  .get(getPaymentSummary);

router.route('/records')
  .get(getPaymentRecords);

router.route('/stats')
  .get(getPaymentStats);

router.route('/export')
  .get(exportPaymentData);

router.route('/project/:projectId')
  .get(getProjectPayments);

router.route('/:paymentId')
  .put(updatePayment)
  .delete(deletePayment);

export default router;
