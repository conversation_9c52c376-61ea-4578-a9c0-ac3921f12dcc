import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import path from 'path';
import { PrismaClient } from '@prisma/client';

// Import routes
import authRoutes from './routes/auth.routes';
import userRoutes from './routes/user.routes';
import projectRoutes from './routes/project.routes';
import taskRoutes from './routes/task.routes';
import subtaskRoutes from './routes/subtask.routes';
import taskCommentRoutes from './routes/taskComment.routes';
import subtaskCommentRoutes from './routes/subtaskComment.routes';
import sectionRoutes from './routes/section.routes';

import customerRoutes from './routes/customer.routes';
import engineerRoutes from './routes/engineer.routes';
import momRoutes from './routes/mom.routes';
import departmentRoutes from './routes/department.routes';
import alertRoutes from './routes/alert.routes';
import userSettingsRoutes from './routes/userSettings.routes';
import projectCategoryRoutes from './routes/projectCategory.routes';
import milestoneTemplateRoutes from './routes/milestoneTemplate.routes';
import paymentRoutes from './routes/payments.routes';

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();
const PORT = parseInt(process.env.PORT || '5002', 10);

// Initialize Prisma client
export const prisma = new PrismaClient();

// CORS configuration for dynamic IP support
const corsOptions = {
  origin: function (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // Allow any localhost origin with any port
    if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
      return callback(null, true);
    }

    // Allow any local network IP (192.168.x.x, 10.x.x.x, 172.16-31.x.x)
    const localNetworkRegex = /^https?:\/\/(192\.168\.\d+\.\d+|10\.\d+\.\d+\.\d+|172\.(1[6-9]|2\d|3[01])\.\d+\.\d+)(:\d+)?$/;
    if (localNetworkRegex.test(origin)) {
      return callback(null, true);
    }

    // For production, you might want to add specific allowed origins here
    // For now, allow all origins for development
    callback(null, true);
  },
  credentials: true, // Allow credentials (cookies, authorization headers)
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  optionsSuccessStatus: 200 // Some legacy browsers choke on 204
};

// Middleware
app.use(cors(corsOptions));
app.use(express.json());

// Serve static files from templates directory
app.use('/templates', express.static(path.join(__dirname, '../templates')));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/subtasks', subtaskRoutes);
app.use('/api/sections', sectionRoutes);

app.use('/api/customers', customerRoutes);
app.use('/api/engineers', engineerRoutes);
app.use('/api/moms', momRoutes);
app.use('/api/departments', departmentRoutes);
app.use('/api/alerts', alertRoutes);
app.use('/api/settings', userSettingsRoutes);
app.use('/api/project-categories', projectCategoryRoutes);
app.use('/api/milestone-templates', milestoneTemplateRoutes);
app.use('/api/payments', paymentRoutes);

// Root route
app.get('/', (req, res) => {
  res.send('Project Management API is running');
});

// Start server on all interfaces (0.0.0.0) to allow connections from any IP
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server is running on port ${PORT} and accepting connections from all interfaces`);
  console.log(`Local access: http://localhost:${PORT}`);
  console.log(`Network access: http://<your-ip>:${PORT}`);
});

server.on('error', (err) => {
  console.error('Server error:', err);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Rejection:', err);
  console.error('Stack trace:', err);
  // Don't exit for debugging
  // process.exit(1);
});
