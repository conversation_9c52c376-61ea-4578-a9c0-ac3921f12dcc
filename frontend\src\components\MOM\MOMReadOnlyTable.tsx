import React, { useState, useEffect, useRef } from 'react';
import { useAtom } from 'jotai';
import { useNavigate } from 'react-router-dom';
import { customers<PERSON>tom, momsAtom, projectsAtom, currentUser<PERSON>tom } from '../../store';
import { MOM } from '../../types';
import { formatDate } from '../../utils/dateFormatter';
import { dataService } from '../../services/dataServiceSingleton';
import { Eye, Edit, Trash2, X, MoreVertical } from 'lucide-react';
import { momsAPI } from '../../services/api';
import { useNotification } from '../../contexts/NotificationContext';
import { createPortal } from 'react-dom';

interface MOMReadOnlyTableProps {
  onMOMCreated?: () => void;
}

// Utility function to format discussion points as bullet points
const formatDiscussionPoints = (discussion: string) => {
  if (!discussion || typeof discussion !== 'string') return [];

  // Split by newlines and filter out empty lines
  const lines = discussion.split('\n').filter(line => line.trim());

  // If only one line and no bullet points, return as single item
  if (lines.length === 1) {
    const cleanLine = lines[0].replace(/^[•\-\*]\s*/, '').trim();
    return cleanLine ? [cleanLine] : [];
  }

  // Multiple lines - process each line
  return lines.map(line => {
    // Remove bullet point if it exists at the beginning
    const cleanLine = line.replace(/^[•\-\*]\s*/, '').trim();
    return cleanLine;
  }).filter(line => line.length > 0);
};

const MOMReadOnlyTable: React.FC<MOMReadOnlyTableProps> = ({ onMOMCreated }) => {
  console.log('🔄 MOMReadOnlyTable component loaded with NEW DISCUSSION POINT FORMAT - v2.0');
  const navigate = useNavigate();
  const [moms] = useAtom(momsAtom);
  const [customers] = useAtom(customersAtom);
  const [projects] = useAtom(projectsAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedMOM, setSelectedMOM] = useState<MOM | null>(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>('');
  const [dropdownPosition, setDropdownPosition] = useState<{top: number, left: number} | null>(null);
  const buttonRefs = useRef<{[key: string]: HTMLButtonElement | null}>({});
  const { showSuccess, showError } = useNotification();

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('🔄 Loading MOM data...');
        await dataService.loadMOMs();
        await dataService.loadCustomers();
        await dataService.loadProjects();
        console.log('✅ MOM data loaded successfully');
      } catch (error) {
        console.error('❌ Error loading MOM data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Action handlers
  const handleView = (mom: MOM) => {
    setSelectedMOM(mom);
    setShowViewModal(true);
  };

  const handleEdit = (mom: MOM) => {
    navigate(`/mom/edit/${mom.id}`);
  };

  const handleDelete = async (mom: MOM) => {
    // Check if user is Director
    if (currentUser?.role !== 'DIRECTOR') {
      showError('Access Denied', 'Only Directors can delete MOMs.');
      return;
    }

    if (!window.confirm(`Are you sure you want to delete the MOM for ${formatDate(mom.date)}? This action cannot be undone.`)) {
      return;
    }

    try {
      await momsAPI.deleteMOM(mom.id);
      await dataService.loadMOMs();
      showSuccess('MOM Deleted', 'MOM deleted successfully!');
    } catch (error: any) {
      console.error('Error deleting MOM:', error);
      showError('Delete Failed', error.response?.data?.message || 'Failed to delete MOM. Please try again.');
    }
  };

  const closeModals = () => {
    setShowViewModal(false);
    setSelectedMOM(null);
    setOpenDropdownId(null);
    setDropdownPosition(null);
  };

  // Filter MOMs by selected customer
  const filteredMOMs = selectedCustomerId
    ? moms.filter(mom => mom.customerId === selectedCustomerId)
    : moms;

  const handleMOMSaved = async (updatedMOM: any) => {
    // Refresh the MOMs data
    await dataService.loadMOMs();
    showSuccess('MOM Updated', 'MOM updated successfully!');
    closeModals();
  };

  const toggleDropdown = (momId: string) => {
    if (openDropdownId === momId) {
      setOpenDropdownId(null);
      setDropdownPosition(null);
    } else {
      const button = buttonRefs.current[momId];
      if (button) {
        const rect = button.getBoundingClientRect();
        setDropdownPosition({
          top: rect.bottom + window.scrollY + 4,
          left: rect.right + window.scrollX - 192 // 192px is dropdown width (w-48)
        });
      }
      setOpenDropdownId(momId);
    }
  };

  // Close dropdown when clicking outside or scrolling
  useEffect(() => {
    const handleClickOutside = () => {
      setOpenDropdownId(null);
      setDropdownPosition(null);
    };

    const handleScroll = () => {
      if (openDropdownId) {
        setOpenDropdownId(null);
        setDropdownPosition(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    window.addEventListener('scroll', handleScroll, true);
    return () => {
      document.removeEventListener('click', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
    };
  }, [openDropdownId]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!moms || moms.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center py-8 text-gray-500">
          <p>No minutes of meeting found. Click "Create MOM" to create one.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Header Section */}
      <div className="mb-6">
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6 rounded-lg shadow-lg">
          <h1 className="text-2xl font-bold mb-2">MOM Discussion Points Overview</h1>
          <p className="text-blue-100">Track the status of all discussion points across meetings</p>
          <div className="mt-4 text-right">
            <span className="text-blue-100 text-sm">Total MOMs: </span>
            <span className="text-white text-xl font-bold">{moms.length}</span>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow border-l-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Queries</p>
              <p className="text-3xl font-bold text-blue-600">{moms.reduce((acc, mom) => acc + (mom.mompoint?.length || 0) + (mom.actionItems?.length || 0), 0)}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <span className="text-blue-600 text-xl">📋</span>
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">100%</div>
          <div className="w-full bg-blue-200 rounded-full h-2 mt-1">
            <div className="bg-blue-600 h-2 rounded-full" style={{width: '100%'}}></div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border-l-4 border-green-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-3xl font-bold text-green-600">
                {moms.reduce((acc, mom) => acc + (mom.mompoint?.filter(p => p.status === 'COMPLETED').length || 0) + (mom.actionItems?.filter(a => a.status === 'COMPLETED').length || 0), 0)}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <span className="text-green-600 text-xl">✅</span>
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">0%</div>
          <div className="w-full bg-green-200 rounded-full h-2 mt-1">
            <div className="bg-green-600 h-2 rounded-full" style={{width: '0%'}}></div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border-l-4 border-yellow-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-3xl font-bold text-yellow-600">
                {moms.reduce((acc, mom) => acc + (mom.mompoint?.filter(p => p.status === 'PENDING' || p.status === 'IN_PROGRESS').length || 0) + (mom.actionItems?.filter(a => a.status === 'PENDING' || a.status === 'IN_PROGRESS').length || 0), 0)}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <span className="text-yellow-600 text-xl">⏳</span>
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">100%</div>
          <div className="w-full bg-yellow-200 rounded-full h-2 mt-1">
            <div className="bg-yellow-600 h-2 rounded-full" style={{width: '100%'}}></div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border-l-4 border-red-500">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Delayed</p>
              <p className="text-3xl font-bold text-red-600">
                {moms.reduce((acc, mom) => acc + (mom.mompoint?.filter(p => p.status === 'DELAYED').length || 0) + (mom.actionItems?.filter(a => a.status === 'DELAYED').length || 0), 0)}
              </p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <span className="text-red-600 text-xl">⚠️</span>
            </div>
          </div>
          <div className="mt-2 text-xs text-gray-500">0%</div>
          <div className="w-full bg-red-200 rounded-full h-2 mt-1">
            <div className="bg-red-600 h-2 rounded-full" style={{width: '0%'}}></div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Minutes of Meeting</h3>
              <p className="text-sm text-gray-600 mt-1">All meeting records and details</p>
            </div>

            {/* Customer Filter */}
            <div className="flex items-center space-x-4">
              <div className="min-w-0 flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Filter by Customer
                </label>
                <select
                  value={selectedCustomerId}
                  onChange={(e) => setSelectedCustomerId(e.target.value)}
                  className="w-64 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Customers</option>
                  {customers?.map((customer) => (
                    <option key={customer.id} value={customer.id}>
                      {customer.name} ({customer.code})
                    </option>
                  ))}
                </select>
              </div>
              {selectedCustomerId && (
                <button
                  onClick={() => setSelectedCustomerId('')}
                  className="mt-6 px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Clear Filter
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="overflow-x-auto" style={{overflowX: 'auto', overflowY: 'visible'}}>
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px]">
                  Project
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Project Code
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  PO Number
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Created By
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Updated By
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Station
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px]">
                  Responsibility
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Planned Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Completion Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px]">
                  Results/Comments
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredMOMs.map((mom) => {
                const customer = customers?.find(c => c.id === mom.customerId);
                const project = projects?.find(p => p.id === mom.projectId);

                // Show only one row per MOM/project, regardless of discussion points
                return (
                  <tr key={mom.id} className="hover:bg-gray-50">
                    {/* Date */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="font-medium">{formatDate(mom.date)}</div>
                    </td>

                    {/* Project */}
                    <td className="px-4 py-4 text-sm text-gray-900">
                      <div className="font-medium">{project?.name || 'N/A'}</div>
                      <div className="text-xs text-gray-500">{customer?.name || 'Unknown Customer'}</div>
                    </td>

                    {/* Project Code */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>{project?.code || 'N/A'}</div>
                    </td>

                    {/* PO Number */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>{project?.poNumber || 'N/A'}</div>
                    </td>

                    {/* Created By */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="font-medium">{mom.user_mom_createdByTouser?.name || 'Unknown'}</div>
                      <div className="text-gray-500 text-xs">{formatDate(mom.createdAt)}</div>
                    </td>

                    {/* Updated By */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="font-medium">{mom.user_mom_updatedByTouser?.name || 'N/A'}</div>
                      <div className="text-gray-500 text-xs">{mom.updatedAt ? formatDate(mom.updatedAt) : 'N/A'}</div>
                    </td>

                    {/* Station - Show first discussion point's station or N/A */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>{mom.mompoint?.[0]?.station || 'N/A'}</div>
                    </td>

                    {/* Responsibility - Show first discussion point's responsibility or action item assignee */}
                    <td className="px-4 py-4 text-sm text-gray-900">
                      <div className="max-w-xs">
                        {mom.mompoint?.[0]?.responsibility || mom.actionItems?.[0]?.assigneeId || 'N/A'}
                      </div>
                    </td>

                    {/* Planned Date - Show first discussion point's planned date or action item due date */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>
                        {mom.mompoint?.[0]?.plannedDate ? formatDate(mom.mompoint[0].plannedDate) :
                         mom.actionItems?.[0]?.dueDate ? formatDate(mom.actionItems[0].dueDate) : 'N/A'}
                      </div>
                    </td>

                    {/* Completion Date - Show first discussion point's completion date or N/A */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>{mom.mompoint?.[0]?.completionDate ? formatDate(mom.mompoint[0].completionDate) : 'N/A'}</div>
                    </td>

                    {/* Status - Show first discussion point's status or action item status */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      {mom.mompoint && mom.mompoint.length > 0 ? (
                        mom.mompoint.length === 1 ? (
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            mom.mompoint[0].status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                            mom.mompoint[0].status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                            mom.mompoint[0].status === 'DELAYED' ? 'bg-red-100 text-red-800' :
                            mom.mompoint[0].status === 'ON_HOLD' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {mom.mompoint[0].status?.replace('_', ' ') || 'Pending'}
                          </span>
                        ) : (
                          <div className="text-xs">
                            <div className="text-gray-600">{mom.mompoint.length} points</div>
                            <div className="flex space-x-1 mt-1">
                              {mom.mompoint.filter(p => p.status === 'COMPLETED').length > 0 && (
                                <span className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-green-100 text-green-800">
                                  {mom.mompoint.filter(p => p.status === 'COMPLETED').length}✓
                                </span>
                              )}
                              {mom.mompoint.filter(p => p.status === 'PENDING' || p.status === 'IN_PROGRESS').length > 0 && (
                                <span className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-blue-100 text-blue-800">
                                  {mom.mompoint.filter(p => p.status === 'PENDING' || p.status === 'IN_PROGRESS').length}⏳
                                </span>
                              )}
                              {mom.mompoint.filter(p => p.status === 'DELAYED').length > 0 && (
                                <span className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-red-100 text-red-800">
                                  {mom.mompoint.filter(p => p.status === 'DELAYED').length}⚠
                                </span>
                              )}
                            </div>
                          </div>
                        )
                      ) : mom.actionItems && mom.actionItems.length > 0 ? (
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          mom.actionItems[0].status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                          mom.actionItems[0].status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                          mom.actionItems[0].status === 'DELAYED' ? 'bg-red-100 text-red-800' :
                          mom.actionItems[0].status === 'ON_HOLD' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {mom.actionItems[0].status?.replace('_', ' ') || 'Pending'}
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          No Points
                        </span>
                      )}
                    </td>

                    {/* Results/Comments - Show first discussion point's remarks only */}
                    <td className="px-4 py-4 text-sm text-gray-900">
                      <div className="max-w-xs">
                        {mom.mompoint?.[0]?.remarks || 'N/A'}
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="relative">
                        <button
                          ref={(el) => buttonRefs.current[mom.id] = el}
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleDropdown(mom.id);
                          }}
                          className="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
                          title="More actions"
                        >
                          <MoreVertical className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* Summary Footer */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>
              {selectedCustomerId
                ? `Showing ${filteredMOMs.length} MOMs for ${customers?.find(c => c.id === selectedCustomerId)?.name || 'Selected Customer'}`
                : `Total MOMs: ${filteredMOMs.length}`
              }
            </span>
            <span>Last updated: {filteredMOMs.length > 0 ? formatDate(filteredMOMs[0].updatedAt) : 'N/A'}</span>
          </div>
        </div>
      </div>

      {/* View MOM Modal */}
      {showViewModal && selectedMOM && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <h3 className="text-2xl font-bold text-gray-900 flex items-center">
                <Eye size={24} className="mr-3 text-blue-600" />
                View Minutes of Meeting
              </h3>
              <button
                onClick={closeModals}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              <div className="space-y-6">
                {/* Basic Info */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                    <p className="text-sm text-gray-900">{formatDate(selectedMOM.date)}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Customer</label>
                    <p className="text-sm text-gray-900">
                      {customers?.find(c => c.id === selectedMOM.customerId)?.name || 'Unknown Customer'}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Project</label>
                    <p className="text-sm text-gray-900">
                      {projects?.find(p => p.id === selectedMOM.projectId)?.name || 'No Project'}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Project Code</label>
                    <p className="text-sm text-gray-900">
                      {projects?.find(p => p.id === selectedMOM.projectId)?.code || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">PO Number</label>
                    <p className="text-sm text-gray-900">
                      {projects?.find(p => p.id === selectedMOM.projectId)?.poNumber || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Created By</label>
                    <p className="text-sm text-gray-900">{selectedMOM.user_mom_createdByTouser?.name || 'Unknown'}</p>
                  </div>
                </div>

                {/* Agenda */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Agenda</label>
                  <p className="text-sm text-gray-900">{selectedMOM.agenda || 'No agenda specified'}</p>
                </div>

                {/* Attendees */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Attendees</label>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-blue-600 mb-2">Mekhos Team</h4>
                      <div className="space-y-1">
                        {selectedMOM.attendees?.filter(a => a.company === 'Mekhos Technology').map((attendee, idx) => (
                          <div key={idx} className="text-sm text-gray-900 bg-blue-50 px-2 py-1 rounded">
                            {attendee.name}
                          </div>
                        )) || <p className="text-sm text-gray-500">No Mekhos attendees</p>}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-green-600 mb-2">Customer Team</h4>
                      <div className="space-y-1">
                        {selectedMOM.attendees?.filter(a => a.company !== 'Mekhos Technology').map((attendee, idx) => (
                          <div key={idx} className="text-sm text-gray-900 bg-green-50 px-2 py-1 rounded">
                            {attendee.name}
                          </div>
                        )) || <p className="text-sm text-gray-500">No customer attendees</p>}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Discussion Points */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Discussion Points</label>
                  <div className="space-y-3">
                    {selectedMOM.mompoint && selectedMOM.mompoint.length > 0 ? (
                      selectedMOM.mompoint.map((point, idx) => {
                        return (
                          <div key={idx} className="bg-gray-50 p-4 rounded border">
                            {/* Discussion Content */}
                            <div className="mb-3">
                              <div className="flex items-center justify-between mb-2">
                                <div className="text-sm font-medium text-gray-900">Discussion #{idx + 1}:</div>
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                  point.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                                  point.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                                  point.status === 'DELAYED' ? 'bg-red-100 text-red-800' :
                                  point.status === 'ON_HOLD' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {point.status?.replace('_', ' ') || 'Pending'}
                                </span>
                              </div>

                              {point.discussion ? (
                                (() => {
                                  const discussionPoints = formatDiscussionPoints(point.discussion);
                                  return discussionPoints.length > 1 ? (
                                    <ul className="text-sm text-gray-700 space-y-1">
                                      {discussionPoints.map((discussionPoint, pointIdx) => (
                                        <li key={pointIdx} className="flex items-start">
                                          <span className="text-blue-600 mr-2 mt-0.5 font-bold">•</span>
                                          <span className="leading-relaxed">{discussionPoint}</span>
                                        </li>
                                      ))}
                                    </ul>
                                  ) : (
                                    <div className="text-sm text-gray-700 leading-relaxed">
                                      {discussionPoints[0] || point.discussion}
                                    </div>
                                  );
                                })()
                              ) : (
                                <div className="text-sm text-gray-400 italic">No discussion content</div>
                              )}
                            </div>

                          {/* Details Grid */}
                          <div className="grid grid-cols-2 gap-3 mb-3">
                            {point.date && (
                              <div>
                                <span className="text-xs font-medium text-blue-700">Date:</span>
                                <p className="text-xs text-gray-600">{formatDate(point.date)}</p>
                              </div>
                            )}
                            {point.discussionType && (
                              <div>
                                <span className="text-xs font-medium text-blue-700">Discussion Type:</span>
                                <p className="text-xs text-gray-600">{point.discussionType}</p>
                              </div>
                            )}
                            {point.station && (
                              <div>
                                <span className="text-xs font-medium text-blue-700">Station:</span>
                                <p className="text-xs text-gray-600">{point.station}</p>
                              </div>
                            )}
                            {point.responsibility && (
                              <div>
                                <span className="text-xs font-medium text-blue-700">Assigned to:</span>
                                <p className="text-xs text-gray-600">{point.responsibility}</p>
                              </div>
                            )}
                          </div>

                          {/* Action Plan */}
                          {point.actionPlan && (
                            <div className="mb-3 p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                              <div className="text-sm font-semibold text-blue-800 mb-1">Action Plan:</div>
                              <div className="text-sm text-blue-700 leading-relaxed">{point.actionPlan}</div>
                            </div>
                          )}

                          {/* Dates */}
                          <div className="grid grid-cols-2 gap-3 mb-3">
                            {point.plannedDate && (
                              <div>
                                <span className="text-xs font-medium text-orange-700">Planned Date:</span>
                                <p className="text-sm text-gray-600">{formatDate(point.plannedDate)}</p>
                              </div>
                            )}
                            {point.completionDate && (
                              <div>
                                <span className="text-xs font-medium text-green-700">Completion Date:</span>
                                <p className="text-sm text-gray-600">{formatDate(point.completionDate)}</p>
                              </div>
                            )}
                          </div>

                          {/* Result Comments */}
                          {point.remarks && (
                            <div className="mb-3 p-3 bg-green-50 rounded border-l-4 border-green-400">
                              <div className="text-sm font-semibold text-green-800 mb-1">Result/Comments:</div>
                              <div className="text-sm text-green-700 leading-relaxed">{point.remarks}</div>
                            </div>
                          )}

                          {/* Footer with Created/Updated info */}
                          <div className="pt-3 border-t border-gray-200">
                            <div className="space-y-1">
                              <div className="text-xs text-gray-500">
                                <span className="font-medium">Created:</span> {formatDate(point.createdAt)}
                                {selectedMOM.user_mom_createdByTouser && (
                                  <span className="ml-1">by {selectedMOM.user_mom_createdByTouser.name}</span>
                                )}
                              </div>
                              {point.updatedAt !== point.createdAt && (
                                <div className="text-xs text-gray-500">
                                  <span className="font-medium">Updated:</span> {formatDate(point.updatedAt)}
                                  {selectedMOM.user_mom_updatedByTouser && (
                                    <span className="ml-1">by {selectedMOM.user_mom_updatedByTouser.name}</span>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        );
                      })
                    ) : (
                      <p className="text-sm text-gray-500">No discussion points</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
              <div className="flex justify-end">
                <button
                  onClick={closeModals}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Portal-based Dropdown Menu */}
      {openDropdownId && dropdownPosition && createPortal(
        <div
          className="fixed w-48 bg-white rounded-md shadow-lg border border-gray-200 z-[9999]"
          style={{
            top: dropdownPosition.top,
            left: dropdownPosition.left
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="py-1">
            <button
              onClick={(e) => {
                e.stopPropagation();
                const mom = filteredMOMs.find(m => m.id === openDropdownId);
                if (mom) handleView(mom);
                setOpenDropdownId(null);
                setDropdownPosition(null);
              }}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <Eye className="h-4 w-4 mr-3 text-blue-500" />
              View Details
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                const mom = filteredMOMs.find(m => m.id === openDropdownId);
                if (mom) handleEdit(mom);
                setOpenDropdownId(null);
                setDropdownPosition(null);
              }}
              className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <Edit className="h-4 w-4 mr-3 text-green-500" />
              Edit MOM
            </button>
            {currentUser?.role === 'DIRECTOR' && (
              <>
                <hr className="my-1 border-gray-200" />
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    const mom = filteredMOMs.find(m => m.id === openDropdownId);
                    if (mom) handleDelete(mom);
                    setOpenDropdownId(null);
                    setDropdownPosition(null);
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                >
                  <Trash2 className="h-4 w-4 mr-3" />
                  Delete MOM
                </button>
              </>
            )}
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default MOMReadOnlyTable;
