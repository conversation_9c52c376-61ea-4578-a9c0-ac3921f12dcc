import express from 'express';
import {
  getProjectCategories,
  getProjectCategory,
  createProjectCategory,
  updateProjectCategory,
  deleteProjectCategory,
} from '../controllers/projectCategory.controller';
import { protect, authorize } from '../middleware/auth.middleware';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(
    authorize('DIRECTOR', 'PROJECT_MANAGER'), // DIRECTOR and PROJECT_MANAGER can view project categories
    getProjectCategories
  )
  .post(
    authorize('DIRECTOR', 'PROJECT_MANAGER'), // DIRECTOR and PROJECT_MANAGER can create project categories
    createProjectCategory
  );

router.route('/:id')
  .get(
    authorize('DIRECTOR', 'PROJECT_MANAGER'), // DIRECTOR and PROJECT_MANAGER can view project categories
    getProjectCategory
  )
  .put(
    authorize('DIRECTOR', 'PROJECT_MANAGER'), // DIRECTOR and PROJECT_MANAGER can edit project categories
    updateProjectCategory
  )
  .delete(
    authorize('DIRECTOR', 'PROJECT_MANAGER'), // DIRECTOR and PROJECT_MANAGER can delete project categories
    deleteProjectCategory
  );

export default router;
