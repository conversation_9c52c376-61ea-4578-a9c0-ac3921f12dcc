import { Request, Response } from 'express';
import { prisma } from '../index';
import { v4 as uuidv4 } from 'uuid';
import { toCamelCase } from '../utils/auth.utils';

// @desc    Import Excel data for project
// @route   POST /api/projects/:id/excel-import
// @access  Private
export const importProjectExcelData = async (req: Request, res: Response): Promise<void> => {
  try {
    const projectId = req.params.id;
    const { sections } = req.body;

    console.log('Excel import request for project:', projectId);
    console.log('Sections to import:', sections?.length);

    // Validate project exists
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        section: {
          include: {
            task: {
              include: {
                subtask: true
              }
            }
          }
        }
      }
    });

    if (!project) {
      res.status(404).json({
        success: false,
        message: 'Project not found'
      });
      return;
    }

    // Check if user has access to this project
    if (
      req.user.role === 'PROJECT_MANAGER' &&
      project.projectManagerId !== req.user.id
    ) {
      res.status(403).json({
        success: false,
        message: 'Not authorized to import data for this project'
      });
      return;
    }

    const importResults = {
      sectionsProcessed: 0,
      tasksCreated: 0,
      tasksUpdated: 0,
      subtasksCreated: 0,
      subtasksUpdated: 0,
      errors: [] as string[]
    };

    // Process each section
    for (const sectionData of sections) {
      try {
        // Find or create section
        let section = await prisma.section.findFirst({
          where: {
            projectId: projectId,
            name: sectionData.name
          }
        });

        if (!section) {
          // Create new section
          section = await prisma.section.create({
            data: {
              id: uuidv4(),
              projectId: projectId,
              name: sectionData.name,
              sequence: sectionData.sequence || 1,
              description: sectionData.description || ''
            }
          });
        }

        importResults.sectionsProcessed++;

        // Process tasks in this section
        for (const taskData of sectionData.tasks || []) {
          try {
            // Check if task exists by name and section (most reliable) or displayId
            let existingTask = null;

            // First, try to find by name within the same section
            existingTask = await prisma.task.findFirst({
              where: {
                projectId: projectId,
                sectionId: section.id,
                name: toCamelCase(taskData.name)
              },
              include: {
                subtask: true
              }
            });

            // If not found by name, try by displayId
            if (!existingTask && taskData.displayId) {
              existingTask = await prisma.task.findFirst({
                where: {
                  projectId: projectId,
                  displayId: taskData.displayId
                },
                include: {
                  subtask: true
                }
              });
            }

            let task;
            if (existingTask) {
              // Update existing task - only update fields that are provided
              const updateData: any = {
                sectionId: section.id // Always update section in case task moved
              };

              // Only update fields that are provided in the Excel data
              if (taskData.name) updateData.name = toCamelCase(taskData.name);
              if (taskData.description !== undefined) updateData.description = taskData.description;
              if (taskData.assigneeId) updateData.assigneeId = taskData.assigneeId;
              if (taskData.assigneeType) updateData.assigneeType = taskData.assigneeType;
              if (taskData.department) updateData.department = taskData.department;
              if (taskData.startDate) updateData.startDate = new Date(taskData.startDate);
              if (taskData.endDate) updateData.endDate = new Date(taskData.endDate);
              if (taskData.status) updateData.status = taskData.status;
              if (taskData.priority) updateData.priority = taskData.priority;

              task = await prisma.task.update({
                where: { id: existingTask.id },
                data: updateData,
                include: {
                  subtask: true
                }
              });
              importResults.tasksUpdated++;
            } else {
              // Create new task
              const taskSequence = await getNextTaskSequence(projectId);
              task = await prisma.task.create({
                data: {
                  id: uuidv4(),
                  projectId: projectId,
                  sectionId: section.id,
                  sequence: taskSequence,
                  displayId: taskData.displayId || `T${taskSequence}`,
                  name: toCamelCase(taskData.name),
                  description: taskData.description || '',
                  assigneeId: taskData.assigneeId || req.user.id,
                  assigneeType: taskData.assigneeType || 'ENGINEER',
                  department: taskData.department || 'GENERAL',
                  startDate: taskData.startDate ? new Date(taskData.startDate) : new Date(),
                  endDate: taskData.endDate ? new Date(taskData.endDate) : new Date(),
                  status: taskData.status || 'NOT_STARTED',
                  priority: taskData.priority || 'Medium',
                  createdBy: req.user.id
                },
                include: {
                  subtask: true
                }
              });
              importResults.tasksCreated++;
            }

            // Process subtasks
            for (const subtaskData of taskData.subtasks || []) {
              try {
                // Check if subtask exists - use multiple criteria to avoid moving existing subtasks
                let existingSubtask = null;

                // First, try to find by name and current parent task (most reliable)
                existingSubtask = await prisma.subtask.findFirst({
                  where: {
                    taskId: task.id,
                    name: toCamelCase(subtaskData.name)
                  }
                });

                // If not found by name, try by displayId but only within the same parent task
                if (!existingSubtask && subtaskData.displayId) {
                  existingSubtask = await prisma.subtask.findFirst({
                    where: {
                      taskId: task.id,
                      displayId: subtaskData.displayId
                    }
                  });
                }

                // SAFETY CHECK: Prevent moving existing subtasks from other parent tasks
                if (!existingSubtask && subtaskData.displayId) {
                  const subtaskInOtherTask = await prisma.subtask.findFirst({
                    where: {
                      displayId: subtaskData.displayId,
                      taskId: { not: task.id }
                    },
                    include: {
                      task: { select: { name: true, displayId: true } }
                    }
                  });

                  if (subtaskInOtherTask) {
                    console.log(`⚠️ WARNING: Subtask "${subtaskData.name}" (${subtaskData.displayId}) already exists under different parent task "${subtaskInOtherTask.task.name}". Skipping to prevent data corruption.`);
                    importResults.errors.push(`Subtask "${subtaskData.name}" already exists under different parent task "${subtaskInOtherTask.task.name}" - skipped to prevent moving`);
                    continue; // Skip this subtask to prevent moving it
                  }
                }

                if (existingSubtask) {
                  // Update existing subtask - only update fields that are provided
                  const subtaskUpdateData: any = {};

                  // Only update fields that are provided in the Excel data
                  if (subtaskData.name) subtaskUpdateData.name = toCamelCase(subtaskData.name);
                  if (subtaskData.description !== undefined) subtaskUpdateData.description = subtaskData.description;
                  if (subtaskData.assigneeId) subtaskUpdateData.assigneeId = subtaskData.assigneeId;
                  if (subtaskData.assigneeType) subtaskUpdateData.assigneeType = subtaskData.assigneeType;
                  if (subtaskData.startDate) subtaskUpdateData.startDate = new Date(subtaskData.startDate);
                  if (subtaskData.endDate) subtaskUpdateData.endDate = new Date(subtaskData.endDate);
                  if (subtaskData.status) subtaskUpdateData.status = subtaskData.status;
                  if (subtaskData.priority) subtaskUpdateData.priority = subtaskData.priority;

                  // Only update if there are fields to update
                  if (Object.keys(subtaskUpdateData).length > 0) {
                    await prisma.subtask.update({
                      where: { id: existingSubtask.id },
                      data: subtaskUpdateData
                    });
                  }
                  importResults.subtasksUpdated++;
                } else {
                  // Before creating new subtask, check for duplicates by name within the same parent task
                  const duplicateSubtask = await prisma.subtask.findFirst({
                    where: {
                      taskId: task.id,
                      name: toCamelCase(subtaskData.name)
                    }
                  });

                  if (duplicateSubtask) {
                    console.log(`⚠️ WARNING: Subtask "${subtaskData.name}" already exists under parent task "${task.name}". Skipping duplicate creation.`);
                    importResults.errors.push(`Subtask "${subtaskData.name}" already exists under parent task "${task.name}" - skipped duplicate`);
                    continue; // Skip creating duplicate
                  }

                  // Create new subtask
                  const subtaskSequence = await getNextSubtaskSequence(task.id);
                  await prisma.subtask.create({
                    data: {
                      id: uuidv4(),
                      taskId: task.id,
                      sequence: subtaskSequence,
                      displayId: subtaskData.displayId || `S${task.sequence}${subtaskSequence}`,
                      name: toCamelCase(subtaskData.name),
                      description: subtaskData.description || '',
                      assigneeId: subtaskData.assigneeId || req.user.id,
                      assigneeType: subtaskData.assigneeType || 'ENGINEER',
                      startDate: subtaskData.startDate ? new Date(subtaskData.startDate) : new Date(),
                      endDate: subtaskData.endDate ? new Date(subtaskData.endDate) : new Date(),
                      status: subtaskData.status || 'NOT_STARTED',
                      priority: subtaskData.priority || 'Medium',
                      createdBy: req.user.id
                    }
                  });
                  importResults.subtasksCreated++;
                }
              } catch (subtaskError) {
                console.error('Error processing subtask:', subtaskError);
                importResults.errors.push(`Failed to process subtask: ${subtaskData.name}`);
              }
            }
          } catch (taskError) {
            console.error('Error processing task:', taskError);
            importResults.errors.push(`Failed to process task: ${taskData.name}`);
          }
        }
      } catch (sectionError) {
        console.error('Error processing section:', sectionError);
        importResults.errors.push(`Failed to process section: ${sectionData.name}`);
      }
    }

    res.status(200).json({
      success: true,
      message: 'Excel data imported successfully',
      data: importResults
    });

  } catch (error: any) {
    console.error('Excel import error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to import Excel data: ' + error.message
    });
  }
};

// Helper function to get next task sequence
const getNextTaskSequence = async (projectId: string): Promise<number> => {
  const lastTask = await prisma.task.findFirst({
    where: { projectId },
    orderBy: { sequence: 'desc' }
  });
  return (lastTask?.sequence || 0) + 1;
};

// Helper function to get next subtask sequence
const getNextSubtaskSequence = async (taskId: string): Promise<number> => {
  const lastSubtask = await prisma.subtask.findFirst({
    where: { taskId },
    orderBy: { sequence: 'desc' }
  });
  return (lastSubtask?.sequence || 0) + 1;
};

// @desc    Get project data for Excel export
// @route   GET /api/projects/:id/excel-export
// @access  Private
export const exportProjectExcelData = async (req: Request, res: Response): Promise<void> => {
  try {
    const projectId = req.params.id;

    // Get project with all related data
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        customer: true,
        user_project_projectManagerIdTouser: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        section: {
          include: {
            task: {
              include: {
                subtask: {
                  include: {
                    user: {
                      select: {
                        id: true,
                        name: true,
                        email: true,
                        role: true
                      }
                    }
                  }
                },
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    role: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!project) {
      res.status(404).json({
        success: false,
        message: 'Project not found'
      });
      return;
    }

    // Check if user has access to this project
    if (
      req.user.role === 'PROJECT_MANAGER' &&
      project.projectManagerId !== req.user.id
    ) {
      res.status(403).json({
        success: false,
        message: 'Not authorized to export this project'
      });
      return;
    }

    // Format data for Excel export
    const formattedProject = {
      ...project,
      sections: project.section.map(section => ({
        ...section,
        tasks: section.task.map(task => ({
          ...task,
          displayId: `T${task.sequence}`,
          subtasks: task.subtask.map(subtask => ({
            ...subtask,
            displayId: `S${task.sequence}${subtask.sequence}`
          }))
        }))
      }))
    };

    res.status(200).json({
      success: true,
      data: formattedProject
    });

  } catch (error: any) {
    console.error('Excel export error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export project data: ' + error.message
    });
  }
};
