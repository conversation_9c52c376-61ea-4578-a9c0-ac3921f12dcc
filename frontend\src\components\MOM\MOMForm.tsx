import React, { useState } from 'react';
import { useAtom } from 'jotai';
import { useForm, useFieldArray, SubmitHandler } from 'react-hook-form';
import { customersAtom, engineersAtom, momsAtom, currentUserAtom } from '../../store';
import { MOM, MOMAttendee, MOMActionItem, Customer } from '../../types';
import { v4 as uuidv4 } from 'uuid';
import { Plus, Trash2, CheckCircle2, XCircle, PlusCircle } from 'lucide-react';
import CustomerDialog from '../Customers/CustomerDialog';
import { useNavigate } from 'react-router-dom';

type FormValues = {
  date: string;
  customer: string;
  engineerId: string;
  agenda: string;
  mekhosAttendees: {
    name: string;
    designation: string;
    email: string;
  }[];
  customerAttendees: {
    name: string;
    designation: string;
    email: string;
  }[];
  points: string[];
  actionItems: {
    description: string;
    assigneeId: string;
    dueDate: string;
  }[];
};

const MOMForm: React.FC = () => {
  const navigate = useNavigate();
  const [customers, setCustomers] = useAtom(customersAtom);
  const [engineers] = useAtom(engineersAtom);
  const [, setMoms] = useAtom(momsAtom);
  const [currentUser] = useAtom(currentUserAtom);

  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState(false);
  const [newCustomerName, setNewCustomerName] = useState('');

  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors }
  } = useForm<FormValues>({
    defaultValues: {
      date: new Date().toISOString().split('T')[0],
      mekhosAttendees: [{ name: '', designation: '', email: '' }],
      customerAttendees: [{ name: '', designation: '', email: '' }],
      points: [''],
      actionItems: [{ description: '', assigneeId: '', dueDate: '' }]
    }
  });

  // Set up field arrays for the dynamic parts of the form
  const {
    fields: mekhosAttendeesFields,
    append: appendMekhosAttendee,
    remove: removeMekhosAttendee
  } = useFieldArray({
    control,
    name: "mekhosAttendees"
  });

  const {
    fields: customerAttendeesFields,
    append: appendCustomerAttendee,
    remove: removeCustomerAttendee
  } = useFieldArray({
    control,
    name: "customerAttendees"
  });

  const {
    fields: pointsFields,
    append: appendPoint,
    remove: removePoint
  } = useFieldArray({
    control,
    name: "points"
  });

  const {
    fields: actionItemsFields,
    append: appendActionItem,
    remove: removeActionItem
  } = useFieldArray({
    control,
    name: "actionItems"
  });

  // Handler for adding a new customer
  const handleAddCustomer = (newCustomer: Customer) => {
    setCustomers((prevCustomers) => [...prevCustomers, newCustomer]);

    // Auto-select the newly created customer in the form
    const customerField = document.getElementById('customer') as HTMLSelectElement;
    if (customerField) {
      customerField.value = newCustomer.id;
      // Trigger change event to update react-hook-form
      const event = new Event('change', { bubbles: true });
      customerField.dispatchEvent(event);
    }
  };

  // Handler for opening the customer dialog
  const handleOpenCustomerDialog = () => {
    setIsCustomerDialogOpen(true);
  };

  const onSubmit: SubmitHandler<FormValues> = (data) => {
    setIsSubmitting(true);

    // Simulate API call delay
    setTimeout(() => {
      try {
        // Combine all attendees with their respective companies
        const allAttendees: MOMAttendee[] = [
          ...data.mekhosAttendees.map(attendee => ({
            id: uuidv4(),
            name: attendee.name,
            company: "Mekhos Technology",
            designation: attendee.designation,
            email: attendee.email
          })),
          ...data.customerAttendees.map(attendee => ({
            id: uuidv4(),
            name: attendee.name,
            company: customers.find(c => c.id === data.customer)?.name || "Unknown",
            designation: attendee.designation,
            email: attendee.email
          }))
        ];

        // Map action items
        const actionItems: MOMActionItem[] = data.actionItems.map(item => ({
          id: uuidv4(),
          description: item.description,
          assigneeId: item.assigneeId,
          dueDate: item.dueDate,
          status: "PENDING"
        }));

        // Create new MOM entry
        const newMOM: MOM = {
          id: uuidv4(),
          date: data.date,
          customerId: data.customer,
          engineerId: data.engineerId,
          agenda: data.agenda,
          attendees: allAttendees,
          points: data.points.filter(point => point.trim() !== ''),
          actionItems: actionItems,
          createdBy: currentUser?.id || '',
          createdAt: new Date().toISOString()
        };

        // Add the new MOM to the state
        setMoms((prevMoms) => [...prevMoms, newMOM]);

        // Show success message
        setSuccess('Minutes of Meeting created successfully!');

        // Reset the form after submission
        reset({
          date: new Date().toISOString().split('T')[0],
          mekhosAttendees: [{ name: '', designation: '', email: '' }],
          customerAttendees: [{ name: '', designation: '', email: '' }],
          points: [''],
          actionItems: [{ description: '', assigneeId: '', dueDate: '' }]
        });

        // Clear success message after 5 seconds
        setTimeout(() => {
          setSuccess(null);
        }, 5002);
      } catch (error) {
        console.error('Error creating MOM:', error);
      } finally {
        setIsSubmitting(false);
      }
    }, 1000);
  };

  return (
    <div className="card p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Create Minutes of Meeting</h2>
      </div>

      {success && (
        <div className="bg-success/10 text-success-dark p-4 rounded-md flex items-center justify-between mb-6 fade-in">
          <div className="flex items-center">
            <CheckCircle2 size={18} className="mr-2" />
            <span>{success}</span>
          </div>
          <button
            onClick={() => setSuccess(null)}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle size={18} />
          </button>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label htmlFor="date" className="form-label">Meeting Date</label>
            <input
              id="date"
              type="date"
              className={`form-input ${errors.date ? 'border-error' : ''}`}
              {...register("date", { required: "Meeting date is required" })}
            />
            {errors.date && (
              <p className="mt-1 text-xs text-error">{errors.date.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="customer" className="form-label">Customer</label>
            <div className="flex space-x-2">
              <div className="flex-1">
                <select
                  id="customer"
                  className={`form-select ${errors.customer ? 'border-error' : ''}`}
                  {...register("customer", { required: "Customer is required" })}
                >
                  <option value="">Select Customer</option>
                  {customers.map((customer) => (
                    <option key={customer.id} value={customer.id}>{customer.name}</option>
                  ))}
                </select>
              </div>
              <button
                type="button"
                className="btn btn-outline flex items-center py-2"
                onClick={() => navigate('/customers?addCustomer=true')}
              >
                <PlusCircle size={16} className="mr-1" />
                New
              </button>
            </div>
            {errors.customer && (
              <p className="mt-1 text-xs text-error">{errors.customer.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="engineerId" className="form-label">Lead Engineer</label>
            <select
              id="engineerId"
              className={`form-select ${errors.engineerId ? 'border-error' : ''}`}
              {...register("engineerId", { required: "Engineer is required" })}
            >
              <option value="">Select Engineer</option>
              {engineers.map((engineer) => (
                <option key={engineer.id} value={engineer.id}>
                  {engineer.name} ({engineer.department})
                </option>
              ))}
            </select>
            {errors.engineerId && (
              <p className="mt-1 text-xs text-error">{errors.engineerId.message}</p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="agenda" className="form-label">Meeting Agenda</label>
          <textarea
            id="agenda"
            className={`form-input ${errors.agenda ? 'border-error' : ''}`}
            rows={3}
            placeholder="Enter the meeting agenda..."
            {...register("agenda", { required: "Agenda is required" })}
          />
          {errors.agenda && (
            <p className="mt-1 text-xs text-error">{errors.agenda.message}</p>
          )}
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-800">Attendees from Mekhos Technology</h3>
            <button
              type="button"
              className="btn btn-outline flex items-center py-1 px-2 text-sm"
              onClick={() => appendMekhosAttendee({ name: '', designation: '', email: '' })}
            >
              <Plus size={16} className="mr-1" />
              Add Attendee
            </button>
          </div>

          {mekhosAttendeesFields.map((field, index) => (
            <div key={field.id} className="grid grid-cols-1 md:grid-cols-3 gap-4 border-b border-gray-100 pb-4">
              <div>
                <label className="form-label">Name</label>
                <input
                  type="text"
                  className={`form-input ${errors.mekhosAttendees?.[index]?.name ? 'border-error' : ''}`}
                  placeholder="Enter name"
                  {...register(`mekhosAttendees.${index}.name`, { required: "Name is required" })}
                />
              </div>

              <div>
                <label className="form-label">Designation</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="Enter designation"
                  {...register(`mekhosAttendees.${index}.designation`)}
                />
              </div>

              <div className="flex items-end">
                <div className="flex-1">
                  <label className="form-label">Email</label>
                  <input
                    type="email"
                    className="form-input"
                    placeholder="Enter email"
                    {...register(`mekhosAttendees.${index}.email`)}
                  />
                </div>

                {mekhosAttendeesFields.length > 1 && (
                  <button
                    type="button"
                    className="ml-2 text-gray-400 hover:text-error transition-colors h-10"
                    onClick={() => removeMekhosAttendee(index)}
                  >
                    <Trash2 size={18} />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-800">Attendees from Customer</h3>
            <button
              type="button"
              className="btn btn-outline flex items-center py-1 px-2 text-sm"
              onClick={() => appendCustomerAttendee({ name: '', designation: '', email: '' })}
            >
              <Plus size={16} className="mr-1" />
              Add Attendee
            </button>
          </div>

          {customerAttendeesFields.map((field, index) => (
            <div key={field.id} className="grid grid-cols-1 md:grid-cols-3 gap-4 border-b border-gray-100 pb-4">
              <div>
                <label className="form-label">Name</label>
                <input
                  type="text"
                  className={`form-input ${errors.customerAttendees?.[index]?.name ? 'border-error' : ''}`}
                  placeholder="Enter name"
                  {...register(`customerAttendees.${index}.name`, { required: "Name is required" })}
                />
              </div>

              <div>
                <label className="form-label">Designation</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="Enter designation"
                  {...register(`customerAttendees.${index}.designation`)}
                />
              </div>

              <div className="flex items-end">
                <div className="flex-1">
                  <label className="form-label">Email</label>
                  <input
                    type="email"
                    className="form-input"
                    placeholder="Enter email"
                    {...register(`customerAttendees.${index}.email`)}
                  />
                </div>

                {customerAttendeesFields.length > 1 && (
                  <button
                    type="button"
                    className="ml-2 text-gray-400 hover:text-error transition-colors h-10"
                    onClick={() => removeCustomerAttendee(index)}
                  >
                    <Trash2 size={18} />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-800">Points Discussed</h3>
            <button
              type="button"
              className="btn btn-outline flex items-center py-1 px-2 text-sm"
              onClick={() => appendPoint('')}
            >
              <Plus size={16} className="mr-1" />
              Add Point
            </button>
          </div>

          {pointsFields.map((field, index) => (
            <div key={field.id} className="flex items-center">
              <span className="font-medium text-gray-500 mr-2">{index + 1}.</span>
              <input
                type="text"
                className="form-input flex-1"
                placeholder="Enter discussion point"
                {...register(`points.${index}`)}
              />

              {pointsFields.length > 1 && (
                <button
                  type="button"
                  className="ml-2 text-gray-400 hover:text-error transition-colors"
                  onClick={() => removePoint(index)}
                >
                  <Trash2 size={18} />
                </button>
              )}
            </div>
          ))}
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-800">Action Items</h3>
            <button
              type="button"
              className="btn btn-outline flex items-center py-1 px-2 text-sm"
              onClick={() => appendActionItem({ description: '', assigneeId: '', dueDate: '' })}
            >
              <Plus size={16} className="mr-1" />
              Add Action Item
            </button>
          </div>

          {actionItemsFields.map((field, index) => (
            <div key={field.id} className="grid grid-cols-1 md:grid-cols-3 gap-4 border-b border-gray-100 pb-4">
              <div className="md:col-span-2">
                <label className="form-label">Description</label>
                <input
                  type="text"
                  className={`form-input ${errors.actionItems?.[index]?.description ? 'border-error' : ''}`}
                  placeholder="Enter action item description"
                  {...register(`actionItems.${index}.description`, { required: "Description is required" })}
                />
              </div>

              <div className="flex items-end">
                <button
                  type="button"
                  className="ml-auto text-gray-400 hover:text-error transition-colors"
                  onClick={() => removeActionItem(index)}
                >
                  <Trash2 size={18} />
                </button>
              </div>

              <div>
                <label className="form-label">Assignee</label>
                <select
                  className={`form-select ${errors.actionItems?.[index]?.assigneeId ? 'border-error' : ''}`}
                  {...register(`actionItems.${index}.assigneeId`, { required: "Assignee is required" })}
                >
                  <option value="">Select Assignee</option>
                  {engineers.map((engineer) => (
                    <option key={engineer.id} value={engineer.id}>
                      {engineer.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="form-label">Due Date</label>
                <input
                  type="date"
                  className={`form-input ${errors.actionItems?.[index]?.dueDate ? 'border-error' : ''}`}
                  {...register(`actionItems.${index}.dueDate`, { required: "Due date is required" })}
                />
              </div>

              <div className="hidden md:block"></div> {/* Empty column for alignment */}
            </div>
          ))}
        </div>

        <div className="flex justify-end mt-6">
          <button
            type="submit"
            className="btn btn-primary flex items-center"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </span>
            ) : (
              <span className="flex items-center">
                <CheckCircle2 size={18} className="mr-2" />
                Create MOM
              </span>
            )}
          </button>
        </div>
      </form>

      {/* Customer Dialog */}
      <CustomerDialog
        isOpen={isCustomerDialogOpen}
        onClose={() => setIsCustomerDialogOpen(false)}
        onSave={handleAddCustomer}
        initialData={{ name: newCustomerName }}
      />
    </div>
  );
};

export default MOMForm;