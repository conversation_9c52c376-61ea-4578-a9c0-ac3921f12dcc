import React, { useState } from 'react';
import { 
  getNameInputInfo, 
  getNumberInputInfo, 
  getCodeInputInfo, 
  getEmailInputInfo, 
  getPhoneInputInfo,
  getDateInputInfo,
  validateName,
  validateNumber,
  validateCode,
  validateEmail,
  validatePhone,
  getMinDate,
  getMaxDateForMOM,
  ValidationInfo 
} from '../../utils/dateValidation';

interface GuidedInputProps {
  type: 'name' | 'number' | 'code' | 'email' | 'phone' | 'date';
  fieldName?: string;
  dateType?: 'start' | 'end' | 'due' | 'mom';
  value: string;
  onChange: (value: string) => void;
  label: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  showValidation?: boolean;
}

export const GuidedInput: React.FC<GuidedInputProps> = ({
  type,
  fieldName = '',
  dateType = 'start',
  value,
  onChange,
  label,
  required = false,
  disabled = false,
  className = '',
  showValidation = true
}) => {
  const [touched, setTouched] = useState(false);
  const [showHelper, setShowHelper] = useState(false);

  // Get input guidance based on type
  const getInputInfo = (): ValidationInfo => {
    switch (type) {
      case 'name':
        return getNameInputInfo();
      case 'number':
        return getNumberInputInfo(fieldName);
      case 'code':
        return getCodeInputInfo(fieldName);
      case 'email':
        return getEmailInputInfo();
      case 'phone':
        return getPhoneInputInfo();
      case 'date':
        return getDateInputInfo(dateType);
      default:
        return { placeholder: '', helperText: '' };
    }
  };

  // Validate input based on type
  const validateInput = () => {
    if (!value || !showValidation) return { isValid: true };

    switch (type) {
      case 'name':
        return validateName(value);
      case 'number':
        return validateNumber(value, fieldName);
      case 'code':
        return validateCode(value, fieldName);
      case 'email':
        return validateEmail(value);
      case 'phone':
        return validatePhone(value);
      default:
        return { isValid: true };
    }
  };

  const inputInfo = getInputInfo();
  const validation = validateInput();
  const showError = touched && !validation.isValid;

  // Get input attributes based on type
  const getInputAttributes = () => {
    const baseAttributes = {
      type: type === 'date' ? 'date' : type === 'number' ? 'number' : type === 'email' ? 'email' : 'text',
      placeholder: inputInfo.placeholder,
      pattern: inputInfo.pattern,
      inputMode: inputInfo.inputMode as any,
    };

    // Add date-specific attributes
    if (type === 'date') {
      if (dateType === 'mom') {
        baseAttributes.max = getMaxDateForMOM();
      } else {
        baseAttributes.min = getMinDate();
      }
    }

    return baseAttributes;
  };

  const inputAttributes = getInputAttributes();

  return (
    <div className={`relative ${className}`}>
      {/* Label */}
      <label className="block text-sm font-medium text-gray-300 mb-2">
        {label}
        {required && <span className="text-red-400 ml-1">*</span>}
        
        {/* Info Icon */}
        <button
          type="button"
          className="ml-2 text-blue-400 hover:text-blue-300 transition-colors"
          onMouseEnter={() => setShowHelper(true)}
          onMouseLeave={() => setShowHelper(false)}
          onClick={() => setShowHelper(!showHelper)}
        >
          <svg className="w-4 h-4 inline" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
          </svg>
        </button>
      </label>

      {/* Input Field */}
      <input
        {...inputAttributes}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onBlur={() => setTouched(true)}
        disabled={disabled}
        className={`
          w-full px-4 py-3 rounded-lg border transition-all duration-200
          ${showError 
            ? 'border-red-500 bg-red-50/10 focus:border-red-400 focus:ring-red-400/20' 
            : 'border-gray-600 bg-gray-800/50 focus:border-blue-500 focus:ring-blue-500/20'
          }
          text-white placeholder-gray-400
          focus:outline-none focus:ring-2
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      />

      {/* Helper Text Tooltip */}
      {showHelper && (
        <div className="absolute z-10 mt-1 p-3 bg-gray-900 border border-gray-600 rounded-lg shadow-lg max-w-sm">
          <p className="text-sm text-gray-300">
            <span className="font-medium text-blue-400">Format:</span> {inputInfo.helperText}
          </p>
          {type === 'name' && (
            <p className="text-xs text-gray-400 mt-1">
              ✅ Examples: "John Doe", "Mary-Jane", "O'Connor"
            </p>
          )}
          {type === 'number' && (
            <p className="text-xs text-gray-400 mt-1">
              ✅ Examples: "123", "45.67", "0.5"
            </p>
          )}
          {type === 'code' && (
            <p className="text-xs text-gray-400 mt-1">
              ✅ Examples: "ABC123", "PROJ001", "USER42"
            </p>
          )}
          {type === 'email' && (
            <p className="text-xs text-gray-400 mt-1">
              ✅ Examples: "<EMAIL>", "<EMAIL>"
            </p>
          )}
          {type === 'phone' && (
            <p className="text-xs text-gray-400 mt-1">
              ✅ Examples: "+****************", "************"
            </p>
          )}
        </div>
      )}

      {/* Error Message */}
      {showError && (
        <p className="mt-1 text-sm text-red-400 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {validation.message}
        </p>
      )}

      {/* Success Message */}
      {touched && validation.isValid && value && (
        <p className="mt-1 text-sm text-green-400 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          Valid format
        </p>
      )}
    </div>
  );
};

export default GuidedInput;
