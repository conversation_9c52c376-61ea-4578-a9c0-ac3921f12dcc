/**
 * Date validation utilities for frontend
 */

export interface DateValidationResult {
  isValid: boolean;
  message?: string;
}

export interface ValidationInfo {
  placeholder: string;
  helperText: string;
  pattern?: string;
  inputMode?: string;
}

/**
 * Validates that dates are not older than 30 days and end date is after start date
 */
export const validateDates = (
  startDate?: string | Date,
  endDate?: string | Date
): DateValidationResult => {
  const now = new Date();
  now.setHours(0, 0, 0, 0); // Set to start of today for comparison

  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  thirtyDaysAgo.setHours(0, 0, 0, 0); // Start of 30 days ago

  if (startDate) {
    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);

    // Allow dates from 30 days ago to future dates
    if (start < thirtyDaysAgo) {
      return {
        isValid: false,
        message: 'Start date cannot be older than 30 days'
      };
    }
  }

  if (endDate) {
    const end = new Date(endDate);
    end.setHours(0, 0, 0, 0);

    // Allow dates from 30 days ago to future dates
    if (end < thirtyDaysAgo) {
      return {
        isValid: false,
        message: 'End date cannot be older than 30 days'
      };
    }
  }

  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (end < start) {
      return {
        isValid: false,
        message: 'End date cannot be before start date'
      };
    }
  }

  return { isValid: true };
};

/**
 * Validates that MOM date is not older than 30 days (future dates are allowed)
 */
export const validateMOMDate = (date?: string | Date): DateValidationResult => {
  if (!date) return { isValid: true };

  const momDate = new Date(date);
  const thirtyDaysAgo = new Date();

  // Set time boundaries
  momDate.setHours(0, 0, 0, 0);
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  thirtyDaysAgo.setHours(0, 0, 0, 0); // Start of 30 days ago

  if (momDate < thirtyDaysAgo) {
    return {
      isValid: false,
      message: 'MOM date cannot be older than 30 days'
    };
  }

  return { isValid: true };
};

/**
 * Validates that due date is not before today
 */
export const validateDueDate = (dueDate?: string | Date): DateValidationResult => {
  if (!dueDate) return { isValid: true };

  const due = new Date(dueDate);
  const now = new Date();
  due.setHours(0, 0, 0, 0);
  now.setHours(0, 0, 0, 0);

  // Allow today's date and future dates
  if (due < now) {
    return {
      isValid: false,
      message: 'Due date cannot be before today'
    };
  }

  return { isValid: true };
};

/**
 * Get minimum date for date inputs (30 days ago in YYYY-MM-DD format)
 */
export const getMinDate = (): string => {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  return thirtyDaysAgo.toISOString().split('T')[0];
};

/**
 * Get maximum date for MOM date inputs (no maximum - future dates allowed)
 */
export const getMaxDateForMOM = (): string | undefined => {
  // No maximum date restriction for MOM - future dates are allowed
  return undefined;
};

/**
 * Get minimum date for MOM date inputs (30 days ago in YYYY-MM-DD format)
 */
export const getMinDateForMOM = (): string => {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  return thirtyDaysAgo.toISOString().split('T')[0];
};

/**
 * Validates that name contains only letters, spaces, hyphens, apostrophes, and dots (for user names)
 */
export const validateName = (name?: string): DateValidationResult => {
  if (!name) return { isValid: true };

  // Allow letters (including accented characters), spaces, hyphens, apostrophes, and dots
  const nameRegex = /^[a-zA-ZÀ-ÿ\s\-'.]+$/;

  if (!nameRegex.test(name)) {
    return {
      isValid: false,
      message: 'Name can only contain letters, spaces, hyphens, apostrophes, and dots'
    };
  }

  // Check for minimum length
  if (name.trim().length < 2) {
    return {
      isValid: false,
      message: 'Name must be at least 2 characters long'
    };
  }

  // Check for maximum length
  if (name.trim().length > 100) {
    return {
      isValid: false,
      message: 'Name must be less than 100 characters long'
    };
  }

  return { isValid: true };
};

/**
 * Validates that task/project name contains allowed characters
 */
export const validateTaskName = (name?: string): DateValidationResult => {
  if (!name) return { isValid: true };

  // Allow letters (including accented characters), numbers, spaces, hyphens, apostrophes, dots, parentheses, and common symbols
  const nameRegex = /^[a-zA-ZÀ-ÿ0-9\s\-'.()\[\]_&@#$%+:,/]+$/;

  if (!nameRegex.test(name)) {
    return {
      isValid: false,
      message: 'Name contains invalid characters. Only letters, numbers, spaces, and common symbols are allowed'
    };
  }

  // Check for minimum length
  if (name.trim().length < 2) {
    return {
      isValid: false,
      message: 'Name must be at least 2 characters long'
    };
  }

  // Check for maximum length
  if (name.trim().length > 200) {
    return {
      isValid: false,
      message: 'Name must be less than 200 characters long'
    };
  }

  return { isValid: true };
};

/**
 * Validates that value is a valid number
 */
export const validateNumber = (value?: string | number, fieldName: string = 'Field'): DateValidationResult => {
  if (value === undefined || value === null || value === '') return { isValid: true };

  const numberValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numberValue)) {
    return {
      isValid: false,
      message: `${fieldName} must be a valid number`
    };
  }

  return { isValid: true };
};

/**
 * Validates that code contains only letters and numbers
 */
export const validateCode = (code?: string, fieldName: string = 'Code'): DateValidationResult => {
  if (!code) return { isValid: true };

  // Allow letters and numbers only
  const codeRegex = /^[a-zA-Z0-9]+$/;

  if (!codeRegex.test(code)) {
    return {
      isValid: false,
      message: `${fieldName} can only contain letters and numbers`
    };
  }

  return { isValid: true };
};

/**
 * Normalizes email to lowercase
 */
export const normalizeEmail = (email: string): string => {
  return email.trim().toLowerCase();
};

/**
 * Validates email format
 */
export const validateEmail = (email?: string): DateValidationResult => {
  if (!email) return { isValid: true };

  // Normalize email before validation
  const normalizedEmail = normalizeEmail(email);
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailRegex.test(normalizedEmail)) {
    return {
      isValid: false,
      message: 'Please enter a valid email address'
    };
  }

  return { isValid: true };
};

/**
 * Validates phone number format - must be exactly 10 digits
 */
export const validatePhone = (phone?: string): DateValidationResult => {
  if (!phone) return { isValid: false, message: 'Phone number is required' };

  // Remove all non-digit characters
  const digitsOnly = phone.replace(/\D/g, '');

  if (digitsOnly.length !== 10) {
    return {
      isValid: false,
      message: 'Phone number must be exactly 10 digits'
    };
  }

  return { isValid: true };
};

// =============================================================================
// INPUT GUIDANCE HELPERS - Show users what format is expected
// =============================================================================

/**
 * Get input guidance for name fields
 */
export const getNameInputInfo = (): ValidationInfo => ({
  placeholder: "Enter name (letters only)",
  helperText: "Only letters, spaces, hyphens, apostrophes, and dots are allowed",
  pattern: "[a-zA-ZÀ-ÿ\\s\\-'.]+",
});

/**
 * Get input guidance for number fields
 */
export const getNumberInputInfo = (fieldName: string = "number"): ValidationInfo => ({
  placeholder: `Enter ${fieldName} (numbers only)`,
  helperText: "Only numeric values are allowed",
  pattern: "[0-9]+(\\.[0-9]+)?",
  inputMode: "numeric",
});

/**
 * Get input guidance for code fields
 */
export const getCodeInputInfo = (fieldName: string = "code"): ValidationInfo => ({
  placeholder: `Enter ${fieldName} (letters and numbers only)`,
  helperText: "Only letters and numbers are allowed (no spaces or special characters)",
  pattern: "[a-zA-Z0-9]+",
});

/**
 * Get input guidance for email fields
 */
export const getEmailInputInfo = (): ValidationInfo => ({
  placeholder: "Enter email address (e.g., <EMAIL>)",
  helperText: "Please enter a valid email address",
  inputMode: "email",
});

/**
 * Get input guidance for phone fields
 */
export const getPhoneInputInfo = (): ValidationInfo => ({
  placeholder: "Enter phone number (e.g., +****************)",
  helperText: "Numbers, spaces, hyphens, parentheses, and plus sign are allowed",
  pattern: "[\\d\\s\\-\\(\\)\\+]+",
  inputMode: "tel",
});

/**
 * Get input guidance for date fields
 */
export const getDateInputInfo = (fieldType: 'start' | 'end' | 'due' | 'mom' = 'start'): ValidationInfo => {
  const today = new Date().toISOString().split('T')[0];

  switch (fieldType) {
    case 'mom':
      return {
        placeholder: "Select meeting date",
        helperText: "Meeting date cannot be older than 30 days",
      };
    case 'due':
      return {
        placeholder: "Select due date",
        helperText: "Due date cannot be in the past",
      };
    case 'end':
      return {
        placeholder: "Select end date",
        helperText: "End date cannot be older than 30 days or before start date",
      };
    default:
      return {
        placeholder: "Select start date",
        helperText: "Start date cannot be older than 30 days",
      };
  }
};
