import express, { Request, Response } from 'express';
import {
  getDepartments,
  getDepartment,
  createDepartment,
  updateDepartment,
  deleteDepartment,
} from '../controllers/department.controller';
import { protect, authorize } from '../middleware/auth.middleware';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(
    authorize('DIRECTOR', 'PROJECT_MANAGER', 'TEAM_LEAD'), // All management roles can view
    getDepartments
  )
  .post(
    authorize('DIRECTOR'), // Only DIRECTOR can create departments
    createDepartment
  );

router.route('/:id')
  .get(
    authorize('DIRECTOR', 'PROJECT_MANAGER', 'TEAM_LEAD'), // All management roles can view
    getDepartment
  )
  .put(
    authorize('DIRECTOR'), // Only DIRECTOR can update departments
    updateDepartment
  )
  .delete(
    authorize('DIRECTOR'), // Only DIRECTOR can delete departments
    deleteDepartment
  );

export default router;
