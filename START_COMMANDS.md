# Project Management System - Start Commands

This document explains how to start the Project Management System in both development and production modes.

## Quick Start

### Development Mode (Recommended for development)
```bash
# From frontend directory
cd frontend
yarn dev
```

### Production Mode (For production deployment)
```bash
# From frontend directory  
cd frontend
yarn start
```

## Available Commands

### Frontend Directory Commands

#### Development Commands
- `yarn dev` - Start development servers (frontend + backend + Prisma setup)
- `yarn frontend:dev` - Start only frontend development server
- `yarn backend:dev` - Start only backend development server

#### Production Commands
- `yarn start` - Build and start production servers
- `yarn build` - Build frontend only
- `yarn build:all` - Build both frontend and backend
- `yarn frontend:start` - Start frontend production server (preview)
- `yarn backend:start` - Start backend production server

#### Database Commands
- `yarn prisma:setup` - Run db pull and generate (full setup)
- `yarn prisma:pull` - Pull database schema
- `yarn prisma:generate` - Generate Prisma client

#### Utility Commands
- `yarn clean` - Clean all build artifacts
- `yarn setup` - Install all dependencies and setup Prisma
- `yarn install:all` - Install dependencies for both frontend and backend

### Backend Directory Commands

#### Development
- `npm run dev` - Start backend in development mode with hot reload
- `npm run start:ts` - Start backend using ts-node (development)

#### Production
- `npm run start` - Start backend in production mode (requires build)
- `npm run build` - Build TypeScript to JavaScript
- `npm run build:clean` - Clean and rebuild

#### Database
- `npm run prisma:generate` - Generate Prisma client
- `npm run prisma:db-pull` - Pull database schema
- `npm run prisma:migrate` - Run database migrations (development)
- `npm run prisma:migrate:prod` - Deploy migrations (production)

## Script Files

### Windows Batch Script
```cmd
# Development mode (default)
start-all.bat

# Production mode
start-all.bat prod
```

### PowerShell Script
```powershell
# Development mode (default)
.\start-all.ps1

# Production mode  
.\start-all.ps1 prod
```

## What Each Mode Does

### Development Mode (`yarn dev`)
1. **Prisma Setup**: Pulls database schema and generates client
2. **Backend**: Starts with hot reload using nodemon + ts-node
3. **Frontend**: Starts Vite dev server with hot reload
4. **Features**: 
   - Hot reload for both frontend and backend
   - TypeScript compilation on-the-fly
   - Development optimizations
   - Source maps enabled

### Production Mode (`yarn start`)
1. **Build**: Compiles TypeScript backend and builds frontend
2. **Backend**: Runs compiled JavaScript from `dist/` folder
3. **Frontend**: Serves built static files via Vite preview
4. **Features**:
   - Optimized builds
   - Minified assets
   - Production performance
   - No hot reload

## Environment Setup

Make sure you have:
- Node.js (v16 or higher)
- Yarn package manager
- MySQL database connection configured in `backend/.env`

## Troubleshooting

### Common Issues
1. **Port conflicts**: Frontend runs on 3004, backend on 5002
2. **Database connection**: Check `backend/.env` for correct DATABASE_URL
3. **Dependencies**: Run `yarn setup` to install all dependencies
4. **Build errors**: Run `yarn clean` then rebuild

### Logs
- Backend logs appear with `[BACKEND]` prefix
- Frontend logs appear with `[FRONTEND]` prefix
- Prisma operations show progress indicators

## Quick Reference

| Command | Purpose | Mode |
|---------|---------|------|
| `yarn dev` | Development with hot reload | Dev |
| `yarn start` | Production build and serve | Prod |
| `yarn build:all` | Build both frontend and backend | Build |
| `yarn clean` | Clean all build artifacts | Utility |
| `yarn setup` | Full project setup | Setup |
