import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAtom } from 'jotai';
import { isAuthenticatedAtom } from '../store';
import LoginForm from '../components/Auth/LoginForm';

const LoginPage = () => {
  const [isAuthenticated] = useAtom(isAuthenticatedAtom);
  const navigate = useNavigate();

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, navigate]);

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Left Side - Hero Section */}
      <div className="hidden lg:flex lg:flex-1 relative overflow-hidden">
        {/* Project Management Background Image */}
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('https://images.pexels.com/photos/7688336/pexels-photo-7688336.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2')`,
          }}
        ></div>
        
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-black bg-opacity-70"></div>

        {/* Animated geometric shapes for 3D effect */}
        <div className="absolute top-16 left-16 w-20 h-20 bg-blue-500/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-32 right-16 w-24 h-24 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-cyan-500/10 rounded-full blur-lg animate-bounce delay-500"></div>

        {/* Content Container with 3D transforms */}
        <div className="relative z-10 flex flex-col justify-center px-12 text-white transform perspective-1000">
          <div className="mb-8 transform hover:scale-105 transition-transform duration-300">
            {/* Enhanced Brand Name - Fixed inline layout */}
            <div className="mb-4">
              <h1 className="text-4xl xl:text-5xl font-black leading-tight whitespace-nowrap">
                <span style={{ color: '#04ace2' }}>Mekhos|</span>
                <span style={{ color: '#0f0338' }}>Technology</span>
              </h1>
            </div>
            
            <p className="text-lg font-light text-gray-300 mb-6">
              Project Management System
            </p>
          </div>

          {/* Feature Cards with 3D effect - Reduced sizes */}
          <div className="space-y-6">
            <div className="group transform hover:translate-x-2 transition-all duration-300 hover:scale-102">
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10 shadow-xl">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-lg flex items-center justify-center shadow-lg transform group-hover:rotate-12 transition-transform duration-300">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-1">Smart Task Management</h3>
                    <p className="text-gray-300 text-sm leading-relaxed">Create, assign, and track tasks with intelligent workflows</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="group transform hover:translate-x-2 transition-all duration-300 hover:scale-102 delay-75">
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10 shadow-xl">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center shadow-lg transform group-hover:rotate-12 transition-transform duration-300">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-1">Meeting Documentation</h3>
                    <p className="text-gray-300 text-sm leading-relaxed">Streamlined Minutes of Meeting recording and management</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="group transform hover:translate-x-2 transition-all duration-300 hover:scale-102 delay-150">
              <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10 shadow-xl">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-emerald-500 rounded-lg flex items-center justify-center shadow-lg transform group-hover:rotate-12 transition-transform duration-300">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-1">Advanced Analytics</h3>
                    <p className="text-gray-300 text-sm leading-relaxed">Comprehensive project insights and performance metrics</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="flex-1 flex flex-col justify-center py-8 px-4 sm:px-6 lg:px-16 xl:px-20 relative">
        {/* Background decoration for mobile */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 opacity-50 lg:hidden"></div>
        
        <div className="mx-auto w-full max-w-sm lg:max-w-md relative z-10">
          {/* Mobile Header */}
          <div className="lg:hidden text-center mb-8">
            <h1 className="text-3xl font-black mb-2 whitespace-nowrap inline-flex items-center justify-center">
              <span className="text-[#04ace2]">Mekhos</span>
              <span className="text-[#0f0338]">&nbsp;Technology</span>
            </h1>
            <p className="text-base text-gray-600">Project Management System</p>

            <div className="relative w-[200px] h-1 mx-auto mt-2">
              <div className="absolute left-0 top-0 h-1 w-1/2 bg-[#04ace2] rounded-l-full"></div>
              <div className="absolute right-0 top-0 h-1 w-1/2 bg-[#0f0338] rounded-r-full"></div>
              <div className="absolute left-1/2 top-[-6px] transform -translate-x-1/2 text-sm font-bold text-gray-500">|</div>
            </div>
          </div>
            <LoginForm />
          </div>
        </div>
      </div>
  );
};

export default LoginPage;