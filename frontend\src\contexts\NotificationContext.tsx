import React, { createContext, useContext, useState, ReactNode } from 'react';
import { SuccessDialog } from '../components/common/SuccessDialog';
import { ErrorDialog } from '../components/common/ErrorDialog';

interface NotificationState {
  success: {
    isOpen: boolean;
    title: string;
    message: string;
    details?: string[];
  };
  error: {
    isOpen: boolean;
    title: string;
    message: string;
    details?: string[];
  };
}

interface NotificationContextType {
  showSuccess: (title: string, message: string, details?: string[]) => void;
  showError: (title: string, message: string, details?: string[]) => void;
  hideSuccess: () => void;
  hideError: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [state, setState] = useState<NotificationState>({
    success: {
      isOpen: false,
      title: '',
      message: '',
      details: []
    },
    error: {
      isOpen: false,
      title: '',
      message: '',
      details: []
    }
  });

  const showSuccess = (title: string, message: string, details?: string[]) => {
    setState(prev => ({
      ...prev,
      success: {
        isOpen: true,
        title,
        message,
        details: details || []
      }
    }));
  };

  const showError = (title: string, message: string, details?: string[]) => {
    setState(prev => ({
      ...prev,
      error: {
        isOpen: true,
        title,
        message,
        details: details || []
      }
    }));
  };

  const hideSuccess = () => {
    setState(prev => ({
      ...prev,
      success: {
        ...prev.success,
        isOpen: false
      }
    }));
  };

  const hideError = () => {
    setState(prev => ({
      ...prev,
      error: {
        ...prev.error,
        isOpen: false
      }
    }));
  };

  const value: NotificationContextType = {
    showSuccess,
    showError,
    hideSuccess,
    hideError
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      
      {/* Success Dialog */}
      <SuccessDialog
        isOpen={state.success.isOpen}
        onClose={hideSuccess}
        title={state.success.title}
        message={state.success.message}
        details={state.success.details}
      />
      
      {/* Error Dialog */}
      <ErrorDialog
        isOpen={state.error.isOpen}
        onClose={hideError}
        title={state.error.title}
        message={state.error.message}
        details={state.error.details}
      />
    </NotificationContext.Provider>
  );
};

export default NotificationProvider;
