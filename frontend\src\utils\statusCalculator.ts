import { TaskStatus } from '../types';

/**
 * Intelligent Status Management System
 *
 * This system automatically manages status for Projects, Tasks, and Subtasks based on:
 * 1. Start/End dates
 * 2. Current completion status
 * 3. Hierarchical dependencies
 *
 * KEY PRINCIPLES:
 *
 * 🔒 COMPLETION PRESERVATION:
 * - Once marked as COMPLETED, items stay COMPLETED forever
 * - Manual completion overrides all date-based logic
 * - Allows flexible project management
 *
 * 📅 DATE-BASED AUTOMATION:
 * - Before start date: NOT_STARTED (automatic)
 * - After start date: IN_PROGRESS (automatic)
 * - Past end date (incomplete): DELAYED (automatic)
 *
 * 🏗️ HIERARCHICAL LOGIC:
 * - Subtask completion → affects Task status
 * - Task completion → affects Project status
 * - Parent status reflects child completion
 *
 * EXAMPLES:
 * - Task with future start date → NOT_STARTED
 * - Task starts today → automatically becomes IN_PROGRESS
 * - All subtasks completed → Task becomes COMPLETED
 * - Task manually marked COMPLETED → stays COMPLETED forever
 * - Task past deadline (incomplete) → DELAYED
 */

export interface StatusCalculationInput {
  startDate: string;
  endDate: string;
  currentStatus?: TaskStatus;
}

export interface TaskStatusInput extends StatusCalculationInput {
  subtasks?: Array<{
    status: TaskStatus;
    startDate: string;
    endDate: string;
  }>;
}

export interface ProjectStatusInput extends StatusCalculationInput {
  tasks?: Array<{
    status: TaskStatus;
    startDate: string;
    endDate: string;
    subtasks?: Array<{
      status: TaskStatus;
      startDate: string;
      endDate: string;
    }>;
  }>;
}

/**
 * Calculate subtask status based on dates and current status
 */
export const calculateSubtaskStatus = (input: StatusCalculationInput): TaskStatus => {
  const now = new Date();
  const startDate = new Date(input.startDate);
  const endDate = new Date(input.endDate);

  // IMPORTANT: If manually marked as completed or on hold, ALWAYS keep that status
  // This preserves user's manual status changes regardless of dates
  // NEVER OVERRIDE THESE STATUSES - they are manually set by users
  if (input.currentStatus === TaskStatus.COMPLETED || input.currentStatus === TaskStatus.ON_HOLD) {
    console.log(`🔒 Preserving manually set status: ${input.currentStatus} for subtask`);
    return input.currentStatus;
  }

  // If start date hasn't arrived yet, always NOT_STARTED
  if (now < startDate) {
    return TaskStatus.NOT_STARTED;
  }

  // If past end date and not completed/on hold, mark as DELAYED
  if (now > endDate) {
    return TaskStatus.DELAYED;
  }

  // If between start and end date, mark as IN_PROGRESS
  // This automatically changes from NOT_STARTED to IN_PROGRESS when start date arrives
  return TaskStatus.IN_PROGRESS;
};

/**
 * Calculate task status based on subtasks and dates
 */
export const calculateTaskStatus = (input: TaskStatusInput): TaskStatus => {
  // IMPORTANT: FIRST CHECK - If manually marked as completed or on hold, ALWAYS keep that status
  // This must be the very first check to prevent any override
  // NEVER OVERRIDE THESE STATUSES - they are manually set by users
  if (input.currentStatus === TaskStatus.COMPLETED || input.currentStatus === TaskStatus.ON_HOLD) {
    console.log(`🔒 Preserving manually set status: ${input.currentStatus} for task`);
    return input.currentStatus;
  }

  const now = new Date();
  const startDate = new Date(input.startDate);
  const endDate = new Date(input.endDate);
  const subtasks = input.subtasks || [];

  // If no subtasks, use date-based logic for the task itself
  if (subtasks.length === 0) {
    // If start date hasn't arrived yet, always NOT_STARTED
    if (now < startDate) {
      return TaskStatus.NOT_STARTED;
    }

    // If past end date and not completed/on hold, mark as DELAYED
    if (now > endDate) {
      return TaskStatus.DELAYED;
    }

    // If between start and end date, mark as IN_PROGRESS
    // This automatically changes from NOT_STARTED to IN_PROGRESS when start date arrives
    return TaskStatus.IN_PROGRESS;
  }

  // Calculate subtask statuses first (preserving their completion status)
  const updatedSubtasks = subtasks.map(subtask => ({
    ...subtask,
    status: calculateSubtaskStatus({
      ...subtask,
      currentStatus: subtask.status // Pass the current status explicitly
    })
  }));

  // Check if all subtasks are completed
  const allSubtasksCompleted = updatedSubtasks.every(
    subtask => subtask.status === TaskStatus.COMPLETED
  );

  // If all subtasks are completed, mark task as completed
  if (allSubtasksCompleted && subtasks.length > 0) {
    return TaskStatus.COMPLETED;
  }

  // Check if any subtask is delayed
  const hasDelayedSubtasks = updatedSubtasks.some(
    subtask => subtask.status === TaskStatus.DELAYED
  );

  if (hasDelayedSubtasks) {
    return TaskStatus.DELAYED;
  }

  // Check if task start date hasn't arrived yet
  if (now < startDate) {
    return TaskStatus.NOT_STARTED;
  }

  // Check if any subtask is in progress
  const hasInProgressSubtasks = updatedSubtasks.some(
    subtask => subtask.status === TaskStatus.IN_PROGRESS
  );

  if (hasInProgressSubtasks) {
    return TaskStatus.IN_PROGRESS;
  }

  // Check if any subtask is on hold
  const hasOnHoldSubtasks = updatedSubtasks.some(
    subtask => subtask.status === TaskStatus.ON_HOLD
  );

  if (hasOnHoldSubtasks) {
    return TaskStatus.ON_HOLD;
  }

  // If start date has passed but no subtasks are in progress yet
  // (all subtasks are NOT_STARTED), mark task as IN_PROGRESS
  if (now >= startDate) {
    return TaskStatus.IN_PROGRESS;
  }

  // Default to NOT_STARTED
  return TaskStatus.NOT_STARTED;
};

/**
 * Calculate project status based on tasks and dates
 */
export const calculateProjectStatus = (input: ProjectStatusInput): TaskStatus => {
  // IMPORTANT: FIRST CHECK - If manually marked as completed or on hold, ALWAYS keep that status
  // This must be the very first check to prevent any override
  // NEVER OVERRIDE THESE STATUSES - they are manually set by users
  if (input.currentStatus === TaskStatus.COMPLETED || input.currentStatus === TaskStatus.ON_HOLD) {
    console.log(`🔒 Preserving manually set status: ${input.currentStatus} for project`);
    return input.currentStatus;
  }

  const now = new Date();
  const startDate = new Date(input.startDate);
  const endDate = new Date(input.endDate);
  const tasks = input.tasks || [];

  // If no tasks, use date-based logic for the project itself
  if (tasks.length === 0) {
    // If start date hasn't arrived yet, always NOT_STARTED
    if (now < startDate) {
      return TaskStatus.NOT_STARTED;
    }

    // If past end date and not completed/on hold, mark as DELAYED
    if (now > endDate) {
      return TaskStatus.DELAYED;
    }

    // If between start and end date, mark as IN_PROGRESS
    // This automatically changes from NOT_STARTED to IN_PROGRESS when start date arrives
    return TaskStatus.IN_PROGRESS;
  }

  // Calculate task statuses first (preserving their completion status)
  const updatedTasks = tasks.map(task => ({
    ...task,
    status: calculateTaskStatus({
      ...task,
      currentStatus: task.status // Pass the current status explicitly
    })
  }));

  // Check if all tasks are completed
  const allTasksCompleted = updatedTasks.every(
    task => task.status === TaskStatus.COMPLETED
  );

  // If all tasks are completed, mark project as completed
  if (allTasksCompleted && tasks.length > 0) {
    return TaskStatus.COMPLETED;
  }

  // Check if any task is delayed
  const hasDelayedTasks = updatedTasks.some(
    task => task.status === TaskStatus.DELAYED
  );

  if (hasDelayedTasks) {
    return TaskStatus.DELAYED;
  }

  // Check if project start date hasn't arrived yet
  if (now < startDate) {
    return TaskStatus.NOT_STARTED;
  }

  // Check if any task is in progress
  const hasInProgressTasks = updatedTasks.some(
    task => task.status === TaskStatus.IN_PROGRESS
  );

  if (hasInProgressTasks) {
    return TaskStatus.IN_PROGRESS;
  }

  // If start date has passed but no tasks are in progress yet
  // (all tasks are NOT_STARTED), mark project as IN_PROGRESS
  if (now >= startDate) {
    return TaskStatus.IN_PROGRESS;
  }

  // Default to NOT_STARTED
  return TaskStatus.NOT_STARTED;
};

/**
 * Update all statuses in a project hierarchy
 * IMPORTANT: Respects manually set COMPLETED and ON_HOLD statuses
 */
export const updateProjectHierarchyStatus = (project: ProjectStatusInput) => {
  const updatedTasks = (project.tasks || []).map(task => {
    // Update subtask statuses - but preserve COMPLETED and ON_HOLD
    const updatedSubtasks = (task.subtasks || []).map(subtask => {
      // NEVER override manually set COMPLETED or ON_HOLD statuses
      if (subtask.status === TaskStatus.COMPLETED || subtask.status === TaskStatus.ON_HOLD) {
        return subtask; // Keep original subtask unchanged
      }

      // Only calculate new status for other statuses
      return {
        ...subtask,
        status: calculateSubtaskStatus({
          ...subtask,
          currentStatus: subtask.status // Pass the current status explicitly
        })
      };
    });

    // Update task status based on updated subtasks
    const updatedTask = {
      ...task,
      subtasks: updatedSubtasks,
      status: calculateTaskStatus({
        ...task,
        currentStatus: task.status, // Pass the current status explicitly
        subtasks: updatedSubtasks
      })
    };

    return updatedTask;
  });

  // Update project status based on updated tasks
  const updatedProject = {
    ...project,
    tasks: updatedTasks,
    status: calculateProjectStatus({
      ...project,
      currentStatus: project.status, // Pass the current status explicitly
      tasks: updatedTasks
    })
  };

  return updatedProject;
};

/**
 * Check if status update is needed (to avoid unnecessary updates)
 * IMPORTANT: Never update COMPLETED or ON_HOLD statuses - they are manually set
 */
export const isStatusUpdateNeeded = (
  currentStatus: TaskStatus,
  calculatedStatus: TaskStatus
): boolean => {
  // NEVER update items that are manually marked as COMPLETED or ON_HOLD
  // These statuses should be preserved regardless of dates
  if (currentStatus === TaskStatus.COMPLETED || currentStatus === TaskStatus.ON_HOLD) {
    return false;
  }

  // Only update if the status has actually changed
  return currentStatus !== calculatedStatus;
};
