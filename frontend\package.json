{"name": "mekhos-project-management", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "dev:frontend": "vite", "dev:backend": "cd ../backend && npm run dev", "start": "npm run build:all && concurrently --kill-others-on-fail --names \"BACKEND,FRONTEND\" -c \"bgBlue.bold,bgGreen.bold\" \"npm run backend:start\" \"npm run frontend:start\"", "build": "vite build", "build:all": "npm run build && npm run backend:build", "lint": "eslint .", "preview": "vite preview", "prisma:setup": "cd ../backend && npm run prisma:db-pull && npm run prisma:generate", "prisma:generate": "cd ../backend && npm run prisma:generate", "prisma:pull": "cd ../backend && npm run prisma:db-pull", "backend:start": "cd ../backend && npm run start", "backend:build": "cd ../backend && npm run build", "backend:clean": "cd ../backend && npm run build:clean", "frontend:start": "vite preview --port 3004 --host", "frontend:clean": "rm -rf dist", "clean": "npm run frontend:clean && npm run backend:clean", "install:all": "npm install && cd ../backend && npm install", "setup": "npm run install:all && npm run prisma:setup"}, "dependencies": {"@faker-js/faker": "^8.4.1", "@headlessui/react": "^1.7.18", "@heroicons/react": "^2.2.0", "@types/three": "^0.176.0", "axios": "^1.9.0", "chart.js": "^4.4.2", "date-fns": "^3.5.0", "jotai": "^2.6.5", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-hook-form": "^7.51.0", "react-router-dom": "^6.22.3", "recharts": "^2.15.3", "three": "^0.176.0", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/axios": "^0.9.36", "@types/node": "^22.15.21", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.8", "@types/xlsx": "^0.0.35", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.1.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}