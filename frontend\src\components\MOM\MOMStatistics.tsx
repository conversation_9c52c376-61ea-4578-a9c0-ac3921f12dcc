import React from 'react';
import { use<PERSON>tom } from 'jotai';
import { momsAtom } from '../../store';
import { FileText, CheckCircle, Clock, AlertTriangle, TrendingUp } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  borderColor: string;
  percentage?: number;
  trend?: 'up' | 'down' | 'neutral';
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  color,
  bgColor,
  borderColor,
  percentage,
  trend
}) => {
  return (
    <div className={`${bgColor} rounded-xl p-6 border ${borderColor} shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className={`text-3xl font-bold ${color} mb-2`}>{value}</p>
          {percentage !== undefined && (
            <div className="flex items-center space-x-2">
              <div className={`w-full bg-gray-200 rounded-full h-2`}>
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    title === 'Completed' ? 'bg-green-500' :
                    title === 'Delayed' ? 'bg-red-500' :
                    title === 'Pending' ? 'bg-yellow-500' :
                    'bg-blue-500'
                  }`}
                  style={{ width: `${Math.min(percentage, 100)}%` }}
                ></div>
              </div>
              <span className="text-xs text-gray-500 font-medium min-w-[35px]">
                {percentage.toFixed(0)}%
              </span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg ${bgColor === 'bg-white' ? 'bg-gray-50' : 'bg-white bg-opacity-20'} ml-4`}>
          {icon}
        </div>
      </div>
    </div>
  );
};

const MOMStatistics: React.FC = () => {
  const [moms] = useAtom(momsAtom);

  // Show loading state if moms data is not yet loaded
  if (!moms) {
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200 animate-pulse">
          <div className="flex items-center justify-between">
            <div>
              <div className="h-6 bg-gray-300 rounded w-64 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-96"></div>
            </div>
            <div className="text-right">
              <div className="h-4 bg-gray-200 rounded w-16 mb-1"></div>
              <div className="h-8 bg-gray-300 rounded w-12"></div>
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white rounded-xl p-6 border border-gray-200 animate-pulse">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
                  <div className="h-8 bg-gray-300 rounded w-12 mb-2"></div>
                  <div className="h-2 bg-gray-200 rounded w-full"></div>
                </div>
                <div className="w-12 h-12 bg-gray-200 rounded-lg ml-4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Calculate statistics from MOM action items
  const calculateStats = () => {
    let totalQueries = 0;
    let completed = 0;
    let pending = 0;
    let delayed = 0;
    let inProgress = 0;

    if (moms && moms.length > 0) {
      moms.forEach(mom => {
        // Check actionItems for discussion points
        if (mom.actionItems && mom.actionItems.length > 0) {
          mom.actionItems.forEach(actionItem => {
            totalQueries++;
            switch (actionItem.status) {
              case 'COMPLETED':
                completed++;
                break;
              case 'PENDING':
                pending++;
                break;
              case 'DELAYED':
                delayed++;
                break;
              case 'IN_PROGRESS':
                inProgress++;
                break;
              default:
                pending++; // Default to pending for any other status
                break;
            }
          });
        }
      });
    }

    return {
      totalQueries,
      completed,
      pending: pending + inProgress, // Combine pending and in-progress as "pending"
      delayed
    };
  };

  const stats = calculateStats();

  // Calculate percentages
  const completedPercentage = stats.totalQueries > 0 ? (stats.completed / stats.totalQueries) * 100 : 0;
  const pendingPercentage = stats.totalQueries > 0 ? (stats.pending / stats.totalQueries) * 100 : 0;
  const delayedPercentage = stats.totalQueries > 0 ? (stats.delayed / stats.totalQueries) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Summary Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-2">MOM Discussion Points Overview</h2>
            <p className="text-gray-600">Track the status of all discussion points across meetings</p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">Total MOMs</p>
            <p className="text-2xl font-bold text-blue-600">{moms?.length || 0}</p>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Queries"
          value={stats.totalQueries}
          icon={<FileText size={24} className="text-blue-600" />}
          color="text-blue-600"
          bgColor="bg-white"
          borderColor="border-blue-200"
          percentage={100} // Always 100% for total
        />

        <StatCard
          title="Completed"
          value={stats.completed}
          icon={<CheckCircle size={24} className="text-green-600" />}
          color="text-green-600"
          bgColor="bg-white"
          borderColor="border-green-200"
          percentage={completedPercentage}
        />

        <StatCard
          title="Pending"
          value={stats.pending}
          icon={<Clock size={24} className="text-yellow-600" />}
          color="text-yellow-600"
          bgColor="bg-white"
          borderColor="border-yellow-200"
          percentage={pendingPercentage}
        />

        <StatCard
          title="Delayed"
          value={stats.delayed}
          icon={<AlertTriangle size={24} className="text-red-600" />}
          color="text-red-600"
          bgColor="bg-white"
          borderColor="border-red-200"
          percentage={delayedPercentage}
        />
      </div>
    </div>
  );
};

export default MOMStatistics;
