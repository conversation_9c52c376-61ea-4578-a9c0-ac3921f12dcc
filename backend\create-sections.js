const { PrismaClient } = require('@prisma/client');
const { v4: uuidv4 } = require('uuid');

const prisma = new PrismaClient();

async function createSectionsForExistingProject() {
  try {
    const projectId = 'fe5a0b2b-1944-4cd2-b4f4-6bae179a1a21';
    
    // Check if sections already exist
    const existingSections = await prisma.section.findMany({
      where: { projectId }
    });
    
    if (existingSections.length > 0) {
      console.log(`Project already has ${existingSections.length} sections:`, existingSections.map(s => s.name));
      return;
    }
    
    // Create default sections
    const defaultSections = [
      { name: 'Design', description: 'Design and planning phase' },
      { name: 'Procurement', description: 'Procurement and sourcing phase' },
      { name: 'Assembly', description: 'Assembly and manufacturing phase' },
      { name: 'Testing', description: 'Testing and quality assurance phase' },
      { name: 'MQ1', description: 'First milestone and quality checkpoint' },
      { name: 'MQ2', description: 'Second milestone and quality checkpoint' }
    ];

    console.log('Creating default sections for project:', projectId);

    for (let i = 0; i < defaultSections.length; i++) {
      const section = defaultSections[i];
      const createdSection = await prisma.section.create({
        data: {
          id: uuidv4(),
          projectId: projectId,
          name: section.name,
          sequence: i + 1,
          description: section.description,
        },
      });
      console.log(`Created section: ${createdSection.name} (${createdSection.id})`);
    }

    console.log('Default sections created successfully');
  } catch (error) {
    console.error('Error creating sections:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createSectionsForExistingProject();
