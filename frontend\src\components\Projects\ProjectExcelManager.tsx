import React, { useState, useRef } from 'react';
import { useAtom } from 'jotai';
import { usersAtom } from '../../store';
import { Project } from '../../types';
import { Download, Upload, FileSpreadsheet, AlertCircle, CheckCircle, X } from 'lucide-react';
import * as XLSX from 'xlsx';
import {
  convertProjectToExcelData,
  convertExcelDataToProject,
  previewExcelData,
  ExcelTaskRow,
  ExcelPreviewRow
} from '../../utils/excelUtils';
import { useNotification } from '../../contexts/NotificationContext';
import { projectsAPI } from '../../services/api';

interface ProjectExcelManagerProps {
  project: Project;
  onDataImported: (sections: any[]) => void;
  onClose: () => void;
}

const ProjectExcelManager: React.FC<ProjectExcelManagerProps> = ({
  project,
  onDataImported,
  onClose
}) => {
  const [users] = useAtom(usersAtom);
  const [isProcessing, setIsProcessing] = useState(false);
  const [importErrors, setImportErrors] = useState<string[]>([]);
  const [importSuccess, setImportSuccess] = useState(false);
  const [previewData, setPreviewData] = useState<ExcelPreviewRow[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [pendingSections, setPendingSections] = useState<any[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showSuccess, showError } = useNotification();

  // Download Excel template
  const downloadTemplate = async () => {
    try {
      setIsProcessing(true);

      // Download the actual template file from backend
      const blob = await projectsAPI.downloadExcelTemplate(project.id);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Create filename with project name
      const sanitizedProjectName = project.name.replace(/[^a-zA-Z0-9\s-_]/g, '').replace(/\s+/g, '_');
      link.download = `${sanitizedProjectName}_Template.xlsx`;

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showSuccess('Template Downloaded', `${sanitizedProjectName}_Template.xlsx downloaded successfully!`);
    } catch (error) {
      console.error('Error downloading template:', error);
      showError('Download Failed', 'Failed to download Excel template');
    } finally {
      setIsProcessing(false);
    }
  };

  // Download current project data
  const downloadProjectData = () => {
    try {
      setIsProcessing(true);

      console.log('🔍 Exporting project data:', project);
      console.log('🔍 Project sections:', project.sections);
      console.log('🔍 Available users:', users);

      // Pass users array to resolve names automatically
      const excelData = convertProjectToExcelData(project, users);

      console.log('🔍 Generated Excel data:', excelData);

      const worksheet = XLSX.utils.json_to_sheet(excelData);
      const workbook = XLSX.utils.book_new();

      // Set column widths
      const colWidths = [
        { wch: 15 }, { wch: 10 }, { wch: 25 }, { wch: 30 }, { wch: 15 },
        { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 10 }, { wch: 12 },
        { wch: 25 }, { wch: 30 }, { wch: 15 }, { wch: 12 }, { wch: 12 },
        { wch: 12 }, { wch: 10 }
      ];
      worksheet['!cols'] = colWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, 'Tasks & Subtasks');

      const fileName = `${project.name}_${project.code}_Data.xlsx`;
      XLSX.writeFile(workbook, fileName);

      showSuccess('Data Downloaded', 'Project data exported successfully!');
    } catch (error) {
      console.error('Error downloading project data:', error);
      showError('Export Failed', 'Failed to export project data');
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsProcessing(true);
    setImportErrors([]);
    setImportSuccess(false);

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });

        // Get the first sheet
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];

        // Convert to JSON
        const jsonData: ExcelTaskRow[] = XLSX.utils.sheet_to_json(worksheet);

        if (jsonData.length === 0) {
          setImportErrors(['Excel file is empty or has no valid data']);
          setIsProcessing(false);
          return;
        }

        // Generate preview data
        const preview = previewExcelData(jsonData, users, project);
        setPreviewData(preview);
        setShowPreview(true);

        // Also prepare the sections for potential import
        const { sections, errors } = convertExcelDataToProject(jsonData, users, project);
        setPendingSections(sections);

        if (errors.length > 0) {
          setImportErrors(errors);
        } else {
          setImportErrors([]);
        }

      } catch (error) {
        console.error('Error processing Excel file:', error);
        setImportErrors(['Failed to process Excel file. Please check the format and try again.']);
      } finally {
        setIsProcessing(false);
      }
    };

    reader.readAsArrayBuffer(file);

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Confirm import after preview
  const confirmImport = async () => {
    setIsProcessing(true);
    try {
      console.log('🔄 Starting import with sections:', pendingSections);
      console.log('🔄 Number of sections to import:', pendingSections.length);

      await onDataImported(pendingSections);

      setImportSuccess(true);
      setShowPreview(false);
      showSuccess('Import Successful', `Imported ${pendingSections.length} sections with tasks and subtasks`);
    } catch (error) {
      console.error('❌ Error importing data:', error);
      showError('Import Failed', 'Failed to import data');
    } finally {
      setIsProcessing(false);
    }
  };

  // Cancel import and go back to upload
  const cancelImport = () => {
    setShowPreview(false);
    setPreviewData([]);
    setPendingSections([]);
    setImportErrors([]);
    setImportSuccess(false);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-5xl w-full max-h-[95vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <h3 className="text-2xl font-bold text-gray-900 flex items-center">
            <FileSpreadsheet size={24} className="mr-3 text-blue-600" />
            Excel Import/Export
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="overflow-y-auto max-h-[calc(95vh-140px)]">
          {!showPreview ? (
            <div className="p-6">
              {/* Project Info Header */}
              <div className="mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                <div className="flex items-center mb-4">
                  <div className="p-2 bg-blue-100 rounded-lg mr-4">
                    <FileSpreadsheet className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="text-xl font-bold text-blue-900">Project: {project.name}</h4>
                    <p className="text-blue-700">Code: {project.code}</p>
                  </div>
                </div>
              </div>

              {/* Step-by-Step Process Guide */}
              <div className="mb-8">
                <h4 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                  <span className="text-2xl mr-3">📋</span>
                  Excel Import Process
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="relative">
                    <div className="flex items-center p-4 bg-blue-50 rounded-xl border-2 border-blue-200 hover:border-blue-300 transition-colors">
                      <div className="flex-shrink-0 w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center text-lg font-bold mr-4">1</div>
                      <div>
                        <h5 className="font-bold text-blue-900">Download Template</h5>
                        <p className="text-sm text-blue-700">Get the correct Excel format with examples</p>
                      </div>
                    </div>
                    <div className="hidden md:block absolute top-1/2 -right-3 w-6 h-0.5 bg-gray-300"></div>
                  </div>
                  <div className="relative">
                    <div className="flex items-center p-4 bg-yellow-50 rounded-xl border-2 border-yellow-200 hover:border-yellow-300 transition-colors">
                      <div className="flex-shrink-0 w-10 h-10 bg-yellow-600 text-white rounded-full flex items-center justify-center text-lg font-bold mr-4">2</div>
                      <div>
                        <h5 className="font-bold text-yellow-900">Fill Your Data</h5>
                        <p className="text-sm text-yellow-700">Add tasks, subtasks, and details</p>
                      </div>
                    </div>
                    <div className="hidden md:block absolute top-1/2 -right-3 w-6 h-0.5 bg-gray-300"></div>
                  </div>
                  <div>
                    <div className="flex items-center p-4 bg-green-50 rounded-xl border-2 border-green-200 hover:border-green-300 transition-colors">
                      <div className="flex-shrink-0 w-10 h-10 bg-green-600 text-white rounded-full flex items-center justify-center text-lg font-bold mr-4">3</div>
                      <div>
                        <h5 className="font-bold text-green-900">Upload & Import</h5>
                        <p className="text-sm text-green-700">Preview and confirm your import</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Main Action Cards */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* Download Template Card */}
                <div className="bg-white border-2 border-blue-200 rounded-2xl p-8 hover:border-blue-300 hover:shadow-lg transition-all">
                  <div className="flex items-center mb-6">
                    <div className="p-4 bg-blue-100 rounded-xl mr-4">
                      <Download className="h-10 w-10 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="text-2xl font-bold text-blue-900">Download Template</h4>
                      <p className="text-blue-700">Get the Excel template with sample data</p>
                    </div>
                  </div>

                  <div className="mb-6 p-4 bg-blue-50 rounded-xl">
                    <h6 className="font-bold text-blue-800 mb-3">📄 Template includes:</h6>
                    <div className="grid grid-cols-2 gap-2 text-sm text-blue-700">
                      <div className="flex items-center">
                        <span className="text-green-500 mr-2">✓</span>
                        Correct column headers
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-500 mr-2">✓</span>
                        Sample tasks & subtasks
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-500 mr-2">✓</span>
                        All milestone categories
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-500 mr-2">✓</span>
                        Formatting examples
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={downloadTemplate}
                    disabled={isProcessing}
                    className="w-full px-6 py-4 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors disabled:opacity-50 font-bold text-lg"
                  >
                    {isProcessing ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                        Generating Template...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <Download className="h-6 w-6 mr-3" />
                        Download Excel Template
                      </div>
                    )}
                  </button>
                </div>

                {/* Upload File Card */}
                <div className="bg-white border-2 border-green-200 rounded-2xl p-8 hover:border-green-300 hover:shadow-lg transition-all">
                  <div className="flex items-center mb-6">
                    <div className="p-4 bg-green-100 rounded-xl mr-4">
                      <Upload className="h-10 w-10 text-green-600" />
                    </div>
                    <div>
                      <h4 className="text-2xl font-bold text-green-900">Upload Excel File</h4>
                      <p className="text-green-700">Import your tasks and subtasks</p>
                    </div>
                  </div>

                  <div className="mb-6 p-4 bg-green-50 rounded-xl">
                    <h6 className="font-bold text-green-800 mb-3">📊 Supported features:</h6>
                    <div className="grid grid-cols-2 gap-2 text-sm text-green-700">
                      <div className="flex items-center">
                        <span className="text-green-500 mr-2">✓</span>
                        Excel files (.xlsx, .xls)
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-500 mr-2">✓</span>
                        Updates existing tasks
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-500 mr-2">✓</span>
                        Creates new items
                      </div>
                      <div className="flex items-center">
                        <span className="text-green-500 mr-2">✓</span>
                        Preview before import
                      </div>
                    </div>
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isProcessing}
                    className="w-full px-6 py-4 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors disabled:opacity-50 font-bold text-lg"
                  >
                    {isProcessing ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                        Processing File...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <Upload className="h-6 w-6 mr-3" />
                        Choose Excel File
                      </div>
                    )}
                  </button>
                </div>
              </div>

              {/* Export Current Data */}
              <div className="mb-8 p-6 bg-gray-50 rounded-xl border border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-3 bg-gray-100 rounded-lg mr-4">
                      <Download className="h-8 w-8 text-gray-600" />
                    </div>
                    <div>
                      <h5 className="text-lg font-bold text-gray-900">Export Current Project Data</h5>
                      <p className="text-gray-600">Download existing tasks and subtasks as Excel file</p>
                    </div>
                  </div>
                  <button
                    onClick={downloadProjectData}
                    disabled={isProcessing}
                    className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 font-medium"
                  >
                    {isProcessing ? 'Exporting...' : 'Export Data'}
                  </button>
                </div>
              </div>

              {/* Status Messages */}
              {importErrors.length > 0 && (
                <div className="mb-6 p-6 bg-red-50 border-l-4 border-red-400 rounded-xl">
                  <div className="flex items-center mb-4">
                    <AlertCircle className="h-8 w-8 text-red-600 mr-3" />
                    <h5 className="text-xl font-bold text-red-800">Import Errors Found</h5>
                  </div>
                  <div className="bg-white rounded-lg p-4 max-h-40 overflow-y-auto">
                    <ul className="space-y-2">
                      {importErrors.map((error, index) => (
                        <li key={index} className="flex items-start text-sm text-red-700">
                          <span className="text-red-500 mr-2 mt-0.5">•</span>
                          {error}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {importSuccess && (
                <div className="mb-6 p-6 bg-green-50 border-l-4 border-green-400 rounded-xl">
                  <div className="flex items-center">
                    <CheckCircle className="h-8 w-8 text-green-600 mr-3" />
                    <div>
                      <h5 className="text-xl font-bold text-green-800">Import Successful!</h5>
                      <p className="text-green-700 mt-1">
                        Tasks and subtasks have been imported successfully.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Instructions & Guidelines */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* How to Use */}
                <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                  <h5 className="text-xl font-bold text-gray-800 mb-6 flex items-center">
                    <span className="text-2xl mr-3">📝</span>
                    How to Use
                  </h5>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <span className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">1</span>
                      <div>
                        <p className="font-medium text-gray-800">Download the template</p>
                        <p className="text-sm text-gray-600">Get the correct Excel format with sample data</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <span className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">2</span>
                      <div>
                        <p className="font-medium text-gray-800">Fill in your data</p>
                        <p className="text-sm text-gray-600">Only TaskName and MilestoneCategory are required</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <span className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">3</span>
                      <div>
                        <p className="font-medium text-gray-800">Upload and preview</p>
                        <p className="text-sm text-gray-600">Review how data will be processed before importing</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <span className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">4</span>
                      <div>
                        <p className="font-medium text-gray-800">Confirm import</p>
                        <p className="text-sm text-gray-600">Complete the import process</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Important Notes */}
                <div className="bg-yellow-50 rounded-xl p-6 border border-yellow-200">
                  <h5 className="text-xl font-bold text-yellow-800 mb-6 flex items-center">
                    <span className="text-2xl mr-3">⚠️</span>
                    Important Notes
                  </h5>
                  <div className="space-y-4">
                    <div className="p-4 bg-white rounded-lg border border-yellow-200">
                      <h6 className="font-bold text-yellow-800 mb-2">Task Updates</h6>
                      <p className="text-sm text-yellow-700">Tasks with same name in same section will be updated with new data</p>
                    </div>
                    <div className="p-4 bg-white rounded-lg border border-yellow-200">
                      <h6 className="font-bold text-yellow-800 mb-2">Subtask Safety</h6>
                      <p className="text-sm text-yellow-700">Existing subtasks cannot be moved between parent tasks to prevent data corruption</p>
                    </div>
                    <div className="p-4 bg-white rounded-lg border border-yellow-200">
                      <h6 className="font-bold text-yellow-800 mb-2">Optional Fields</h6>
                      <p className="text-sm text-yellow-700">Dates, assignee, status, and priority are optional - leave blank to keep existing values</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Preview Section */
            <div>
              <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="font-semibold text-yellow-900 mb-2">📋 Import Preview</h4>
                <p className="text-yellow-800 text-sm">
                  Review how your Excel data will be processed before importing. Check assignments, dates, and actions.
                </p>
              </div>

              {/* Preview Summary */}
              <div className="mb-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-green-50 p-3 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {previewData.filter(p => p.action.includes('CREATE')).length}
                  </div>
                  <div className="text-sm text-green-700">Will Create</div>
                </div>
                <div className="bg-blue-50 p-3 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {previewData.filter(p => p.action.includes('UPDATE')).length}
                  </div>
                  <div className="text-sm text-blue-700">Will Update</div>
                </div>
                <div className="bg-red-50 p-3 rounded-lg text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {previewData.filter(p => p.action === 'ERROR').length}
                  </div>
                  <div className="text-sm text-red-700">Errors</div>
                </div>
                <div className="bg-yellow-50 p-3 rounded-lg text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {previewData.reduce((sum, p) => sum + p.warnings.length, 0)}
                  </div>
                  <div className="text-sm text-yellow-700">Warnings</div>
                </div>
              </div>

              {/* Preview Table */}
              <div className="mb-6 border border-gray-200 rounded-lg overflow-hidden">
                <div className="max-h-96 overflow-y-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-gray-50 sticky top-0">
                      <tr>
                        <th className="px-3 py-2 text-left font-medium text-gray-700">Row</th>
                        <th className="px-3 py-2 text-left font-medium text-gray-700">Action</th>
                        <th className="px-3 py-2 text-left font-medium text-gray-700">Task Name</th>
                        <th className="px-3 py-2 text-left font-medium text-gray-700">Section</th>
                        <th className="px-3 py-2 text-left font-medium text-gray-700">Will Assign To</th>
                        <th className="px-3 py-2 text-left font-medium text-gray-700">Status</th>
                        <th className="px-3 py-2 text-left font-medium text-gray-700">Issues</th>
                      </tr>
                    </thead>
                    <tbody>
                      {previewData.map((row, index) => (
                        <tr key={index} className={`border-t ${
                          row.action === 'ERROR' ? 'bg-red-50' :
                          row.warnings.length > 0 ? 'bg-yellow-50' : 'bg-white'
                        }`}>
                          <td className="px-3 py-2">{row.rowNumber}</td>
                          <td className="px-3 py-2">
                            <span className={`px-2 py-1 rounded text-xs font-medium ${
                              row.action === 'CREATE_TASK' ? 'bg-green-100 text-green-800' :
                              row.action === 'UPDATE_TASK' ? 'bg-blue-100 text-blue-800' :
                              row.action === 'CREATE_SUBTASK' ? 'bg-green-100 text-green-800' :
                              row.action === 'UPDATE_SUBTASK' ? 'bg-blue-100 text-blue-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {row.action.replace('_', ' ')}
                            </span>
                          </td>
                          <td className="px-3 py-2 font-medium">{row.taskName}</td>
                          <td className="px-3 py-2">{row.sectionName}</td>
                          <td className="px-3 py-2">{row.willBeAssigned.assigneeName}</td>
                          <td className="px-3 py-2">{row.status || 'NOT_STARTED'}</td>
                          <td className="px-3 py-2">
                            {row.errors.length > 0 && (
                              <div className="text-red-600 text-xs mb-1">
                                {row.errors.map((error, i) => (
                                  <div key={i}>❌ {error}</div>
                                ))}
                              </div>
                            )}
                            {row.warnings.length > 0 && (
                              <div className="text-yellow-600 text-xs">
                                {row.warnings.slice(0, 2).map((warning, i) => (
                                  <div key={i}>⚠️ {warning}</div>
                                ))}
                                {row.warnings.length > 2 && (
                                  <div>... +{row.warnings.length - 2} more</div>
                                )}
                              </div>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Import Errors */}
              {importErrors.length > 0 && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center mb-2">
                    <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                    <h5 className="font-semibold text-red-800">Critical Errors</h5>
                  </div>
                  <ul className="list-disc list-inside text-sm text-red-700">
                    {importErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Preview Actions */}
              <div className="flex justify-between items-center">
                <button
                  onClick={cancelImport}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                >
                  ← Back to Upload
                </button>
                <div className="flex space-x-3">
                  <button
                    onClick={confirmImport}
                    disabled={isProcessing || previewData.some(p => p.action === 'ERROR')}
                    className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isProcessing ? 'Importing...' : 'Confirm Import'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectExcelManager;
