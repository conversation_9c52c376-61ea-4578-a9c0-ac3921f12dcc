const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function testPaymentRecords() {
  try {
    console.log('=== TESTING PAYMENT RECORDS ENDPOINT ===\n');

    // Get all payment records with project and user details
    const paymentRecords = await prisma.payment.findMany({
      include: {
        project: {
          select: {
            name: true,
            code: true,
            customer: {
              select: {
                name: true
              }
            }
          }
        },
        user: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        paymentDate: 'desc'
      }
    });

    console.log(`Found ${paymentRecords.length} payment records:\n`);

    // Format the response
    const formattedRecords = paymentRecords.map((payment, index) => {
      const formatted = {
        id: payment.id,
        projectId: payment.projectId,
        projectName: payment.project.name,
        projectCode: payment.project.code,
        customerName: payment.project.customer?.name || 'Unknown',
        amount: Number(payment.amount),
        paymentDate: payment.paymentDate.toISOString(),
        paymentMethod: payment.paymentMethod,
        referenceNumber: payment.referenceNumber,
        description: payment.description,
        createdBy: payment.createdBy,
        createdAt: payment.createdAt.toISOString(),
        user: {
          name: payment.user.name,
          email: payment.user.email
        }
      };

      console.log(`${index + 1}. Payment Record:`);
      console.log(`   ID: ${formatted.id}`);
      console.log(`   Project: ${formatted.projectName} (${formatted.projectCode})`);
      console.log(`   Customer: ${formatted.customerName}`);
      console.log(`   Amount: ₹${formatted.amount.toLocaleString('en-IN')}`);
      console.log(`   Date: ${new Date(formatted.paymentDate).toLocaleDateString('en-IN')}`);
      console.log(`   Method: ${formatted.paymentMethod}`);
      console.log(`   Reference: ${formatted.referenceNumber || 'N/A'}`);
      console.log(`   Created by: ${formatted.user.name} (${formatted.user.email})`);
      console.log('');

      return formatted;
    });

    const totalAmount = formattedRecords.reduce((sum, record) => sum + record.amount, 0);

    console.log('=== SUMMARY ===');
    console.log(`Total Records: ${formattedRecords.length}`);
    console.log(`Total Amount: ₹${totalAmount.toLocaleString('en-IN')}`);

    console.log('\n=== API RESPONSE FORMAT ===');
    console.log(JSON.stringify({
      success: true,
      data: {
        payments: formattedRecords,
        total: formattedRecords.length
      }
    }, null, 2));

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPaymentRecords();
