import express, { Request, Response } from 'express';
import {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
} from '../controllers/customer.controller';
import { protect, authorize } from '../middleware/auth.middleware';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(getCustomers) // All authenticated users can view customers
  .post(
    authorize('DIRECTOR'), // Only DIRECTOR can create customers
    createCustomer
  );

router.route('/:id')
  .get(getCustomer) // All authenticated users can view individual customer
  .put(
    authorize('DIRECTOR'), // Only DIRECTOR can update customers
    updateCustomer
  )
  .delete(
    authorize('DIRECTOR'), // Only DIRECTOR can delete customers
    deleteCustomer
  );

export default router;
