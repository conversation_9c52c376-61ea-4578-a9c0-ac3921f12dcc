@tailwind base;
@tailwind components;
@tailwind utilities;

/* Real-time validation animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-4px);
  }
  75% {
    transform: translateX(4px);
  }
}

@keyframes pulse-error {
  0%, 100% {
    border-color: rgb(239 68 68);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
  }
  50% {
    border-color: rgb(220 38 38);
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
  }
}

@keyframes pulse-success {
  0%, 100% {
    border-color: rgb(34 197 94);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }
  50% {
    border-color: rgb(22 163 74);
    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.1);
  }
}

/* Utility classes */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-pulse-error {
  animation: pulse-error 1.5s ease-in-out infinite;
}

.animate-pulse-success {
  animation: pulse-success 1.5s ease-in-out infinite;
}

/* Input validation states */
.input-error {
  @apply border-red-500 bg-red-50 focus:border-red-500 focus:ring-red-500/20;
  animation: shake 0.5s ease-in-out;
}

.input-success {
  @apply border-green-500 bg-green-50 focus:border-green-500 focus:ring-green-500/20;
}

.input-warning {
  @apply border-yellow-500 bg-yellow-50 focus:border-yellow-500 focus:ring-yellow-500/20;
}

/* Real-time error message styling */
.error-message {
  @apply text-red-600 text-sm flex items-start space-x-2;
  animation: fadeIn 0.3s ease-out;
}

.success-message {
  @apply text-green-600 text-sm flex items-start space-x-2;
  animation: fadeIn 0.3s ease-out;
}

.warning-message {
  @apply text-yellow-600 text-sm flex items-start space-x-2;
  animation: fadeIn 0.3s ease-out;
}

/* Character counter styling */
.char-counter {
  @apply text-xs transition-colors duration-200;
}

.char-counter.warning {
  @apply text-yellow-600;
}

.char-counter.error {
  @apply text-red-600 font-medium;
}

/* Form validation indicators */
.validation-icon {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 transition-all duration-200;
}

.validation-icon.error {
  @apply text-red-500;
  animation: shake 0.5s ease-in-out;
}

.validation-icon.success {
  @apply text-green-500;
  animation: fadeIn 0.3s ease-out;
}

/* Loading states for validation */
.validation-loading {
  @apply animate-spin text-gray-400;
}

/* Smooth transitions for all form elements */
input, select, textarea {
  @apply transition-all duration-200 ease-in-out;
}

/* Focus states with enhanced visual feedback */
input:focus, select:focus, textarea:focus {
  @apply outline-none ring-2 ring-opacity-50;
  transform: scale(1.01);
}

/* Disabled state styling */
input:disabled, select:disabled, textarea:disabled {
  @apply opacity-50 cursor-not-allowed bg-gray-100;
}

/* Custom scrollbar for textareas */
textarea::-webkit-scrollbar {
  width: 6px;
}

textarea::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

textarea::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded hover:bg-gray-400;
}

@layer base {
  :root {
    --primary: 221 70% 33%;
    --primary-light: 221 70% 43%;
    --primary-dark: 221 70% 23%;

    --secondary: 174 89% 32%;
    --secondary-light: 174 89% 42%;
    --secondary-dark: 174 89% 22%;

    --accent: 25 95% 53%;
    --accent-light: 25 95% 63%;
    --accent-dark: 25 95% 43%;

    --success: 142 76% 36%;
    --warning: 38 92% 50%;
    --error: 0 84% 60%;

    --gray-50: 0 0% 98%;
    --gray-100: 0 0% 96%;
    --gray-200: 0 0% 90%;
    --gray-300: 0 0% 83%;
    --gray-400: 0 0% 64%;
    --gray-500: 0 0% 45%;
    --gray-600: 0 0% 32%;
    --gray-700: 0 0% 25%;
    --gray-800: 0 0% 15%;
    --gray-900: 0 0% 9%;
    --gray-950: 0 0% 4%;
  }


}

@layer base {
  body {
    @apply bg-gray-50 text-gray-900 font-sans antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-medium leading-tight;
  }

  h1 {
    @apply text-3xl md:text-4xl;
  }

  h2 {
    @apply text-2xl md:text-3xl;
  }

  h3 {
    @apply text-xl md:text-2xl;
  }

  h4 {
    @apply text-lg md:text-xl;
  }

  h5 {
    @apply text-base md:text-lg;
  }

  h6 {
    @apply text-sm md:text-base;
  }

  p {
    @apply leading-relaxed;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-light active:bg-primary-dark focus:ring-primary;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-light active:bg-secondary-dark focus:ring-secondary;
  }

  .btn-accent {
    @apply bg-accent text-white hover:bg-accent-light active:bg-accent-dark focus:ring-accent;
  }

  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100 focus:ring-gray-400;
  }

  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-colors duration-300;
  }

  .dark .card {
    @apply bg-gray-800;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-300;
  }

  .dark .form-input {
    @apply bg-gray-700 border-gray-600 text-gray-100;
  }

  .form-select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-300;
  }

  .dark .form-select {
    @apply bg-gray-700 border-gray-600 text-gray-100;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .badge {
    @apply inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-success {
    @apply bg-success/20 text-success-dark;
  }

  .badge-warning {
    @apply bg-warning/20 text-warning-dark;
  }

  .badge-error {
    @apply bg-error/20 text-error-dark;
  }

  .badge-gray {
    @apply bg-gray-200 text-gray-800;
  }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-in-out;
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(10px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}