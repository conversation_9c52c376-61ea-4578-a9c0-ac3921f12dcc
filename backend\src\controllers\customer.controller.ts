import { Request, Response } from 'express';
import { prisma } from '../index';
import { validateName, validateEmail, validateCode, validatePhone } from '../utils/auth.utils';

// @desc    Get all customers
// @route   GET /api/customers
// @access  Private
export const getCustomers = async (req: Request, res: Response): Promise<void> => {
  try {
    const customers = await prisma.customer.findMany({
      include: {
        _count: {
          select: {
            project: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    res.status(200).json({
      success: true,
      count: customers.length,
      data: customers,
    });
  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single customer
// @route   GET /api/customers/:id
// @access  Private
export const getCustomer = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        project: true,
      },
    });

    if (!customer) {
      res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('Get customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create customer
// @route   POST /api/customers
// @access  Private/Admin
export const createCustomer = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, code, contactName, email, phone, address } = req.body;

    // Validate required fields
    if (!name) {
      res.status(400).json({
        success: false,
        message: 'Please provide a customer name',
      });
      return;
    }

    // Validate customer name
    const nameValidation = validateName(name);
    if (!nameValidation.isValid) {
      res.status(400).json({
        success: false,
        message: nameValidation.message
      });
      return;
    }

    // Validate customer code if provided
    if (code) {
      const codeValidation = validateCode(code, 'Customer code');
      if (!codeValidation.isValid) {
        res.status(400).json({
          success: false,
          message: codeValidation.message
        });
        return;
      }
    }

    // Validate contact name if provided
    if (contactName) {
      const contactNameValidation = validateName(contactName);
      if (!contactNameValidation.isValid) {
        res.status(400).json({
          success: false,
          message: `Contact ${contactNameValidation.message}`
        });
        return;
      }
    }

    // Validate email if provided
    if (email) {
      const emailValidation = validateEmail(email);
      if (!emailValidation.isValid) {
        res.status(400).json({
          success: false,
          message: emailValidation.message
        });
        return;
      }
    }

    // Validate phone if provided
    if (phone) {
      const phoneValidation = validatePhone(phone);
      if (!phoneValidation.isValid) {
        res.status(400).json({
          success: false,
          message: phoneValidation.message
        });
        return;
      }
    }

    console.log('Creating customer with data:', {
      name,
      code: code || 'Not provided',
      contactName: contactName || 'Not provided',
      email: email || 'Not provided',
      phone: phone || 'Not provided',
      address: address || 'Not provided',
    });

    // Check if customer with same name or code already exists
    if (code) {
      const existingCustomer = await prisma.customer.findFirst({
        where: {
          OR: [
            { name },
            { code },
          ],
        },
      });

      if (existingCustomer) {
        res.status(400).json({
          success: false,
          message: 'Customer with this name or code already exists',
        });
        return;
      }
    }

    // Generate a unique ID for the customer
    const { v4: uuidv4 } = require('uuid');
    const customerId = uuidv4();

    // Create customer
    const customer = await prisma.customer.create({
      data: {
        id: customerId,
        name,
        code,
        contactName,
        email,
        phone,
        address,
      },
    });

    console.log('Customer created successfully:', customer.id);

    res.status(201).json({
      success: true,
      data: customer,
    });
  } catch (error: any) {
    console.error('Create customer error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      res.status(400).json({
        success: false,
        message: `A customer with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during customer creation',
    });
  }
};

// @desc    Update customer
// @route   PUT /api/customers/:id
// @access  Private/Admin
export const updateCustomer = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, code, contactName, email, phone, address } = req.body;

    console.log('Updating customer with ID:', id);
    console.log('Update data:', {
      name: name || 'Not changed',
      code: code || 'Not changed',
      contactName: contactName || 'Not changed',
      email: email || 'Not changed',
      phone: phone || 'Not changed',
      address: address || 'Not changed',
    });

    // Check if customer exists
    const customerExists = await prisma.customer.findUnique({
      where: { id },
    });

    if (!customerExists) {
      res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
      return;
    }

    // Validate customer name if provided
    if (name) {
      const nameValidation = validateName(name);
      if (!nameValidation.isValid) {
        res.status(400).json({
          success: false,
          message: nameValidation.message
        });
        return;
      }
    }

    // Validate customer code if provided
    if (code) {
      const codeValidation = validateCode(code, 'Customer code');
      if (!codeValidation.isValid) {
        res.status(400).json({
          success: false,
          message: codeValidation.message
        });
        return;
      }
    }

    // Validate contact name if provided
    if (contactName) {
      const contactNameValidation = validateName(contactName);
      if (!contactNameValidation.isValid) {
        res.status(400).json({
          success: false,
          message: `Contact ${contactNameValidation.message}`
        });
        return;
      }
    }

    // Validate email if provided
    if (email) {
      const emailValidation = validateEmail(email);
      if (!emailValidation.isValid) {
        res.status(400).json({
          success: false,
          message: emailValidation.message
        });
        return;
      }
    }

    // Validate phone if provided
    if (phone) {
      const phoneValidation = validatePhone(phone);
      if (!phoneValidation.isValid) {
        res.status(400).json({
          success: false,
          message: phoneValidation.message
        });
        return;
      }
    }

    // Check for duplicate name or code (excluding current customer)
    if (name || code) {
      const duplicateCustomer = await prisma.customer.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                ...(name ? [{ name }] : []),
                ...(code ? [{ code }] : []),
              ],
            },
          ],
        },
      });

      if (duplicateCustomer) {
        res.status(400).json({
          success: false,
          message: 'Customer with this name or code already exists',
        });
        return;
      }
    }

    // Prepare update data - only include fields that are provided
    const updateData: any = {};

    if (name !== undefined) updateData.name = name;
    if (code !== undefined) updateData.code = code;
    if (contactName !== undefined) updateData.contactName = contactName;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (address !== undefined) updateData.address = address;

    // Update customer
    const customer = await prisma.customer.update({
      where: { id },
      data: updateData,
    });

    console.log('Customer updated successfully:', customer.id);

    res.status(200).json({
      success: true,
      data: customer,
    });
  } catch (error: any) {
    console.error('Update customer error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      res.status(400).json({
        success: false,
        message: `A customer with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during customer update',
    });
  }
};

// @desc    Delete customer
// @route   DELETE /api/customers/:id
// @access  Private/Admin
export const deleteCustomer = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    console.log('Deleting customer with ID:', id);

    // Check if customer exists
    const customerExists = await prisma.customer.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            project: true,
          },
        },
      },
    });

    if (!customerExists) {
      res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
      return;
    }

    // Check if customer has related projects
    if (customerExists._count.project > 0) {
      res.status(400).json({
        success: false,
        message: 'Cannot delete customer with associated projects',
      });
      return;
    }

    // Delete customer
    await prisma.customer.delete({
      where: { id },
    });

    console.log('Customer deleted successfully:', id);

    res.status(200).json({
      success: true,
      message: 'Customer deleted successfully',
    });
  } catch (error: any) {
    console.error('Delete customer error:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Server error during customer deletion',
    });
  }
};
