import express from 'express';
import {
  getAlerts,
  getAlert,
  createAlert,
  updateAlert,
  deleteAlert,
  markAsRead,
  createSystemAlert,
  checkDeadlineAlerts,
  cleanupOldAlerts,
  getUnreadAlertsCount,
} from '../controllers/alert.controller';
import { protect, authorize } from '../middleware/auth.middleware';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(getAlerts)
  .post(createAlert);

router.route('/unread-count')
  .get(getUnreadAlertsCount);

router.route('/system')
  .post(authorize('DIRECTOR'), createSystemAlert);

router.route('/cleanup')
  .post(authorize('DIRECTOR'), cleanupOldAlerts);

router.route('/check-deadlines')
  .post(checkDeadlineAlerts);

router.route('/:id')
  .get(getAlert)
  .put(updateAlert)
  .delete(deleteAlert);

router.route('/:id/read')
  .put(markAsRead);

export default router;
