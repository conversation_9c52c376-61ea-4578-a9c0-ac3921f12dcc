import React, { useState, useRef, useEffect, KeyboardEvent } from 'react';
import { Plus, Trash2 } from 'lucide-react';
import './EditableGrid.css';

export interface GridColumn {
  id: string;
  header: string;
  width?: string;
  type?: 'text' | 'date' | 'select' | 'email';
  options?: { value: string; label: string }[];
  required?: boolean;
  validator?: (value: any) => boolean;
}

export interface GridRow {
  id: string;
  [key: string]: any;
}

interface EditableGridProps {
  columns: GridColumn[];
  rows: GridRow[];
  onRowsChange: (rows: GridRow[]) => void;
  onAddRow?: () => void;
  onRemoveRow?: (rowId: string) => void;
  addRowButtonText?: string;
  className?: string;
  emptyMessage?: string;
  renderCustomCell?: (row: GridRow, columnId: string) => React.ReactNode;
}

const EditableGrid: React.FC<EditableGridProps> = ({
  columns,
  rows,
  onRowsChange,
  onAddRow,
  onRemoveRow,
  addRowButtonText = 'Add Row',
  className = '',
  emptyMessage = 'No data available',
  renderCustomCell
}) => {
  const [activeCell, setActiveCell] = useState<{ rowId: string; colId: string } | null>(null);
  const cellRefs = useRef<{ [key: string]: HTMLTableCellElement }>({});

  // Handle cell value change
  const handleCellChange = (rowId: string, colId: string, value: any) => {
    const updatedRows = rows.map(row => {
      if (row.id === rowId) {
        return { ...row, [colId]: value };
      }
      return row;
    });
    onRowsChange(updatedRows);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: KeyboardEvent<HTMLTableCellElement>, rowIndex: number, colIndex: number) => {
    if (!activeCell) return;

    switch (e.key) {
      case 'Tab':
        e.preventDefault();
        navigateToNextCell(rowIndex, colIndex, !e.shiftKey);
        break;
      case 'ArrowUp':
        e.preventDefault();
        navigateToCell(rowIndex - 1, colIndex);
        break;
      case 'ArrowDown':
        e.preventDefault();
        navigateToCell(rowIndex + 1, colIndex);
        break;
      case 'ArrowLeft':
        navigateToCell(rowIndex, colIndex - 1);
        break;
      case 'ArrowRight':
        navigateToCell(rowIndex, colIndex + 1);
        break;
      case 'Enter':
        e.preventDefault();
        if (e.shiftKey) {
          navigateToCell(rowIndex - 1, colIndex);
        } else {
          navigateToCell(rowIndex + 1, colIndex);
        }
        break;
    }
  };

  // Navigate to the next/previous cell
  const navigateToNextCell = (currentRowIndex: number, currentColIndex: number, forward: boolean) => {
    if (forward) {
      if (currentColIndex < columns.length - 1) {
        navigateToCell(currentRowIndex, currentColIndex + 1);
      } else if (currentRowIndex < rows.length - 1) {
        navigateToCell(currentRowIndex + 1, 0);
      } else if (onAddRow) {
        onAddRow();
        setTimeout(() => {
          navigateToCell(rows.length, 0);
        }, 0);
      }
    } else {
      if (currentColIndex > 0) {
        navigateToCell(currentRowIndex, currentColIndex - 1);
      } else if (currentRowIndex > 0) {
        navigateToCell(currentRowIndex - 1, columns.length - 1);
      }
    }
  };

  // Navigate to a specific cell
  const navigateToCell = (rowIndex: number, colIndex: number) => {
    if (rowIndex >= 0 && rowIndex < rows.length && colIndex >= 0 && colIndex < columns.length) {
      const rowId = rows[rowIndex].id;
      const colId = columns[colIndex].id;
      setActiveCell({ rowId, colId });
    }
  };

  // Focus on the active cell
  useEffect(() => {
    if (activeCell) {
      const cellKey = `${activeCell.rowId}-${activeCell.colId}`;
      const cellElement = cellRefs.current[cellKey];
      if (cellElement) {
        const inputElement = cellElement.querySelector('input, select') as HTMLElement;
        if (inputElement) {
          inputElement.focus();
        }
      }
    }
  }, [activeCell]);

  // Render cell content based on column type
  const renderCellContent = (row: GridRow, column: GridColumn, rowIndex: number, colIndex: number) => {
    const isActive = activeCell?.rowId === row.id && activeCell?.colId === column.id;
    const cellKey = `${row.id}-${column.id}`;
    const value = row[column.id] !== undefined ? row[column.id] : '';

    return (
      <td
        key={cellKey}
        className={`${isActive ? 'selected' : ''}`}
        onClick={() => setActiveCell({ rowId: row.id, colId: column.id })}
        ref={el => {
          if (el) cellRefs.current[cellKey] = el;
        }}
        onKeyDown={(e) => handleKeyDown(e, rowIndex, colIndex)}
        tabIndex={0}
      >
        {renderCustomCell && renderCustomCell(row, column.id) ? (
          renderCustomCell(row, column.id)
        ) : column.type === 'select' ? (
          <select
            value={value}
            onChange={(e) => handleCellChange(row.id, column.id, e.target.value)}
            className="w-full bg-transparent focus:outline-none"
            onFocus={() => setActiveCell({ rowId: row.id, colId: column.id })}
          >
            <option value="">Select...</option>
            {column.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        ) : (
          <input
            type={column.type || 'text'}
            value={value}
            onChange={(e) => handleCellChange(row.id, column.id, e.target.value)}
            className="w-full bg-transparent focus:outline-none"
            placeholder={`Enter ${column.header.toLowerCase()}`}
            onFocus={() => setActiveCell({ rowId: row.id, colId: column.id })}
          />
        )}
      </td>
    );
  };

  return (
    <div className={`editable-grid ${className}`}>
      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-200">
          <thead>
            <tr>
              <th className="row-number">#</th>
              {columns.map((column, index) => (
                <th
                  key={column.id}
                  style={{ width: column.width }}
                >
                  {column.header}
                  {column.required && <span className="text-error ml-1">*</span>}
                </th>
              ))}
              {onRemoveRow && <th className="w-10"></th>}
            </tr>
          </thead>
          <tbody>
            {rows.length > 0 ? (
              rows.map((row, rowIndex) => (
                <tr key={row.id}>
                  <td className="row-number">{rowIndex + 1}</td>
                  {columns.map((column, colIndex) => (
                    renderCellContent(row, column, rowIndex, colIndex)
                  ))}
                  {onRemoveRow && (
                    <td className="text-center">
                      <button
                        onClick={() => onRemoveRow(row.id)}
                        className="remove-row-btn"
                        title="Remove row"
                      >
                        <Trash2 size={16} />
                      </button>
                    </td>
                  )}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={columns.length + 1 + (onRemoveRow ? 1 : 0)} className="text-center text-gray-500 py-4">
                  {emptyMessage}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {onAddRow && (
        <div className="mt-2">
          <button
            type="button"
            onClick={onAddRow}
            className="add-row-btn"
          >
            <Plus size={16} className="mr-1" />
            {addRowButtonText}
          </button>
        </div>
      )}
    </div>
  );
};

export default EditableGrid;
