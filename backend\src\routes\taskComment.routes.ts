import express from 'express';
import {
  getTaskComments,
  createTaskComment,
  updateTaskComment,
  deleteTaskComment,
} from '../controllers/taskComment.controller';
import { protect } from '../middleware/auth.middleware';

const router = express.Router({ mergeParams: true });

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(getTaskComments)
  .post(createTaskComment);

router.route('/:commentId')
  .put(updateTaskComment)
  .delete(deleteTaskComment);

export default router;
