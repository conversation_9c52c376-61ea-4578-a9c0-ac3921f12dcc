/*
  Warnings:

  - You are about to drop the column `projectType` on the `project` table. All the data in the column will be lost.
  - Added the required column `projectCategory` to the `project` table without a default value. This is not possible if the table is not empty.

*/

-- Step 1: Add the new projectCategory column with a default value
ALTER TABLE `project` ADD COLUMN `projectCategory` ENUM('PROJECTS', 'PRECISION_PROJECTS', 'SPARE', 'SERVICE') NOT NULL DEFAULT 'PROJECTS';

-- Step 2: Copy data from projectType to projectCategory
UPDATE `project` SET `projectCategory` = `projectType`;

-- Step 3: Drop the old projectType column
ALTER TABLE `project` DROP COLUMN `projectType`;
