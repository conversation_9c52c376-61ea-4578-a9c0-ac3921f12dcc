import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAtom } from 'jotai';
import { currentUserAtom, isAuthenticatedAtom, isDataLoadingAtom } from '../../store';
import { LogIn, CheckCircle2, AlertCircle } from 'lucide-react';
import { authAPI } from '../../services/api';
import { normalizeEmail } from '../../utils/dateValidation';

const LoginForm: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  const [, setCurrentUser] = useAtom(currentUserAtom);
  const [, setIsAuthenticated] = useAtom(isAuthenticatedAtom);
  const [isDataLoading] = useAtom(isDataLoadingAtom);

  const navigate = useNavigate();

  // Handle navigation after data loading completes
  useEffect(() => {
    // If user is authenticated, login was successful, and data loading is complete
    if (success && !isDataLoading && !loading) {
      const timer = setTimeout(() => {
        navigate('/dashboard');
      }, 500); // Small delay to show success message

      return () => clearTimeout(timer);
    }
  }, [success, isDataLoading, loading, navigate]);

  // Update loading state based on data loading
  useEffect(() => {
    if (success && !isDataLoading) {
      setLoading(false);
      setSuccess('Login successful! Redirecting...');
    } else if (success && isDataLoading) {
      setLoading(true);
      setSuccess('Login successful! Loading your data...');
    }
  }, [success, isDataLoading]);

  // Show detailed loading progress
  const getLoadingMessage = () => {
    if (!loading) return '';
    if (success && isDataLoading) return 'Loading your data...';
    if (success) return 'Login successful! Redirecting...';
    return 'Signing in...';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Normalize email before sending to API
      const normalizedEmail = normalizeEmail(email);

      // Call the login API
      const response = await authAPI.login(normalizedEmail, password);

      // Save token and user data to localStorage
      localStorage.setItem('token', response.data.token);

      // Store user data without the token to avoid duplication
      const { token, ...userData } = response.data;
      localStorage.setItem('user', JSON.stringify(userData));

      // Update state
      setSuccess('Login successful! Loading your data...');
      console.log('🔐 Setting authentication state after login:', userData.name);
      setCurrentUser(userData);
      setIsAuthenticated(true);
      console.log('✅ Authentication state updated, should trigger data loading');

      // Don't set loading to false yet - keep it true until data loading completes
      // The loading state will be managed by the data loading process
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Invalid email or password');
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md">
      <form onSubmit={handleSubmit} className="card p-8 space-y-6">
        <div className="mb-2 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-1">Welcome Back</h2>
          <p className="text-gray-600">Sign in to access your account</p>
        </div>

        {error && (
          <div className="bg-error/10 text-error-dark p-3 rounded-md flex items-center space-x-2">
            <AlertCircle size={18} />
            <span>{error}</span>
          </div>
        )}

        {success && (
          <div className="bg-success/10 text-success-dark p-3 rounded-md flex items-center space-x-2">
            <CheckCircle2 size={18} />
            <span>{success}</span>
          </div>
        )}

        <div className="space-y-4">
          <div>
            <label htmlFor="email" className="form-label">Email</label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="form-input"
              placeholder="Enter your email"
              required
            />
          </div>

          <div>
            <div className="flex justify-between mb-1">
              <label htmlFor="password" className="form-label">Password</label>
              <a href="#" className="text-sm text-primary hover:text-primary-dark transition-colors">
                Forgot password?
              </a>
            </div>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="form-input"
              placeholder="Enter your password"
              required
            />
          </div>

          <div className="pt-2">
            <button
              type="submit"
              className="btn btn-primary w-full flex items-center justify-center"
              disabled={loading}
            >
              {loading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {getLoadingMessage()}
                </span>
              ) : (
                <span className="flex items-center">
                  <LogIn size={18} className="mr-2" />
                  Sign In
                </span>
              )}
            </button>
          </div>
        </div>

        <div className="text-center text-sm text-gray-600">
          <p>For demo purposes, use any of these accounts:</p>
          <div className="mt-2 text-xs space-y-1 text-left bg-gray-50 p-2 rounded border border-gray-200">
            <p><strong>Director:</strong> <EMAIL> / password</p>
            <p><strong>General Manager:</strong> <EMAIL> / password</p>
            <p><strong>Manager:</strong> <EMAIL> / password</p>
            <p><strong>Team Lead:</strong> <EMAIL> / password</p>
            <p><strong>Engineer:</strong> <EMAIL> / password</p>
          </div>
        </div>
      </form>
    </div>
  );
};

export default LoginForm;