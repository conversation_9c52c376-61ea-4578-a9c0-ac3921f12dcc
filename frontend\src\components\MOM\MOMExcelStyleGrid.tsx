import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { momsAtom, projectsAtom, customersAtom } from '../../store';
import { MOM, MOMPoint } from '../../types';
import { formatDate } from '../../utils/dateFormatter';
import { dataService } from '../../services/dataServiceSingleton';
import { ChevronDown, Plus, Save, X } from 'lucide-react';

interface MOMExcelStyleGridProps {
  onMOMCreated?: () => void;
}

const MOMExcelStyleGrid: React.FC<MOMExcelStyleGridProps> = ({ onMOMCreated }) => {
  const [moms] = useAtom(momsAtom);
  const [customers] = useAtom(customersAtom);
  const [projects] = useAtom(projectsAtom);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [editingCell, setEditingCell] = useState<{pointId: string, field: string} | null>(null);
  const [editValue, setEditValue] = useState('');
  const [isCreatingMOM, setIsCreatingMOM] = useState(false);

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('🔄 Loading MOM data...');
        await dataService.loadMOMs();
        await dataService.loadCustomers();
        await dataService.loadProjects();
        console.log('✅ MOM data loaded successfully');
      } catch (error) {
        console.error('❌ Error loading MOM data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Auto-select first project if none selected
  useEffect(() => {
    if (projects && projects.length > 0 && !selectedProjectId) {
      setSelectedProjectId(projects[0].id);
    }
  }, [projects, selectedProjectId]);

  // Get current MOM based on selected project
  const currentMOM = selectedProjectId ? moms?.find(m => m.projectId === selectedProjectId) : null;
  const currentProject = selectedProjectId ? projects?.find(p => p.id === selectedProjectId) : null;
  const currentCustomer = currentProject ? customers?.find(c => c.id === currentProject.customerId) : null;

  // Calculate totals
  const totalQueries = currentMOM?.mompoint?.length || 0;
  const completedQueries = currentMOM?.mompoint?.filter(p => p.status === 'COMPLETED').length || 0;
  const pendingQueries = totalQueries - completedQueries;

  // Create MOM for selected project if it doesn't exist
  const createMOMForProject = async () => {
    if (!selectedProjectId) return;

    setIsCreatingMOM(true);
    try {
      const response = await fetch('/api/moms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          projectId: selectedProjectId,
        }),
      });

      if (response.ok) {
        await dataService.loadMOMs();
        console.log('✅ MOM created for project');
      } else {
        console.error('❌ Failed to create MOM');
      }
    } catch (error) {
      console.error('❌ Error creating MOM:', error);
    } finally {
      setIsCreatingMOM(false);
    }
  };

  // Add new MOM point
  const addMOMPoint = async () => {
    if (!currentMOM) return;

    try {
      const response = await fetch(`/api/moms/${currentMOM.id}/points`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          discussion: 'New discussion point',
          status: 'PENDING',
        }),
      });

      if (response.ok) {
        await dataService.loadMOMs();
        console.log('✅ MOM point added');
      }
    } catch (error) {
      console.error('❌ Error adding MOM point:', error);
    }
  };

  // Update MOM point
  const updateMOMPoint = async (pointId: string, field: string, value: string) => {
    if (!currentMOM) return;

    try {
      const response = await fetch(`/api/moms/${currentMOM.id}/points/${pointId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          [field]: value,
        }),
      });

      if (response.ok) {
        await dataService.loadMOMs();
        console.log('✅ MOM point updated');
      }
    } catch (error) {
      console.error('❌ Error updating MOM point:', error);
    }
  };

  // Handle cell edit
  const handleCellEdit = (pointId: string, field: string, currentValue: string) => {
    setEditingCell({ pointId, field });
    setEditValue(currentValue || '');
  };

  // Save cell edit
  const saveCellEdit = async () => {
    if (!editingCell) return;

    await updateMOMPoint(editingCell.pointId, editingCell.field, editValue);
    setEditingCell(null);
    setEditValue('');
  };

  // Cancel cell edit
  const cancelCellEdit = () => {
    setEditingCell(null);
    setEditValue('');
  };

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      {/* Project Selection */}
      <div className="bg-gray-50 px-4 py-3 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">Select Project:</label>
            <div className="relative">
              <select
                value={selectedProjectId}
                onChange={(e) => setSelectedProjectId(e.target.value)}
                className="appearance-none bg-white border border-gray-300 rounded-md px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Choose a project...</option>
                {projects?.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.code} - {project.name}
                  </option>
                ))}
              </select>
              <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
          </div>

          {selectedProjectId && !currentMOM && (
            <button
              onClick={createMOMForProject}
              disabled={isCreatingMOM}
              className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 disabled:opacity-50"
            >
              {isCreatingMOM ? 'Creating...' : 'Create MOM'}
            </button>
          )}

          {currentMOM && (
            <button
              onClick={addMOMPoint}
              className="px-4 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700 flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Add Point</span>
            </button>
          )}
        </div>
      </div>

      {/* Header Section */}
      <div className="bg-purple-100 px-4 py-3">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-900">MOM</h1>
          <div className="flex items-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-gray-700">Total Queries:</span>
              <span className="bg-white px-2 py-1 rounded border">{totalQueries}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-gray-700">Completed:</span>
              <span className="bg-white px-2 py-1 rounded border">{completedQueries}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-gray-700">Pending:</span>
              <span className="bg-white px-2 py-1 rounded border">{pendingQueries}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Project Info Section */}
      {selectedProjectId && (
        <div className="bg-green-100 px-4 py-2 border-b">
          <div className="grid grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Date:</span>
              <span className="ml-2">{currentMOM ? formatDate(currentMOM.date) : 'N/A'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Project Name:</span>
              <span className="ml-2">{currentProject?.name || 'N/A'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Customer Name:</span>
              <span className="ml-2">{currentCustomer?.name || 'N/A'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">PO No:</span>
              <span className="ml-2">{currentProject?.poNumber || 'N/A'}</span>
            </div>
          </div>
        </div>
      )}

      {/* Excel-style Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full border-collapse">
          <thead>
            <tr className="bg-green-200">
              <th className="border border-gray-400 px-2 py-2 text-xs font-medium text-gray-700 text-center w-12">
                Sl No
              </th>
              <th className="border border-gray-400 px-2 py-2 text-xs font-medium text-gray-700 text-center w-20">
                Date
              </th>
              <th className="border border-gray-400 px-2 py-2 text-xs font-medium text-gray-700 text-center w-24">
                Discussion Type
              </th>
              <th className="border border-gray-400 px-2 py-2 text-xs font-medium text-gray-700 text-center w-20">
                Station
              </th>
              <th className="border border-gray-400 px-2 py-2 text-xs font-medium text-gray-700 text-center w-40">
                Discussion
              </th>
              <th className="border border-gray-400 px-2 py-2 text-xs font-medium text-gray-700 text-center w-32">
                Action Plan
              </th>
              <th className="border border-gray-400 px-2 py-2 text-xs font-medium text-gray-700 text-center w-24">
                Responsibility
              </th>
              <th className="border border-gray-400 px-2 py-2 text-xs font-medium text-gray-700 text-center w-20">
                Planned Date
              </th>
              <th className="border border-gray-400 px-2 py-2 text-xs font-medium text-gray-700 text-center w-20">
                Completion Date
              </th>
              <th className="border border-gray-400 px-2 py-2 text-xs font-medium text-gray-700 text-center w-16">
                Status
              </th>
              <th className="border border-gray-400 px-2 py-2 text-xs font-medium text-gray-700 text-center w-24">
                Image/Attachment
              </th>
              <th className="border border-gray-400 px-2 py-2 text-xs font-medium text-gray-700 text-center w-32">
                Remarks
              </th>
            </tr>
          </thead>
          <tbody>
            {currentMOM?.mompoint && currentMOM.mompoint.length > 0 ? (
              currentMOM.mompoint.map((point, index) => (
                <tr key={point.id} className="hover:bg-gray-50">
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center">
                    {point.slNo}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center">
                    {editingCell?.pointId === point.id && editingCell?.field === 'date' ? (
                      <div className="flex items-center space-x-1">
                        <input
                          type="date"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="w-full text-xs border-none outline-none bg-transparent"
                          autoFocus
                        />
                        <button onClick={saveCellEdit} className="text-green-600 hover:text-green-800">
                          <Save className="h-3 w-3" />
                        </button>
                        <button onClick={cancelCellEdit} className="text-red-600 hover:text-red-800">
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ) : (
                      <div
                        onClick={() => handleCellEdit(point.id, 'date', point.date ? formatDate(point.date) : '')}
                        className="cursor-pointer hover:bg-blue-50 p-1 rounded"
                      >
                        {point.date ? formatDate(point.date) : 'Click to edit'}
                      </div>
                    )}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center">
                    {editingCell?.pointId === point.id && editingCell?.field === 'discussionType' ? (
                      <div className="flex items-center space-x-1">
                        <input
                          type="text"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="w-full text-xs border-none outline-none bg-transparent"
                          placeholder="Discussion Type"
                          autoFocus
                        />
                        <button onClick={saveCellEdit} className="text-green-600 hover:text-green-800">
                          <Save className="h-3 w-3" />
                        </button>
                        <button onClick={cancelCellEdit} className="text-red-600 hover:text-red-800">
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ) : (
                      <div
                        onClick={() => handleCellEdit(point.id, 'discussionType', point.discussionType || '')}
                        className="cursor-pointer hover:bg-blue-50 p-1 rounded"
                      >
                        {point.discussionType || 'Click to edit'}
                      </div>
                    )}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center">
                    {editingCell?.pointId === point.id && editingCell?.field === 'station' ? (
                      <div className="flex items-center space-x-1">
                        <input
                          type="text"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="w-full text-xs border-none outline-none bg-transparent"
                          autoFocus
                        />
                        <button onClick={saveCellEdit} className="text-green-600 hover:text-green-800">
                          <Save className="h-3 w-3" />
                        </button>
                        <button onClick={cancelCellEdit} className="text-red-600 hover:text-red-800">
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ) : (
                      <div
                        onClick={() => handleCellEdit(point.id, 'station', point.station || '')}
                        className="cursor-pointer hover:bg-blue-50 p-1 rounded"
                      >
                        {point.station || 'Click to edit'}
                      </div>
                    )}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs">
                    {editingCell?.pointId === point.id && editingCell?.field === 'discussion' ? (
                      <div className="flex items-center space-x-1">
                        <textarea
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="w-full text-xs border-none outline-none bg-transparent resize-none"
                          rows={2}
                          autoFocus
                        />
                        <button onClick={saveCellEdit} className="text-green-600 hover:text-green-800">
                          <Save className="h-3 w-3" />
                        </button>
                        <button onClick={cancelCellEdit} className="text-red-600 hover:text-red-800">
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ) : (
                      <div
                        onClick={() => handleCellEdit(point.id, 'discussion', point.discussion || '')}
                        className="cursor-pointer hover:bg-blue-50 p-1 rounded"
                      >
                        {point.discussion || 'Click to edit'}
                      </div>
                    )}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs">
                    {editingCell?.pointId === point.id && editingCell?.field === 'actionPlan' ? (
                      <div className="flex items-center space-x-1">
                        <textarea
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="w-full text-xs border-none outline-none bg-transparent resize-none"
                          rows={2}
                          autoFocus
                        />
                        <button onClick={saveCellEdit} className="text-green-600 hover:text-green-800">
                          <Save className="h-3 w-3" />
                        </button>
                        <button onClick={cancelCellEdit} className="text-red-600 hover:text-red-800">
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ) : (
                      <div
                        onClick={() => handleCellEdit(point.id, 'actionPlan', point.actionPlan || '')}
                        className="cursor-pointer hover:bg-blue-50 p-1 rounded"
                      >
                        {point.actionPlan || 'Click to edit'}
                      </div>
                    )}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center">
                    {editingCell?.pointId === point.id && editingCell?.field === 'responsibility' ? (
                      <div className="flex items-center space-x-1">
                        <input
                          type="text"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="w-full text-xs border-none outline-none bg-transparent"
                          autoFocus
                        />
                        <button onClick={saveCellEdit} className="text-green-600 hover:text-green-800">
                          <Save className="h-3 w-3" />
                        </button>
                        <button onClick={cancelCellEdit} className="text-red-600 hover:text-red-800">
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ) : (
                      <div
                        onClick={() => handleCellEdit(point.id, 'responsibility', point.responsibility || '')}
                        className="cursor-pointer hover:bg-blue-50 p-1 rounded"
                      >
                        {point.responsibility || 'Click to edit'}
                      </div>
                    )}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center">
                    {editingCell?.pointId === point.id && editingCell?.field === 'plannedDate' ? (
                      <div className="flex items-center space-x-1">
                        <input
                          type="date"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="w-full text-xs border-none outline-none bg-transparent"
                          autoFocus
                        />
                        <button onClick={saveCellEdit} className="text-green-600 hover:text-green-800">
                          <Save className="h-3 w-3" />
                        </button>
                        <button onClick={cancelCellEdit} className="text-red-600 hover:text-red-800">
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ) : (
                      <div
                        onClick={() => handleCellEdit(point.id, 'plannedDate', point.plannedDate ? formatDate(point.plannedDate) : '')}
                        className="cursor-pointer hover:bg-blue-50 p-1 rounded"
                      >
                        {point.plannedDate ? formatDate(point.plannedDate) : 'Click to edit'}
                      </div>
                    )}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center">
                    {editingCell?.pointId === point.id && editingCell?.field === 'completionDate' ? (
                      <div className="flex items-center space-x-1">
                        <input
                          type="date"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="w-full text-xs border-none outline-none bg-transparent"
                          autoFocus
                        />
                        <button onClick={saveCellEdit} className="text-green-600 hover:text-green-800">
                          <Save className="h-3 w-3" />
                        </button>
                        <button onClick={cancelCellEdit} className="text-red-600 hover:text-red-800">
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ) : (
                      <div
                        onClick={() => handleCellEdit(point.id, 'completionDate', point.completionDate ? formatDate(point.completionDate) : '')}
                        className="cursor-pointer hover:bg-blue-50 p-1 rounded"
                      >
                        {point.completionDate ? formatDate(point.completionDate) : 'Click to edit'}
                      </div>
                    )}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center">
                    {editingCell?.pointId === point.id && editingCell?.field === 'status' ? (
                      <div className="flex items-center space-x-1">
                        <select
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="w-full text-xs border-none outline-none bg-transparent"
                          autoFocus
                        >
                          <option value="PENDING">Pending</option>
                          <option value="IN_PROGRESS">In Progress</option>
                          <option value="COMPLETED">Completed</option>
                          <option value="DELAYED">Delayed</option>
                          <option value="ON_HOLD">On Hold</option>
                        </select>
                        <button onClick={saveCellEdit} className="text-green-600 hover:text-green-800">
                          <Save className="h-3 w-3" />
                        </button>
                        <button onClick={cancelCellEdit} className="text-red-600 hover:text-red-800">
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ) : (
                      <div
                        onClick={() => handleCellEdit(point.id, 'status', point.status || '')}
                        className="cursor-pointer hover:bg-blue-50 p-1 rounded"
                      >
                        <span className={`px-1 py-0.5 rounded text-xs ${
                          point.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                          point.status === 'IN_PROGRESS' ? 'bg-yellow-100 text-yellow-800' :
                          point.status === 'PENDING' ? 'bg-gray-100 text-gray-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {point.status}
                        </span>
                      </div>
                    )}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center">
                    {editingCell?.pointId === point.id && editingCell?.field === 'imageAttachment' ? (
                      <div className="flex items-center space-x-1">
                        <input
                          type="text"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="w-full text-xs border-none outline-none bg-transparent"
                          autoFocus
                        />
                        <button onClick={saveCellEdit} className="text-green-600 hover:text-green-800">
                          <Save className="h-3 w-3" />
                        </button>
                        <button onClick={cancelCellEdit} className="text-red-600 hover:text-red-800">
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ) : (
                      <div
                        onClick={() => handleCellEdit(point.id, 'imageAttachment', point.imageAttachment || '')}
                        className="cursor-pointer hover:bg-blue-50 p-1 rounded"
                      >
                        {point.imageAttachment || 'Click to edit'}
                      </div>
                    )}
                  </td>
                  <td className="border border-gray-300 px-2 py-2 text-xs">
                    {editingCell?.pointId === point.id && editingCell?.field === 'remarks' ? (
                      <div className="flex items-center space-x-1">
                        <textarea
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="w-full text-xs border-none outline-none bg-transparent resize-none"
                          rows={2}
                          autoFocus
                        />
                        <button onClick={saveCellEdit} className="text-green-600 hover:text-green-800">
                          <Save className="h-3 w-3" />
                        </button>
                        <button onClick={cancelCellEdit} className="text-red-600 hover:text-red-800">
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ) : (
                      <div
                        onClick={() => handleCellEdit(point.id, 'remarks', point.remarks || '')}
                        className="cursor-pointer hover:bg-blue-50 p-1 rounded"
                      >
                        {point.remarks || 'Click to edit'}
                      </div>
                    )}
                  </td>
                </tr>
              ))
            ) : (
              // Empty rows to show the Excel-like structure
              Array.from({ length: 15 }, (_, index) => (
                <tr key={`empty-${index}`} className="hover:bg-gray-50">
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center h-8"></td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center h-8"></td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center h-8"></td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center h-8"></td>
                  <td className="border border-gray-300 px-2 py-2 text-xs h-8"></td>
                  <td className="border border-gray-300 px-2 py-2 text-xs h-8"></td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center h-8"></td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center h-8"></td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center h-8"></td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center h-8"></td>
                  <td className="border border-gray-300 px-2 py-2 text-xs text-center h-8"></td>
                  <td className="border border-gray-300 px-2 py-2 text-xs h-8"></td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Footer */}
      {!selectedProjectId && (
        <div className="p-6 text-center text-gray-500">
          <p>Please select a project to view or create its MOM.</p>
        </div>
      )}

      {selectedProjectId && !currentMOM && (
        <div className="p-6 text-center text-gray-500">
          <p>No MOM found for this project. Click "Create MOM" to get started.</p>
        </div>
      )}
    </div>
  );
};

export default MOMExcelStyleGrid;
