const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function checkPayments() {
  try {
    console.log('=== CHECKING PAYMENT DATA ===\n');

    // Get all projects with their payments
    const projects = await prisma.project.findMany({
      where: {
        poValue: {
          not: null,
          gt: 0
        }
      },
      include: {
        customer: {
          select: {
            name: true
          }
        },
        payment: {
          select: {
            amount: true,
            paymentDate: true,
            paymentMethod: true,
            referenceNumber: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    console.log(`Found ${projects.length} projects with PO Value\n`);

    let totalPoValue = 0;
    let totalPaid = 0;
    let totalPending = 0;

    projects.forEach((project, index) => {
      const poValue = Number(project.poValue) || 0;
      const paidAmount = project.payment.reduce((sum, payment) => sum + Number(payment.amount), 0);
      const pendingAmount = poValue - paidAmount;
      const paidPercentage = poValue > 0 ? (paidAmount / poValue) * 100 : 0;

      let status = 'PENDING';
      if (paidPercentage >= 100) {
        status = 'FULLY_PAID';
      } else if (paidPercentage > 0) {
        status = 'PARTIALLY_PAID';
      }

      console.log(`${index + 1}. ${project.name}`);
      console.log(`   Customer: ${project.customer?.name || 'Unknown'}`);
      console.log(`   PO Value: ₹${poValue.toLocaleString('en-IN')}`);
      console.log(`   Paid: ₹${paidAmount.toLocaleString('en-IN')} (${paidPercentage.toFixed(1)}%)`);
      console.log(`   Pending: ₹${pendingAmount.toLocaleString('en-IN')}`);
      console.log(`   Status: ${status}`);
      console.log(`   Payments: ${project.payment.length}`);
      
      if (project.payment.length > 0) {
        project.payment.forEach((payment, payIndex) => {
          console.log(`     ${payIndex + 1}. ₹${Number(payment.amount).toLocaleString('en-IN')} via ${payment.paymentMethod} (${payment.referenceNumber || 'No ref'})`);
        });
      }
      console.log('');

      totalPoValue += poValue;
      totalPaid += paidAmount;
      totalPending += pendingAmount;
    });

    console.log('=== SUMMARY ===');
    console.log(`Total PO Value: ₹${totalPoValue.toLocaleString('en-IN')}`);
    console.log(`Total Paid: ₹${totalPaid.toLocaleString('en-IN')} (${totalPoValue > 0 ? ((totalPaid / totalPoValue) * 100).toFixed(1) : 0}%)`);
    console.log(`Total Pending: ₹${totalPending.toLocaleString('en-IN')} (${totalPoValue > 0 ? ((totalPending / totalPoValue) * 100).toFixed(1) : 0}%)`);

    // Check if there are any payment calculation issues
    const calculationCheck = totalPoValue - (totalPaid + totalPending);
    if (Math.abs(calculationCheck) > 0.01) {
      console.log(`\n⚠️  CALCULATION ISSUE: Difference of ₹${calculationCheck.toFixed(2)}`);
    } else {
      console.log('\n✅ Payment calculations are correct');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkPayments();
