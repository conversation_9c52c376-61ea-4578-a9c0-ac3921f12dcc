import { Request, Response } from 'express';
import { prisma } from '../index';

// @desc    Get all departments
// @route   GET /api/departments
// @access  Private
export const getDepartments = async (req: Request, res: Response): Promise<void> => {
  try {
    const departments = await prisma.department.findMany({
      orderBy: {
        name: 'asc',
      },
    });

    res.status(200).json({
      success: true,
      count: departments.length,
      data: departments,
    });
  } catch (error) {
    console.error('Get departments error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single department
// @route   GET /api/departments/:id
// @access  Private
export const getDepartment = async (req: Request, res: Response): Promise<void> => {
  try {
    const department = await prisma.department.findUnique({
      where: { id: req.params.id },
    });

    if (!department) {
      res.status(404).json({
        success: false,
        message: 'Department not found',
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: department,
    });
  } catch (error) {
    console.error('Get department error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create department
// @route   POST /api/departments
// @access  Private/Admin
export const createDepartment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, code, description } = req.body;

    // Role-based restrictions for department creation
    const currentUserRole = req.user.role;

    // Only DIRECTOR can create departments
    if (currentUserRole !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Only Directors can create departments',
      });
      return;
    }

    // Validate required fields
    if (!name || !code) {
      res.status(400).json({
        success: false,
        message: 'Please provide department name and code',
      });
      return;
    }

    console.log('Creating department with data:', {
      name,
      code,
      description: description || 'Not provided',
    });

    // Check if department already exists
    const departmentExists = await prisma.department.findFirst({
      where: {
        OR: [
          { name },
          { code },
        ],
      },
    });

    if (departmentExists) {
      res.status(400).json({
        success: false,
        message: 'Department with this name or code already exists',
      });
      return;
    }

    // Generate a unique ID for the department
    const { v4: uuidv4 } = require('uuid');
    const departmentId = uuidv4();

    // Create department
    const department = await prisma.department.create({
      data: {
        id: departmentId,
        name,
        code,
        description,
      },
    });

    console.log('Department created successfully:', department.id);

    res.status(201).json({
      success: true,
      data: department,
    });
  } catch (error: any) {
    console.error('Create department error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      res.status(400).json({
        success: false,
        message: `A department with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during department creation',
    });
  }
};

// @desc    Update department
// @route   PUT /api/departments/:id
// @access  Private/Admin
export const updateDepartment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, code, description } = req.body;

    // Role-based restrictions for department updates
    const currentUserRole = req.user.role;

    // Only DIRECTOR can update departments
    if (currentUserRole !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Only Directors can update departments',
      });
      return;
    }

    console.log('Updating department with ID:', req.params.id);
    console.log('Update data:', {
      name: name || 'Not changed',
      code: code || 'Not changed',
      description: description || 'Not changed',
    });

    // Check if department exists
    const departmentExists = await prisma.department.findUnique({
      where: { id: req.params.id },
    });

    if (!departmentExists) {
      res.status(404).json({
        success: false,
        message: 'Department not found',
      });
      return;
    }

    // Check if name or code is already in use by another department
    if ((name && name !== departmentExists.name) || (code && code !== departmentExists.code)) {
      const existingDepartment = await prisma.department.findFirst({
        where: {
          OR: [
            { name },
            { code },
          ],
          NOT: {
            id: req.params.id,
          },
        },
      });

      if (existingDepartment) {
        res.status(400).json({
          success: false,
          message: 'Department with this name or code already exists',
        });
        return;
      }
    }

    // Prepare update data - only include fields that are provided
    const updateData: any = {};

    if (name !== undefined) updateData.name = name;
    if (code !== undefined) updateData.code = code;
    if (description !== undefined) updateData.description = description;

    // Update department
    const department = await prisma.department.update({
      where: { id: req.params.id },
      data: updateData,
    });

    console.log('Department updated successfully:', department.id);

    res.status(200).json({
      success: true,
      data: department,
    });
  } catch (error: any) {
    console.error('Update department error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      res.status(400).json({
        success: false,
        message: `A department with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during department update',
    });
  }
};

// @desc    Delete department
// @route   DELETE /api/departments/:id
// @access  Private/Admin
export const deleteDepartment = async (req: Request, res: Response): Promise<void> => {
  try {
    // Role-based restrictions for department deletion
    const currentUserRole = req.user.role;

    // Only DIRECTOR can delete departments
    if (currentUserRole !== 'DIRECTOR') {
      res.status(403).json({
        success: false,
        message: 'Only Directors can delete departments',
      });
      return;
    }

    // Check if department exists
    const departmentExists = await prisma.department.findUnique({
      where: { id: req.params.id },
    });

    if (!departmentExists) {
      res.status(404).json({
        success: false,
        message: 'Department not found',
      });
      return;
    }

    // Check if department has users
    const usersInDepartment = await prisma.user.findMany({
      where: { department: departmentExists.name },
    });

    if (usersInDepartment.length > 0) {
      res.status(400).json({
        success: false,
        message: `Cannot delete department. ${usersInDepartment.length} user(s) are assigned to this department.`,
      });
      return;
    }

    // Delete department
    await prisma.department.delete({
      where: { id: req.params.id },
    });

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete department error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
