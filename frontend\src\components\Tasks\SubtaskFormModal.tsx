import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { useForm, SubmitHandler } from 'react-hook-form';
import { engineersAtom, projectsAtom, currentUserAtom } from '../../store';
import { Task, Subtask, TaskStatus, TaskAssigneeType } from '../../types';
import { X, Save } from 'lucide-react';
import { tasksAPI } from '../../services/api';
import { toCamelCase } from '../../utils/textUtils';
import { useState } from 'react';

interface SubtaskFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (subtask: Subtask) => void;
  parentTask: Task;
  subtask?: Subtask; // If provided, we're editing an existing subtask
  title?: string;
}

interface FormValues {
  name: string;
  description: string;
  assigneeId: string;
  startDate: string;
  endDate: string;
  status: TaskStatus;
}

const SubtaskFormModal: React.FC<SubtaskFormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  parentTask,
  subtask,
  title = 'Add Subtask'
}) => {
  const [engineers] = useAtom(engineersAtom);
  const [projects] = useAtom(projectsAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [subtaskName, setSubtaskName] = useState('');

  // Filter users for subtask assignment - only engineers
  const engineersOnly = engineers.filter(user => user.role === 'ENGINEER');

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setValue
  } = useForm<FormValues>({
    defaultValues: {
      name: '',
      description: '',
      assigneeId: '',
      startDate: new Date().toISOString().split('T')[0], // Default to today
      endDate: '',
      status: TaskStatus.NOT_STARTED
    }
  });

  // Initialize form with subtask data if editing
  useEffect(() => {
    if (subtask) {
      setSubtaskName(subtask.name);
      setValue('name', subtask.name);
      setValue('description', subtask.description || '');
      setValue('assigneeId', subtask.assigneeId || '');
      setValue('startDate', subtask.startDate);
      setValue('endDate', subtask.endDate);
      setValue('status', subtask.status);
    } else {
      // Reset form when adding a new subtask
      setSubtaskName('');
      reset({
        name: '',
        description: '',
        assigneeId: '',
        startDate: new Date().toISOString().split('T')[0], // Default to today
        endDate: '',
        status: TaskStatus.NOT_STARTED
      });
    }
  }, [subtask, reset, setValue, parentTask]);

  const handleFormSubmit: SubmitHandler<FormValues> = async (data) => {
    setIsSubmitting(true);

    try {
      // Get the existing subtasks for this task to determine the next subtask number
      const existingSubtasks = parentTask.subtasks || [];

      // Extract the task number from the parent task ID (e.g., "T1" -> "1")
      const taskNumber = parentTask.id.startsWith('T')
        ? parentTask.id.substring(1)
        : '1'; // Default to 1 if not in expected format

      // Generate the next subtask ID (e.g., S11, S12, etc.)
      const nextSubtaskNumber = existingSubtasks.length + 1;
      const subtaskId = `S${taskNumber}${nextSubtaskNumber}`;

      console.log(`Creating subtask with ID: ${subtaskId} for parent task: ${parentTask.id}`);

      const subtaskData: Subtask = subtask
        ? {
            ...subtask,
            name: toCamelCase(data.name),
            description: data.description,
            assigneeId: data.assigneeId,
            startDate: data.startDate,
            ...(data.endDate && { endDate: data.endDate }),
            status: data.status,
          }
        : {
            id: subtaskId, // Use our sequential ID format
            taskId: parentTask.id,
            name: toCamelCase(data.name),
            description: data.description,
            assigneeId: data.assigneeId,
            assigneeType: TaskAssigneeType.ENGINEER,
            startDate: data.startDate,
            ...(data.endDate && { endDate: data.endDate }),
            status: data.status,
            totalTime: 0,
            createdBy: currentUser?.id || '',
            createdAt: new Date().toISOString()
          };

      onSubmit(subtaskData);
      onClose();
    } catch (error) {
      console.error('Error submitting subtask:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-800">
            {title} <span className="text-blue-600">for {parentTask.name}</span>
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="p-6 space-y-4">
          <div className="space-y-4">
            <div>
              <label className="form-label">Subtask Name</label>
              <input
                type="text"
                className={`form-input w-full ${errors.name ? 'border-error' : ''}`}
                placeholder="Enter subtask name"
                value={subtaskName}
                onChange={(e) => {
                  setSubtaskName(e.target.value);
                  setValue('name', e.target.value);
                }}
                {...register('name', { required: 'Subtask name is required' })}
              />
              {errors.name && (
                <p className="text-error text-sm mt-1">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="form-label">Description</label>
              <textarea
                className="form-textarea w-full"
                rows={3}
                placeholder="Enter subtask description"
                {...register('description')}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="form-label">Assignee</label>
                <select
                  className="form-select w-full"
                  {...register('assigneeId')}
                >
                  <option value="">Select Engineer</option>
                  {engineersOnly.map(engineer => (
                    <option key={engineer.id} value={engineer.id}>
                      {engineer.name} ({engineer.department})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="form-label">Status</label>
                <select
                  className="form-select w-full"
                  {...register('status')}
                >
                  {Object.values(TaskStatus).map(status => (
                    <option key={status} value={status}>
                      {status.replace(/_/g, ' ')}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="form-label">Start Date</label>
                <input
                  type="date"
                  className="form-input w-full"
                  {...register('startDate', { required: 'Start date is required' })}
                  min={new Date().toISOString().split('T')[0]}
                />
                {errors.startDate && (
                  <p className="text-error text-sm mt-1">{errors.startDate.message}</p>
                )}
              </div>

              <div>
                <label className="form-label">End Date</label>
                <input
                  type="date"
                  className="form-input w-full"
                  {...register('endDate')}
                  min={new Date().toISOString().split('T')[0]}
                />
                {errors.endDate && (
                  <p className="text-error text-sm mt-1">{errors.endDate.message}</p>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t mt-6">
            <button
              type="button"
              className="btn btn-outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary flex items-center"
              disabled={isSubmitting}
            >
              <Save size={18} className="mr-2" />
              {isSubmitting ? 'Saving...' : 'Save Subtask'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SubtaskFormModal;
