import express, { Request, Response } from 'express';
import {
  getTasks,
  getTask,
  createTask,
  updateTask,
  deleteTask,
} from '../controllers/task.controller';
import { protect, authorize } from '../middleware/auth.middleware';
import taskCommentRoutes from './taskComment.routes';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(getTasks)
  .post(
    authorize('DIRECTOR', 'PROJECT_MANAGER'),
    createTask
  );

router.route('/:id')
  .get(getTask)
  .put(updateTask) // All authenticated users can update their assigned tasks
  .delete(
    authorize('DIRECTOR', 'PROJECT_MANAGER'),
    deleteTask
  );

// Comment routes
router.use('/:taskId/comments', taskCommentRoutes);

export default router;
