.editable-grid table {
  border-collapse: collapse;
  width: 100%;
}

.editable-grid th {
  background-color: #f3f4f6;
  font-weight: 600;
  text-align: left;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 10;
}

.editable-grid td {
  padding: 0;
  border: 1px solid #e5e7eb;
  position: relative;
}

.editable-grid td input,
.editable-grid td select {
  width: 100%;
  height: 100%;
  padding: 0.5rem 0.75rem;
  border: none;
  background-color: transparent;
  outline: none;
  font-size: 0.875rem;
}

.editable-grid td:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
  z-index: 20;
}

.editable-grid tr:hover {
  background-color: #f9fafb;
}

.editable-grid .row-number {
  width: 40px;
  text-align: center;
  background-color: #f3f4f6;
  font-weight: 500;
  color: #6b7280;
}

.editable-grid .add-row-btn {
  margin-top: 0.5rem;
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
  border: 1px solid #d1d5db;
  background-color: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.editable-grid .add-row-btn:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.editable-grid .remove-row-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  transition: color 0.2s;
}

.editable-grid .remove-row-btn:hover {
  color: #ef4444;
}

/* Excel-like cell selection styles */
.editable-grid td.selected {
  background-color: rgba(59, 130, 246, 0.1);
}

.editable-grid td.editing {
  background-color: white;
  box-shadow: 0 0 0 2px #3b82f6;
  z-index: 30;
}

/* Responsive styles */
@media (max-width: 640px) {
  .editable-grid {
    overflow-x: auto;
  }
  
  .editable-grid table {
    min-width: 100%;
  }
}
