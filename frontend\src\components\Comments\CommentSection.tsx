import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { currentUserAtom } from '../../store';
import { Comment } from '../../services/commentAPI';
import { formatDistanceToNow } from 'date-fns';
import {
  MessageSquare,
  Send,
  Edit3,
  Trash2,
  Check,
  X,
  User
} from 'lucide-react';

interface CommentSectionProps {
  title?: string;
  comments: Comment[];
  loading: boolean;
  onAddComment: (content: string) => Promise<void>;
  onUpdateComment: (commentId: string, content: string) => Promise<void>;
  onDeleteComment: (commentId: string) => Promise<void>;
  placeholder?: string;
}

const CommentSection: React.FC<CommentSectionProps> = ({
  title = "Comments",
  comments = [],
  loading,
  onAddComment,
  onUpdateComment,
  onDeleteComment,
  placeholder = "Add a comment..."
}) => {
  const [currentUser] = useAtom(currentUserAtom);
  const [newComment, setNewComment] = useState('');
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const safeComments = Array.isArray(comments) ? comments : [];

  const handleAddComment = async () => {
    if (!newComment.trim() || submitting) return;

    setSubmitting(true);
    try {
      await onAddComment(newComment.trim());
      setNewComment('');
    } catch (error) {
      console.error('Error adding comment:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditComment = (comment: Comment) => {
    setEditingCommentId(comment.id);
    setEditingContent(comment.content);
  };

  const handleUpdateComment = async () => {
    if (!editingContent.trim() || !editingCommentId || submitting) return;

    setSubmitting(true);
    try {
      await onUpdateComment(editingCommentId, editingContent.trim());
      setEditingCommentId(null);
      setEditingContent('');
    } catch (error) {
      console.error('Error updating comment:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (submitting) return;

    if (window.confirm('Are you sure you want to delete this comment?')) {
      setSubmitting(true);
      try {
        await onDeleteComment(commentId);
      } catch (error) {
        console.error('Error deleting comment:', error);
      } finally {
        setSubmitting(false);
      }
    }
  };

  const cancelEdit = () => {
    setEditingCommentId(null);
    setEditingContent('');
  };

  const formatCommentTime = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (error) {
      return 'Unknown time';
    }
  };

  const canEditComment = (comment: Comment) => {
    return currentUser?.id === comment?.user?.id;
  };

  const canDeleteComment = (comment: Comment) => {
    return currentUser?.id === comment?.user?.id ||
           currentUser?.role === 'DIRECTOR' ||
           currentUser?.role === 'PROJECT_MANAGER';
  };

  return (
    <div className="border-t bg-gradient-to-br from-white to-gray-50 shadow-inner">
      <div className="p-6">
        {/* Header with 3D effect */}
        <div className="mb-6 p-4 bg-white rounded-xl shadow-lg border border-gray-100 transform hover:scale-[1.01] transition-all duration-200">
          <h2 className="font-semibold text-lg flex items-center text-gray-800">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-lg transform hover:rotate-3 transition-transform duration-200">
              <MessageSquare size={20} className="text-white" />
            </div>
            {title} 
            <span className="ml-2 px-3 py-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm rounded-full shadow-md">
              {safeComments.length}
            </span>
          </h2>
        </div>

        {/* Comment list with enhanced cards */}
        <div className="space-y-4 mb-6 max-h-96 overflow-y-auto">
          {loading ? (
            <div className="text-center py-8">
              <div className="relative">
                <div className="animate-spin rounded-full h-8 w-8 border-4 border-blue-200 border-t-blue-600 mx-auto shadow-lg"></div>
                <div className="absolute inset-0 rounded-full bg-blue-100 opacity-20 animate-ping"></div>
              </div>
              <p className="text-sm text-gray-600 mt-4 font-medium">Loading comments...</p>
            </div>
          ) : safeComments.length === 0 ? (
            <div className="text-center py-12 bg-white rounded-xl shadow-lg border border-gray-100">
              <div className="w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full flex items-center justify-center mx-auto mb-4 shadow-inner">
                <MessageSquare size={32} className="text-gray-400" />
              </div>
              <p className="text-gray-600 text-base font-medium mb-1">No comments yet</p>
              <p className="text-gray-400 text-sm">Be the first to start the conversation</p>
            </div>
          ) : (
            safeComments.map(comment => {
              if (!comment || !comment.id) {
                return null;
              }

              return (
                <div 
                  key={comment.id} 
                  className="bg-white rounded-xl shadow-md border border-gray-100 p-4 transform hover:scale-[1.01] hover:shadow-lg transition-all duration-200"
                >
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center shadow-lg transform hover:rotate-6 transition-transform duration-200">
                        <User size={18} className="text-white" />
                      </div>
                      <div>
                        <span className="font-semibold text-gray-800 text-sm">
                          {comment.user?.name || 'Unknown User'}
                        </span>
                        <div className="flex items-center mt-1">
                          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                            {comment.user?.role?.replace(/_/g, ' ') || 'Unknown Role'}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                        {formatCommentTime(comment.createdAt || '')}
                      </span>
                      {canEditComment(comment) && (
                        <button
                          onClick={() => handleEditComment(comment)}
                          className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 transform hover:scale-110"
                          disabled={submitting}
                        >
                          <Edit3 size={14} />
                        </button>
                      )}
                      {canDeleteComment(comment) && (
                        <button
                          onClick={() => handleDeleteComment(comment.id)}
                          className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 transform hover:scale-110"
                          disabled={submitting}
                        >
                          <Trash2 size={14} />
                        </button>
                      )}
                    </div>
                  </div>

                  {editingCommentId === comment.id ? (
                    <div className="space-y-3 bg-gray-50 p-4 rounded-lg">
                      <textarea
                        value={editingContent}
                        onChange={(e) => setEditingContent(e.target.value)}
                        className="w-full p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-inner transition-all duration-200"
                        rows={3}
                        disabled={submitting}
                      />
                      <div className="flex space-x-2">
                        <button
                          onClick={handleUpdateComment}
                          disabled={!editingContent.trim() || submitting}
                          className="px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-sm shadow-lg transform hover:scale-105 transition-all duration-200"
                        >
                          <Check size={14} className="mr-1" />
                          Save
                        </button>
                        <button
                          onClick={cancelEdit}
                          disabled={submitting}
                          className="px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-sm shadow-lg transform hover:scale-105 transition-all duration-200"
                        >
                          <X size={14} className="mr-1" />
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <p className="text-gray-700 text-sm leading-relaxed whitespace-pre-wrap">
                        {comment.content || ''}
                      </p>
                    </div>
                  )}
                </div>
              );
            }).filter(Boolean)
          )}
        </div>

        {/* Enhanced new comment section */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-4 transform hover:scale-[1.005] transition-all duration-200">
          <div className="space-y-4">
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder={placeholder}
              className="w-full p-4 border border-gray-200 rounded-xl resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-inner transition-all duration-200 hover:shadow-md"
              rows={3}
              disabled={submitting}
            />
            <div className="flex justify-end">
              <button
                onClick={handleAddComment}
                disabled={!newComment.trim() || submitting}
                className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center shadow-lg transform hover:scale-105 transition-all duration-200 font-medium"
              >
                <div className="w-5 h-5 mr-2 flex items-center justify-center">
                  <Send size={16} />
                </div>
                {submitting ? 'Posting...' : 'Post Comment'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommentSection;