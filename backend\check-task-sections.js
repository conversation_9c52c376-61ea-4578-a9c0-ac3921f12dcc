const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkTaskSections() {
  try {
    const projectId = 'fe5a0b2b-1944-4cd2-b4f4-6bae179a1a21';
    
    console.log('=== PROJECT SECTIONS ===');
    const sections = await prisma.section.findMany({
      where: { projectId },
      orderBy: { sequence: 'asc' }
    });
    
    sections.forEach(section => {
      console.log(`Section ${section.sequence}: ${section.name} (${section.id})`);
    });
    
    console.log('\n=== PROJECT TASKS ===');
    const tasks = await prisma.task.findMany({
      where: { projectId },
      orderBy: { sequence: 'asc' }
    });
    
    tasks.forEach(task => {
      console.log(`Task ${task.displayId || task.sequence}: ${task.name} - sectionId: ${task.sectionId || 'NULL'}`);
    });
    
    console.log('\n=== TASKS BY SECTION ===');
    for (const section of sections) {
      const sectionTasks = await prisma.task.findMany({
        where: { 
          projectId,
          sectionId: section.id 
        },
        orderBy: { sequence: 'asc' }
      });
      
      console.log(`\n${section.name} section (${section.id}):`);
      if (sectionTasks.length === 0) {
        console.log('  No tasks assigned to this section');
      } else {
        sectionTasks.forEach(task => {
          console.log(`  - ${task.displayId || task.sequence}: ${task.name}`);
        });
      }
    }
    
    // Check for tasks without section assignment
    const unassignedTasks = await prisma.task.findMany({
      where: { 
        projectId,
        sectionId: null 
      },
      orderBy: { sequence: 'asc' }
    });
    
    if (unassignedTasks.length > 0) {
      console.log('\n=== UNASSIGNED TASKS ===');
      unassignedTasks.forEach(task => {
        console.log(`  - ${task.displayId || task.sequence}: ${task.name} (no section assigned)`);
      });
    }
    
  } catch (error) {
    console.error('Error checking task sections:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTaskSections();
