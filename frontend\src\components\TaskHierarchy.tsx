import React, { useState, useEffect } from 'react';
import './TaskHierarchy.css';

interface Task {
  id: string;
  displayId: string;
  projectId: string;
  sequence: number;
  name: string;
  assignedTo: string;
  status: string;
  category?: string;
  startDate: string;
  endDate: string;
  duration: string;
  priority: string;
  subtasks?: Task[];
}

interface TaskHierarchyProps {
  tasks: Task[];
}

const TaskHierarchy: React.FC<TaskHierarchyProps> = ({ tasks }) => {
  const [localTasks, setLocalTasks] = useState<Task[]>(tasks);

  // Calculate task status based on subtasks
  const calculateTaskStatus = (task: Task): string => {
    // IMPORTANT: Never override manually set COMPLETED or ON_HOLD statuses
    // These should be preserved regardless of subtask changes
    if (task.status === 'COMPLETED' || task.status === 'ON_HOLD') {
      return task.status; // Preserve manually set status
    }

    if (!task.subtasks || task.subtasks.length === 0) {
      return task.status; // No subtasks, keep original status
    }

    const subtasks = task.subtasks;
    const statusCounts = {
      COMPLETED: 0,
      'IN PROGRESS': 0,
      'NOT STARTED': 0,
      DELAYED: 0,
      'ON HOLD': 0
    };

    subtasks.forEach((subtask: Task) => {
      const status = subtask.status.toUpperCase().replace(/_/g, ' ');
      if (statusCounts.hasOwnProperty(status)) {
        statusCounts[status as keyof typeof statusCounts]++;
      }
    });

    const total = subtasks.length;

    // Business logic for task status based on subtasks:
    // 1. If ANY subtask is DELAYED -> Task is DELAYED (highest priority)
    // 2. If ALL subtasks are COMPLETED -> Task is COMPLETED
    // 3. If ANY subtask is IN PROGRESS -> Task is IN PROGRESS
    // 4. If ANY subtask is ON HOLD and none are IN PROGRESS/DELAYED -> Task is ON HOLD
    // 5. If ALL subtasks are NOT STARTED -> Task is NOT STARTED

    if (statusCounts.DELAYED > 0) {
      return 'DELAYED';
    } else if (statusCounts.COMPLETED === total) {
      return 'COMPLETED';
    } else if (statusCounts['IN PROGRESS'] > 0) {
      return 'IN PROGRESS';
    } else if (statusCounts['ON HOLD'] > 0) {
      return 'ON HOLD';
    } else {
      return 'NOT STARTED';
    }
  };

  // Update local tasks with calculated status when tasks prop changes
  useEffect(() => {
    const tasksWithCalculatedStatus = tasks.map(task => ({
      ...task,
      status: calculateTaskStatus(task)
    }));
    setLocalTasks(tasksWithCalculatedStatus);
  }, [tasks]);

  // Group tasks and their subtasks
  const groupTasksWithSubtasks = (tasks: Task[]) => {
    const taskMap = new Map<string, Task>();
    const rootTasks: Task[] = [];

    // First pass: Create a map of all tasks
    tasks.forEach(task => {
      taskMap.set(task.displayId, { ...task, subtasks: [] });
    });

    // Second pass: Organize into hierarchy
    tasks.forEach(task => {
      const isSubtask = task.displayId.includes('S');
      if (isSubtask) {
        // For a subtask like "S1", get its parent task's displayId (e.g., "T1")
        const parentTaskId = task.displayId.replace('S', 'T');
        const parentTask = taskMap.get(parentTaskId);
        if (parentTask && parentTask.subtasks) {
          parentTask.subtasks.push(taskMap.get(task.displayId)!);
        }
      } else {
        rootTasks.push(taskMap.get(task.displayId)!);
      }
    });

    return rootTasks;
  };

  const getStatusClass = (status: string) => {
    const statusMap: { [key: string]: string } = {
      'COMPLETED': 'status-completed',
      'IN PROGRESS': 'status-in-progress',
      'DELAYED': 'status-delayed',
      'ON HOLD': 'status-on-hold'
    };
    return statusMap[status] || 'status-default';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    });
  };

  // Group tasks by project
  const groupedByProject = localTasks.reduce((acc, task) => {
    const { projectId } = task;
    if (!acc[projectId]) {
      acc[projectId] = [];
    }
    acc[projectId].push(task);
    return acc;
  }, {} as { [key: string]: Task[] });

  return (
    <div className="task-hierarchy">
      {Object.entries(groupedByProject).map(([projectId, projectTasks]) => {
        const organizedTasks = groupTasksWithSubtasks(projectTasks);
        return (
          <div key={projectId} className="project-container">
            <h2 className="project-title">Project {projectId}</h2>
            <div className="tasks-container">
              {organizedTasks
                .sort((a, b) => a.sequence - b.sequence)
                .map(task => (
                  <div key={task.displayId} className="task-group">
                    <div className="task-header">
                      <div className="task-id-title">
                        <span className="task-id">{task.displayId}</span>
                        <span className="task-title">{task.name}</span>
                      </div>
                      <span className={`task-status ${getStatusClass(task.status)}`}>
                        {task.status}
                      </span>
                    </div>
                    <div className="task-details">
                      <div className="detail-row">
                        <span className="detail-label">Assigned to:</span>
                        <span className="detail-value">{task.assignedTo}</span>
                      </div>
                      {task.category && (
                        <div className="detail-row">
                          <span className="detail-label">Category:</span>
                          <span className="detail-value">{task.category}</span>
                        </div>
                      )}
                      <div className="detail-row">
                        <span className="detail-label">Duration:</span>
                        <span className="detail-value">{task.duration}</span>
                      </div>
                      <div className="detail-row">
                        <span className="detail-label">Priority:</span>
                        <span className="detail-value priority-tag">{task.priority}</span>
                      </div>
                      <div className="date-row">
                        <div className="date-item">
                          <span className="date-label">Start:</span>
                          <span className="date-value">{formatDate(task.startDate)}</span>
                        </div>
                        <div className="date-item">
                          <span className="date-label">End:</span>
                          <span className="date-value">{formatDate(task.endDate)}</span>
                        </div>
                      </div>
                    </div>
                    {task.subtasks && task.subtasks.length > 0 && (
                      <div className="subtasks-container">
                        <h4 className="subtasks-title">Subtasks</h4>
                        {task.subtasks.map(subtask => (
                          <div key={subtask.displayId} className="subtask-item">
                            <div className="subtask-header">
                              <div className="task-id-title">
                                <span className="subtask-id">{subtask.displayId}</span>
                                <span className="subtask-title">{subtask.name}</span>
                              </div>
                              <span className={`task-status ${getStatusClass(subtask.status)}`}>
                                {subtask.status}
                              </span>
                            </div>
                            <div className="task-details">
                              <div className="detail-row">
                                <span className="detail-label">Assigned to:</span>
                                <span className="detail-value">{subtask.assignedTo}</span>
                              </div>
                              <div className="date-row">
                                <div className="date-item">
                                  <span className="date-label">Start:</span>
                                  <span className="date-value">{formatDate(subtask.startDate)}</span>
                                </div>
                                <div className="date-item">
                                  <span className="date-label">End:</span>
                                  <span className="date-value">{formatDate(subtask.endDate)}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default TaskHierarchy;