import jwt, { SignOptions } from 'jsonwebtoken';
import bcrypt from 'bcrypt';

// Generate JWT token
export const generateToken = (id: string): string => {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  const options: any = {
    expiresIn: process.env.JWT_EXPIRES_IN || '30d',
  };

  return jwt.sign({ id }, secret, options);
};

// Hash password
export const hashPassword = async (password: string): Promise<string> => {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
};

// Compare password
export const comparePassword = async (
  enteredPassword: string,
  hashedPassword: string
): Promise<boolean> => {
  return bcrypt.compare(enteredPassword, hashedPassword);
};

// Date validation utility - Allow dates up to 30 days in the past for project editing
export const validateDates = (startDate?: string | Date, endDate?: string | Date): { isValid: boolean; message?: string } => {
  const now = new Date();
  now.setHours(0, 0, 0, 0); // Set to start of today for comparison

  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  thirtyDaysAgo.setHours(0, 0, 0, 0); // Start of 30 days ago

  if (startDate) {
    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);

    // Allow dates from 30 days ago to future dates
    if (start < thirtyDaysAgo) {
      return {
        isValid: false,
        message: 'Start date cannot be older than 30 days'
      };
    }
  }

  if (endDate) {
    const end = new Date(endDate);
    end.setHours(0, 0, 0, 0);

    // Allow dates from 30 days ago to future dates
    if (end < thirtyDaysAgo) {
      return {
        isValid: false,
        message: 'End date cannot be older than 30 days'
      };
    }
  }

  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (end < start) {
      return {
        isValid: false,
        message: 'End date cannot be before start date'
      };
    }
  }

  return { isValid: true };
};

// Date validation utility for new project creation - Only allow current date and future dates
export const validateNewProjectDates = (startDate?: string | Date, endDate?: string | Date): { isValid: boolean; message?: string } => {
  const now = new Date();
  now.setHours(0, 0, 0, 0); // Set to start of today for comparison

  if (startDate) {
    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);

    // Allow today's date and future dates only for new projects
    if (start < now) {
      return {
        isValid: false,
        message: 'Start date cannot be before today'
      };
    }
  }

  if (endDate) {
    const end = new Date(endDate);
    end.setHours(0, 0, 0, 0);

    // Allow today's date and future dates only for new projects
    if (end < now) {
      return {
        isValid: false,
        message: 'End date cannot be before today'
      };
    }
  }

  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (end < start) {
      return {
        isValid: false,
        message: 'End date cannot be before start date'
      };
    }
  }

  return { isValid: true };
};

// MOM date validation utility
export const validateMOMDate = (date?: string | Date): { isValid: boolean; message?: string } => {
  if (!date) return { isValid: true };

  const momDate = new Date(date);
  const thirtyDaysAgo = new Date();

  // Set time boundaries
  momDate.setHours(0, 0, 0, 0);
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  thirtyDaysAgo.setHours(0, 0, 0, 0); // Start of 30 days ago

  if (momDate < thirtyDaysAgo) {
    return {
      isValid: false,
      message: 'MOM date cannot be older than 30 days'
    };
  }

  return { isValid: true };
};

// User name validation utility - strict for user names (only letters, spaces, hyphens, apostrophes, dots)
export const validateName = (name?: string): { isValid: boolean; message?: string } => {
  if (!name) return { isValid: true };

  // Allow letters (including accented characters), spaces, hyphens, apostrophes, and dots
  const nameRegex = /^[a-zA-ZÀ-ÿ\s\-'.]+$/;

  if (!nameRegex.test(name)) {
    return {
      isValid: false,
      message: 'Name can only contain letters, spaces, hyphens, apostrophes, and dots'
    };
  }

  // Check for minimum length
  if (name.trim().length < 2) {
    return {
      isValid: false,
      message: 'Name must be at least 2 characters long'
    };
  }

  // Check for maximum length
  if (name.trim().length > 100) {
    return {
      isValid: false,
      message: 'Name must be less than 100 characters long'
    };
  }

  return { isValid: true };
};

// Task/Project name validation utility - flexible for task/project names
export const validateTaskName = (name?: string): { isValid: boolean; message?: string } => {
  if (!name) return { isValid: true };

  // Allow letters (including accented characters), numbers, spaces, hyphens, apostrophes, dots, parentheses, and common symbols
  const nameRegex = /^[a-zA-ZÀ-ÿ0-9\s\-'.()\[\]_&@#$%+:,/]+$/;

  if (!nameRegex.test(name)) {
    return {
      isValid: false,
      message: 'Name contains invalid characters. Only letters, numbers, spaces, and common symbols are allowed'
    };
  }

  // Check for minimum length
  if (name.trim().length < 2) {
    return {
      isValid: false,
      message: 'Name must be at least 2 characters long'
    };
  }

  // Check for maximum length
  if (name.trim().length > 200) {
    return {
      isValid: false,
      message: 'Name must be less than 200 characters long'
    };
  }

  return { isValid: true };
};

// Number validation utility - only numbers
export const validateNumber = (value?: string | number, fieldName: string = 'Field'): { isValid: boolean; message?: string } => {
  if (value === undefined || value === null || value === '') return { isValid: true };

  const numberValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numberValue)) {
    return {
      isValid: false,
      message: `${fieldName} must be a valid number`
    };
  }

  return { isValid: true };
};

// Code validation utility - alphanumeric only
export const validateCode = (code?: string, fieldName: string = 'Code'): { isValid: boolean; message?: string } => {
  if (!code) return { isValid: true };

  // Allow letters and numbers only
  const codeRegex = /^[a-zA-Z0-9]+$/;

  if (!codeRegex.test(code)) {
    return {
      isValid: false,
      message: `${fieldName} can only contain letters and numbers`
    };
  }

  return { isValid: true };
};

// Project code validation utility - specific format: ****-**-***
export const validateProjectCode = (code?: string): { isValid: boolean; message?: string } => {
  if (!code) return { isValid: false, message: 'Project code is required' };

  // Check exact format: 4 chars, dash, 2 chars, dash, 3 chars
  const projectCodeRegex = /^[a-zA-Z0-9]{4}-[a-zA-Z0-9]{2}-[a-zA-Z0-9]{3}$/;

  if (!projectCodeRegex.test(code.trim())) {
    return {
      isValid: false,
      message: 'Project code must follow format: ****-**-*** (4 characters, dash, 2 characters, dash, 3 characters)'
    };
  }

  return { isValid: true };
};

// Email normalization utility - converts email to lowercase
export const normalizeEmail = (email: string): string => {
  return email.trim().toLowerCase();
};

// Email validation utility
export const validateEmail = (email?: string): { isValid: boolean; message?: string } => {
  if (!email) return { isValid: true };

  // Normalize email before validation
  const normalizedEmail = normalizeEmail(email);
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailRegex.test(normalizedEmail)) {
    return {
      isValid: false,
      message: 'Please enter a valid email address'
    };
  }

  return { isValid: true };
};

// Phone validation utility - only numbers, spaces, hyphens, parentheses, and plus
export const validatePhone = (phone?: string): { isValid: boolean; message?: string } => {
  if (!phone) return { isValid: true };

  const phoneRegex = /^[\d\s\-\(\)\+]+$/;

  if (!phoneRegex.test(phone)) {
    return {
      isValid: false,
      message: 'Phone number can only contain numbers, spaces, hyphens, parentheses, and plus sign'
    };
  }

  return { isValid: true };
};

// Text formatting utility - converts text to camelCase
export const toCamelCase = (text: string): string => {
  if (!text) return '';

  return text
    .trim()
    .toLowerCase()
    .replace(/[^a-zA-Z0-9\s]/g, ' ') // Replace special characters with spaces
    .split(/\s+/) // Split by whitespace
    .filter(word => word.length > 0) // Remove empty strings
    .map((word, index) => {
      if (index === 0) {
        return word.toLowerCase();
      }
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join('');
};
