import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAtom } from 'jotai';
import { isAuthenticatedAtom, currentUserAtom, isAuthLoadingAtom, isDataLoadingAtom } from '../../store';

interface ProtectedRouteProps {
  children?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const [isAuthenticated] = useAtom(isAuthenticatedAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [isAuthLoading] = useAtom(isAuthLoadingAtom);
  const [isDataLoading] = useAtom(isDataLoadingAtom);
  const location = useLocation();

  // Show loading state while checking authentication or loading data
  if (isAuthLoading || (isAuthenticated && isDataLoading)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Check if password change is required
  if (currentUser?.passwordChanged === 'N' && location.pathname !== '/change-password') {
    return <Navigate to="/change-password" replace />;
  }

  // If authenticated, render the child routes or children
  return children ? <>{children}</> : <Outlet />;
};

export default ProtectedRoute;
