import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { projectsAtom, customersAtom, currentUserAtom } from '../../store';
import { X, Plus, Calendar, Save, Trash2 } from 'lucide-react';
import { MOM, MOMPoint, MOMDiscussionType, MOMPointStatus } from '../../types';
import { formatDateForInput } from '../../utils/dateFormatter';

interface MOMEditModalProps {
  mom: MOM;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedMOM: MOM) => void;
}

interface MOMPointFormData {
  id?: string;
  date?: string;
  discussionType?: MOMDiscussionType;
  station?: string;
  discussion: string;
  actionPlan?: string;
  responsibility?: string;
  plannedDate?: string;
  completionDate?: string;
  status: MOMPointStatus;
  remarks?: string;
  isNew?: boolean;
}

const MOMEditModal: React.FC<MOMEditModalProps> = ({ mom, isOpen, onClose, onSave }) => {
  const [projects] = useAtom(projectsAtom);
  const [customers] = useAtom(customersAtom);
  const [currentUser] = useAtom(currentUserAtom);

  // Form state
  const [meetingDate, setMeetingDate] = useState<string>('');
  const [mekhosAttendees, setMekhosAttendees] = useState<string[]>(['']);
  const [customerAttendees, setCustomerAttendees] = useState<string[]>(['']);
  const [discussionPoints, setDiscussionPoints] = useState<MOMPointFormData[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get project and customer info
  const project = projects.find(p => p.id === mom.projectId);
  const customer = customers.find(c => c.id === mom.customerId);

  useEffect(() => {
    if (mom && isOpen) {
      // Initialize form with MOM data
      setMeetingDate(mom.date ? formatDateForInput(new Date(mom.date)) : '');

      // Initialize attendees (you might need to adjust this based on your MOM structure)
      setMekhosAttendees(mom.attendees?.filter(a => a.type === 'mekhos').map(a => a.name) || ['']);
      setCustomerAttendees(mom.attendees?.filter(a => a.type === 'customer').map(a => a.name) || ['']);

      // Initialize discussion points
      const points: MOMPointFormData[] = mom.mompoint?.map(point => ({
        id: point.id,
        date: point.date ? formatDateForInput(new Date(point.date)) : '',
        discussionType: point.discussionType,
        station: point.station || '',
        discussion: point.discussion,
        actionPlan: point.actionPlan || '',
        responsibility: point.responsibility || '',
        plannedDate: point.plannedDate ? formatDateForInput(new Date(point.plannedDate)) : '',
        completionDate: point.completionDate ? formatDateForInput(new Date(point.completionDate)) : '',
        status: point.status,
        remarks: point.remarks || '',
      })) || [];

      // Add empty row if no points exist
      if (points.length === 0) {
        points.push({
          discussion: '',
          status: MOMPointStatus.PENDING,
          isNew: true
        });
      }

      setDiscussionPoints(points);
    }
  }, [mom, isOpen]);

  const addDiscussionPoint = () => {
    setDiscussionPoints([...discussionPoints, {
      discussion: '',
      status: MOMPointStatus.PENDING,
      isNew: true
    }]);
  };

  const removeDiscussionPoint = (index: number) => {
    setDiscussionPoints(discussionPoints.filter((_, i) => i !== index));
  };

  const updateDiscussionPoint = (index: number, field: keyof MOMPointFormData, value: any) => {
    const updated = [...discussionPoints];
    updated[index] = { ...updated[index], [field]: value };
    setDiscussionPoints(updated);
  };

  const addAttendee = (type: 'mekhos' | 'customer') => {
    if (type === 'mekhos') {
      setMekhosAttendees([...mekhosAttendees, '']);
    } else {
      setCustomerAttendees([...customerAttendees, '']);
    }
  };

  const removeAttendee = (type: 'mekhos' | 'customer', index: number) => {
    if (type === 'mekhos') {
      setMekhosAttendees(mekhosAttendees.filter((_, i) => i !== index));
    } else {
      setCustomerAttendees(customerAttendees.filter((_, i) => i !== index));
    }
  };

  const updateAttendee = (type: 'mekhos' | 'customer', index: number, value: string) => {
    if (type === 'mekhos') {
      const updated = [...mekhosAttendees];
      updated[index] = value;
      setMekhosAttendees(updated);
    } else {
      const updated = [...customerAttendees];
      updated[index] = value;
      setCustomerAttendees(updated);
    }
  };

  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // Prepare updated MOM data
      const updatedMOM: MOM = {
        ...mom,
        date: new Date(meetingDate).toISOString(),
        // You'll need to update attendees structure based on your needs
        mompoint: discussionPoints.filter(point => point.discussion.trim()).map((point, index) => ({
          id: point.id || '',
          momId: mom.id,
          slNo: index + 1,
          date: point.date ? new Date(point.date).toISOString() : null,
          discussionType: point.discussionType,
          station: point.station,
          discussion: point.discussion,
          actionPlan: point.actionPlan,
          responsibility: point.responsibility,
          plannedDate: point.plannedDate ? new Date(point.plannedDate).toISOString() : null,
          completionDate: point.completionDate ? new Date(point.completionDate).toISOString() : null,
          status: point.status,
          remarks: point.remarks,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }))
      };

      onSave(updatedMOM);
    } catch (error) {
      console.error('Error saving MOM:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-7xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Calendar className="h-5 w-5 text-blue-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900">Edit Minutes of Meeting</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Project Information */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Project Information</h3>
            <div className="grid grid-cols-2 gap-6 bg-blue-50 p-4 rounded-lg">
              <div>
                <label className="block text-sm font-medium text-blue-700 mb-1">Project:</label>
                <p className="text-blue-900 font-medium">{project?.name || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-blue-700 mb-1">Project Code:</label>
                <p className="text-blue-900 font-medium">{project?.code || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-blue-700 mb-1">Customer:</label>
                <p className="text-blue-900 font-medium">{customer?.name || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-blue-700 mb-1">PO Number:</label>
                <p className="text-blue-900 font-medium">{project?.poNumber || 'N/A'}</p>
              </div>
            </div>
          </div>

          {/* Meeting Date */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="inline h-4 w-4 mr-1" />
              Meeting Date
            </label>
            <input
              type="date"
              value={meetingDate}
              onChange={(e) => setMeetingDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Discussion Points */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Discussion Points</h3>
              <button
                onClick={addDiscussionPoint}
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Point
              </button>
            </div>

            {/* Table Header */}
            <div className="overflow-x-auto">
              <table className="w-full border border-gray-300 rounded-lg overflow-hidden">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Date</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Type</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Station</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Discussion</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Action Plan</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Responsibility</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Planned Date</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Completion Date</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Status</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">Result/Comments</th>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {discussionPoints.map((point, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-2 py-2 border-r border-gray-300">
                        <input
                          type="date"
                          value={point.date || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'date', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="dd-mm-yyyy"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <input
                          type="text"
                          value={point.discussionType || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'discussionType', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="Discussion Type"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <input
                          type="text"
                          value={point.station || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'station', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="Station"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <textarea
                          value={point.discussion}
                          onChange={(e) => updateDiscussionPoint(index, 'discussion', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                          rows={2}
                          placeholder="Enter discussion"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <textarea
                          value={point.actionPlan || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'actionPlan', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                          rows={2}
                          placeholder="Action plan"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <input
                          type="text"
                          value={point.responsibility || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'responsibility', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="Responsibility"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <input
                          type="date"
                          value={point.plannedDate || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'plannedDate', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <input
                          type="date"
                          value={point.completionDate || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'completionDate', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <select
                          value={point.status}
                          onChange={(e) => updateDiscussionPoint(index, 'status', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          {Object.values(MOMPointStatus).map(status => (
                            <option key={status} value={status}>{status}</option>
                          ))}
                        </select>
                      </td>
                      <td className="px-2 py-2 border-r border-gray-300">
                        <textarea
                          value={point.remarks || ''}
                          onChange={(e) => updateDiscussionPoint(index, 'remarks', e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                          rows={2}
                          placeholder="Result/comments"
                        />
                      </td>
                      <td className="px-2 py-2">
                        <button
                          onClick={() => removeDiscussionPoint(index)}
                          className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors"
                          title="Remove point"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSubmitting}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MOMEditModal;
