import { User, Project, Task, Department, Engineer, MOM, Customer, Alert, Subtask, ProjectCategoryItem, MilestoneTemplate } from '../types';

// Use the same IP address as the client to connect to the backend
// This allows the app to work both on localhost and when accessed from other devices
const getApiUrl = () => {
  const hostname = window.location.hostname;
  return `http://${hostname}:5002/api`;
};

const API_URL = getApiUrl();

// Track the last request time to add delay between requests
let lastRequestTime = 0;
const MIN_REQUEST_INTERVAL = 100; // 100ms minimum interval between requests

// Helper function for making API requests
async function apiRequest<T>(
  endpoint: string,
  method: string = 'GET',
  data?: any,
  params?: Record<string, any>
): Promise<T> {
  // Add a small delay between requests to prevent overwhelming the server
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
    await new Promise(resolve => setTimeout(resolve, MIN_REQUEST_INTERVAL - timeSinceLastRequest));
  }

  lastRequestTime = Date.now();

  let url = `${API_URL}${endpoint}`;

  // Add query parameters if provided
  if (params && Object.keys(params).length > 0) {
    const queryParams = new URLSearchParams();
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null) {
        queryParams.append(key, params[key].toString());
      }
    }
    url += `?${queryParams.toString()}`;
  }

  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };

  // Add auth token if available
  const token = localStorage.getItem('token');
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  const config: RequestInit = {
    method,
    headers,
    body: data && method !== 'GET' ? JSON.stringify(data) : undefined,
    // Add timeout to avoid hanging requests
    signal: AbortSignal.timeout(30000), // 30 seconds timeout
    // Only include credentials for same-origin requests or when explicitly needed
    credentials: window.location.hostname === new URL(url).hostname ? 'include' : 'omit',
  };

  console.log(`API Request: ${method} ${url}`, method !== 'GET' ? {
    data: method === 'POST' || method === 'PUT' ?
      { ...data, password: data?.password ? '******' : undefined } :
      undefined
  } : undefined);

  try {
    console.log(`[API Request] Sending ${method} request to ${url}`, {
      method,
      headers: config.headers,
      body: config.body ? JSON.parse(config.body as string) : undefined
    });

    const response = await fetch(url, config);

    console.log(`[API Response] Received response from ${url}:`, {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries([...response.headers.entries()])
    });

    if (!response.ok) {
      // First, let's try to get the response text to see what we're dealing with
      const responseText = await response.text();
      console.error('Raw error response:', responseText);

      try {
        // Try to parse as JSON
        const errorData = JSON.parse(responseText);
        console.error('API Error:', {
          status: response.status,
          statusText: response.statusText,
          errorData
        });
        console.error('Error message from server:', errorData.message);

        // Handle specific error cases
        if (response.status === 401) {
          // Clear authentication data on 401 errors
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          throw new Error(errorData.message || 'Authentication error: Please log in again');
        } else if (response.status === 403) {
          throw new Error(errorData.message || 'You do not have permission to perform this action.');
        } else if (response.status === 404) {
          throw new Error(errorData.message || 'The requested resource was not found.');
        } else if (response.status >= 500) {
          throw new Error(errorData.message || 'Server error. Please try again later.');
        } else {
          throw new Error(errorData.message || `Server error: ${response.status}`);
        }
      } catch (parseError) {
        // If parsing JSON fails, use the raw response text
        console.error('JSON parsing failed:', parseError);
        console.error('API Error (non-JSON):', {
          status: response.status,
          statusText: response.statusText,
          responseText,
          url,
          method
        });

        if (response.status === 0 || !navigator.onLine) {
          throw new Error('Network error. Please check your internet connection.');
        } else if (response.status === 401) {
          // Clear authentication data on 401 errors
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          throw new Error('Authentication error: Please log in again');
        } else if (response.status === 403) {
          throw new Error('You do not have permission to perform this action.');
        } else if (response.status === 404) {
          throw new Error('The requested resource was not found.');
        } else if (response.status >= 500) {
          throw new Error('Server error. Please try again later.');
        } else {
          // Include response text in error for debugging
          const errorMsg = responseText ? `Server error: ${response.status} - ${responseText}` : `Server error: ${response.status} - ${response.statusText}`;
          throw new Error(errorMsg);
        }
      }
    }

    try {
      const jsonData = await response.json();
      console.log(`Parsed JSON response from ${url}:`, jsonData);
      return jsonData;
    } catch (e) {
      console.error(`Failed to parse JSON response from ${url}:`, e);
      throw new Error('Invalid response format from server');
    }
  } catch (error) {
    console.error(`API Request failed: ${method} ${url}`, error);
    if (error instanceof DOMException && error.name === 'AbortError') {
      throw new Error('Request timed out. Please try again.');
    }
    throw error;
  }
}

// Auth API
export const authAPI = {
  login: async (email: string, password: string): Promise<{ data: User & { token: string } }> => {
    return apiRequest('/auth/login', 'POST', { email, password });
  },

  register: async (userData: Partial<User>): Promise<{ data: User & { token: string } }> => {
    return apiRequest('/auth/register', 'POST', userData);
  },

  getCurrentUser: async (): Promise<{ data: User }> => {
    return apiRequest('/auth/me');
  },

  changePassword: async (currentPassword: string, newPassword: string): Promise<{ success: boolean; message: string }> => {
    return apiRequest('/auth/change-password', 'POST', { currentPassword, newPassword });
  },
};

// Settings API
export const settingsAPI = {
  getProfile: async (): Promise<{ data: User }> => {
    return apiRequest('/settings/profile');
  },

  updateProfile: async (profileData: { name: string; email: string; phone?: string }): Promise<{ data: User }> => {
    return apiRequest('/settings/profile', 'PUT', profileData);
  },

  changePassword: async (currentPassword: string, newPassword: string): Promise<{ success: boolean; message: string }> => {
    return apiRequest('/settings/password', 'PUT', { currentPassword, newPassword });
  },

  // Notification settings
  getNotificationSettings: async (): Promise<{ success: boolean; data: any }> => {
    return apiRequest('/settings/notifications');
  },

  updateNotificationSettings: async (settingsData: any): Promise<{ success: boolean; data: any }> => {
    return apiRequest('/settings/notifications', 'PUT', settingsData);
  },

  // All user settings (legacy)
  getUserSettings: async (): Promise<{ success: boolean; data: any }> => {
    return apiRequest('/settings/notifications');
  },

  updateUserSettings: async (settingsData: any): Promise<{ success: boolean; data: any }> => {
    return apiRequest('/settings/notifications', 'PUT', settingsData);
  },
};

// Users API
export const usersAPI = {
  getUsers: async (): Promise<{ data: User[] }> => {
    return apiRequest('/users');
  },

  getUser: async (id: string): Promise<{ data: User }> => {
    return apiRequest(`/users/${id}`);
  },

  createUser: async (userData: Partial<User>): Promise<{ data: User }> => {
    return apiRequest('/users', 'POST', userData);
  },

  updateUser: async (id: string, userData: Partial<User>): Promise<{ data: User }> => {
    return apiRequest(`/users/${id}`, 'PUT', userData);
  },

  deleteUser: async (id: string): Promise<{ data: {} }> => {
    return apiRequest(`/users/${id}`, 'DELETE');
  },
};

// Projects API
export const projectsAPI = {
  getProjects: async (): Promise<{ data: Project[] }> => {
    // Add cache-busting parameter to ensure fresh data
    return apiRequest('/projects', 'GET', undefined, { _t: Date.now() });
  },

  getProject: async (id: string): Promise<{ data: Project }> => {
    // Add cache-busting parameter to ensure fresh data
    return apiRequest(`/projects/${id}`, 'GET', undefined, { _t: Date.now() });
  },

  createProject: async (projectData: Partial<Project>): Promise<{ data: Project }> => {
    return apiRequest('/projects', 'POST', projectData);
  },

  updateProject: async (id: string, projectData: Partial<Project>): Promise<{ data: Project }> => {
    return apiRequest(`/projects/${id}`, 'PUT', projectData);
  },

  deleteProject: async (id: string): Promise<{ data: {} }> => {
    return apiRequest(`/projects/${id}`, 'DELETE');
  },

  getExcelTemplateInfo: async (): Promise<{ data: any }> => {
    return apiRequest('/projects/excel-template-info');
  },

  downloadExcelTemplate: async (projectId: string): Promise<Blob> => {
    const response = await fetch(`${API_URL}/projects/${projectId}/excel-template`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to download template');
    }

    return response.blob();
  },

  // Excel import/export
  importExcelData: async (id: string, sections: any[]): Promise<{ data: any }> => {
    console.log('🌐 API: Making Excel import request');
    console.log('🌐 API: Project ID:', id);
    console.log('🌐 API: Sections:', sections);
    console.log('🌐 API: URL:', `/projects/${id}/excel-import`);

    // Debug: Check the structure of each section
    sections.forEach((section, index) => {
      console.log(`🔍 Section ${index}: ${section.name}`);
      console.log(`🔍 Section ${index} tasks property:`, section.tasks);
      console.log(`🔍 Section ${index} task property:`, section.task);
      console.log(`🔍 Section ${index} tasks length:`, section.tasks?.length || 0);

      // Show task names being sent
      section.tasks?.forEach((task, taskIndex) => {
        console.log(`  📋 Task ${taskIndex + 1}: "${task.name}" (ID: ${task.id})`);
      });
    });

    const result = await apiRequest(`/projects/${id}/excel-import`, 'POST', { sections });

    console.log('🌐 API: Excel import response:', result);
    console.log('🌐 API: Import results data:', result.data);
    return result;
  },

  exportExcelData: async (id: string): Promise<{ data: any }> => {
    return apiRequest(`/projects/${id}/excel-export`);
  },
};

// Tasks API
export const tasksAPI = {
  getTasks: async (): Promise<{ data: Task[] }> => {
    // Add cache-busting parameter to ensure fresh data
    return apiRequest('/tasks', 'GET', undefined, { _t: Date.now() });
  },

  getTask: async (id: string): Promise<{ data: Task }> => {
    // Add cache-busting parameter to ensure fresh assignee data
    return apiRequest(`/tasks/${id}`, 'GET', undefined, { _t: Date.now() });
  },

  createTask: async (taskData: Partial<Task>): Promise<{ data: Task }> => {
    return apiRequest('/tasks', 'POST', taskData);
  },

  updateTask: async (id: string, taskData: Partial<Task>): Promise<{ data: Task }> => {
    console.log('[API] updateTask called with:', { id, taskData });
    try {
      const result = await apiRequest(`/tasks/${id}`, 'PUT', taskData);
      console.log('[API] updateTask result:', result);
      return result as { data: Task };
    } catch (error) {
      console.error('[API] updateTask error:', error);
      throw error;
    }
  },

  deleteTask: async (id: string): Promise<{ data: {} }> => {
    return apiRequest(`/tasks/${id}`, 'DELETE');
  },
};



// Departments API (legacy - keeping for backward compatibility)
export const departmentsAPI = {
  getDepartments: async (): Promise<{ data: Department[] }> => {
    return apiRequest('/departments');
  },

  getDepartment: async (id: string): Promise<{ data: Department }> => {
    return apiRequest(`/departments/${id}`);
  },

  createDepartment: async (departmentData: Partial<Department>): Promise<{ data: Department }> => {
    return apiRequest('/departments', 'POST', departmentData);
  },

  updateDepartment: async (id: string, departmentData: Partial<Department>): Promise<{ data: Department }> => {
    return apiRequest(`/departments/${id}`, 'PUT', departmentData);
  },

  deleteDepartment: async (id: string): Promise<{ data: {} }> => {
    return apiRequest(`/departments/${id}`, 'DELETE');
  },
};

// Customers API
export const customersAPI = {
  getCustomers: async (): Promise<{ data: Customer[] }> => {
    return apiRequest('/customers');
  },

  getCustomer: async (id: string): Promise<{ data: Customer }> => {
    return apiRequest(`/customers/${id}`);
  },

  createCustomer: async (customerData: Partial<Customer>): Promise<{ data: Customer }> => {
    return apiRequest('/customers', 'POST', customerData);
  },

  updateCustomer: async (id: string, customerData: Partial<Customer>): Promise<{ data: Customer }> => {
    return apiRequest(`/customers/${id}`, 'PUT', customerData);
  },

  deleteCustomer: async (id: string): Promise<{ data: {} }> => {
    return apiRequest(`/customers/${id}`, 'DELETE');
  },
};

// Engineers API
export const engineersAPI = {
  getEngineers: async (): Promise<{ data: Engineer[] }> => {
    return apiRequest('/engineers');
  },

  getEngineer: async (id: string): Promise<{ data: Engineer }> => {
    return apiRequest(`/engineers/${id}`);
  },

  createEngineer: async (engineerData: Partial<Engineer>): Promise<{ data: Engineer }> => {
    return apiRequest('/engineers', 'POST', engineerData);
  },

  updateEngineer: async (id: string, engineerData: Partial<Engineer>): Promise<{ data: Engineer }> => {
    return apiRequest(`/engineers/${id}`, 'PUT', engineerData);
  },

  deleteEngineer: async (id: string): Promise<{ data: {} }> => {
    return apiRequest(`/engineers/${id}`, 'DELETE');
  },
};

// MOMs API
export const momsAPI = {
  getMOMs: async (): Promise<{ data: MOM[] }> => {
    return apiRequest('/moms');
  },

  getMOM: async (id: string): Promise<{ data: MOM }> => {
    return apiRequest(`/moms/${id}`);
  },

  getMOMByProject: async (projectId: string): Promise<{ success: boolean; data: MOM }> => {
    return apiRequest(`/moms/project/${projectId}`);
  },

  createMOM: async (momData: { projectId: string }): Promise<{ success: boolean; data: MOM }> => {
    return apiRequest('/moms', 'POST', momData);
  },

  updateMOM: async (id: string, momData: Partial<MOM>): Promise<{ success: boolean; data: MOM }> => {
    return apiRequest(`/moms/${id}`, 'PUT', momData);
  },

  deleteMOM: async (id: string): Promise<{ success: boolean; data: {} }> => {
    return apiRequest(`/moms/${id}`, 'DELETE');
  },

  addMOMPoint: async (momId: string, pointData: any): Promise<{ success: boolean; data: any }> => {
    return apiRequest(`/moms/${momId}/points`, 'POST', pointData);
  },

  updateMOMPoint: async (momId: string, pointId: string, pointData: any): Promise<{ success: boolean; data: any }> => {
    return apiRequest(`/moms/${momId}/points/${pointId}`, 'PUT', pointData);
  },

  deleteMOMPoint: async (momId: string, pointId: string): Promise<{ success: boolean; data: {} }> => {
    return apiRequest(`/moms/${momId}/points/${pointId}`, 'DELETE');
  },
};

// Alerts API
export const alertsAPI = {
  getAlerts: async (params?: {
    priority?: string;
    status?: string;
    type?: string;
    search?: string;
  }): Promise<{ data: Alert[] }> => {
    return apiRequest('/alerts', 'GET', null, params);
  },

  getUnreadAlertsCount: async (): Promise<{ data: { count: number } }> => {
    return apiRequest('/alerts/unread-count', 'GET');
  },

  getAlert: async (id: string): Promise<{ data: Alert }> => {
    return apiRequest(`/alerts/${id}`);
  },

  createAlert: async (alertData: Partial<Alert>): Promise<{ data: Alert }> => {
    return apiRequest('/alerts', 'POST', alertData);
  },

  updateAlert: async (id: string, alertData: Partial<Alert>): Promise<{ data: Alert }> => {
    return apiRequest(`/alerts/${id}`, 'PUT', alertData);
  },

  deleteAlert: async (id: string): Promise<{ data: {} }> => {
    return apiRequest(`/alerts/${id}`, 'DELETE');
  },

  markAsRead: async (id: string): Promise<{ data: Alert }> => {
    return apiRequest(`/alerts/${id}/read`, 'PUT');
  },

  resolveAlert: async (id: string): Promise<{ data: Alert }> => {
    return apiRequest(`/alerts/${id}/resolve`, 'PUT');
  },

  cleanupOldAlerts: async (): Promise<{ success: boolean; message: string; data: { updatedCount: number } }> => {
    return apiRequest('/alerts/cleanup', 'POST');
  },
};

// Subtasks API
export const subtasksAPI = {
  getSubtasks: async (): Promise<{ data: Subtask[] }> => {
    return apiRequest('/subtasks');
  },

  getSubtask: async (id: string): Promise<{ data: Subtask }> => {
    return apiRequest(`/subtasks/${id}`);
  },

  createSubtask: async (subtaskData: Partial<Subtask>): Promise<{ data: Subtask }> => {
    return apiRequest('/subtasks', 'POST', subtaskData);
  },

  updateSubtask: async (id: string, subtaskData: Partial<Subtask>): Promise<{ data: Subtask }> => {
    return apiRequest(`/subtasks/${id}`, 'PUT', subtaskData);
  },

  deleteSubtask: async (id: string): Promise<{ data: any }> => {
    return apiRequest(`/subtasks/${id}`, 'DELETE');
  },
};

// Project Categories API
export const projectCategoriesAPI = {
  getProjectCategories: async (): Promise<{ data: ProjectCategoryItem[] }> => {
    return apiRequest('/project-categories');
  },

  getProjectCategory: async (id: string): Promise<{ data: ProjectCategoryItem }> => {
    return apiRequest(`/project-categories/${id}`);
  },

  createProjectCategory: async (categoryData: Omit<ProjectCategoryItem, 'id' | 'code' | 'createdAt' | 'updatedAt' | '_count'>): Promise<{ data: ProjectCategoryItem }> => {
    return apiRequest('/project-categories', 'POST', categoryData);
  },

  updateProjectCategory: async (id: string, categoryData: Partial<Omit<ProjectCategoryItem, 'id' | 'code' | 'createdAt' | 'updatedAt' | '_count'>>): Promise<{ data: ProjectCategoryItem }> => {
    return apiRequest(`/project-categories/${id}`, 'PUT', categoryData);
  },

  deleteProjectCategory: async (id: string): Promise<{ data: {} }> => {
    return apiRequest(`/project-categories/${id}`, 'DELETE');
  },
};

// Milestone Templates API
export const milestoneTemplatesAPI = {
  getMilestoneTemplates: async (): Promise<{ data: MilestoneTemplate[] }> => {
    return apiRequest('/milestone-templates');
  },

  getMilestoneTemplate: async (id: string): Promise<{ data: MilestoneTemplate }> => {
    return apiRequest(`/milestone-templates/${id}`);
  },

  createMilestoneTemplate: async (templateData: Omit<MilestoneTemplate, 'id' | 'sequence' | 'createdAt' | 'updatedAt'>): Promise<{ data: MilestoneTemplate }> => {
    return apiRequest('/milestone-templates', 'POST', templateData);
  },

  updateMilestoneTemplate: async (id: string, templateData: Partial<Omit<MilestoneTemplate, 'id' | 'sequence' | 'createdAt' | 'updatedAt'>>): Promise<{ data: MilestoneTemplate }> => {
    return apiRequest(`/milestone-templates/${id}`, 'PUT', templateData);
  },

  deleteMilestoneTemplate: async (id: string): Promise<{ data: {} }> => {
    return apiRequest(`/milestone-templates/${id}`, 'DELETE');
  },
};

// Payments API
export const paymentsAPI = {
  getPaymentData: async (): Promise<{ success: boolean; data: { summary: any; payments: any[] } }> => {
    return apiRequest('/payments');
  },

  getPaymentSummary: async (): Promise<{ success: boolean; data: any }> => {
    return apiRequest('/payments/summary');
  },

  getPaymentRecords: async (): Promise<{ success: boolean; data: { payments: any[]; total: number } }> => {
    return apiRequest('/payments/records');
  },

  getPaymentStats: async (): Promise<{ success: boolean; data: any }> => {
    return apiRequest('/payments/stats');
  },

  exportPaymentData: async (): Promise<{ success: boolean; data: any }> => {
    return apiRequest('/payments/export');
  },
};

// General API object for direct requests
export const api = {
  get: async <T>(endpoint: string, params?: Record<string, any>): Promise<T> => {
    return apiRequest(endpoint, 'GET', undefined, params);
  },

  post: async <T>(endpoint: string, data?: any): Promise<T> => {
    return apiRequest(endpoint, 'POST', data);
  },

  put: async <T>(endpoint: string, data?: any): Promise<T> => {
    return apiRequest(endpoint, 'PUT', data);
  },

  delete: async <T>(endpoint: string): Promise<T> => {
    return apiRequest(endpoint, 'DELETE');
  },
};


