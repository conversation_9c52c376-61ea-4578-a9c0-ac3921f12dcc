import React, { useState } from 'react';
import { useAtom } from 'jotai';
import { customers<PERSON><PERSON>, engineers<PERSON>tom, moms<PERSON>tom, currentUser<PERSON>tom } from '../../store';
import { MOM, MOMAttendee, MOMActionItem, Customer } from '../../types';
import { v4 as uuidv4 } from 'uuid';
import { CheckCircle2, XCircle, PlusCircle } from 'lucide-react';
import CustomerDialog from '../Customers/CustomerDialog';
import EditableGrid, { GridColumn, GridRow } from '../common/EditableGrid';
import { useNavigate } from 'react-router-dom';

const MOMFormGrid: React.FC = () => {
  const navigate = useNavigate();
  const [customers, setCustomers] = useAtom(customersAtom);
  const [engineers] = useAtom(engineersAtom);
  const [, setMoms] = useAtom(momsAtom);
  const [currentUser] = useAtom(currentUserAtom);

  const [date, setDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [customerId, setCustomerId] = useState<string>('');
  const [engineerId, setEngineerId] = useState<string>('');
  const [agenda, setAgenda] = useState<string>('');
  
  const [mekhosAttendees, setMekhosAttendees] = useState<GridRow[]>([
    { id: uuidv4(), name: '', designation: '', email: '' }
  ]);
  
  const [customerAttendees, setCustomerAttendees] = useState<GridRow[]>([
    { id: uuidv4(), name: '', designation: '', email: '' }
  ]);
  
  const [points, setPoints] = useState<GridRow[]>([
    { id: uuidv4(), point: '' }
  ]);
  
  const [actionItems, setActionItems] = useState<GridRow[]>([
    { id: uuidv4(), description: '', assigneeId: '', dueDate: '' }
  ]);

  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState(false);
  const [newCustomerName, setNewCustomerName] = useState('');
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Column definitions for each grid
  const mekhosAttendeesColumns: GridColumn[] = [
    { id: 'name', header: 'Name', required: true },
    { id: 'designation', header: 'Designation' },
    { id: 'email', header: 'Email', type: 'email' }
  ];

  const customerAttendeesColumns: GridColumn[] = [
    { id: 'name', header: 'Name', required: true },
    { id: 'designation', header: 'Designation' },
    { id: 'email', header: 'Email', type: 'email' }
  ];

  const pointsColumns: GridColumn[] = [
    { id: 'point', header: 'Discussion Point', width: '100%' }
  ];

  const actionItemsColumns: GridColumn[] = [
    { id: 'description', header: 'Description', width: '50%', required: true },
    { 
      id: 'assigneeId', 
      header: 'Assignee', 
      type: 'select',
      options: engineers.map(eng => ({ value: eng.id, label: eng.name })),
      required: true
    },
    { id: 'dueDate', header: 'Due Date', type: 'date', required: true }
  ];

  // Handler for adding a new customer
  const handleAddCustomer = (newCustomer: Customer) => {
    setCustomers((prevCustomers) => [...prevCustomers, newCustomer]);
    setCustomerId(newCustomer.id);
    setIsCustomerDialogOpen(false);
  };

  // Validation function
  const validate = (): boolean => {
    const newErrors: {[key: string]: string} = {};
    
    if (!date) newErrors.date = 'Meeting date is required';
    if (!customerId) newErrors.customerId = 'Customer is required';
    if (!engineerId) newErrors.engineerId = 'Engineer is required';
    if (!agenda.trim()) newErrors.agenda = 'Agenda is required';
    
    // Check if at least one attendee from Mekhos has a name
    if (!mekhosAttendees.some(a => a.name?.trim())) {
      newErrors.mekhosAttendees = 'At least one Mekhos attendee is required';
    }
    
    // Check if at least one attendee from customer has a name
    if (!customerAttendees.some(a => a.name?.trim())) {
      newErrors.customerAttendees = 'At least one customer attendee is required';
    }
    
    // Check if at least one point is entered
    if (!points.some(p => p.point?.trim())) {
      newErrors.points = 'At least one discussion point is required';
    }
    
    // Check action items
    const invalidActionItems = actionItems.filter(item => 
      item.description?.trim() && (!item.assigneeId || !item.dueDate)
    );
    
    if (invalidActionItems.length > 0) {
      newErrors.actionItems = 'All action items must have an assignee and due date';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Form submission handler
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validate()) {
      return;
    }
    
    setIsSubmitting(true);
    
    // Simulate API call delay
    setTimeout(() => {
      try {
        // Combine all attendees with their respective companies
        const allAttendees: MOMAttendee[] = [
          ...mekhosAttendees
            .filter(attendee => attendee.name?.trim())
            .map(attendee => ({
              id: attendee.id,
              name: attendee.name,
              company: "Mekhos Technology",
              designation: attendee.designation || '',
              email: attendee.email || ''
            })),
          ...customerAttendees
            .filter(attendee => attendee.name?.trim())
            .map(attendee => ({
              id: attendee.id,
              name: attendee.name,
              company: customers.find(c => c.id === customerId)?.name || "Unknown",
              designation: attendee.designation || '',
              email: attendee.email || ''
            }))
        ];

        // Map action items
        const formattedActionItems: MOMActionItem[] = actionItems
          .filter(item => item.description?.trim())
          .map(item => ({
            id: item.id,
            description: item.description,
            assigneeId: item.assigneeId,
            dueDate: item.dueDate,
            status: "PENDING"
          }));

        // Create new MOM entry
        const newMOM: MOM = {
          id: uuidv4(),
          date,
          customerId,
          engineerId,
          agenda,
          attendees: allAttendees,
          points: points.filter(p => p.point?.trim()).map(p => p.point),
          actionItems: formattedActionItems,
          createdBy: currentUser?.id || '',
          createdAt: new Date().toISOString()
        };

        // Add the new MOM to the state
        setMoms((prevMoms) => [...prevMoms, newMOM]);

        // Show success message
        setSuccess('Minutes of Meeting created successfully!');

        // Reset the form
        resetForm();

        // Clear success message after 5 seconds
        setTimeout(() => {
          setSuccess(null);
        }, 5002);
      } catch (error) {
        console.error('Error creating MOM:', error);
      } finally {
        setIsSubmitting(false);
      }
    }, 1000);
  };

  // Reset form to initial state
  const resetForm = () => {
    setDate(new Date().toISOString().split('T')[0]);
    setCustomerId('');
    setEngineerId('');
    setAgenda('');
    setMekhosAttendees([{ id: uuidv4(), name: '', designation: '', email: '' }]);
    setCustomerAttendees([{ id: uuidv4(), name: '', designation: '', email: '' }]);
    setPoints([{ id: uuidv4(), point: '' }]);
    setActionItems([{ id: uuidv4(), description: '', assigneeId: '', dueDate: '' }]);
    setErrors({});
  };

  return (
    <div className="card p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Create Minutes of Meeting</h2>
      </div>

      {success && (
        <div className="bg-success/10 text-success-dark p-4 rounded-md flex items-center justify-between mb-6 fade-in">
          <div className="flex items-center">
            <CheckCircle2 size={18} className="mr-2" />
            <span>{success}</span>
          </div>
          <button
            onClick={() => setSuccess(null)}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle size={18} />
          </button>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label htmlFor="date" className="form-label">Meeting Date</label>
            <input
              id="date"
              type="date"
              className={`form-input ${errors.date ? 'border-error' : ''}`}
              value={date}
              onChange={(e) => setDate(e.target.value)}
              required
            />
            {errors.date && (
              <p className="mt-1 text-xs text-error">{errors.date}</p>
            )}
          </div>

          <div>
            <label htmlFor="customer" className="form-label">Customer</label>
            <div className="flex space-x-2">
              <div className="flex-1">
                <select
                  id="customer"
                  className={`form-select ${errors.customerId ? 'border-error' : ''}`}
                  value={customerId}
                  onChange={(e) => setCustomerId(e.target.value)}
                  required
                >
                  <option value="">Select Customer</option>
                  {customers.map((customer) => (
                    <option key={customer.id} value={customer.id}>{customer.name}</option>
                  ))}
                </select>
              </div>
              <button
                type="button"
                className="btn btn-outline flex items-center py-2"
                onClick={() => navigate('/customers?addCustomer=true')}
              >
                <PlusCircle size={16} className="mr-1" />
                New
              </button>
            </div>
            {errors.customerId && (
              <p className="mt-1 text-xs text-error">{errors.customerId}</p>
            )}
          </div>

          <div>
            <label htmlFor="engineerId" className="form-label">Lead Engineer</label>
            <select
              id="engineerId"
              className={`form-select ${errors.engineerId ? 'border-error' : ''}`}
              value={engineerId}
              onChange={(e) => setEngineerId(e.target.value)}
              required
            >
              <option value="">Select Engineer</option>
              {engineers.map((engineer) => (
                <option key={engineer.id} value={engineer.id}>
                  {engineer.name} ({engineer.department})
                </option>
              ))}
            </select>
            {errors.engineerId && (
              <p className="mt-1 text-xs text-error">{errors.engineerId}</p>
            )}
          </div>
        </div>

        <div>
          <label htmlFor="agenda" className="form-label">Meeting Agenda</label>
          <textarea
            id="agenda"
            className={`form-input ${errors.agenda ? 'border-error' : ''}`}
            rows={3}
            placeholder="Enter the meeting agenda..."
            value={agenda}
            onChange={(e) => setAgenda(e.target.value)}
            required
          />
          {errors.agenda && (
            <p className="mt-1 text-xs text-error">{errors.agenda}</p>
          )}
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-800">Attendees from Mekhos Technology</h3>
          </div>
          
          <EditableGrid
            columns={mekhosAttendeesColumns}
            rows={mekhosAttendees}
            onRowsChange={setMekhosAttendees}
            onAddRow={() => setMekhosAttendees([...mekhosAttendees, { id: uuidv4(), name: '', designation: '', email: '' }])}
            onRemoveRow={(rowId) => setMekhosAttendees(mekhosAttendees.filter(row => row.id !== rowId))}
            addRowButtonText="Add Attendee"
          />
          
          {errors.mekhosAttendees && (
            <p className="mt-1 text-xs text-error">{errors.mekhosAttendees}</p>
          )}
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-800">Attendees from Customer</h3>
          </div>
          
          <EditableGrid
            columns={customerAttendeesColumns}
            rows={customerAttendees}
            onRowsChange={setCustomerAttendees}
            onAddRow={() => setCustomerAttendees([...customerAttendees, { id: uuidv4(), name: '', designation: '', email: '' }])}
            onRemoveRow={(rowId) => setCustomerAttendees(customerAttendees.filter(row => row.id !== rowId))}
            addRowButtonText="Add Attendee"
          />
          
          {errors.customerAttendees && (
            <p className="mt-1 text-xs text-error">{errors.customerAttendees}</p>
          )}
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-800">Points Discussed</h3>
          </div>
          
          <EditableGrid
            columns={pointsColumns}
            rows={points}
            onRowsChange={setPoints}
            onAddRow={() => setPoints([...points, { id: uuidv4(), point: '' }])}
            onRemoveRow={(rowId) => setPoints(points.filter(row => row.id !== rowId))}
            addRowButtonText="Add Point"
          />
          
          {errors.points && (
            <p className="mt-1 text-xs text-error">{errors.points}</p>
          )}
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-800">Action Items</h3>
          </div>
          
          <EditableGrid
            columns={actionItemsColumns}
            rows={actionItems}
            onRowsChange={setActionItems}
            onAddRow={() => setActionItems([...actionItems, { id: uuidv4(), description: '', assigneeId: '', dueDate: '' }])}
            onRemoveRow={(rowId) => setActionItems(actionItems.filter(row => row.id !== rowId))}
            addRowButtonText="Add Action Item"
          />
          
          {errors.actionItems && (
            <p className="mt-1 text-xs text-error">{errors.actionItems}</p>
          )}
        </div>

        <div className="flex justify-end mt-6">
          <button
            type="submit"
            className="btn btn-primary flex items-center"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </span>
            ) : (
              <span className="flex items-center">
                <CheckCircle2 size={18} className="mr-2" />
                Create MOM
              </span>
            )}
          </button>
        </div>
      </form>

      {/* Customer Dialog */}
      <CustomerDialog
        isOpen={isCustomerDialogOpen}
        onClose={() => setIsCustomerDialogOpen(false)}
        onSave={handleAddCustomer}
        initialData={{ name: newCustomerName }}
      />
    </div>
  );
};

export default MOMFormGrid;
