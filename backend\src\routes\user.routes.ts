import express, { Request, Response } from 'express';
import {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
} from '../controllers/user.controller';
import { protect, authorize } from '../middleware/auth.middleware';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(
    authorize('DIRECTOR', 'PROJECT_MANAGER', 'TEAM_LEAD'),
    getUsers
  )
  .post(
    authorize('DIRECTOR', 'PROJECT_MANAGER', 'TEAM_LEAD'), // Fine-grained restrictions in controller
    createUser
  );

router.route('/:id')
  .get(
    authorize('DIRECTOR', 'PROJECT_MANAGER', 'TEAM_LEAD'),
    getUser
  )
  .put(
    authorize('DIRECTOR', 'PROJECT_MANAGER', 'TEAM_LEAD', 'ENGINEER'), // Users can update themselves
    updateUser
  )
  .delete(
    authorize('DIRECTOR', 'PROJECT_MANAGER'), // Only higher roles can delete
    deleteUser
  );

export default router;
