interface TaskHierarchy {
  [projectId: string]: {
    tasks: {
      [taskId: string]: {
        subtasks: string[];
      };
    };
  };
}

/**
 * Groups tasks and subtasks by their project and task hierarchy
 * @param {string[]} backendIds - Array of backend IDs
 * @returns {TaskHierarchy} Hierarchical object grouping tasks and subtasks
 */
export function groupTasksByHierarchy(backendIds: string[]): TaskHierarchy {
  const hierarchy: TaskHierarchy = {};

  backendIds.forEach(id => {
    const projectMatch = id.match(/P(\d+)/);
    const taskMatch = id.match(/T(\d+)/);
    const subtaskMatch = id.match(/S(\d+)/);

    if (projectMatch) {
      const projectId = `P${projectMatch[1]}`;
      
      // Initialize project if it doesn't exist
      if (!hierarchy[projectId]) {
        hierarchy[projectId] = { tasks: {} };
      }

      // Add task
      if (taskMatch) {
        const taskId = id;
        
        if (!hierarchy[projectId].tasks[taskId]) {
          hierarchy[projectId].tasks[taskId] = { subtasks: [] };
        }

        // Add subtask if it exists
        if (subtaskMatch) {
          hierarchy[projectId].tasks[taskId].subtasks.push(id);
        }
      }
    }
  });

  return hierarchy;
} 