generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model alert {
  id          String         @id
  type        alert_type
  message     String
  relatedTo   String?
  read        <PERSON><PERSON><PERSON>        @default(false)
  createdAt   DateTime       @default(now())
  assigneeId  String?
  createdBy   String?
  department  String?
  dueDate     DateTime?
  priority    alert_priority @default(MEDIUM)
  relatedType String?
  resolved    Boolean        @default(false)
  title       String
  updatedAt   DateTime       @updatedAt
  user        user?          @relation(fields: [assigneeId], references: [id], map: "Alert_assigneeId_fkey")

  @@index([assigneeId], map: "Alert_assigneeId_fkey")
  @@index([priority], map: "Alert_priority_idx")
  @@index([read], map: "Alert_read_idx")
  @@index([type], map: "Alert_type_idx")
}

model customer {
  id          String    @id
  name        String
  code        String    @unique(map: "Customer_code_key")
  contactName String?
  email       String?
  phone       String?
  address     String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  project     project[]
}

model department {
  id          String  @id
  name        String  @unique(map: "Department_name_key")
  code        String  @unique(map: "Department_code_key")
  description String?
}

model mom {
  id                       String     @id
  date                     DateTime   @default(now())
  createdBy                String
  createdAt                DateTime   @default(now())
  updatedAt                DateTime   @updatedAt
  projectId                String     @unique
  updatedBy                String?
  agenda                   String?    @db.Text
  user_mom_createdByTouser user       @relation("mom_createdByTouser", fields: [createdBy], references: [id], map: "MOM_createdBy_fkey")
  project                  project    @relation(fields: [projectId], references: [id], map: "MOM_projectId_fkey")
  user_mom_updatedByTouser user?      @relation("mom_updatedByTouser", fields: [updatedBy], references: [id], map: "MOM_updatedBy_fkey")
  mompoint                 mompoint[]

  @@index([createdBy], map: "MOM_createdBy_fkey")
  @@index([projectId], map: "MOM_projectId_fkey")
  @@index([updatedBy], map: "MOM_updatedBy_fkey")
}

model mompoint {
  id              String                    @id
  momId           String
  slNo            Int
  date            DateTime?
  discussionType  mompoint_discussion_type?
  station         String?
  discussion      String                    @db.Text
  actionPlan      String?                   @db.Text
  responsibility  String?
  plannedDate     DateTime?
  completionDate  DateTime?
  status          mompoint_status           @default(PENDING)
  imageAttachment String?
  remarks         String?                   @db.Text
  createdAt       DateTime                  @default(now())
  updatedAt       DateTime                  @updatedAt
  mom             mom                       @relation(fields: [momId], references: [id], onDelete: Cascade, map: "MOMPoint_momId_fkey")

  @@unique([momId, slNo], name: "mompoint_mom_slno")
  @@index([momId], map: "MOMPoint_momId_fkey")
}

model momactionitem {
  id          String   @id
  description String
  assigneeId  String?
  dueDate     DateTime
  status      String
  projectId   String?
  user        user?    @relation(fields: [assigneeId], references: [id], map: "MOMActionItem_assigneeId_fkey")
  project     project? @relation(fields: [projectId], references: [id], map: "MOMActionItem_projectId_fkey")

  @@index([assigneeId], map: "MOMActionItem_assigneeId_fkey")
  @@index([projectId], map: "MOMActionItem_projectId_fkey")
}

model project {
  id                                  String           @id
  name                                String
  code                                String           @unique(map: "Project_code_key")
  poNumber                            String
  poDate                              DateTime
  startDate                           DateTime
  endDate                             DateTime?
  department                          String
  projectManagerId                    String
  status                              project_status
  createdBy                           String
  createdAt                           DateTime         @default(now())
  updatedAt                           DateTime         @updatedAt
  customerId                          String
  projectCategory                     project_category
  poValue                             Decimal?         @db.Decimal(15, 2)
  mom                                 mom?
  momactionitem                       momactionitem[]
  payment                             payment[]
  user_project_createdByTouser        user             @relation("project_createdByTouser", fields: [createdBy], references: [id], map: "Project_createdBy_fkey")
  customer                            customer         @relation(fields: [customerId], references: [id], map: "Project_customerId_fkey")
  user_project_projectManagerIdTouser user             @relation("project_projectManagerIdTouser", fields: [projectManagerId], references: [id], map: "Project_projectManagerId_fkey")
  section                             section[]
  task                                task[]

  @@index([createdBy], map: "Project_createdBy_fkey")
  @@index([customerId], map: "Project_customerId_fkey")
  @@index([projectManagerId], map: "Project_projectManagerId_fkey")
}

model payment {
  id              String         @id @default(uuid())
  projectId       String
  amount          Decimal        @db.Decimal(15, 2)
  paymentDate     DateTime
  paymentMethod   payment_method @default(BANK_TRANSFER)
  referenceNumber String?
  description     String?
  createdBy       String
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  user            user           @relation(fields: [createdBy], references: [id])
  project         project        @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId])
  @@index([createdBy])
  @@index([paymentDate])
}

model subtask {
  id             String               @id
  displayId      String               @default("")
  taskId         String
  sequence       Int                  @default(1)
  name           String
  description    String
  assigneeId     String
  assigneeType   subtask_assigneeType
  startDate      DateTime
  endDate        DateTime
  status         subtask_status
  totalTime      Int?
  createdBy      String
  createdAt      DateTime             @default(now())
  updatedAt      DateTime             @updatedAt
  priority       String               @default("Medium")
  user           user                 @relation(fields: [assigneeId], references: [id], map: "Subtask_assigneeId_fkey")
  task           task                 @relation(fields: [taskId], references: [id], onDelete: Cascade, map: "Subtask_taskId_fkey")
  subtaskcomment subtaskcomment[]

  @@unique([taskId, sequence], name: "subtask_task_sequence")
  @@index([assigneeId], map: "Subtask_assigneeId_fkey")
  @@index([taskId], map: "Subtask_taskId_fkey")
}

model section {
  id          String   @id @default(uuid())
  projectId   String
  name        String
  sequence    Int      @default(1)
  description String   @default("")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  project     project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  task        task[]

  @@unique([projectId, sequence], name: "section_project_sequence")
  @@unique([projectId, name], name: "section_project_name")
  @@index([projectId])
}

model task {
  id           String            @id
  displayId    String            @default("")
  projectId    String
  sequence     Int               @default(1)
  name         String
  description  String
  assigneeId   String
  assigneeType task_assigneeType
  department   String
  startDate    DateTime
  endDate      DateTime
  status       task_status
  createdBy    String
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  priority     String            @default("Medium")
  sectionId    String
  subtask      subtask[]
  user         user              @relation(fields: [assigneeId], references: [id], map: "Task_assigneeId_fkey")
  project      project           @relation(fields: [projectId], references: [id], onDelete: Cascade, map: "Task_projectId_fkey")
  section      section           @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  taskcomment  taskcomment[]

  @@unique([projectId, sequence], name: "task_project_sequence")
  @@index([sectionId])
  @@index([assigneeId], map: "Task_assigneeId_fkey")
  @@index([projectId], map: "Task_projectId_fkey")
}

model user {
  id                                     String           @id
  email                                  String           @unique(map: "User_email_key")
  password                               String
  name                                   String
  role                                   user_role
  department                             String
  profileImage                           String?
  code                                   String?          @unique(map: "User_code_key")
  skills                                 String?
  joinDate                               DateTime?
  createdAt                              DateTime         @default(now())
  updatedAt                              DateTime         @updatedAt
  passwordChanged                        String?          @default("N") @db.VarChar(1)
  alert                                  alert[]
  mom_mom_createdByTouser                mom[]            @relation("mom_createdByTouser")
  mom_mom_updatedByTouser                mom[]            @relation("mom_updatedByTouser")
  momactionitem                          momactionitem[]
  payment                                payment[]
  project_project_createdByTouser        project[]        @relation("project_createdByTouser")
  project_project_projectManagerIdTouser project[]        @relation("project_projectManagerIdTouser")
  subtask                                subtask[]
  subtaskcomment                         subtaskcomment[]
  task                                   task[]
  taskcomment                            taskcomment[]
  usersettings                           usersettings?
}

model subtaskcomment {
  id        String   @id
  subtaskId String
  userId    String
  content   String   @db.Text
  createdAt DateTime @default(now())
  updatedAt DateTime
  subtask   subtask  @relation(fields: [subtaskId], references: [id], onDelete: Cascade, map: "SubtaskComment_subtaskId_fkey")
  user      user     @relation(fields: [userId], references: [id], map: "SubtaskComment_userId_fkey")

  @@index([createdAt], map: "SubtaskComment_createdAt_idx")
  @@index([subtaskId], map: "SubtaskComment_subtaskId_fkey")
  @@index([userId], map: "SubtaskComment_userId_fkey")
}

model taskcomment {
  id        String   @id
  taskId    String
  userId    String
  content   String   @db.Text
  createdAt DateTime @default(now())
  updatedAt DateTime
  task      task     @relation(fields: [taskId], references: [id], onDelete: Cascade, map: "TaskComment_taskId_fkey")
  user      user     @relation(fields: [userId], references: [id], map: "TaskComment_userId_fkey")

  @@index([createdAt], map: "TaskComment_createdAt_idx")
  @@index([taskId], map: "TaskComment_taskId_fkey")
  @@index([userId], map: "TaskComment_userId_fkey")
}

model usersettings {
  id                     String   @id
  userId                 String   @unique(map: "userSettings_userId_key")
  emailNotifications     Boolean  @default(true)
  pushNotifications      Boolean  @default(true)
  taskReminders          Boolean  @default(true)
  projectUpdates         Boolean  @default(true)
  securityAlerts         Boolean  @default(true)
  language               String   @default("en")
  timezone               String   @default("UTC")
  dateFormat             String   @default("MM/DD/YYYY")
  timeFormat             String   @default("12h")
  createdAt              DateTime @default(now())
  updatedAt              DateTime
  notifyCriticalPriority Boolean  @default(true)
  notifyHighPriority     Boolean  @default(true)
  notifyLowPriority      Boolean  @default(true)
  notifyMediumPriority   Boolean  @default(true)
  user                   user     @relation(fields: [userId], references: [id], onDelete: Cascade, map: "userSettings_userId_fkey")

  @@index([userId], map: "UserSettings_userId_fkey")
}

model project_category_item {
  id          String   @id @default(cuid())
  name        String   @unique
  code        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("project_categories")
}

model milestone_template {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  sequence    Int      @unique @default(1)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("milestone_templates")
}

enum user_role {
  DIRECTOR
  PROJECT_MANAGER
  TEAM_LEAD
  ENGINEER
}

enum subtask_assigneeType {
  ENGINEER
  OUTSIDE_VENDOR
  CUSTOMER
}

enum task_assigneeType {
  ENGINEER
  OUTSIDE_VENDOR
  CUSTOMER
}

enum project_status {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  DELAYED
  ON_HOLD
}

enum subtask_status {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  DELAYED
  ON_HOLD
}

enum task_status {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  DELAYED
  ON_HOLD
}

enum alert_type {
  SYSTEM
  PROJECT
  TASK
  SUBTASK
  DEADLINE
  MOM
  USER
  SECURITY
  MAINTENANCE
}

enum alert_priority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum project_category {
  PROJECTS
  PRECISION_PROJECTS
  SPARE
  SERVICE
}

enum mompoint_discussion_type {
  DESIGN
  PROCUREMENT
  ASSEMBLY
  TESTING
  QUALITY
  DELIVERY
  OTHER
}

enum mompoint_status {
  PENDING
  IN_PROGRESS
  COMPLETED
  DELAYED
  ON_HOLD
}

enum payment_method {
  CASH
  BANK_TRANSFER
  CHEQUE
  CREDIT_CARD
  UPI
  NEFT
  RTGS
  OTHER
}
