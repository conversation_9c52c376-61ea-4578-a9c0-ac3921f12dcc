import React, { useState } from 'react';
import { use<PERSON>tom } from 'jotai';
import { engineers<PERSON>tom, departmentsAtom } from '../../store';
import { Engineer, UserRole } from '../../types';
import { v4 as uuidv4 } from 'uuid';
import {
  Plus,
  Edit,
  Trash2,
  CheckCircle2,
  XCircle,
  Search,
  Filter
} from 'lucide-react';

const EngineerManager: React.FC = () => {
  const [engineers, setEngineers] = useAtom(engineersAtom);
  const [departments] = useAtom(departmentsAtom);
  const [showModal, setShowModal] = useState(false);
  const [editingEngineer, setEditingEngineer] = useState<Engineer | null>(null);

  // Form state
  const [name, setName] = useState('');
  const [code, setCode] = useState('');
  const [department, setDepartment] = useState('');
  const [role, setRole] = useState<UserRole>(UserRole.ENGINEER);
  const [email, setEmail] = useState('');
  const [skills, setSkills] = useState<string[]>([]);
  const [joinDate, setJoinDate] = useState('');
  const [newSkill, setNewSkill] = useState('');

  // UI state
  const [searchQuery, setSearchQuery] = useState('');
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [departmentFilter, setDepartmentFilter] = useState<string>('');
  const [roleFilter, setRoleFilter] = useState<string>('');

  // Filter engineers based on search query and filters
  const filteredEngineers = engineers.filter(
    eng =>
      (eng.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      eng.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      eng.email.toLowerCase().includes(searchQuery.toLowerCase())) &&
      (departmentFilter ? eng.department === departmentFilter : true) &&
      (roleFilter ? eng.role === roleFilter : true)
  );

  const openModal = (engineer?: Engineer) => {
    if (engineer) {
      setEditingEngineer(engineer);
      setName(engineer.name);
      setCode(engineer.code);
      setDepartment(engineer.department);
      setRole(engineer.role);
      setEmail(engineer.email);
      setSkills(engineer.skills || []);
      setJoinDate(engineer.joinDate);
    } else {
      setEditingEngineer(null);
      setName('');
      setCode('');
      setDepartment('');
      setRole(UserRole.ENGINEER);
      setEmail('');
      setSkills([]);
      setJoinDate(new Date().toISOString().split('T')[0]);
    }
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingEngineer(null);
    setNewSkill('');
  };

  const addSkill = () => {
    if (newSkill.trim() && !skills.includes(newSkill.trim())) {
      setSkills([...skills, newSkill.trim()]);
      setNewSkill('');
    }
  };

  const removeSkill = (skillToRemove: string) => {
    setSkills(skills.filter(skill => skill !== skillToRemove));
  };

  const saveEngineer = () => {
    if (editingEngineer) {
      // Update existing engineer
      setEngineers(prevEngineers =>
        prevEngineers.map(eng =>
          eng.id === editingEngineer.id
            ? { ...eng, name, code, department, role, email, skills, joinDate }
            : eng
        )
      );
      setSuccessMessage('Engineer updated successfully!');
    } else {
      // Create new engineer
      const newEngineer: Engineer = {
        id: uuidv4(),
        name,
        code,
        department,
        role,
        email,
        skills: skills.length > 0 ? skills : undefined,
        joinDate
      };

      setEngineers(prevEngineers => [...prevEngineers, newEngineer]);
      setSuccessMessage('Engineer created successfully!');
    }

    closeModal();

    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const deleteEngineer = (id: string) => {
    // In a real app, you might want to add a confirmation dialog here
    setEngineers(prevEngineers => prevEngineers.filter(eng => eng.id !== id));
    setSuccessMessage('Engineer deleted successfully!');

    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const resetFilters = () => {
    setDepartmentFilter('');
    setRoleFilter('');
    setSearchQuery('');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">Teams & Engineers Management</h2>
        <button
          onClick={() => openModal()}
          className="btn btn-primary flex items-center"
        >
          <Plus size={18} className="mr-2" />
          Add Engineer
        </button>
      </div>

      {successMessage && (
        <div className="bg-success/10 text-success-dark p-4 rounded-md flex items-center justify-between fade-in">
          <div className="flex items-center">
            <CheckCircle2 size={18} className="mr-2" />
            <span>{successMessage}</span>
          </div>
          <button
            onClick={() => setSuccessMessage(null)}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle size={18} />
          </button>
        </div>
      )}

      <div className="flex flex-col md:flex-row gap-4 mb-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search size={18} className="text-gray-400" />
          </div>
          <input
            type="search"
            className="form-input pl-10"
            placeholder="Search engineers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex space-x-2">
          <div>
            <select
              className="form-select"
              value={departmentFilter}
              onChange={(e) => setDepartmentFilter(e.target.value)}
            >
              <option value="">All Departments</option>
              {departments.map(dept => (
                <option key={dept.id} value={dept.name}>{dept.name}</option>
              ))}
            </select>
          </div>

          <div>
            <select
              className="form-select"
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
            >
              <option value="">All Roles</option>
              {Object.values(UserRole).map(role => (
                <option key={role} value={role}>{role.replace(/_/g, ' ')}</option>
              ))}
            </select>
          </div>

          <button
            onClick={resetFilters}
            className="btn btn-outline py-1 flex items-center"
            disabled={!departmentFilter && !roleFilter && !searchQuery}
          >
            <Filter size={18} className="mr-1" />
            Reset
          </button>
        </div>
      </div>

      <div className="card overflow-hidden shadow">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Code
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Department
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Join Date
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredEngineers.map((engineer) => (
                <tr key={engineer.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{engineer.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{engineer.code}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{engineer.department}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      engineer.role === UserRole.DIRECTOR
                        ? 'bg-primary/10 text-primary-dark'
                        : engineer.role === UserRole.GENERAL_MANAGER
                        ? 'bg-secondary/10 text-secondary-dark'
                        : engineer.role === UserRole.MANAGER
                        ? 'bg-accent/10 text-accent-dark'
                        : engineer.role === UserRole.TEAM_LEAD
                        ? 'bg-success/10 text-success-dark'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {engineer.role ? engineer.role.replace(/_/g, ' ') : 'ENGINEER'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{engineer.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{engineer.joinDate}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => openModal(engineer)}
                      className="text-primary hover:text-primary-dark mr-4"
                    >
                      <Edit size={18} />
                    </button>
                    <button
                      onClick={() => deleteEngineer(engineer.id)}
                      className="text-gray-400 hover:text-error"
                    >
                      <Trash2 size={18} />
                    </button>
                  </td>
                </tr>
              ))}

              {filteredEngineers.length === 0 && (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                    No engineers found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Engineer Form Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-xl p-6 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {editingEngineer ? 'Edit Engineer' : 'Add New Engineer'}
              </h3>
              <button
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-500"
              >
                <XCircle size={20} />
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="name" className="form-label">Name</label>
                  <input
                    type="text"
                    id="name"
                    className="form-input"
                    placeholder="Enter name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required
                  />
                </div>

                <div>
                  <label htmlFor="code" className="form-label">Code</label>
                  <input
                    type="text"
                    id="code"
                    className="form-input"
                    placeholder="Enter code"
                    value={code}
                    onChange={(e) => setCode(e.target.value)}
                    required
                  />
                </div>

                <div>
                  <label htmlFor="department" className="form-label">Department</label>
                  <select
                    id="department"
                    className="form-select"
                    value={department}
                    onChange={(e) => setDepartment(e.target.value)}
                    required
                  >
                    <option value="">Select Department</option>
                    {departments.map(dept => (
                      <option key={dept.id} value={dept.name}>{dept.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="role" className="form-label">Role</label>
                  <select
                    id="role"
                    className="form-select"
                    value={role}
                    onChange={(e) => setRole(e.target.value as UserRole)}
                    required
                  >
                    {Object.values(UserRole).map(roleOption => (
                      <option key={roleOption} value={roleOption}>
                        {roleOption.replace(/_/g, ' ')}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="email" className="form-label">Email</label>
                  <input
                    type="email"
                    id="email"
                    className="form-input"
                    placeholder="Enter email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>

                <div>
                  <label htmlFor="joinDate" className="form-label">Join Date</label>
                  <input
                    type="date"
                    id="joinDate"
                    className="form-input"
                    value={joinDate}
                    onChange={(e) => setJoinDate(e.target.value)}
                    required
                  />
                </div>
              </div>

              <div>
                <label className="form-label">Skills</label>
                <div className="flex">
                  <input
                    type="text"
                    className="form-input flex-1"
                    placeholder="Add a skill"
                    value={newSkill}
                    onChange={(e) => setNewSkill(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        addSkill();
                      }
                    }}
                  />
                  <button
                    type="button"
                    className="btn btn-primary ml-2"
                    onClick={addSkill}
                  >
                    Add
                  </button>
                </div>

                <div className="mt-2 flex flex-wrap gap-2">
                  {skills.map((skill, index) => (
                    <div
                      key={index}
                      className="bg-gray-100 px-3 py-1 rounded-full text-sm flex items-center"
                    >
                      <span>{skill}</span>
                      <button
                        type="button"
                        className="ml-2 text-gray-500 hover:text-error"
                        onClick={() => removeSkill(skill)}
                      >
                        <XCircle size={14} />
                      </button>
                    </div>
                  ))}

                  {skills.length === 0 && (
                    <span className="text-gray-500 text-sm">No skills added yet</span>
                  )}
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-2">
                <button
                  type="button"
                  className="btn btn-outline"
                  onClick={closeModal}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={saveEngineer}
                  disabled={!name || !code || !department || !role || !email || !joinDate}
                >
                  {editingEngineer ? 'Update' : 'Create'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EngineerManager;