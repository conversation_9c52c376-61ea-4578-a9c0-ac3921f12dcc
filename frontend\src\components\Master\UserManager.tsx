import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useAtom } from 'jotai';
import { departmentsAtom, currentUserAtom, usersAtom } from '../../store';
import { User, UserRole } from '../../types';
import {
  Plus,
  Edit,
  Trash2,
  CheckCircle2,
  XCircle,
  Search,
  Filter,
  Eye,
  EyeOff,
  Lock
} from 'lucide-react';
import { usersAPI } from '../../services/api';
import { dataService } from '../../services/dataServiceSingleton';
import { normalizeEmail } from '../../utils/dateValidation';
import UserHierarchyTreeView from './UserHierarchyTreeView';
import { BuildingOfficeIcon } from '@heroicons/react/24/solid';
import { useRealTimeValidation } from '../../hooks/useRealTimeValidation';
import { SmartInput } from '../common/SmartInput';
import { useNotification } from '../../contexts/NotificationContext';

// Validation configuration for user fields
const validationConfig = {
  name: [
    { type: 'name' as const, message: 'Name can only contain letters, spaces, hyphens, apostrophes, and dots' },
    { type: 'custom' as const, message: 'Name is required',
      customValidator: (value: string) => {
        if (!value || value.trim().length === 0) {
          return { isValid: false, message: 'Name is required' };
        }
        return { isValid: true };
      }
    }
  ],
  email: [
    { type: 'email' as const, message: 'Please enter a valid email address' },
    { type: 'custom' as const, message: 'Email address is required',
      customValidator: (value: string) => {
        if (!value || value.trim().length === 0) {
          return { isValid: false, message: 'Email address is required' };
        }
        return { isValid: true };
      }
    }
  ],
  password: [
    { type: 'custom' as const, message: 'Password must be at least 6 characters',
      customValidator: (value: string) => {
        if (!value || value.trim().length === 0) {
          return { isValid: false, message: 'Password is required' };
        }
        if (value.length < 6) {
          return { isValid: false, message: 'Password must be at least 6 characters' };
        }
        return { isValid: true };
      }
    }
  ],
  confirmPassword: [
    { type: 'custom' as const, message: 'Passwords do not match',
      customValidator: (value: string, formData?: any) => {
        if (!value || value.trim().length === 0) {
          return { isValid: false, message: 'Please confirm your password' };
        }
        if (formData?.password && value !== formData.password) {
          return { isValid: false, message: 'Passwords do not match' };
        }
        return { isValid: true };
      }
    }
  ],
  code: [
    { type: 'code' as const, message: 'Code must be 2-10 characters, letters/numbers/hyphens/underscores only' }
  ]
};

// Debounce utility function for user operations
const useDebounce = (callback: Function, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback((...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

const UserManager: React.FC = () => {
  const { showSuccess, showError } = useNotification();

  const [departments] = useAtom(departmentsAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [users] = useAtom(usersAtom); // Use global users atom instead of local state


  const [showModal, setShowModal] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false); // Remove initial loading since data is managed globally
  const [showPassword, setShowPassword] = useState(false);

  // Real-time validation hook
  const {
    fields,
    updateField,
    touchField,
    getFieldProps,
    isFormValid,
    resetAllFields
  } = useRealTimeValidation(validationConfig);

  // Extract values from fields for easier access
  const values = {
    name: fields.name?.value || '',
    email: fields.email?.value || '',
    password: fields.password?.value || '',
    confirmPassword: fields.confirmPassword?.value || '',
    code: fields.code?.value || ''
  };

  // Form state for non-validated fields
  const [role, setRole] = useState<UserRole>(UserRole.ENGINEER);
  const [department, setDepartment] = useState('');
  const [skills, setSkills] = useState<string[]>([]);
  const [joinDate, setJoinDate] = useState('');
  const [newSkill, setNewSkill] = useState('');

  // Reset password modal state
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false);
  const [resetPasswordUserId, setResetPasswordUserId] = useState<string>('');
  const [resetPasswordUserName, setResetPasswordUserName] = useState('');
  const [resetPasswordUserRole, setResetPasswordUserRole] = useState<UserRole>(UserRole.ENGINEER);
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');

  // UI state
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState<string>('');
  const [filterDepartment, setFilterDepartment] = useState<string>('');
  const [viewMode, setViewMode] = useState<'normal' | 'hierarchy'>('normal');
  const [isUpdating, setIsUpdating] = useState<boolean>(false);

  // Debounced refresh function to reduce API calls
  const debouncedRefreshUsers = useDebounce(async () => {
    try {
      console.log('🔄 Refreshing users data...');
      await dataService.loadUsers();
      console.log('✅ Users data refreshed successfully');
    } catch (error) {
      console.error('❌ Error refreshing users:', error);
      setErrorMessage('Failed to refresh users. Please try again.');
    }
  }, 1000); // 1 second debounce

  const openModal = (user: User | null = null) => {
    if (user) {
      setEditingUser(user);
      // Update validation fields
      updateField('name', user.name);
      updateField('email', user.email);
      updateField('code', user.code || '');
      updateField('password', '');
      updateField('confirmPassword', '');
      // Update non-validation fields
      setRole(user.role as UserRole);
      setDepartment(user.department);
      setSkills(user.skills ? user.skills.split(',') : []);
      setJoinDate(user.joinDate ? new Date(user.joinDate).toISOString().split('T')[0] : '');
    } else {
      resetForm();
    }

    setShowModal(true);
  };

  const openResetPasswordModal = (user: User) => {
    setResetPasswordUserId(user.id);
    setResetPasswordUserName(user.name);
    setResetPasswordUserRole(user.role as UserRole);
    setNewPassword('');
    setConfirmNewPassword('');
    setShowResetPasswordModal(true);
  };

  const closeResetPasswordModal = () => {
    setShowResetPasswordModal(false);
    setResetPasswordUserId('');
    setResetPasswordUserName('');
    setResetPasswordUserRole(UserRole.ENGINEER);
    setNewPassword('');
    setConfirmNewPassword('');
  };

  const closeModal = () => {
    setShowModal(false);
    resetForm();
  };

  const resetForm = () => {
    setEditingUser(null);
    // Reset validation form
    resetAllFields();
    // Reset non-validation fields
    setRole(UserRole.ENGINEER);
    setDepartment('');
    setSkills([]);
    setJoinDate('');
    setNewSkill('');
  };

  const addSkill = () => {
    if (newSkill.trim() && !skills.includes(newSkill.trim())) {
      setSkills([...skills, newSkill.trim()]);
      setNewSkill('');
    }
  };

  const removeSkill = (skillToRemove: string) => {
    setSkills(skills.filter(skill => skill !== skillToRemove));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage(null);

    // Check if form is valid and required fields are filled
    if (!isFormValid || !values.name || !values.email) {
      return; // Let individual field validation handle errors
    }

    if (!editingUser && (!values.password || !values.confirmPassword)) {
      return; // Let individual field validation handle errors
    }

    if (!department) {
      setErrorMessage('Please select a department.');
      return;
    }

    try {
      setIsUpdating(true);

      // Normalize email before sending
      const normalizedEmail = normalizeEmail(values.email);

      const userData = {
        name: values.name,
        email: normalizedEmail,
        role,
        department,
        code: values.code || undefined,
        skills: skills.length > 0 ? skills.join(',') : undefined,
        joinDate: joinDate ? new Date(joinDate).toISOString() : undefined,
        ...(values.password ? { password: values.password } : {})
      };

      console.log('Submitting user data:', { ...userData, password: values.password ? '******' : undefined });

      if (editingUser) {
        console.log(`Updating user with ID: ${editingUser.id}`);
        const result = await usersAPI.updateUser(editingUser.id, userData);
        console.log('User update response:', result);
        showSuccess('User Updated', 'User updated successfully!');
      } else {
        console.log('Creating new user');
        const result = await usersAPI.createUser(userData);
        console.log('User creation response:', result);
        showSuccess('User Created', 'User created successfully!');
      }

      // Use debounced refresh to reduce API calls
      debouncedRefreshUsers();
      closeModal();
    } catch (error: any) {
      console.error('Error saving user:', error);

      if (error.message.includes('already exists')) {
        setErrorMessage(error.message);
      } else if (error.message.includes('least 6 characters')) {
        setErrorMessage('Password must be at least 6 characters long.');
      } else if (error.message.includes('Network error') || error.message.includes('timed out')) {
        setErrorMessage('Network error: Cannot connect to the server. Please check if the backend server is running.');
      } else if (error.message.includes('not authorized') || error.message.includes('permission')) {
        setErrorMessage('Permission error: You do not have permission to perform this action.');
      } else {
        setErrorMessage(error.message || 'Failed to save user. Please try again.');
      }

      console.log('Current user role:', currentUser?.role);
      console.log('Form data:', {
        name: values.name,
        email: values.email,
        role,
        department,
        code: values.code || 'Not provided',
        skills: skills.length > 0 ? skills.join(',') : 'Not provided',
        joinDate: joinDate || 'Not provided',
        passwordProvided: !!values.password
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage(null);

    if (!resetPasswordUserId) {
      setErrorMessage('User ID is missing.');
      return;
    }

    if (!newPassword) {
      setErrorMessage('New password is required.');
      return;
    }

    if (newPassword.length < 6) {
      setErrorMessage('Password must be at least 6 characters long.');
      return;
    }

    if (newPassword !== confirmNewPassword) {
      setErrorMessage('Passwords do not match.');
      return;
    }

    try {
      setIsUpdating(true);
      console.log('🔐 Resetting password for user:', resetPasswordUserName);
      await usersAPI.updateUser(resetPasswordUserId, { password: newPassword });
      showSuccess('Password Reset', `Password for ${resetPasswordUserName} has been reset successfully!`);

      // Use debounced refresh to ensure data consistency
      debouncedRefreshUsers();
      closeResetPasswordModal();
      console.log('✅ Password reset completed');
    } catch (error: any) {
      console.error('❌ Error resetting password:', error);

      if (error.message.includes('least 6 characters')) {
        setErrorMessage('Password must be at least 6 characters long.');
      } else {
        setErrorMessage(error.message || 'Failed to reset password. Please try again.');
      }
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!window.confirm('Are you sure you want to delete this user?')) {
      return;
    }

    try {
      console.log('🗑️ Deleting user:', userId);
      await usersAPI.deleteUser(userId);
      showSuccess('User Deleted', 'User deleted successfully!');

      // Use debounced refresh to reduce API calls
      debouncedRefreshUsers();
      console.log('✅ User deletion completed');
    } catch (error) {
      console.error('❌ Error deleting user:', error);
      setErrorMessage('Failed to delete user. Please try again.');
    }
  };

  const canCreateUser = (): boolean => {
    if (!currentUser) return false;
    return currentUser.role === UserRole.DIRECTOR ||
           currentUser.role === UserRole.PROJECT_MANAGER ||
           currentUser.role === UserRole.TEAM_LEAD;
  };

  const getAllowedRoles = (): UserRole[] => {
    if (!currentUser) return [];

    switch (currentUser.role) {
      case UserRole.DIRECTOR:
        return [UserRole.PROJECT_MANAGER, UserRole.TEAM_LEAD, UserRole.ENGINEER];
      case UserRole.PROJECT_MANAGER:
        return [UserRole.TEAM_LEAD, UserRole.ENGINEER];
      case UserRole.TEAM_LEAD:
        return [UserRole.ENGINEER];
      default:
        return [];
    }
  };

  const getAllowedDepartments = (): string[] => {
    if (!currentUser) return [];

    switch (currentUser.role) {
      case UserRole.DIRECTOR:
      case UserRole.PROJECT_MANAGER:
        return departments.map(dept => dept.name);
      case UserRole.TEAM_LEAD:
        return [currentUser.department];
      default:
        return [];
    }
  };

  const canEditUser = (user: User): boolean => {
    if (!currentUser) return false;

    if (currentUser.id === user.id) return true;

    switch (currentUser.role) {
      case UserRole.DIRECTOR:
        return true;
      case UserRole.PROJECT_MANAGER:
        return [UserRole.TEAM_LEAD, UserRole.ENGINEER].includes(user.role as UserRole);
      case UserRole.TEAM_LEAD:
        return user.role === UserRole.ENGINEER && user.department === currentUser.department;
      default:
        return false;
    }
  };

  const canDeleteUser = (user: User): boolean => {
    if (!currentUser) return false;

    if (currentUser.id === user.id) return false;

    switch (currentUser.role) {
      case UserRole.DIRECTOR:
        return [UserRole.PROJECT_MANAGER, UserRole.TEAM_LEAD, UserRole.ENGINEER].includes(user.role as UserRole);
      case UserRole.PROJECT_MANAGER:
        return [UserRole.TEAM_LEAD, UserRole.ENGINEER].includes(user.role as UserRole);
      default:
        return false;
    }
  };

  const canResetUserPassword = (user: User): boolean => {
    if (!currentUser) return false;

    if (currentUser.id === user.id) return true;

    if (currentUser.role === UserRole.DIRECTOR) return true;

    if (currentUser.role === UserRole.GENERAL_MANAGER) {
      return [UserRole.DIRECTOR, UserRole.GENERAL_MANAGER, UserRole.MANAGER].includes(user.role as UserRole);
    }

    if (currentUser.role === UserRole.MANAGER) {
      return [UserRole.MANAGER, UserRole.TEAM_LEAD].includes(user.role as UserRole);
    }

    if (currentUser.role === UserRole.TEAM_LEAD) {
      return (user.role === UserRole.ENGINEER && user.department === currentUser.department) ||
             (user.id === currentUser.id);
    }

    return currentUser.id === user.id;
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = searchTerm === '' ||
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.code?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = filterRole === '' || user.role === filterRole;
    const matchesDepartment = filterDepartment === '' || user.department === filterDepartment;

    return matchesSearch && matchesRole && matchesDepartment;
  });

  return (
    <div className="space-y-6">

      {/* View Mode Toggle */}
      <div className="bg-white rounded-xl p-6 transform transition-all duration-300 hover:shadow-xl hover:scale-[1.005] border border-gray-100"
           style={{ boxShadow: '0 6px 12px rgba(0, 0, 0, 0.05)' }}>
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-medium text-gray-900">View Mode</h3>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('normal')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-all duration-200 transform hover:scale-105 ${
                  viewMode === 'normal'
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
                style={{ boxShadow: viewMode === 'normal' ? '0 4px 8px rgba(0, 0, 0, 0.1)' : 'none' }}
              >
                Normal View
              </button>
              <button
                onClick={() => setViewMode('hierarchy')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-all duration-200 transform hover:scale-105 ${
                  viewMode === 'hierarchy'
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
                style={{ boxShadow: viewMode === 'hierarchy' ? '0 4px 8px rgba(0, 0, 0, 0.1)' : 'none' }}
              >
                Hierarchy View
              </button>
            </div>
          </div>
        </div>
      </div>



      {errorMessage && (
        <div className="bg-red-50 text-red-800 p-4 rounded-md flex items-center justify-between transform transition-all duration-300 hover:shadow-lg hover:scale-[1.005]"
             style={{ boxShadow: '0 6px 12px rgba(0, 0, 0, 0.05)' }}>
          <div className="flex items-center">
            <XCircle size={18} className="mr-2" />
            <span>{errorMessage}</span>
          </div>
          <button
            onClick={() => setErrorMessage(null)}
            className="text-gray-500 hover:text-gray-700 transform transition-all duration-200 hover:scale-110"
          >
            <XCircle size={18} />
          </button>
        </div>
      )}

      {/* Search/Filters and Add User Button */}
      <div className="bg-white rounded-xl p-6 transform transition-all duration-300 hover:shadow-xl hover:scale-[1.005] border border-gray-100"
           style={{ boxShadow: '0 6px 12px rgba(0, 0, 0, 0.05)' }}>
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <div className="flex flex-col md:flex-row gap-4 flex-grow">
              <div className="relative flex-grow">
                <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search users..."
                  className="form-input pl-10 rounded-md border-gray-200 shadow-sm focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex gap-2">
                <select
                  className="form-select rounded-md border-gray-200 shadow-sm focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                  value={filterRole}
                  onChange={(e) => setFilterRole(e.target.value)}
                >
                  <option value="">All Roles</option>
                  {Object.values(UserRole).map(role => (
                    <option key={role} value={role}>{role.replace(/_/g, ' ')}</option>
                  ))}
                </select>
                <select
                  className="form-select rounded-md border-gray-200 shadow-sm focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                  value={filterDepartment}
                  onChange={(e) => setFilterDepartment(e.target.value)}
                >
                  <option value="">All Departments</option>
                  {departments.map(dept => (
                    <option key={dept.id} value={dept.name}>{dept.name}</option>
                  ))}
                </select>
              </div>
            </div>

            {canCreateUser() && (
              <button
                onClick={() => openModal()}
                className="btn btn-primary flex items-center ml-4 rounded-md transform transition-all duration-200 hover:scale-105 shadow-md hover:shadow-lg"
                style={{ boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' }}
              >
                <Plus size={18} className="mr-2" />
                Add User
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Users display - changes based on view mode */}
      {viewMode === 'normal' ? (
        <div className="card overflow-hidden rounded-xl shadow-lg border-0"
             style={{
               background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',
               backdropFilter: 'blur(20px)',
               boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8)'
             }}>
          <table className="min-w-full divide-y divide-gray-200/50">
            <thead style={{
              background: 'linear-gradient(135deg, rgba(249, 250, 251, 0.8) 0%, rgba(243, 244, 246, 0.8) 100%)'
            }}>
              <tr>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Name</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Email</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Role</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Department</th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Code</th>
                <th className="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200/30">
              {loading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-8 text-center">
                    <div className="flex justify-center">
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/40 rounded-full blur-sm"></div>
                        <svg className="relative animate-spin h-6 w-6 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </div>
                    </div>
                  </td>
                </tr>
              ) : (
                <>
                  {filteredUsers.map((user, index) => (
                    <tr key={user.id} 
                        className="hover:bg-gradient-to-r hover:from-gray-50/50 hover:to-blue-50/30 transition-all duration-300 transform hover:scale-[1.01]"
                        style={{
                          animationDelay: `${index * 50}ms`
                        }}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.code}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                          ${user.role === UserRole.DIRECTOR ? 'bg-purple-100 text-purple-800' :
                            user.role === UserRole.PROJECT_MANAGER ? 'bg-blue-100 text-blue-800' :
                            user.role === UserRole.TEAM_LEAD ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'}`}>
                          {user.role.replace(/_/g, ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{user.department}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{user.code || '—'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-3">
                          {canEditUser(user) && (
                            <>
                              <button
                                onClick={() => openModal(user)}
                                className="text-primary hover:text-primary-dark transform hover:scale-110 active:scale-95 transition-all duration-200 p-2 rounded-lg hover:bg-primary/10"
                                title="Edit User"
                              >
                                <Edit size={18} />
                              </button>
                              <button
                                onClick={() => openResetPasswordModal(user)}
                                className="text-warning hover:text-warning-dark transform hover:scale-110 active:scale-95 transition-all duration-200 p-2 rounded-lg hover:bg-warning/10"
                                title="Reset Password"
                              >
                                <Lock size={18} />
                              </button>
                            </>
                          )}
                          {canDeleteUser(user) && (
                            <button
                              onClick={() => handleDeleteUser(user.id)}
                              className="text-gray-400 hover:text-error transform hover:scale-110 active:scale-95 transition-all duration-200 p-2 rounded-lg hover:bg-red-50"
                              title="Delete User"
                            >
                              <Trash2 size={18} />
                            </button>
                          )}
                          {!canEditUser(user) && !canResetUserPassword(user) && !canDeleteUser(user) && (
                            <span className="text-gray-400 text-sm bg-gray-100/50 px-3 py-1 rounded-full">
                              Read-only
                            </span>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}

                  {filteredUsers.length === 0 && (
                    <tr>
                      <td colSpan={6} className="px-6 py-12 text-center">
                        <div className="text-gray-500">
                          <div className="relative inline-block mb-4">
                            <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full blur-xl opacity-20"></div>
                            <div className="relative bg-gray-100 rounded-full p-4">
                              <Search size={24} className="text-gray-400" />
                            </div>
                          </div>
                          <p className="text-sm">No users found</p>
                        </div>
                      </td>
                    </tr>
                  )}
                </>
              )}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="bg-white rounded-xl p-6 transform transition-all duration-300 hover:shadow-xl border border-gray-100"
             style={{ boxShadow: '0 6px 12px rgba(0, 0, 0, 0.05)' }}>
          <UserHierarchyTreeView
            users={filteredUsers}
            onEditUser={openModal}
            onResetPassword={openResetPasswordModal}
            onDeleteUser={handleDeleteUser}
            canEditUser={canEditUser}
            canResetUserPassword={canResetUserPassword}
            canDeleteUser={canDeleteUser}
          />
        </div>
      )}

      {/* User Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-95 hover:scale-100 shadow-2xl"
               style={{ boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2)' }}>
            <h3 className="text-xl font-semibold mb-4 transform transition-all duration-300 hover:scale-105"
                style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
              {editingUser ? 'Edit User' : 'Add New User'}
            </h3>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <SmartInput
                    label="Name *"
                    type="text"
                    placeholder="Enter full name"
                    required
                    helpText="Only letters, spaces, hyphens, apostrophes, and dots allowed"
                    maxLength={100}
                    showValidationIcon
                    {...getFieldProps('name')}
                  />
                </div>

                <div>
                  <SmartInput
                    label="Email *"
                    type="email"
                    placeholder="Enter email address"
                    required
                    helpText="Valid email address format required"
                    inputMode="email"
                    showValidationIcon
                    {...getFieldProps('email')}
                  />
                </div>

                {!editingUser && (
                  <>
                    <div>
                      <SmartInput
                        label="Password *"
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter password"
                        required
                        helpText="Must be at least 6 characters"
                        showValidationIcon
                        {...getFieldProps('password')}
                      />
                    </div>

                    <div>
                      <SmartInput
                        label="Confirm Password *"
                        type={showPassword ? "text" : "password"}
                        placeholder="Confirm password"
                        required
                        helpText="Must match the password above"
                        showValidationIcon
                        {...getFieldProps('confirmPassword')}
                      />
                    </div>
                  </>
                )}

                <div>
                  <label className="form-label">Role <span className="text-red-500">*</span></label>
                  <select
                    className="form-select rounded-md border-gray-200 shadow-sm focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                    value={role}
                    onChange={(e) => setRole(e.target.value as UserRole)}
                    required
                    disabled={editingUser && !canEditUser(editingUser)}
                  >
                    <option value="">Select Role</option>
                    {getAllowedRoles().map(allowedRole => (
                      <option key={allowedRole} value={allowedRole}>
                        {allowedRole.replace(/_/g, ' ')}
                      </option>
                    ))}
                  </select>
                  {getAllowedRoles().length === 0 && (
                    <p className="text-sm text-gray-500 mt-1">
                      You don't have permission to assign roles.
                    </p>
                  )}
                </div>

                <div>
                  <label className="form-label">Department <span className="text-red-500">*</span></label>
                  <select
                    className="form-select rounded-md border-gray-200 shadow-sm focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                    value={department}
                    onChange={(e) => setDepartment(e.target.value)}
                    required
                    disabled={editingUser && !canEditUser(editingUser)}
                  >
                    <option value="">Select Department</option>
                    {getAllowedDepartments().map(deptName => {
                      const dept = departments.find(d => d.name === deptName);
                      return dept ? (
                        <option key={dept.id} value={dept.name}>{dept.name}</option>
                      ) : null;
                    })}
                  </select>
                  {currentUser?.role === UserRole.TEAM_LEAD && (
                    <p className="text-sm text-gray-500 mt-1">
                      As a Team Lead, you can only create users in your department: {currentUser.department}
                    </p>
                  )}
                </div>

                <div>
                  <SmartInput
                    label="Employee Code"
                    type="text"
                    placeholder="Enter employee code (optional)"
                    helpText="2-10 characters, letters/numbers/hyphens/underscores only"
                    maxLength={10}
                    showValidationIcon
                    {...getFieldProps('code')}
                  />
                </div>

                <div>
                  <label className="form-label">Join Date</label>
                  <input
                    type="date"
                    className="form-input rounded-md border-gray-200 shadow-sm focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                    value={joinDate}
                    onChange={(e) => setJoinDate(e.target.value)}
                  />
                </div>
              </div>

              <div>
                <label className="form-label">Skills</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    className="form-input flex-grow rounded-md border-gray-200 shadow-sm focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                    value={newSkill}
                    onChange={(e) => setNewSkill(e.target.value)}
                    placeholder="Add a skill"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
                  />
                  <button
                    type="button"
                    className="btn btn-primary rounded-md transform transition-all duration-200 hover:scale-105 shadow-md hover:shadow-lg"
                    onClick={addSkill}
                    style={{ boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' }}
                  >
                    Add
                  </button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {skills.map(skill => (
                    <span
                      key={skill}
                      className="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded flex items-center transform transition-all duration-200 hover:scale-105"
                      style={{ boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}
                    >
                      {skill}
                      <button
                        type="button"
                        className="ml-1 text-blue-600 hover:text-blue-800 transform transition-all duration-200 hover:scale-110"
                        onClick={() => removeSkill(skill)}
                      >
                        <XCircle size={14} />
                      </button>
                    </span>
                  ))}
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <button
                  type="button"
                  className="btn btn-outline rounded-md transform transition-all duration-200 hover:scale-105 shadow-md hover:shadow-lg"
                  onClick={closeModal}
                  disabled={isUpdating}
                  style={{ boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn btn-primary rounded-md transform transition-all duration-200 hover:scale-105 shadow-md hover:shadow-lg"
                  disabled={isUpdating || !department || (!editingUser && !isFormValid)}
                  style={{ boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' }}
                >
                  {isUpdating ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {editingUser ? 'Updating...' : 'Creating...'}
                    </span>
                  ) : (
                    editingUser ? 'Update User' : 'Create User'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Reset Password Modal */}
      {showResetPasswordModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-8 max-w-md w-full transform transition-all duration-300 scale-95 hover:scale-100 shadow-2xl"
               style={{ boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2)' }}>
            <h3 className="text-xl font-semibold mb-2 transform transition-all duration-300 hover:scale-105"
                style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
              Reset Password for {resetPasswordUserName}
            </h3>
            <div className="flex items-center mb-2">
              <span className={`px-2 py-1 text-xs font-semibold rounded-full transform transition-all duration-200 hover:scale-105 ${
                resetPasswordUserRole === UserRole.DIRECTOR
                  ? 'bg-blue-100 text-blue-800'
                  : resetPasswordUserRole === UserRole.GENERAL_MANAGER
                  ? 'bg-purple-100 text-purple-800'
                  : resetPasswordUserRole === UserRole.MANAGER
                  ? 'bg-green-100 text-green-800'
                  : resetPasswordUserRole === UserRole.TEAM_LEAD
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-gray-100 text-gray-800'
              }`} style={{ boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' }}>
                {resetPasswordUserRole.replace(/_/g, ' ')}
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-4 transform transition-all duration-300 hover:text-gray-700">
              {currentUser?.id === resetPasswordUserId
                ? "You are resetting your own password."
                : "You are resetting another user's password based on your role permissions."}
            </p>

            <form onSubmit={handleResetPassword} className="space-y-4">
              <div>
                <label className="form-label">New Password <span className="text-red-500">*</span></label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    className="form-input pr-10 rounded-md border-gray-200 shadow-sm focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    required
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-all duration-200"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              </div>

              <div>
                <label className="form-label">Confirm New Password <span className="text-red-500">*</span></label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    className="form-input pr-10 rounded-md border-gray-200 shadow-sm focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                    value={confirmNewPassword}
                    onChange={(e) => setConfirmNewPassword(e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <button
                  type="button"
                  className="btn btn-outline rounded-md transform transition-all duration-200 hover:scale-105 shadow-md hover:shadow-lg"
                  onClick={closeResetPasswordModal}
                  disabled={isUpdating}
                  style={{ boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn btn-warning rounded-md transform transition-all duration-200 hover:scale-105 shadow-md hover:shadow-lg"
                  disabled={isUpdating}
                  style={{ boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' }}
                >
                  {isUpdating ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Resetting...
                    </span>
                  ) : (
                    'Reset Password'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManager;