import React, { useState, useEffect } from 'react';
import { useAtom } from 'jotai';
import { projectsAtom, currentUserAtom } from '../../store';
import { MOM, MOMPoint, MOMDiscussionType, MOMPointStatus, Project } from '../../types';
import { v4 as uuidv4 } from 'uuid';
import { Plus, Save, X, Calendar, FileText, User, Clock } from 'lucide-react';
import { momsAPI } from '../../services/api';
import { useNotification } from '../../contexts/NotificationContext';
import { formatDate, formatDateForInput } from '../../utils/dateFormatter';

interface ProjectMOMGridProps {
  projectId: string;
}

const ProjectMOMGrid: React.FC<ProjectMOMGridProps> = ({ projectId }) => {
  const [projects] = useAtom(projectsAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [mom, setMom] = useState<MOM | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [newPoint, setNewPoint] = useState<Partial<MOMPoint> | null>(null);
  const { showSuccess, showError } = useNotification();

  const project = projects.find(p => p.id === projectId);

  useEffect(() => {
    loadMOM();
  }, [projectId]);

  const loadMOM = async () => {
    try {
      setIsLoading(true);
      const response = await momsAPI.getMOMByProject(projectId);
      if (response.success) {
        setMom(response.data);
      } else {
        setMom(null);
      }
    } catch (error) {
      console.error('Error loading MOM:', error);
      setMom(null);
    } finally {
      setIsLoading(false);
    }
  };

  const createMOM = async () => {
    try {
      setIsCreating(true);
      const response = await momsAPI.createMOM({ projectId });
      if (response.success) {
        setMom(response.data);
        showSuccess('MOM created successfully');
      } else {
        showError('Failed to create MOM');
      }
    } catch (error) {
      console.error('Error creating MOM:', error);
      showError('Failed to create MOM');
    } finally {
      setIsCreating(false);
    }
  };

  const addNewPoint = () => {
    if (!mom) return;

    const nextSlNo = mom.mompoint ? Math.max(...mom.mompoint.map(p => p.slNo), 0) + 1 : 1;

    setNewPoint({
      slNo: nextSlNo,
      discussion: '',
      status: MOMPointStatus.PENDING,
    });
  };

  const saveNewPoint = async () => {
    if (!mom || !newPoint || !newPoint.discussion?.trim()) return;

    try {
      const response = await momsAPI.addMOMPoint(mom.id, {
        ...newPoint,
        discussion: newPoint.discussion.trim(),
      });

      if (response.success) {
        await loadMOM(); // Reload to get updated data
        setNewPoint(null);
        showSuccess('Point added successfully');
      } else {
        showError('Failed to add point');
      }
    } catch (error) {
      console.error('Error adding point:', error);
      showError('Failed to add point');
    }
  };

  const updatePoint = async (pointId: string, updates: Partial<MOMPoint>) => {
    if (!mom) return;

    try {
      const response = await momsAPI.updateMOMPoint(mom.id, pointId, updates);
      if (response.success) {
        await loadMOM(); // Reload to get updated data
        showSuccess('Point updated successfully');
      } else {
        showError('Failed to update point');
      }
    } catch (error) {
      console.error('Error updating point:', error);
      showError('Failed to update point');
    }
  };

  const deletePoint = async (pointId: string) => {
    if (!mom) return;

    try {
      const response = await momsAPI.deleteMOMPoint(mom.id, pointId);
      if (response.success) {
        await loadMOM(); // Reload to get updated data
        showSuccess('Point deleted successfully');
      } else {
        showError('Failed to delete point');
      }
    } catch (error) {
      console.error('Error deleting point:', error);
      showError('Failed to delete point');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Project not found</p>
      </div>
    );
  }

  if (!mom) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No MOM exists for this project</h3>
          <p className="mt-1 text-sm text-gray-500">
            Create a Minutes of Meeting document to start tracking discussions and action items.
          </p>
          <div className="mt-6">
            <button
              onClick={createMOM}
              disabled={isCreating}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isCreating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Create MOM
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              MOM - {project.code}: {project.name}
            </h2>
            <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center">
                <User className="h-4 w-4 mr-1" />
                Customer: {project.customer?.name}
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                Created: {formatDate(mom.createdAt)}
              </div>
              {mom.updatedAt !== mom.createdAt && (
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  Updated: {formatDate(mom.updatedAt)}
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Total Queries: {mom.mompoint?.length || 0}</span>
            <div className="flex space-x-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Completed: {mom.mompoint?.filter(p => p.status === MOMPointStatus.COMPLETED).length || 0}
              </span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Pending: {mom.mompoint?.filter(p => p.status === MOMPointStatus.PENDING).length || 0}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* MOM Grid */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                  Sl No
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                  Date
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Discussion Type
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                  Station
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Discussion
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action Plan
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Responsibility
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Planned Date
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Completion Date
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                  Status
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  Image/Attachment
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Remarks
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {mom.mompoint?.map((point) => (
                <MOMPointRow
                  key={point.id}
                  point={point}
                  onUpdate={(updates) => updatePoint(point.id, updates)}
                  onDelete={() => deletePoint(point.id)}
                />
              ))}

              {/* New Point Row */}
              {newPoint && (
                <NewMOMPointRow
                  point={newPoint}
                  onChange={setNewPoint}
                  onSave={saveNewPoint}
                  onCancel={() => setNewPoint(null)}
                />
              )}

              {/* Add New Point Button Row */}
              {!newPoint && (
                <tr>
                  <td colSpan={13} className="px-3 py-4 text-center">
                    <button
                      onClick={addNewPoint}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add New Point
                    </button>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// MOM Point Row Component
interface MOMPointRowProps {
  point: MOMPoint;
  onUpdate: (updates: Partial<MOMPoint>) => void;
  onDelete: () => void;
}

const MOMPointRow: React.FC<MOMPointRowProps> = ({ point, onUpdate, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<Partial<MOMPoint>>(point);

  const handleSave = () => {
    onUpdate(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData(point);
    setIsEditing(false);
  };

  const getStatusColor = (status: MOMPointStatus) => {
    switch (status) {
      case MOMPointStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case MOMPointStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case MOMPointStatus.DELAYED:
        return 'bg-red-100 text-red-800';
      case MOMPointStatus.ON_HOLD:
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isEditing) {
    return (
      <tr className="bg-blue-50">
        <td className="px-3 py-2 text-sm text-gray-900">{point.slNo}</td>
        <td className="px-3 py-2">
          <input
            type="date"
            value={editData.date ? formatDateForInput(editData.date) : ''}
            onChange={(e) => setEditData({ ...editData, date: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <input
            type="text"
            value={editData.discussionType || ''}
            onChange={(e) => setEditData({ ...editData, discussionType: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="Discussion Type"
          />
        </td>
        <td className="px-3 py-2">
          <input
            type="text"
            value={editData.station || ''}
            onChange={(e) => setEditData({ ...editData, station: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <textarea
            value={editData.discussion || ''}
            onChange={(e) => setEditData({ ...editData, discussion: e.target.value })}
            rows={2}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <textarea
            value={editData.actionPlan || ''}
            onChange={(e) => setEditData({ ...editData, actionPlan: e.target.value })}
            rows={2}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <input
            type="text"
            value={editData.responsibility || ''}
            onChange={(e) => setEditData({ ...editData, responsibility: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <input
            type="date"
            value={editData.plannedDate ? formatDateForInput(editData.plannedDate) : ''}
            onChange={(e) => setEditData({ ...editData, plannedDate: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <input
            type="date"
            value={editData.completionDate ? formatDateForInput(editData.completionDate) : ''}
            onChange={(e) => setEditData({ ...editData, completionDate: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <select
            value={editData.status || MOMPointStatus.PENDING}
            onChange={(e) => setEditData({ ...editData, status: e.target.value as MOMPointStatus })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            {Object.values(MOMPointStatus).map(status => (
              <option key={status} value={status}>{status.replace('_', ' ')}</option>
            ))}
          </select>
        </td>
        <td className="px-3 py-2">
          <input
            type="text"
            value={editData.imageAttachment || ''}
            onChange={(e) => setEditData({ ...editData, imageAttachment: e.target.value })}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <textarea
            value={editData.remarks || ''}
            onChange={(e) => setEditData({ ...editData, remarks: e.target.value })}
            rows={2}
            className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          />
        </td>
        <td className="px-3 py-2">
          <div className="flex space-x-1">
            <button
              onClick={handleSave}
              className="p-1 text-green-600 hover:text-green-800"
            >
              <Save className="h-4 w-4" />
            </button>
            <button
              onClick={handleCancel}
              className="p-1 text-gray-600 hover:text-gray-800"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
    );
  }

  return (
    <tr className="hover:bg-gray-50" onDoubleClick={() => setIsEditing(true)}>
      <td className="px-3 py-4 text-sm text-gray-900">{point.slNo}</td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.date ? formatDate(point.date) : '-'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.discussionType || '-'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.station || '-'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        <div className="max-w-xs truncate" title={point.discussion}>
          {point.discussion}
        </div>
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        <div className="max-w-xs truncate" title={point.actionPlan || ''}>
          {point.actionPlan || '-'}
        </div>
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.responsibility || '-'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.plannedDate ? formatDate(point.plannedDate) : '-'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.completionDate ? formatDate(point.completionDate) : '-'}
      </td>
      <td className="px-3 py-4 text-sm">
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(point.status)}`}>
          {point.status.replace('_', ' ')}
        </span>
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        {point.imageAttachment || '-'}
      </td>
      <td className="px-3 py-4 text-sm text-gray-900">
        <div className="max-w-xs truncate" title={point.remarks || ''}>
          {point.remarks || '-'}
        </div>
      </td>
      <td className="px-3 py-4 text-sm">
        <div className="flex space-x-1">
          <button
            onClick={() => setIsEditing(true)}
            className="p-1 text-blue-600 hover:text-blue-800"
            title="Edit"
          >
            <FileText className="h-4 w-4" />
          </button>
          <button
            onClick={onDelete}
            className="p-1 text-red-600 hover:text-red-800"
            title="Delete"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </td>
    </tr>
  );
};

// New MOM Point Row Component
interface NewMOMPointRowProps {
  point: Partial<MOMPoint>;
  onChange: (point: Partial<MOMPoint>) => void;
  onSave: () => void;
  onCancel: () => void;
}

const NewMOMPointRow: React.FC<NewMOMPointRowProps> = ({ point, onChange, onSave, onCancel }) => {
  return (
    <tr className="bg-green-50">
      <td className="px-3 py-2 text-sm text-gray-900">{point.slNo}</td>
      <td className="px-3 py-2">
        <input
          type="date"
          value={point.date ? formatDateForInput(point.date) : ''}
          onChange={(e) => onChange({ ...point, date: e.target.value })}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
      </td>
      <td className="px-3 py-2">
        <input
          type="text"
          value={point.discussionType || ''}
          onChange={(e) => onChange({ ...point, discussionType: e.target.value })}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          placeholder="Discussion Type"
        />
      </td>
      <td className="px-3 py-2">
        <input
          type="text"
          value={point.station || ''}
          onChange={(e) => onChange({ ...point, station: e.target.value })}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          placeholder="Station"
        />
      </td>
      <td className="px-3 py-2">
        <textarea
          value={point.discussion || ''}
          onChange={(e) => onChange({ ...point, discussion: e.target.value })}
          rows={2}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          placeholder="Discussion details..."
          required
        />
      </td>
      <td className="px-3 py-2">
        <textarea
          value={point.actionPlan || ''}
          onChange={(e) => onChange({ ...point, actionPlan: e.target.value })}
          rows={2}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          placeholder="Action plan..."
        />
      </td>
      <td className="px-3 py-2">
        <input
          type="text"
          value={point.responsibility || ''}
          onChange={(e) => onChange({ ...point, responsibility: e.target.value })}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          placeholder="Responsible person"
        />
      </td>
      <td className="px-3 py-2">
        <input
          type="date"
          value={point.plannedDate ? formatDateForInput(point.plannedDate) : ''}
          onChange={(e) => onChange({ ...point, plannedDate: e.target.value })}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
      </td>
      <td className="px-3 py-2">
        <input
          type="date"
          value={point.completionDate ? formatDateForInput(point.completionDate) : ''}
          onChange={(e) => onChange({ ...point, completionDate: e.target.value })}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
      </td>
      <td className="px-3 py-2">
        <select
          value={point.status || MOMPointStatus.PENDING}
          onChange={(e) => onChange({ ...point, status: e.target.value as MOMPointStatus })}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
        >
          {Object.values(MOMPointStatus).map(status => (
            <option key={status} value={status}>{status.replace('_', ' ')}</option>
          ))}
        </select>
      </td>
      <td className="px-3 py-2">
        <input
          type="text"
          value={point.imageAttachment || ''}
          onChange={(e) => onChange({ ...point, imageAttachment: e.target.value })}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          placeholder="Image/Attachment"
        />
      </td>
      <td className="px-3 py-2">
        <textarea
          value={point.remarks || ''}
          onChange={(e) => onChange({ ...point, remarks: e.target.value })}
          rows={2}
          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          placeholder="Remarks..."
        />
      </td>
      <td className="px-3 py-2">
        <div className="flex space-x-1">
          <button
            onClick={onSave}
            disabled={!point.discussion?.trim()}
            className="p-1 text-green-600 hover:text-green-800 disabled:text-gray-400"
          >
            <Save className="h-4 w-4" />
          </button>
          <button
            onClick={onCancel}
            className="p-1 text-gray-600 hover:text-gray-800"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </td>
    </tr>
  );
};

export default ProjectMOMGrid;
