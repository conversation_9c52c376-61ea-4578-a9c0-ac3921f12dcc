import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAtom } from 'jotai';
import {
  currentUserAtom,
  sidebarExpandedAtom,
  unreadAlertsCountStandaloneAtom,
  isAuthenticatedAtom
} from '../../store';
import { User, UserRole } from '../../types';
import {
  LayoutDashboard,
  ClipboardList,
  Users,
  Building2,
  Settings,
  FilePlus,
  Bell,
  FileText,
  ChevronLeft,
  ChevronRight,
  Gauge,
  LogOut,
  Building,
  UserCog,
  ListTodo,
  Calendar,
  Layers,
  Target,
  DollarSign
} from 'lucide-react';

const Sidebar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [currentUser, setCurrentUser] = useAtom(currentUserAtom);
  const [, setIsAuthenticated] = useAtom(isAuthenticatedAtom);
  const [sidebarExpanded, setSidebarExpanded] = useAtom(sidebarExpandedAtom);
  const [unreadAlertsCount] = useAtom(unreadAlertsCountStandaloneAtom);

  const toggleSidebar = () => {
    setSidebarExpanded(!sidebarExpanded);
  };

  // Check permissions for menu items
  const canAccessMasterData = (user: User | null): boolean => {
    if (!user) return false;
    return [UserRole.DIRECTOR, UserRole.PROJECT_MANAGER, UserRole.TEAM_LEAD].includes(user.role as UserRole);
  };

  const canAccessAddTask = (user: User | null): boolean => {
    if (!user) return false;
    return [UserRole.DIRECTOR, UserRole.PROJECT_MANAGER].includes(user.role as UserRole);
  };

  // Handle logout
  const handleLogout = () => {
    setCurrentUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    navigate('/login');
  };

  return (
    <aside
      className={`relative flex flex-col transition-all duration-400 ease-out h-screen sticky top-0 ${
        sidebarExpanded ? 'w-64' : 'w-18'
      } z-40`}
      style={{
        background: 'linear-gradient(180deg, #0f172a 0%, #1e293b 40%, #334155 100%)',
        boxShadow: `
          4px 0 24px rgba(0, 0, 0, 0.15),
          inset -1px 0 0 rgba(148, 163, 184, 0.1)
        `,
        borderRight: '1px solid rgba(148, 163, 184, 0.08)'
      }}
    >
      {/* Professional header */}
      <div
        className="px-4 py-4 border-b border-slate-700/50"
        style={{
          background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%)',
          boxShadow: 'inset 0 1px 0 rgba(148, 163, 184, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1)'
        }}
      >
        <div className={`flex items-center ${sidebarExpanded ? 'justify-between' : 'justify-center'}`}>
          <div className="flex items-center">
            <div
              className="p-2 rounded-lg transition-all duration-200 hover:scale-105"
              style={{
                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                boxShadow: '0 2px 8px rgba(59, 130, 246, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
              }}
            >
              <svg viewBox="0 0 24 24" className="h-5 w-5 text-white" fill="none" xmlns="http://www.w3.org/2000/svg">
                {/* Project management icon with tasks and timeline */}
                <rect x="3" y="4" width="18" height="16" rx="2" stroke="currentColor" strokeWidth="2"/>
                <path d="M8 8h8M8 12h6M8 16h4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                <circle cx="5.5" cy="8" r="1" fill="currentColor"/>
                <circle cx="5.5" cy="12" r="1" fill="currentColor"/>
                <circle cx="5.5" cy="16" r="1" fill="currentColor"/>
                <path d="M16 6v12" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round"/>
                <path d="M18 8l2 2-2 2" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            {sidebarExpanded && (
              <div className="ml-3">
                <h1 className="text-white font-semibold text-sm tracking-wide">Mekhos Technology</h1>
                <p className="text-slate-300 text-xs">Project Management</p>
              </div>
            )}
          </div>

          {sidebarExpanded && (
            <button
              onClick={toggleSidebar}
              className="p-1.5 rounded-md text-slate-400 hover:text-white hover:bg-slate-700/50 transition-all duration-200"
              style={{ boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)' }}
              aria-label="Collapse sidebar"
              title="Collapse sidebar"
            >
              <ChevronLeft size={16} />
            </button>
          )}
        </div>

        {!sidebarExpanded && (
          <button
            onClick={toggleSidebar}
            className="absolute -right-3 top-1/2 transform -translate-y-1/2 p-1.5 rounded-full bg-slate-600 text-white hover:bg-slate-500 transition-all duration-200 shadow-lg"
            style={{
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
              border: '2px solid #0f172a'
            }}
            aria-label="Expand sidebar"
            title="Expand sidebar"
          >
            <ChevronRight size={14} />
          </button>
        )}
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto py-2" style={{ scrollbarWidth: 'thin', scrollbarColor: 'rgba(148, 163, 184, 0.3) transparent' }}>
        <nav className="px-2 space-y-1">
          <NavItem
            to="/dashboard"
            icon={<LayoutDashboard size={18} />}
            label="Dashboard"
            active={location.pathname === '/dashboard'}
            expanded={sidebarExpanded}
          />

          <NavItem
            to="/mom"
            icon={<FileText size={18} />}
            label="Minutes of Meeting"
            active={location.pathname === '/mom'}
            expanded={sidebarExpanded}
          />

          <NavItem
            to="/mom/assigned"
            icon={<UserCog size={18} />}
            label="MOM Assigned Tasks"
            active={location.pathname === '/mom/assigned'}
            expanded={sidebarExpanded}
          />



          <NavItem
            to="/alerts"
            icon={<Bell size={18} />}
            label="Alerts"
            active={location.pathname === '/alerts'}
            expanded={sidebarExpanded}
            badge={unreadAlertsCount > 0 ? unreadAlertsCount.toString() : undefined}
          />

          {/* Subtasks for Engineers */}
          {currentUser?.role === UserRole.ENGINEER && (
            <NavItem
              to="/subtasks"
              icon={<ListTodo size={18} />}
              label="My Subtasks"
              active={location.pathname === '/subtasks'}
              expanded={sidebarExpanded}
            />
          )}

          {canAccessMasterData(currentUser) && (
            <>
              <div className={`mt-6 mb-2 ${!sidebarExpanded && 'hidden'}`}>
                <div
                  className="mx-2 px-3 py-2 rounded-md"
                  style={{
                    background: 'linear-gradient(135deg, rgba(51, 65, 85, 0.6) 0%, rgba(30, 41, 59, 0.4) 100%)',
                    border: '1px solid rgba(148, 163, 184, 0.1)'
                  }}
                >
                  <span className="text-xs font-medium text-slate-300 uppercase tracking-wider">
                    Master Data
                  </span>
                </div>
              </div>

              <NavItem
                to="/users"
                icon={<Users size={18} />}
                label="User Management"
                active={location.pathname === '/users'}
                expanded={sidebarExpanded}
              />

              <NavItem
                to="/departments"
                icon={<Building2 size={18} />}
                label="Departments"
                active={location.pathname === '/departments'}
                expanded={sidebarExpanded}
              />

              <NavItem
                to="/customers"
                icon={<Building size={18} />}
                label="Customers"
                active={location.pathname === '/customers'}
                expanded={sidebarExpanded}
              />

              {/* Show Project Categories for Directors and Project Managers */}
              {(currentUser?.role === UserRole.DIRECTOR || currentUser?.role === UserRole.PROJECT_MANAGER) && (
                <NavItem
                  to="/project-categories"
                  icon={<Layers size={18} />}
                  label="Project Categories"
                  active={location.pathname === '/project-categories'}
                  expanded={sidebarExpanded}
                />
              )}

              {/* Show Milestones for Directors only */}
              {currentUser?.role === UserRole.DIRECTOR && (
                <NavItem
                  to="/milestones"
                  icon={<Target size={18} />}
                  label="Milestones"
                  active={location.pathname === '/milestones'}
                  expanded={sidebarExpanded}
                />
              )}

              {/* Show Payments for Directors only */}
              {currentUser?.role === UserRole.DIRECTOR && (
                <NavItem
                  to="/payments"
                  icon={<DollarSign size={18} />}
                  label="Payments"
                  active={location.pathname === '/payments'}
                  expanded={sidebarExpanded}
                />
              )}

              {/* Show Projects for Directors, Project Managers, and Team Leads */}
              {(currentUser?.role === UserRole.DIRECTOR ||
                currentUser?.role === UserRole.PROJECT_MANAGER ||
                currentUser?.role === UserRole.TEAM_LEAD) && (
                <NavItem
                  to="/projects"
                  icon={<ClipboardList size={18} />}
                  label="Projects"
                  active={location.pathname === '/projects' || location.pathname.startsWith('/projects/')}
                  expanded={sidebarExpanded}
                />
              )}
            </>
          )}
        </nav>
      </div>

      {/* Footer */}
      <div
        className="border-t border-slate-700/50 py-2"
        style={{
          background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.6) 0%, rgba(15, 23, 42, 0.8) 100%)',
          boxShadow: 'inset 0 1px 0 rgba(148, 163, 184, 0.05)'
        }}
      >
        <div className="px-2 space-y-1">
          <NavItem
            to="/settings"
            icon={<Settings size={18} />}
            label="Settings"
            active={location.pathname === '/settings'}
            expanded={sidebarExpanded}
          />
          <LogoutButton
            icon={<LogOut size={18} />}
            label="Logout"
            expanded={sidebarExpanded}
            onClick={handleLogout}
          />
        </div>
      </div>

      <style>{`
        /* Professional scrollbar */
        ::-webkit-scrollbar {
          width: 4px;
        }

        ::-webkit-scrollbar-track {
          background: rgba(30, 41, 59, 0.3);
        }

        ::-webkit-scrollbar-thumb {
          background: rgba(148, 163, 184, 0.3);
          border-radius: 2px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: rgba(148, 163, 184, 0.5);
        }
      `}</style>
    </aside>
  );
};

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  active: boolean;
  expanded: boolean;
  badge?: string;
}

interface LogoutButtonProps {
  icon: React.ReactNode;
  label: string;
  expanded: boolean;
  onClick: () => void;
}

const LogoutButton: React.FC<LogoutButtonProps> = ({ icon, label, expanded, onClick }) => {
  return (
    <button
      onClick={onClick}
      className={`relative flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group w-full ${
        expanded ? 'justify-start' : 'justify-center'
      }`}
      style={{
        background: 'transparent',
        color: '#cbd5e1',
        boxShadow: 'none',
        borderLeft: '3px solid transparent'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.background = 'linear-gradient(135deg, rgba(220, 38, 38, 0.4) 0%, rgba(185, 28, 28, 0.3) 100%)';
        e.currentTarget.style.color = '#ffffff';
        e.currentTarget.style.boxShadow = 'inset 0 1px 0 rgba(220, 38, 38, 0.1), 0 1px 3px rgba(0, 0, 0, 0.05)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.background = 'transparent';
        e.currentTarget.style.color = '#cbd5e1';
        e.currentTarget.style.boxShadow = 'none';
      }}
    >
      {/* Icon */}
      <span className="flex-shrink-0 transition-transform duration-200 group-hover:scale-110">
        {icon}
      </span>

      {/* Label */}
      {expanded && (
        <span className="ml-3 truncate">
          {label}
        </span>
      )}

      {/* Tooltip for collapsed state */}
      {!expanded && (
        <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50"
             style={{ boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)' }}>
          {label}
          <div className="absolute left-0 top-1/2 transform -translate-x-1 -translate-y-1/2 w-0 h-0 border-r-4 border-r-gray-900 border-t-2 border-b-2 border-t-transparent border-b-transparent"></div>
        </div>
      )}
    </button>
  );
};

const NavItem: React.FC<NavItemProps> = ({ to, icon, label, active, expanded, badge }) => {
  return (
    <Link
      to={to}
      className={`relative flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group ${
        expanded ? 'justify-start' : 'justify-center'
      }`}
      style={{
        background: active
          ? 'linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(37, 99, 235, 0.1) 100%)'
          : 'transparent',
        color: active ? '#ffffff' : '#cbd5e1',
        boxShadow: active
          ? 'inset 0 1px 0 rgba(59, 130, 246, 0.2), 0 1px 3px rgba(0, 0, 0, 0.1)'
          : 'none',
        borderLeft: active ? '3px solid #3b82f6' : '3px solid transparent'
      }}
      onMouseEnter={(e) => {
        if (!active) {
          e.currentTarget.style.background = 'linear-gradient(135deg, rgba(51, 65, 85, 0.4) 0%, rgba(30, 41, 59, 0.3) 100%)';
          e.currentTarget.style.color = '#ffffff';
          e.currentTarget.style.boxShadow = 'inset 0 1px 0 rgba(148, 163, 184, 0.1), 0 1px 3px rgba(0, 0, 0, 0.05)';
        }
      }}
      onMouseLeave={(e) => {
        if (!active) {
          e.currentTarget.style.background = 'transparent';
          e.currentTarget.style.color = '#cbd5e1';
          e.currentTarget.style.boxShadow = 'none';
        }
      }}
    >
      {/* Icon */}
      <span className="flex-shrink-0 transition-transform duration-200 group-hover:scale-110">
        {icon}
      </span>

      {/* Label */}
      {expanded && (
        <span className="ml-3 truncate">
          {label}
        </span>
      )}

      {/* Badge for expanded state */}
      {badge && expanded && (
        <span
          className="ml-auto text-white text-xs px-2 py-0.5 rounded-full font-semibold"
          style={{
            background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
            boxShadow: '0 1px 3px rgba(220, 38, 38, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)'
          }}
        >
          {badge}
        </span>
      )}

      {/* Badge for collapsed state */}
      {badge && !expanded && (
        <span
          className="absolute -top-1 -right-1 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full font-semibold"
          style={{
            background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
            boxShadow: '0 1px 3px rgba(220, 38, 38, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
            border: '1.5px solid #0f172a',
            fontSize: '10px'
          }}
        >
          {parseInt(badge) > 9 ? '9+' : badge}
        </span>
      )}

      {/* Tooltip for collapsed state */}
      {!expanded && (
        <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50"
             style={{ boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)' }}>
          {label}
          <div className="absolute left-0 top-1/2 transform -translate-x-1 -translate-y-1/2 w-0 h-0 border-r-4 border-r-gray-900 border-t-2 border-b-2 border-t-transparent border-b-transparent"></div>
        </div>
      )}
    </Link>
  );
};

export default Sidebar;