import React from 'react';
import { useAtom } from 'jotai';
import { userSubtasksAtom, currentUserAtom } from '../../store';
import { format } from 'date-fns';
import { ArrowUpRight, ArrowDownRight, Minus, ExternalLink, Clock, CheckCircle2, AlertCircle, Play, Pause } from 'lucide-react';
import { Subtask, TaskStatus } from '../../types';
import { useNavigate } from 'react-router-dom';

const RecentSubtasks: React.FC = () => {
  const [userSubtasks] = useAtom(userSubtasksAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const navigate = useNavigate();

  // Show only recent subtasks (limit to 3)
  const recentSubtasks = [...userSubtasks]
    .sort((a, b) => new Date(b.createdAt || b.startDate).getTime() - new Date(a.createdAt || a.startDate).getTime())
    .slice(0, 3);

  // Get status icon
  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.NOT_STARTED:
        return <Clock size={16} className="text-gray-600" />;
      case TaskStatus.IN_PROGRESS:
        return <Play size={16} className="text-blue-600" />;
      case TaskStatus.COMPLETED:
        return <CheckCircle2 size={16} className="text-green-600" />;
      case TaskStatus.DELAYED:
        return <AlertCircle size={16} className="text-red-600" />;
      case TaskStatus.ON_HOLD:
        return <Pause size={16} className="text-yellow-600" />;
      default:
        return <Clock size={16} className="text-gray-600" />;
    }
  };

  // Get status colors
  const getStatusColors = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.NOT_STARTED:
        return {
          bg: 'from-gray-100 to-gray-200',
          text: 'text-gray-700',
          border: 'border-gray-300/50',
          shadow: 'shadow-gray-200/50'
        };
      case TaskStatus.IN_PROGRESS:
        return {
          bg: 'from-blue-100 to-blue-200',
          text: 'text-blue-700',
          border: 'border-blue-300/50',
          shadow: 'shadow-blue-200/50'
        };
      case TaskStatus.COMPLETED:
        return {
          bg: 'from-green-100 to-green-200',
          text: 'text-green-700',
          border: 'border-green-300/50',
          shadow: 'shadow-green-200/50'
        };
      case TaskStatus.DELAYED:
        return {
          bg: 'from-red-100 to-red-200',
          text: 'text-red-700',
          border: 'border-red-300/50',
          shadow: 'shadow-red-200/50'
        };
      case TaskStatus.ON_HOLD:
        return {
          bg: 'from-yellow-100 to-yellow-200',
          text: 'text-yellow-700',
          border: 'border-yellow-300/50',
          shadow: 'shadow-yellow-200/50'
        };
      default:
        return {
          bg: 'from-gray-100 to-gray-200',
          text: 'text-gray-700',
          border: 'border-gray-300/50',
          shadow: 'shadow-gray-200/50'
        };
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'high':
        return 'text-red-600';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM dd, yyyy');
  };

  // Calculate days remaining
  const getDaysRemaining = (dueDate: string) => {
    const today = new Date();
    const due = new Date(dueDate);
    const daysRemaining = Math.ceil((due.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysRemaining;
  };

  return (
    <div className="
      relative group h-full flex flex-col
      bg-gradient-to-br from-purple-50 via-white to-blue-50
      border border-purple-200/50
      rounded-2xl
      shadow-2xl shadow-purple-300/20
      transform-gpu transition-all duration-500 ease-out
      hover:shadow-3xl hover:shadow-purple-400/30
      backdrop-blur-sm
      before:absolute before:inset-0 before:rounded-2xl
      before:bg-gradient-to-t before:from-black/[0.02] before:to-white/10
      before:opacity-0 before:transition-opacity before:duration-300
      hover:before:opacity-100
      p-8
    ">
      {/* Top highlight for 3D effect */}
      <div className="absolute top-0 left-6 right-6 h-px bg-gradient-to-r from-transparent via-white/60 to-transparent"></div>

      {/* Left edge highlight */}
      <div className="absolute top-6 bottom-6 left-0 w-px bg-gradient-to-b from-transparent via-white/40 to-transparent"></div>

      {/* Header */}
      <div className="flex justify-between items-center mb-8 flex-shrink-0">
        <h3 className="text-xl font-bold text-slate-800 drop-shadow-sm">Recent Subtasks</h3>
        <button
          onClick={() => navigate('/subtasks')}
          className="
            group/link flex items-center gap-2 px-4 py-2
            text-sm font-medium text-purple-600
            bg-gradient-to-r from-purple-100 to-blue-100
            border border-purple-300/50
            rounded-xl shadow-lg shadow-purple-200/50
            hover:shadow-xl hover:shadow-purple-300/60
            hover:from-purple-200 hover:to-blue-200
            hover:text-purple-800
            transform transition-all duration-300
            hover:scale-105 hover:-translate-y-0.5
            active:scale-95 active:translate-y-0
          "
        >
          <span>View All Subtasks</span>
          <ExternalLink size={14} className="group-hover/link:translate-x-0.5 group-hover/link:-translate-y-0.5 transition-transform duration-300" />
        </button>
      </div>

      <div className="space-y-6 flex-1 overflow-y-auto min-h-0">
        {recentSubtasks.map((subtask, index) => {
          const statusColors = getStatusColors(subtask.status);
          const daysRemaining = subtask.dueDate ? getDaysRemaining(subtask.dueDate) : null;

          return (
            <div
              key={subtask.id}
              className="
                relative group/item
                bg-gradient-to-br from-white via-purple-50/30 to-white
                border border-purple-200/60
                rounded-xl p-6
                shadow-lg shadow-purple-200/40
                hover:shadow-xl hover:shadow-purple-300/50
                transform transition-all duration-300
                hover:scale-[1.02] hover:-translate-y-1
                last:mb-0
                backdrop-blur-sm
                before:absolute before:inset-0 before:rounded-xl
                before:bg-gradient-to-br before:from-white/40 before:to-transparent
                before:opacity-0 before:transition-opacity before:duration-300
                hover:before:opacity-100
                cursor-pointer
              "
              style={{
                animationDelay: `${index * 100}ms`,
              }}
              onClick={() => navigate(`/subtasks/${subtask.id}`)}
            >
              {/* Inner highlight */}
              <div className="absolute top-0 left-4 right-4 h-px bg-gradient-to-r from-transparent via-white/80 to-transparent"></div>

              <div className="relative z-10">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h4 className="font-bold text-slate-900 mb-2 drop-shadow-sm text-lg">
                      {subtask.name}
                    </h4>
                    <div className="flex items-center gap-2 mb-2">
                      <span className="
                        inline-block px-3 py-1
                        text-sm font-medium text-slate-600
                        bg-gradient-to-r from-slate-100 to-slate-200
                        border border-slate-300/50
                        rounded-lg shadow-sm
                      ">
                        {subtask.taskName}
                      </span>
                      <span className="text-slate-400">•</span>
                      <span className="text-sm text-slate-600">{subtask.projectName}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className={`
                        inline-flex items-center gap-1 px-3 py-1
                        text-sm font-medium rounded-lg
                        bg-gradient-to-r ${statusColors.bg}
                        border ${statusColors.border}
                        ${statusColors.text}
                        shadow-lg ${statusColors.shadow}
                      `}>
                        {getStatusIcon(subtask.status)}
                        <span>{subtask.status.replace(/_/g, ' ')}</span>
                      </div>
                      <span className={`text-sm font-medium ${getPriorityColor(subtask.priority || 'medium')}`}>
                        {subtask.priority || 'Medium'} Priority
                      </span>
                    </div>
                  </div>
                  <div className="text-right ml-4">
                    {daysRemaining !== null && (
                      <div className={`
                        inline-flex items-center px-3 py-1 mb-1
                        text-sm font-bold rounded-lg
                        shadow-lg border
                        ${daysRemaining > 0
                          ? 'text-slate-700 bg-gradient-to-r from-slate-100 to-slate-200 border-slate-300/50 shadow-slate-200/50'
                          : 'text-red-700 bg-gradient-to-r from-red-100 to-red-200 border-red-300/50 shadow-red-200/50'
                        }
                      `}>
                        {daysRemaining > 0 ? `${daysRemaining} days left` : `${Math.abs(daysRemaining)} days overdue`}
                      </div>
                    )}
                    <div className="text-xs text-slate-500 font-medium">
                      ID: {subtask.displayId || subtask.id}
                    </div>
                  </div>
                </div>

                {subtask.description && (
                  <div className="mt-4 p-3 bg-gradient-to-r from-slate-50 to-slate-100 rounded-lg border border-slate-200/50">
                    <p className="text-sm text-slate-600 line-clamp-2">{subtask.description}</p>
                  </div>
                )}

                <div className="mt-4 flex justify-between items-center text-xs text-slate-500">
                  <span>
                    {subtask.startDate && `Started: ${formatDate(subtask.startDate)}`}
                  </span>
                  <span>
                    {subtask.dueDate && `Due: ${formatDate(subtask.dueDate)}`}
                  </span>
                </div>
              </div>

              {/* Bottom shadow for depth */}
              <div className="absolute -bottom-1 left-2 right-2 h-2 bg-purple-900/5 rounded-full blur-sm transform scale-95 group-hover/item:scale-100 transition-transform duration-300"></div>
            </div>
          );
        })}

        {recentSubtasks.length === 0 && (
          <div className="
            text-center py-12 flex-1 flex flex-col items-center justify-center
            bg-gradient-to-br from-purple-50 to-blue-50
            border-2 border-dashed border-purple-300
            rounded-xl
          ">
            <p className="text-purple-500 font-medium text-lg drop-shadow-sm">No subtasks found</p>
            <p className="text-purple-400 text-sm mt-2">You don't have any subtasks assigned yet.</p>
          </div>
        )}
      </div>

      {/* Enhanced bottom shadow for depth */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/5 to-blue-900/10 rounded-2xl blur-xl translate-y-4 scale-95 -z-10 group-hover:translate-y-6 group-hover:blur-2xl transition-all duration-500"></div>
    </div>
  );
};

export default RecentSubtasks;
