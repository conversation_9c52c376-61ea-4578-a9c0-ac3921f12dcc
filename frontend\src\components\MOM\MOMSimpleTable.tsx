import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useAtom } from 'jotai';
import { customers<PERSON><PERSON>, engineers<PERSON>tom, moms<PERSON>tom, currentUser<PERSON>tom, projectsAtom, teamLeadsAtom, userTasks<PERSON>tom } from '../../store';
import { MOM, MOMAttendee, MOMActionItem, Customer, UserRole } from '../../types';
import { v4 as uuidv4 } from 'uuid';
import { CheckCircle2, XCircle, PlusCircle, Target, Edit3, Check, Plus, Trash2, X, Eye, Edit } from 'lucide-react';
import CustomerDialog from '../Customers/CustomerDialog';
import { momsAPI, tasksAPI } from '../../services/api';
import { dataService } from '../../services/dataServiceSingleton';
import { useNavigate } from 'react-router-dom';
import { formatDate, formatDateForInput } from '../../utils/dateFormatter';
import { useNotification } from '../../contexts/NotificationContext';

// Debounce utility function
const useDebounce = (callback: Function, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  return useCallback((...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
};

// Simple row structure with only the required fields
interface SimpleRow {
  id: string;
  descriptions?: string[];      // Multiple discussion points
  assigneeId: string;           // Assignee
  dueDate: string;              // Due date
  mekhosAttendees?: string[];   // Mekhos attendees
  customerAttendees?: string[]; // Customer attendees
}

// MOM Card Component
interface MOMCardProps {
  mom: MOM;
  customers: any[];
  projects: any[];
  teamLeads: any[];
  engineers: any[];
  onUpdate: (updatedMOM: any) => void;
  onDelete: () => void;
}

const MOMCard: React.FC<MOMCardProps> = ({ mom, customers, projects, teamLeads, engineers, onUpdate, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editingMOM, setEditingMOM] = useState(mom);
  const [editingRows, setEditingRows] = useState<SimpleRow[]>([]);

  useEffect(() => {
    setEditingMOM(mom);
    // Convert action items to rows for editing
    const rows = mom.actionItems?.map(item => ({
      id: item.id || uuidv4(),
      mekhosAttendees: mom.attendees?.filter(a => a.company === 'Mekhos Technology').map(a => a.name) || [],
      customerAttendees: mom.attendees?.filter(a => a.company !== 'Mekhos Technology').map(a => a.name) || [],
      descriptions: item.description ? [item.description] : [''],
      assigneeId: item.assigneeId || '',
      dueDate: item.dueDate ? formatDateForInput(item.dueDate) : ''
    })) || [];

    // Add empty row if no rows exist
    if (rows.length === 0) {
      rows.push({
        id: uuidv4(),
        mekhosAttendees: [],
        customerAttendees: [],
        descriptions: [''],
        assigneeId: '',
        dueDate: ''
      });
    }

    setEditingRows(rows);
  }, [mom]);

  const handleSave = async () => {
    try {
      // Process rows to extract attendees and action items
      const attendees: MOMAttendee[] = [];
      const actionItems: MOMActionItem[] = [];

      editingRows.forEach(row => {
        // Add Mekhos attendees
        row.mekhosAttendees?.forEach(name => {
          if (name.trim() && !attendees.find(a => a.name === name && a.company === 'Mekhos Technology')) {
            attendees.push({
              id: uuidv4(),
              name: name.trim(),
              company: 'Mekhos Technology',
              designation: '',
              email: ''
            });
          }
        });

        // Add customer attendees
        row.customerAttendees?.forEach(name => {
          if (name.trim() && !attendees.find(a => a.name === name && a.company !== 'Mekhos Technology')) {
            attendees.push({
              id: uuidv4(),
              name: name.trim(),
              company: 'Customer',
              designation: '',
              email: ''
            });
          }
        });

        // Add action items for each description
        row.descriptions?.forEach((description, descIndex) => {
          if (description?.trim()) {
            actionItems.push({
              id: `${row.id}-${descIndex}`,
              description: description.trim(),
              assigneeId: row.assigneeId || '',
              dueDate: row.dueDate ? new Date(row.dueDate).toISOString() : '',
              status: 'PENDING'
            });
          }
        });
      });

      const updatedMOM = {
        ...editingMOM,
        attendees,
        actionItems
      };

      await onUpdate(updatedMOM);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving MOM:', error);
    }
  };

  const addRow = () => {
    setEditingRows([...editingRows, {
      id: uuidv4(),
      mekhosAttendees: [],
      customerAttendees: [],
      descriptions: [''],
      assigneeId: '',
      dueDate: ''
    }]);
  };

  const updateRow = (index: number, field: keyof SimpleRow, value: any) => {
    const newRows = [...editingRows];
    newRows[index] = { ...newRows[index], [field]: value };
    setEditingRows(newRows);
  };

  const updateDescription = (rowIndex: number, descIndex: number, value: string) => {
    const newRows = [...editingRows];
    const newDescriptions = [...(newRows[rowIndex].descriptions || [])];
    newDescriptions[descIndex] = value;
    newRows[rowIndex] = { ...newRows[rowIndex], descriptions: newDescriptions };
    setEditingRows(newRows);
  };

  const addDescription = (rowIndex: number) => {
    const newRows = [...editingRows];
    const newDescriptions = [...(newRows[rowIndex].descriptions || []), ''];
    newRows[rowIndex] = { ...newRows[rowIndex], descriptions: newDescriptions };
    setEditingRows(newRows);
  };

  const removeDescription = (rowIndex: number, descIndex: number) => {
    const newRows = [...editingRows];
    const newDescriptions = [...(newRows[rowIndex].descriptions || [])];
    if (newDescriptions.length > 1) {
      newDescriptions.splice(descIndex, 1);
      newRows[rowIndex] = { ...newRows[rowIndex], descriptions: newDescriptions };
      setEditingRows(newRows);
    }
  };

  const removeRow = (index: number) => {
    if (editingRows.length > 1) {
      setEditingRows(editingRows.filter((_, i) => i !== index));
    }
  };

  const customer = customers?.find(c => c.id === mom.customerId);
  const project = projects?.find(p => p.id === mom.projectId);

  if (isEditing) {
    return (
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold text-gray-800">Edit Minutes of Meeting</h3>
          <div className="flex space-x-2">
            <button
              onClick={handleSave}
              className="inline-flex items-center px-3 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <Check size={16} className="mr-1" />
              Save
            </button>
            <button
              onClick={() => setIsEditing(false)}
              className="inline-flex items-center px-3 py-1.5 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              <X size={16} className="mr-1" />
              Cancel
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {/* Meeting Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Meeting Date</label>
            <input
              type="date"
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={formatDateForInput(editingMOM.date)}
              onChange={(e) => setEditingMOM({ ...editingMOM, date: new Date(e.target.value).toISOString() })}
            />
          </div>

          {/* Customer */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Customer</label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={editingMOM.customerId}
              onChange={(e) => setEditingMOM({ ...editingMOM, customerId: e.target.value })}
            >
              {customers?.map((customer) => (
                <option key={customer.id} value={customer.id}>{customer.name}</option>
              ))}
            </select>
          </div>

          {/* Project */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Project</label>
            <select
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={editingMOM.projectId || ''}
              onChange={(e) => setEditingMOM({ ...editingMOM, projectId: e.target.value || null })}
            >
              <option value="">No Project</option>
              {projects?.filter(p => p.customerId === editingMOM.customerId).map((project) => (
                <option key={project.id} value={project.id}>{project.name}</option>
              ))}
            </select>
          </div>

          {/* Meeting Agenda */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Meeting Agenda</label>
            <textarea
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={2}
              value={editingMOM.agenda || ''}
              onChange={(e) => setEditingMOM({ ...editingMOM, agenda: e.target.value })}
              placeholder="Enter the meeting agenda..."
            />
          </div>
        </div>

        {/* Meeting Details Table */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="text-md font-medium text-gray-700">Meeting Details</h4>
            <button
              onClick={addRow}
              className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus size={16} className="mr-1" />
              Add Row
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full border border-gray-300 rounded-lg">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">#</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Mekhos Attendees</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Customer Attendees</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Assignee</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Due Date</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {editingRows.map((row, index) => (
                  <tr key={row.id}>
                    <td className="px-3 py-2 text-sm text-gray-500">{index + 1}</td>
                    <td className="px-3 py-2">
                      <input
                        type="text"
                        className="w-full p-1 border border-gray-300 rounded text-sm"
                        value={row.mekhosAttendees?.join(', ') || ''}
                        onChange={(e) => updateRow(index, 'mekhosAttendees', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                        placeholder="Add Mekhos attendee"
                      />
                    </td>
                    <td className="px-3 py-2">
                      <input
                        type="text"
                        className="w-full p-1 border border-gray-300 rounded text-sm"
                        value={row.customerAttendees?.join(', ') || ''}
                        onChange={(e) => updateRow(index, 'customerAttendees', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                        placeholder="Add Customer attendee"
                      />
                    </td>
                    <td className="px-3 py-2">
                      <div className="space-y-2">
                        {(row.descriptions || ['']).map((description, descIndex) => (
                          <div key={descIndex} className="flex items-center space-x-2">
                            <textarea
                              className="flex-1 p-1 border border-gray-300 rounded text-sm resize-none overflow-hidden"
                              rows={1}
                              value={description}
                              onChange={(e) => {
                                updateDescription(index, descIndex, e.target.value);
                                // Auto-resize textarea
                                e.target.style.height = 'auto';
                                e.target.style.height = e.target.scrollHeight + 'px';
                              }}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && !e.shiftKey) {
                                  e.preventDefault();
                                  // If this is the last description and it has content, add a new one
                                  if (descIndex === (row.descriptions?.length || 1) - 1 && description.trim()) {
                                    addDescription(index);
                                  }
                                }
                              }}
                              onInput={(e) => {
                                // Auto-resize on input
                                const target = e.target as HTMLTextAreaElement;
                                target.style.height = 'auto';
                                target.style.height = target.scrollHeight + 'px';
                              }}
                              style={{ minHeight: '32px' }}
                              placeholder="Enter discussion point"
                            />
                            {(row.descriptions?.length || 0) > 1 && (
                              <button
                                type="button"
                                onClick={() => removeDescription(index, descIndex)}
                                className="text-red-600 hover:text-red-800 p-1"
                              >
                                <X size={14} />
                              </button>
                            )}
                          </div>
                        ))}
                      </div>
                    </td>
                    <td className="px-3 py-2">
                      <select
                        className="w-full p-1 border border-gray-300 rounded text-sm"
                        value={row.assigneeId}
                        onChange={(e) => updateRow(index, 'assigneeId', e.target.value)}
                      >
                        <option value="">Select Team Lead</option>
                        {teamLeads?.map((teamLead) => (
                          <option key={teamLead.id} value={teamLead.id}>{teamLead.name}</option>
                        ))}
                      </select>
                    </td>
                    <td className="px-3 py-2">
                      <input
                        type="date"
                        className="w-full p-1 border border-gray-300 rounded text-sm"
                        value={row.dueDate}
                        onChange={(e) => updateRow(index, 'dueDate', e.target.value)}
                      />
                    </td>
                    <td className="px-3 py-2">
                      {editingRows.length > 1 && (
                        <button
                          onClick={() => removeRow(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 size={16} />
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  }

  // View Mode
  return (
    <div className="bg-white rounded-lg shadow border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <div className="flex items-center space-x-4 mb-2">
            <h3 className="text-lg font-semibold text-gray-800">
              Minutes of Meeting - {customer?.name || 'Unknown Customer'}
            </h3>
            <span className="text-sm text-gray-500">
              {formatDate(mom.date)}
            </span>
          </div>

          <div className="flex items-center space-x-4 text-sm text-gray-600">
            {project && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {project.name}
              </span>
            )}
            {mom.agenda && (
              <span className="truncate max-w-md">{mom.agenda}</span>
            )}
          </div>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={() => setIsEditing(true)}
            className="inline-flex items-center px-3 py-1.5 bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors"
          >
            <Edit3 size={16} className="mr-1" />
            Edit
          </button>
          <button
            onClick={onDelete}
            className="inline-flex items-center px-3 py-1.5 bg-red-50 text-red-700 rounded-md hover:bg-red-100 transition-colors"
          >
            <Trash2 size={16} className="mr-1" />
            Delete
          </button>
        </div>
      </div>

      {/* Attendees */}
      {mom.attendees && mom.attendees.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Attendees</h4>
          <div className="flex flex-wrap gap-2">
            {mom.attendees.map((attendee, index) => (
              <span
                key={index}
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  attendee.company === 'Mekhos Technology'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-green-100 text-green-800'
                }`}
              >
                {attendee.name}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Discussion Points */}
      {mom.actionItems && mom.actionItems.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Discussion Points</h4>
          <div className="space-y-2">
            {mom.actionItems.map((item, index) => (
              <div key={index} className="p-3 bg-gray-50 rounded border">
                <div className="font-medium text-gray-800 mb-1">{item.description}</div>
                <div className="flex items-center justify-between text-xs text-gray-600">
                  {item.assigneeId && (
                    <span>
                      Assigned to: {teamLeads?.find(tl => tl.id === item.assigneeId)?.name || 'Unknown'}
                    </span>
                  )}
                  {item.dueDate && (
                    <span>
                      Due: {formatDate(item.dueDate)}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Excel-like styles for MOM interface (borderless)
const excelStyles = {
  // Container styles
  container: "w-full h-full bg-white rounded-lg overflow-hidden",
  tableContainer: "overflow-auto max-h-[600px]",

  // Table styles
  table: "w-full border-collapse table-fixed min-w-[1400px]",

  // Header styles
  headerRow: "bg-gray-50 sticky top-0 z-10",
  headerCell: "px-3 py-3 text-xs font-semibold text-gray-700 text-left bg-gray-50 select-none",

  // Cell styles
  cell: "p-0 relative group hover:bg-gray-50 transition-colors",
  cellInput: "w-full h-full px-3 py-2 border-none outline-none bg-transparent text-base resize-none focus:bg-blue-50 focus:ring-1 focus:ring-blue-300",
  cellSelect: "w-full h-full px-3 py-2 border-none outline-none bg-transparent text-base cursor-pointer focus:bg-blue-50 focus:ring-1 focus:ring-blue-300",
  cellDate: "w-full h-full px-3 py-2 border-none outline-none bg-transparent text-base focus:bg-blue-50 focus:ring-1 focus:ring-blue-300",

  // Row styles
  row: "hover:bg-gray-50 transition-colors",
  rowNumber: "bg-gray-50 text-center py-2 px-3 text-xs font-semibold text-gray-600 select-none min-w-[50px]",

  // Focus and selection styles
  focusedCell: "ring-2 ring-blue-500 ring-inset bg-blue-50 z-20",
  selectedCell: "bg-blue-50",

  // Action styles
  addButton: "inline-flex items-center px-3 py-2 bg-green-600 text-white text-sm font-medium rounded hover:bg-green-700 transition-colors",
  deleteButton: "text-red-500 hover:text-red-700 p-1 opacity-0 group-hover:opacity-100 transition-opacity",

  // Column widths
  colDate: "w-32",
  colCustomer: "w-40",
  colProject: "w-40",
  colAgenda: "w-60",
  colMekhosAttendees: "w-48",
  colCustomerAttendees: "w-48",
  colDescription: "w-60",
  colAssignee: "w-40",
  colDueDate: "w-32",
  colActions: "w-20"
};

interface MOMSimpleTableProps {
  onMOMCreated?: () => void;
}

const MOMSimpleTable: React.FC<MOMSimpleTableProps> = ({ onMOMCreated }) => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotification();

  const [customers, setCustomers] = useAtom(customersAtom);
  const [engineers] = useAtom(engineersAtom);
  const [moms, setMoms] = useAtom(momsAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [teamLeads] = useAtom(teamLeadsAtom);
  const [userTasks, setUserTasks] = useAtom(userTasksAtom);

  // Basic meeting info
  const [date, setDate] = useState<string>(formatDateForInput(new Date()));
  const [customerId, setCustomerId] = useState<string>('');
  const [projectId, setProjectId] = useState<string>('');
  const [agenda, setAgenda] = useState<string>('');

  // Load all users for attendees
  const [allUsers, setAllUsers] = useState<any[]>([]);

  // Use the projects from the global state
  const [projects] = useAtom(projectsAtom);

  // Filtered projects based on selected customer
  const filteredProjects = projects ? projects.filter(project =>
    project.customerId === customerId
  ) : [];

  // Rows with only required fields
  const [rows, setRows] = useState<SimpleRow[]>([
    {
      id: uuidv4(),
      descriptions: [''],
      assigneeId: '',
      dueDate: '',
      mekhosAttendees: [],
      customerAttendees: []
    }
  ]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedMOM, setSelectedMOM] = useState<MOM | null>(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);



  // Inline editing state for MOM table
  const [editingMOMId, setEditingMOMId] = useState<string | null>(null);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editValue, setEditValue] = useState<string>('');
  const [pendingUpdates, setPendingUpdates] = useState<Map<string, any>>(new Map());

  // Debounced update function to reduce API calls
  const debouncedUpdateMOM = useDebounce(async (momId: string, updatedMOM: any) => {
    try {
      console.log('🔄 Updating MOM with debounced call:', momId);
      await momsAPI.updateMOM(momId, updatedMOM);

      // Refresh MOMs data using dataService to ensure consistency
      await dataService.loadMOMs();
      console.log('✅ MOMs data refreshed after update');
    } catch (error) {
      console.error('❌ Error updating MOM:', error);
    }
  }, 1000); // 1 second debounce

  // Delete MOM function (Director only)
  const handleDeleteMOM = async (momId: string) => {
    // Check if user is Director
    if (currentUser?.role !== 'DIRECTOR') {
      showError('Access Denied', 'Only Directors can delete MOMs.');
      return;
    }

    if (!window.confirm('Are you sure you want to delete this MOM? This action cannot be undone.')) {
      return;
    }

    try {
      console.log('🗑️ Deleting MOM:', momId);
      await momsAPI.deleteMOM(momId);

      // Refresh MOMs data using dataService to ensure consistency
      await dataService.loadMOMs();
      console.log('✅ MOM deleted successfully');

      showSuccess('MOM Deleted', 'MOM deleted successfully!');
    } catch (error: any) {
      console.error('❌ Error deleting MOM:', error);
      showError('Delete Failed', error.response?.data?.message || 'Failed to delete MOM. Please try again.');
    }
  };





  // Load all users for attendees
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const usersResponse = await fetch('/api/users', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });
        if (usersResponse.ok) {
          const usersData = await usersResponse.json();
          setAllUsers(usersData.data || []);
        }
      } catch (error) {
        console.error('Error loading users:', error);
      }
    };

    loadUsers();
  }, []);

  // Set loading state to false after initial render and check data availability
  useEffect(() => {
    const timer = setTimeout(() => {
      // Only set loading to false if we have the required data
      if (customers && projects && moms) {
        setIsLoading(false);
      } else {
        // If data is still missing after timeout, set loading to false anyway
        // to avoid infinite loading state
        setTimeout(() => setIsLoading(false), 1000);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [customers, projects, moms]);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Handler for adding a new customer
  const handleAddCustomer = async (newCustomer: Customer) => {
    setCustomers((prevCustomers) => [...prevCustomers, newCustomer]);
    setCustomerId(newCustomer.id);
    setIsCustomerDialogOpen(false);
  };

  // Add a new row
  const addRow = () => {
    setRows([...rows, {
      id: uuidv4(),
      descriptions: [''],
      assigneeId: '',
      dueDate: '',
      mekhosAttendees: [],
      customerAttendees: []
    }]);
  };

  // Add a new Mekhos attendee to a row
  const addMekhosAttendee = (rowId: string, attendee: string) => {
    if (!attendee.trim()) return;

    setRows(rows.map(row =>
      row.id === rowId
        ? {
            ...row,
            mekhosAttendees: [...(row.mekhosAttendees || []), attendee.trim()]
          }
        : row
    ));
  };

  // Add a new Customer attendee to a row
  const addCustomerAttendee = (rowId: string, attendee: string) => {
    if (!attendee.trim()) return;

    setRows(rows.map(row =>
      row.id === rowId
        ? {
            ...row,
            customerAttendees: [...(row.customerAttendees || []), attendee.trim()]
          }
        : row
    ));
  };

  // Remove an attendee from a row
  const removeAttendee = (rowId: string, type: 'mekhos' | 'customer', index: number) => {
    setRows(rows.map(row => {
      if (row.id !== rowId) return row;

      if (type === 'mekhos') {
        const newAttendees = [...(row.mekhosAttendees || [])];
        newAttendees.splice(index, 1);
        return { ...row, mekhosAttendees: newAttendees };
      } else {
        const newAttendees = [...(row.customerAttendees || [])];
        newAttendees.splice(index, 1);
        return { ...row, customerAttendees: newAttendees };
      }
    }));
  };

  // Remove a row
  const removeRow = (id: string) => {
    setRows(rows.filter(row => row.id !== id));
  };

  // Update a row field
  const updateRow = (id: string, field: string, value: string) => {
    setRows(rows.map(row =>
      row.id === id ? { ...row, [field]: value } : row
    ));
  };

  // Update a specific description in a row
  const updateDescription = (rowId: string, descIndex: number, value: string) => {
    setRows(rows.map(row => {
      if (row.id === rowId) {
        const newDescriptions = [...(row.descriptions || [])];
        newDescriptions[descIndex] = value;
        return { ...row, descriptions: newDescriptions };
      }
      return row;
    }));
  };

  // Add a new description to a row
  const addDescription = (rowId: string) => {
    setRows(rows.map(row => {
      if (row.id === rowId) {
        const newDescriptions = [...(row.descriptions || []), ''];
        return { ...row, descriptions: newDescriptions };
      }
      return row;
    }));

    // Focus on the new description field after a short delay
    setTimeout(() => {
      const rowIndex = rows.findIndex(r => r.id === rowId);
      const currentRow = rows[rowIndex];
      const newDescIndex = (currentRow?.descriptions?.length || 0);
      const newTextarea = document.querySelector(`textarea[data-row-id="${rowId}"][data-desc-index="${newDescIndex}"]`) as HTMLTextAreaElement;
      if (newTextarea) {
        newTextarea.focus();
        // Auto-resize the new textarea
        newTextarea.style.height = 'auto';
        newTextarea.style.height = newTextarea.scrollHeight + 'px';
      }
    }, 50);
  };

  // Auto-resize all textareas on component mount and when rows change
  useEffect(() => {
    const resizeAllTextareas = () => {
      const textareas = document.querySelectorAll('textarea[data-row-id]');
      textareas.forEach((textarea) => {
        const element = textarea as HTMLTextAreaElement;
        element.style.height = 'auto';
        element.style.height = element.scrollHeight + 'px';
      });
    };

    // Delay to ensure DOM is updated
    setTimeout(resizeAllTextareas, 100);
  }, [rows]);

  // Remove a description from a row
  const removeDescription = (rowId: string, descIndex: number) => {
    setRows(rows.map(row => {
      if (row.id === rowId) {
        const newDescriptions = [...(row.descriptions || [])];
        if (newDescriptions.length > 1) {
          newDescriptions.splice(descIndex, 1);
          return { ...row, descriptions: newDescriptions };
        }
      }
      return row;
    }));
  };

  // Validation function
  const validate = (): boolean => {
    const newErrors: {[key: string]: string} = {};

    if (!date) newErrors.date = 'Meeting date is required';
    if (!customerId) newErrors.customerId = 'Customer is required';
    if (!agenda.trim()) newErrors.agenda = 'Agenda is required';

    // Check if at least one row has attendees or a description
    if (!rows.some(r =>
      (r.mekhosAttendees && r.mekhosAttendees.length > 0) ||
      (r.customerAttendees && r.customerAttendees.length > 0) ||
      (r.descriptions && r.descriptions.some(desc => desc?.trim()))
    )) {
      newErrors.rows = 'At least one entry with attendees or description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Form submission handler
  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    if (!validate()) {
      return;
    }

    // Create MOM directly
    handleMOMCreation();
  };



  // Function to handle MOM creation
  const handleMOMCreation = async () => {
    setIsSubmitting(true);

    try {
      // Use the current form state data

      // Process all rows
      const attendees: MOMAttendee[] = [];
      const points: string[] = [];
      const actionItems: MOMActionItem[] = [];

      rows.forEach((row: any) => {
        console.log('Processing row:', row);
        console.log('Row description:', row.description);
        console.log('Row assigneeId:', row.assigneeId);
        console.log('Row dueDate:', row.dueDate);

        // Process Mekhos attendees
        if (row.mekhosAttendees && row.mekhosAttendees.length > 0) {
          row.mekhosAttendees.forEach(attendeeName => {
            attendees.push({
              id: uuidv4(),
              name: attendeeName,
              company: 'Mekhos Technology',
              designation: '',
              email: ''
            });
          });
        }

        // Process Customer attendees
        if (row.customerAttendees && row.customerAttendees.length > 0) {
          row.customerAttendees.forEach(attendeeName => {
            attendees.push({
              id: uuidv4(),
              name: attendeeName,
              company: 'Customer',
              designation: '',
              email: ''
            });
          });
        }

        // Process each description as a separate action item
        if (row.descriptions && row.descriptions.length > 0) {
          row.descriptions.forEach((description, descIndex) => {
            if (description?.trim()) {
              console.log('Adding action item:', description);

              try {
                // Format the due date as ISO string if provided, otherwise use empty string
                let formattedDueDate = '';
                if (row.dueDate && row.dueDate.trim()) {
                  const dateObj = new Date(row.dueDate);
                  formattedDueDate = dateObj.toISOString();
                  console.log('Formatted due date for action item:', formattedDueDate);
                }

                actionItems.push({
                  id: `${row.id}-${descIndex}`,
                  description: description,
                  assigneeId: row.assigneeId || '', // Allow empty assignee
                  dueDate: formattedDueDate, // Allow empty due date
                  status: "PENDING"
                });
              } catch (error) {
                console.error('Error formatting due date:', error);
                // If date formatting fails, use the original string or empty
                actionItems.push({
                  id: `${row.id}-${descIndex}`,
                  description: description,
                  assigneeId: row.assigneeId || '',
                  dueDate: row.dueDate || '',
                  status: "PENDING"
                });
              }
            }
          });
        }
      });

      console.log('Discussion points to be saved:', points);

      // Create new MOM entry with proper date format
      const momId = uuidv4();

      // Ensure date is in ISO format
      const formattedDate = new Date(date);
      console.log('Formatted date for API:', formattedDate.toISOString());

      const newMOM: MOM = {
        id: momId,
        date: formattedDate.toISOString(), // Use ISO format for date
        customerId,
        projectId: projectId || '', // Include project ID
        engineerId: currentUser?.id || '', // Use current user ID as engineer ID
        agenda,
        attendees,
        points,
        actionItems,
        createdBy: currentUser?.id || '',
        createdAt: new Date().toISOString()
      };

      console.log('Sending MOM data to API:', {
        ...newMOM,
        points: points.length > 0 ? points : []
      });

      // Send the data to the API using dataService
      try {
        const data = await momsAPI.createMOM({
          ...newMOM,
          points: points.length > 0 ? points : []
        });
        console.log('API response:', data);

        // Refresh MOMs data using dataService to ensure consistency
        await dataService.loadMOMs();
        console.log('✅ MOMs data refreshed after creation');

        showSuccess('MOM Created', 'Minutes of Meeting created successfully!');

        // Reset the form
        resetForm();

        // Notify parent component
        if (onMOMCreated) {
          onMOMCreated();
        }
      } catch (apiError) {
        console.error('API Error creating MOM:', apiError);
        showError('Save Failed', `Failed to save MOM: ${apiError instanceof Error ? apiError.message : 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error creating MOM:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form to initial state
  const resetForm = () => {
    setDate(formatDateForInput(new Date()));
    setCustomerId('');
    setProjectId('');
    setAgenda('');
    setRows([{
      id: uuidv4(),
      descriptions: [''],
      assigneeId: '',
      dueDate: '',
      mekhosAttendees: [],
      customerAttendees: []
    }]);
    setErrors({});
    setShowForm(false); // Hide the form after successful submission
  };

  // Inline editing handlers for MOM table
  const handleCellClick = (momId: string, field: string, currentValue: string) => {
    // Prevent editing of date field
    if (field === 'date') {
      return;
    }
    setEditingMOMId(momId);
    setEditingField(field);
    setEditValue(currentValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setEditValue(e.target.value);
  };

  const handleInputClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleDoneClick();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEdit();
    }
  };

  const handleDoneClick = async () => {
    if (!editingMOMId || !editingField) return;

    try {
      const mom = moms?.find(m => m.id === editingMOMId);
      if (!mom) return;

      let updatedMOM: any = { ...mom };

      // Handle different field types
      if (editingField === 'customerId') {
        updatedMOM.customerId = editValue;
      } else if (editingField === 'projectId') {
        updatedMOM.projectId = editValue || null;
      } else if (editingField === 'agenda') {
        updatedMOM.agenda = editValue;
      } else if (editingField.startsWith('mekhosAttendees')) {
        const newMekhosNames = editValue.split(',').map(s => s.trim()).filter(s => s);
        const newMekhosAttendees = newMekhosNames.map(name => ({
          id: uuidv4(),
          name,
          company: 'Mekhos Technology',
          designation: '',
          email: ''
        }));
        const customerAttendees = mom.attendees?.filter(a => a.company !== 'Mekhos Technology') || [];
        updatedMOM.attendees = [...newMekhosAttendees, ...customerAttendees];
      } else if (editingField.startsWith('customerAttendees')) {
        const newCustomerNames = editValue.split(',').map(s => s.trim()).filter(s => s);
        const newCustomerAttendees = newCustomerNames.map(name => ({
          id: uuidv4(),
          name,
          company: 'Customer',
          designation: '',
          email: ''
        }));
        const mekhosAttendees = mom.attendees?.filter(a => a.company === 'Mekhos Technology') || [];
        updatedMOM.attendees = [...mekhosAttendees, ...newCustomerAttendees];
      } else if (editingField.startsWith('actionItem_')) {
        const [, actionIndex, actionField] = editingField.split('_');
        const updatedActionItems = [...(mom.actionItems || [])];
        const index = parseInt(actionIndex);

        if (updatedActionItems.length === 0 && index === 0) {
          updatedActionItems.push({
            id: uuidv4(),
            description: '',
            assigneeId: '',
            dueDate: '',
            status: 'PENDING'
          });
        }

        if (actionField === 'description') {
          updatedActionItems[index] = { ...updatedActionItems[index], description: editValue };
        } else if (actionField === 'assigneeId') {
          updatedActionItems[index] = { ...updatedActionItems[index], assigneeId: editValue };
        } else if (actionField === 'dueDate') {
          updatedActionItems[index] = { ...updatedActionItems[index], dueDate: editValue ? new Date(editValue).toISOString() : '' };
        }

        updatedMOM.actionItems = updatedActionItems;
      }

      // Update local state immediately for better UX
      setMoms(prevMoms => prevMoms?.map(m => m.id === editingMOMId ? updatedMOM : m) || []);

      // Use debounced API call to reduce server load
      debouncedUpdateMOM(editingMOMId, updatedMOM);

      handleCancelEdit();
    } catch (error) {
      console.error('Error updating MOM:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditingMOMId(null);
    setEditingField(null);
    setEditValue('');
  };

  const handleCancelClick = () => {
    handleCancelEdit();
  };

  // View MOM handler
  const handleViewMOM = (mom: MOM) => {
    setSelectedMOM(mom);
    setShowViewModal(true);
  };

  // Edit MOM handler
  const handleEditMOM = (mom: MOM) => {
    setSelectedMOM(mom);
    setShowEditModal(true);
  };

  // Close modals
  const closeModals = () => {
    setShowViewModal(false);
    setShowEditModal(false);
    setSelectedMOM(null);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Minutes of Meeting</h2>
        <div className="flex items-center space-x-3">
          <button
            type="button"
            className="btn btn-outline flex items-center"
            onClick={() => {
              setSelectedMOM(null);
              setShowViewModal(true);
            }}
            disabled={!moms || moms.length === 0}
          >
            <Eye size={16} className="mr-2" />
            Excel View
          </button>
          <button
            type="button"
            className={`btn ${showForm ? 'btn-outline' : 'btn-primary'} flex items-center`}
            onClick={() => setShowForm(!showForm)}
          >
            {showForm ? (
              'Cancel'
            ) : (
              'Add MOM'
            )}
          </button>
        </div>
      </div>



      {isLoading ? (
        // Loading indicator
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : (
        // Display existing MOMs
        <div className="space-y-4">
          {!moms || moms.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No minutes of meeting found. Click "Add MOM" to create one.</p>
            </div>
          ) : (
            <div className={excelStyles.container}>
              <div className={excelStyles.tableContainer}>
                <table className={excelStyles.table}>
                  <thead>
                    <tr className={excelStyles.headerRow}>
                      <th className={`${excelStyles.headerCell} ${excelStyles.colDate} sticky top-0 bg-gray-50 z-10`}>Date</th>
                      <th className={`${excelStyles.headerCell} ${excelStyles.colCustomer} sticky top-0 bg-gray-50 z-10`}>Customer</th>
                      <th className={`${excelStyles.headerCell} ${excelStyles.colProject} sticky top-0 bg-gray-50 z-10`}>Project</th>
                      <th className={`${excelStyles.headerCell} ${excelStyles.colAgenda} sticky top-0 bg-gray-50 z-10`}>Agenda</th>
                      <th className={`${excelStyles.headerCell} ${excelStyles.colMekhosAttendees} sticky top-0 bg-gray-50 z-10`}>Mekhos Attendees</th>
                      <th className={`${excelStyles.headerCell} ${excelStyles.colCustomerAttendees} sticky top-0 bg-gray-50 z-10`}>Customer Attendees</th>
                      <th className={`${excelStyles.headerCell} ${excelStyles.colDescription} sticky top-0 bg-gray-50 z-10`}>Description</th>
                      <th className={`${excelStyles.headerCell} ${excelStyles.colAssignee} sticky top-0 bg-gray-50 z-10`}>Assignee</th>
                      <th className={`${excelStyles.headerCell} ${excelStyles.colDueDate} sticky top-0 bg-gray-50 z-10`}>Due Date</th>
                      <th className={`${excelStyles.headerCell} ${excelStyles.colActions} sticky top-0 bg-gray-50 z-10`}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {!moms || moms.length === 0 ? (
                      <tr>
                        <td colSpan={10} className="px-6 py-12 text-center text-gray-500 text-lg">
                          {!moms ? 'Loading MOMs...' : 'No MOMs found. Create a new MOM to get started.'}
                        </td>
                      </tr>
                    ) : moms.map((mom) => {
                      const customer = customers?.find(c => c.id === mom.customerId);
                      const project = projects?.find(p => p.id === mom.projectId);

                      // Get attendees by company
                      const mekhosAttendees = mom.attendees?.filter(a => a.company === 'Mekhos Technology') || [];
                      const customerAttendees = mom.attendees?.filter(a => a.company !== 'Mekhos Technology') || [];

                      // Get first action item for main row, others will be in additional rows
                      const mainActionItem = mom.actionItems?.[0];
                      const additionalActionItems = mom.actionItems?.slice(1) || [];

                      return (
                        <React.Fragment key={mom.id}>
                          {/* Main MOM Row */}
                          <tr className={excelStyles.row}>
                            {/* Date */}
                            <td className={excelStyles.cell}>
                              <div className="p-2 text-base text-gray-700 font-medium">
                                {formatDate(mom.date)}
                              </div>
                            </td>

                            {/* Customer */}
                            <td className={excelStyles.cell}>
                              {editingMOMId === mom.id && editingField === 'customerId' ? (
                                <div className="flex items-center space-x-2 p-1">
                                  <select
                                    className={`${excelStyles.cellSelect} ${excelStyles.focusedCell}`}
                                    value={editValue}
                                    onChange={handleInputChange}
                                    onKeyDown={handleKeyDown}
                                    onClick={handleInputClick}
                                    autoFocus
                                  >
                                    {customers?.map((customer) => (
                                      <option key={customer.id} value={customer.id}>{customer.name}</option>
                                    ))}
                                  </select>
                                  <div className="flex space-x-1">
                                    <button
                                      onClick={handleDoneClick}
                                      className="w-6 h-6 bg-green-500 text-white rounded text-xs hover:bg-green-600"
                                      title="Save"
                                    >
                                      ✓
                                    </button>
                                    <button
                                      onClick={handleCancelClick}
                                      className="w-6 h-6 bg-gray-500 text-white rounded text-xs hover:bg-gray-600"
                                      title="Cancel"
                                    >
                                      ✗
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <div
                                  className="p-2 text-base cursor-pointer hover:bg-blue-50 transition-colors"
                                  onClick={() => handleCellClick(mom.id, 'customerId', mom.customerId)}
                                >
                                  {customer?.name || 'Unknown Customer'}
                                </div>
                              )}
                            </td>

                            {/* Project */}
                            <td className={excelStyles.cell}>
                              {editingMOMId === mom.id && editingField === 'projectId' ? (
                                <div className="flex items-center space-x-2 p-1">
                                  <select
                                    className={`${excelStyles.cellSelect} ${excelStyles.focusedCell}`}
                                    value={editValue}
                                    onChange={handleInputChange}
                                    onKeyDown={handleKeyDown}
                                    onClick={handleInputClick}
                                    autoFocus
                                  >
                                    <option value="">No Project</option>
                                    {projects?.filter(p => p.customerId === mom.customerId).map((project) => (
                                      <option key={project.id} value={project.id}>{project.name}</option>
                                    ))}
                                  </select>
                                  <div className="flex space-x-1">
                                    <button
                                      onClick={handleDoneClick}
                                      className="w-6 h-6 bg-green-500 text-white rounded text-xs hover:bg-green-600"
                                      title="Save"
                                    >
                                      ✓
                                    </button>
                                    <button
                                      onClick={handleCancelClick}
                                      className="w-6 h-6 bg-gray-500 text-white rounded text-xs hover:bg-gray-600"
                                      title="Cancel"
                                    >
                                      ✗
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <div
                                  className="p-2 text-base cursor-pointer hover:bg-blue-50 transition-colors"
                                  onClick={() => handleCellClick(mom.id, 'projectId', mom.projectId || '')}
                                >
                                  {project?.name || 'No Project'}
                                </div>
                              )}
                            </td>

                            {/* Agenda */}
                            <td className={excelStyles.cell}>
                              {editingMOMId === mom.id && editingField === 'agenda' ? (
                                <div className="flex items-center space-x-2 p-1">
                                  <textarea
                                    className={`${excelStyles.cellInput} ${excelStyles.focusedCell}`}
                                    rows={2}
                                    value={editValue}
                                    onChange={handleInputChange}
                                    onKeyDown={handleKeyDown}
                                    onClick={handleInputClick}
                                    placeholder="Meeting agenda..."
                                    autoFocus
                                  />
                                  <div className="flex space-x-1">
                                    <button
                                      onClick={handleDoneClick}
                                      className="w-6 h-6 bg-green-500 text-white rounded text-xs hover:bg-green-600"
                                      title="Save"
                                    >
                                      ✓
                                    </button>
                                    <button
                                      onClick={handleCancelClick}
                                      className="w-6 h-6 bg-gray-500 text-white rounded text-xs hover:bg-gray-600"
                                      title="Cancel"
                                    >
                                      ✗
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <div
                                  className="p-2 text-base cursor-pointer hover:bg-blue-50 transition-colors"
                                  onClick={() => handleCellClick(mom.id, 'agenda', mom.agenda || '')}
                                >
                                  {mom.agenda || 'Meeting agenda...'}
                                </div>
                              )}
                            </td>

                            {/* Mekhos Attendees */}
                            <td className={excelStyles.cell}>
                              {editingMOMId === mom.id && editingField === 'mekhosAttendees' ? (
                                <div className="flex items-center space-x-2 p-1">
                                  <input
                                    type="text"
                                    className={`${excelStyles.cellInput} ${excelStyles.focusedCell}`}
                                    value={editValue}
                                    onChange={handleInputChange}
                                    onKeyDown={handleKeyDown}
                                    onClick={handleInputClick}
                                    placeholder="Add Mekhos attendees..."
                                    autoFocus
                                  />
                                  <div className="flex space-x-1">
                                    <button
                                      onClick={handleDoneClick}
                                      className="w-6 h-6 bg-green-500 text-white rounded text-xs hover:bg-green-600"
                                      title="Save"
                                    >
                                      ✓
                                    </button>
                                    <button
                                      onClick={handleCancelClick}
                                      className="w-6 h-6 bg-gray-500 text-white rounded text-xs hover:bg-gray-600"
                                      title="Cancel"
                                    >
                                      ✗
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <div
                                  className="p-2 text-base cursor-pointer hover:bg-blue-50 transition-colors"
                                  onClick={() => handleCellClick(mom.id, 'mekhosAttendees', mekhosAttendees.map(a => a.name).join(', '))}
                                >
                                  <div className="flex flex-wrap gap-1">
                                    {mekhosAttendees.map((attendee, idx) => (
                                      <span key={idx} className="inline-flex items-center bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded">
                                        {attendee.name}
                                      </span>
                                    ))}
                                    {mekhosAttendees.length === 0 && (
                                      <span className="text-gray-400 text-sm">Add Mekhos attendees...</span>
                                    )}
                                  </div>
                                </div>
                              )}
                            </td>

                            {/* Customer Attendees */}
                            <td className={excelStyles.cell}>
                              {editingMOMId === mom.id && editingField === 'customerAttendees' ? (
                                <div className="flex items-center space-x-2 p-1">
                                  <input
                                    type="text"
                                    className={`${excelStyles.cellInput} ${excelStyles.focusedCell}`}
                                    value={editValue}
                                    onChange={handleInputChange}
                                    onKeyDown={handleKeyDown}
                                    onClick={handleInputClick}
                                    placeholder="Add customer attendees..."
                                    autoFocus
                                  />
                                  <div className="flex space-x-1">
                                    <button
                                      onClick={handleDoneClick}
                                      className="w-6 h-6 bg-green-500 text-white rounded text-xs hover:bg-green-600"
                                      title="Save"
                                    >
                                      ✓
                                    </button>
                                    <button
                                      onClick={handleCancelClick}
                                      className="w-6 h-6 bg-gray-500 text-white rounded text-xs hover:bg-gray-600"
                                      title="Cancel"
                                    >
                                      ✗
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <div
                                  className="p-2 text-base cursor-pointer hover:bg-blue-50 transition-colors"
                                  onClick={() => handleCellClick(mom.id, 'customerAttendees', customerAttendees.map(a => a.name).join(', '))}
                                >
                                  <div className="flex flex-wrap gap-1">
                                    {customerAttendees.map((attendee, idx) => (
                                      <span key={idx} className="inline-flex items-center bg-green-100 text-green-800 text-sm px-2 py-1 rounded">
                                        {attendee.name}
                                      </span>
                                    ))}
                                    {customerAttendees.length === 0 && (
                                      <span className="text-gray-400 text-sm">Add customer attendees...</span>
                                    )}
                                  </div>
                                </div>
                              )}
                            </td>

                            {/* Description (First Action Item) */}
                            <td className={excelStyles.cell}>
                              {editingMOMId === mom.id && editingField === 'actionItem_0_description' ? (
                                <div className="flex items-center space-x-2 p-1">
                                  <textarea
                                    className={`${excelStyles.cellInput} ${excelStyles.focusedCell}`}
                                    rows={2}
                                    value={editValue}
                                    onChange={handleInputChange}
                                    onKeyDown={handleKeyDown}
                                    onClick={handleInputClick}
                                    placeholder="Enter discussion point..."
                                    autoFocus
                                  />
                                  <div className="flex space-x-1">
                                    <button
                                      onClick={handleDoneClick}
                                      className="w-6 h-6 bg-green-500 text-white rounded text-xs hover:bg-green-600"
                                      title="Save"
                                    >
                                      ✓
                                    </button>
                                    <button
                                      onClick={handleCancelClick}
                                      className="w-6 h-6 bg-gray-500 text-white rounded text-xs hover:bg-gray-600"
                                      title="Cancel"
                                    >
                                      ✗
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <div
                                  className="p-2 text-base cursor-pointer hover:bg-blue-50 transition-colors"
                                  onClick={() => handleCellClick(mom.id, 'actionItem_0_description', mainActionItem?.description || '')}
                                >
                                  {mainActionItem?.description || 'Enter discussion point...'}
                                </div>
                              )}
                            </td>

                            {/* Assignee (First Action Item) */}
                            <td className={excelStyles.cell}>
                              <select
                                className={excelStyles.cellSelect}
                                value={mainActionItem?.assigneeId || ''}
                                onChange={(e) => {
                                  try {
                                    const updatedActionItems = [...(mom.actionItems || [])];
                                    if (updatedActionItems.length === 0) {
                                      updatedActionItems.push({
                                        id: uuidv4(),
                                        description: '',
                                        assigneeId: e.target.value,
                                        dueDate: '',
                                        status: 'PENDING'
                                      });
                                    } else {
                                      updatedActionItems[0] = { ...updatedActionItems[0], assigneeId: e.target.value };
                                    }

                                    const updatedMOM = { ...mom, actionItems: updatedActionItems };

                                    // Update local state immediately for better UX
                                    setMoms(prevMoms => prevMoms.map(m =>
                                      m.id === mom.id ? updatedMOM : m
                                    ));

                                    // Use debounced API call to reduce server load
                                    debouncedUpdateMOM(mom.id, updatedMOM);
                                  } catch (error) {
                                    console.error('Error updating assignee:', error);
                                  }
                                }}
                              >
                                <option value="">Select Team Lead</option>
                                {teamLeads?.map((teamLead) => (
                                  <option key={teamLead.id} value={teamLead.id}>{teamLead.name}</option>
                                ))}
                              </select>
                            </td>

                            {/* Due Date (First Action Item) */}
                            <td className={excelStyles.cell}>
                              {editingMOMId === mom.id && editingField === 'actionItem_0_dueDate' ? (
                                <div className="flex items-center space-x-2 p-1">
                                  <input
                                    type="date"
                                    className={`${excelStyles.cellDate} ${excelStyles.focusedCell}`}
                                    value={editValue}
                                    onChange={handleInputChange}
                                    onKeyDown={handleKeyDown}
                                    onClick={handleInputClick}
                                    autoFocus
                                  />
                                  <div className="flex space-x-1">
                                    <button
                                      onClick={handleDoneClick}
                                      className="w-6 h-6 bg-green-500 text-white rounded text-xs hover:bg-green-600"
                                      title="Save"
                                    >
                                      ✓
                                    </button>
                                    <button
                                      onClick={handleCancelClick}
                                      className="w-6 h-6 bg-gray-500 text-white rounded text-xs hover:bg-gray-600"
                                      title="Cancel"
                                    >
                                      ✗
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <div
                                  className="p-2 text-base cursor-pointer hover:bg-blue-50 transition-colors"
                                  onClick={() => handleCellClick(mom.id, 'actionItem_0_dueDate', mainActionItem?.dueDate ? formatDateForInput(mainActionItem.dueDate) : '')}
                                >
                                  {mainActionItem?.dueDate ? formatDate(mainActionItem.dueDate) : 'Select due date...'}
                                </div>
                              )}
                            </td>

                            {/* Actions */}
                            <td className={excelStyles.cell}>
                              <div className="flex justify-center space-x-2 p-2">
                                <button
                                  onClick={() => handleViewMOM(mom)}
                                  className="w-8 h-8 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors flex items-center justify-center"
                                  title="View MOM"
                                >
                                  <Eye size={14} />
                                </button>
                                <button
                                  onClick={() => handleEditMOM(mom)}
                                  className="w-8 h-8 bg-green-500 text-white rounded hover:bg-green-600 transition-colors flex items-center justify-center"
                                  title="Edit MOM"
                                >
                                  <Edit size={14} />
                                </button>
                                {currentUser?.role === 'DIRECTOR' && (
                                  <button
                                    onClick={() => handleDeleteMOM(mom.id)}
                                    className="w-8 h-8 bg-red-500 text-white rounded hover:bg-red-600 transition-colors flex items-center justify-center"
                                    title="Delete MOM"
                                  >
                                    <Trash2 size={14} />
                                  </button>
                                )}
                              </div>
                            </td>
                          </tr>

                          {/* Additional Action Item Rows */}
                          {additionalActionItems.map((item, index) => (
                            <tr key={`${mom.id}-${index + 1}`} className={`${excelStyles.row} bg-gray-25`}>
                              {/* Empty cells for Date, Customer, Project, Agenda, Attendees */}
                              <td className={`${excelStyles.cell} bg-gray-25`}></td>
                              <td className={`${excelStyles.cell} bg-gray-25`}></td>
                              <td className={`${excelStyles.cell} bg-gray-25`}></td>
                              <td className={`${excelStyles.cell} bg-gray-25`}></td>
                              <td className={`${excelStyles.cell} bg-gray-25`}></td>
                              <td className={`${excelStyles.cell} bg-gray-25`}></td>

                              {/* Description */}
                              <td className={excelStyles.cell}>
                                <textarea
                                  className={excelStyles.cellInput}
                                  rows={1}
                                  value={item.description || ''}
                                  onChange={(e) => {
                                    try {
                                      const updatedActionItems = [...(mom.actionItems || [])];
                                      updatedActionItems[index + 1] = { ...updatedActionItems[index + 1], description: e.target.value };

                                      const updatedMOM = { ...mom, actionItems: updatedActionItems };

                                      // Update local state immediately for better UX
                                      setMoms(prevMoms => prevMoms.map(m =>
                                        m.id === mom.id ? updatedMOM : m
                                      ));

                                      // Use debounced API call to reduce server load
                                      debouncedUpdateMOM(mom.id, updatedMOM);
                                    } catch (error) {
                                      console.error('Error updating description:', error);
                                    }
                                  }}
                                  placeholder="Enter discussion point..."
                                />
                              </td>

                              {/* Assignee */}
                              <td className={excelStyles.cell}>
                                <select
                                  className={excelStyles.cellSelect}
                                  value={item.assigneeId || ''}
                                  onChange={(e) => {
                                    try {
                                      const updatedActionItems = [...(mom.actionItems || [])];
                                      updatedActionItems[index + 1] = { ...updatedActionItems[index + 1], assigneeId: e.target.value };

                                      const updatedMOM = { ...mom, actionItems: updatedActionItems };

                                      // Update local state immediately for better UX
                                      setMoms(prevMoms => prevMoms.map(m =>
                                        m.id === mom.id ? updatedMOM : m
                                      ));

                                      // Use debounced API call to reduce server load
                                      debouncedUpdateMOM(mom.id, updatedMOM);
                                    } catch (error) {
                                      console.error('Error updating assignee:', error);
                                    }
                                  }}
                                >
                                  <option value="">Select Team Lead</option>
                                  {teamLeads?.map((teamLead) => (
                                    <option key={teamLead.id} value={teamLead.id}>{teamLead.name}</option>
                                  ))}
                                </select>
                              </td>

                              {/* Due Date */}
                              <td className={excelStyles.cell}>
                                <input
                                  type="date"
                                  className={excelStyles.cellDate}
                                  value={item.dueDate ? formatDateForInput(item.dueDate) : ''}
                                  onChange={(e) => {
                                    try {
                                      const updatedActionItems = [...(mom.actionItems || [])];
                                      updatedActionItems[index + 1] = { ...updatedActionItems[index + 1], dueDate: e.target.value ? new Date(e.target.value).toISOString() : '' };

                                      const updatedMOM = { ...mom, actionItems: updatedActionItems };

                                      // Update local state immediately for better UX
                                      setMoms(prevMoms => prevMoms.map(m =>
                                        m.id === mom.id ? updatedMOM : m
                                      ));

                                      // Use debounced API call to reduce server load
                                      debouncedUpdateMOM(mom.id, updatedMOM);
                                    } catch (error) {
                                      console.error('Error updating due date:', error);
                                    }
                                  }}
                                />
                              </td>

                              {/* Empty cell for actions column */}
                              <td className={`${excelStyles.cell} bg-gray-25`}></td>
                            </tr>
                          ))}
                        </React.Fragment>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      )}



      )}

      {/* Customer Dialog */}
      <CustomerDialog
        isOpen={isCustomerDialogOpen}
        onClose={() => setIsCustomerDialogOpen(false)}
        onSave={handleAddCustomer}
        initialData={{ name: '' }}
      />

      {/* Create MOM Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <h3 className="text-2xl font-bold text-gray-900 flex items-center">
                <Plus size={24} className="mr-3 text-blue-600" />
                Create Minutes of Meeting
              </h3>
              <button
                onClick={() => setShowForm(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Meeting Header Information */}
                <div className="mom-header grid grid-cols-1 md:grid-cols-12 gap-4">
                  <div className="md:col-span-3">
                    <label htmlFor="date" className="form-label">Meeting Date</label>
                    <input
                      id="date"
                      type="date"
                      className={`form-input w-full ${errors.date ? 'border-error' : ''}`}
                      value={date}
                      onChange={(e) => setDate(e.target.value)}
                      required
                    />
                    {errors.date && (
                      <p className="mt-1 text-xs text-error">{errors.date}</p>
                    )}
                  </div>

                  <div className="md:col-span-5">
                    <label htmlFor="customer" className="form-label">Customer</label>
                    <div className="flex space-x-2">
                      <div className="flex-1">
                        <select
                          id="customer"
                          className={`form-select w-full ${errors.customerId ? 'border-error' : ''}`}
                          value={customerId}
                          onChange={(e) => {
                            setCustomerId(e.target.value);
                            setProjectId(''); // Reset project when customer changes
                          }}
                          required
                        >
                          <option value="">Select Customer</option>
                          {customers ? customers.map((customer) => (
                            <option key={customer.id} value={customer.id}>{customer.name}</option>
                          )) : null}
                        </select>
                      </div>
                      <button
                        type="button"
                        className="btn btn-outline flex items-center py-2 whitespace-nowrap"
                        onClick={() => navigate('/customers?addCustomer=true')}
                      >
                        <PlusCircle size={16} className="mr-1" />
                        New
                      </button>
                    </div>
                    {errors.customerId && (
                      <p className="mt-1 text-xs text-error">{errors.customerId}</p>
                    )}
                  </div>

                  <div className="md:col-span-4">
                    <label htmlFor="project" className="form-label">Project</label>
                    <select
                      id="project"
                      className={`form-select w-full ${!customerId ? 'bg-gray-100' : ''}`}
                      value={projectId}
                      onChange={(e) => setProjectId(e.target.value)}
                      disabled={!customerId}
                    >
                      <option value="">Select Project</option>
                      {filteredProjects.map((project) => (
                        <option key={project.id} value={project.id}>{project.name}</option>
                      ))}
                    </select>
                    {customerId && filteredProjects.length === 0 && (
                      <p className="mt-1 text-xs text-warning">No projects available for this customer</p>
                    )}
                    {!customerId && (
                      <p className="mt-1 text-xs text-gray-500">Please select a customer first</p>
                    )}
                  </div>
                </div>

                <div className="col-span-12">
                  <label htmlFor="agenda" className="form-label">Meeting Agenda</label>
                  <textarea
                    id="agenda"
                    className={`form-input w-full ${errors.agenda ? 'border-error' : ''}`}
                    rows={3}
                    placeholder="Enter the meeting agenda..."
                    value={agenda}
                    onChange={(e) => setAgenda(e.target.value)}
                    required
                  />
                  {errors.agenda && (
                    <p className="mt-1 text-xs text-error">{errors.agenda}</p>
                  )}
                </div>

                {/* Excel-like Table for Meeting Details */}
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-gray-800">Meeting Details</h3>
                    <button
                      type="button"
                      className={excelStyles.addButton}
                      onClick={addRow}
                    >
                      <Plus size={16} className="mr-1" />
                      Add Row
                    </button>
                  </div>

                  <div className={excelStyles.container}>
                    <div className={excelStyles.tableContainer}>
                      <table className={excelStyles.table}>
                        <thead>
                          <tr className={excelStyles.headerRow}>
                            <th className={`${excelStyles.headerCell} w-12`}>#</th>
                            <th className={`${excelStyles.headerCell} ${excelStyles.colMekhosAttendees}`}>Mekhos Attendees</th>
                            <th className={`${excelStyles.headerCell} ${excelStyles.colCustomerAttendees}`}>Customer Attendees</th>
                            <th className={`${excelStyles.headerCell} ${excelStyles.colDescription}`}>Description</th>
                            <th className={`${excelStyles.headerCell} ${excelStyles.colAssignee}`}>Assignee</th>
                            <th className={`${excelStyles.headerCell} ${excelStyles.colDueDate}`}>Due Date</th>
                          </tr>
                        </thead>
                        <tbody>
                          {rows.map((row, index) => (
                            <tr key={row.id} className={excelStyles.row}>
                              <td className={excelStyles.rowNumber}>{index + 1}</td>

                              {/* Mekhos Attendees */}
                              <td className={excelStyles.cell}>
                                <div className="p-2 min-h-[40px]">
                                  <div className="flex flex-wrap gap-1 mb-2">
                                    {row.mekhosAttendees && row.mekhosAttendees.map((attendee, idx) => (
                                      <span key={idx} className="inline-flex items-center bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                                        {attendee}
                                        <button
                                          type="button"
                                          className="ml-1 text-blue-600 hover:text-red-500"
                                          onClick={() => removeAttendee(row.id, 'mekhos', idx)}
                                        >
                                          ×
                                        </button>
                                      </span>
                                    ))}
                                  </div>
                                  <input
                                    type="text"
                                    className="w-full text-sm bg-transparent rounded px-2 py-1 focus:bg-blue-50 focus:ring-1 focus:ring-blue-300"
                                    placeholder="Add Mekhos attendee..."
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') {
                                        addMekhosAttendee(row.id, e.currentTarget.value);
                                        e.currentTarget.value = '';
                                        e.preventDefault();
                                      }
                                    }}
                                  />
                                </div>
                              </td>

                              {/* Customer Attendees */}
                              <td className={excelStyles.cell}>
                                <div className="p-2 min-h-[40px]">
                                  <div className="flex flex-wrap gap-1 mb-2">
                                    {row.customerAttendees && row.customerAttendees.map((attendee, idx) => (
                                      <span key={idx} className="inline-flex items-center bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                                        {attendee}
                                        <button
                                          type="button"
                                          className="ml-1 text-green-600 hover:text-red-500"
                                          onClick={() => removeAttendee(row.id, 'customer', idx)}
                                        >
                                          ×
                                        </button>
                                      </span>
                                    ))}
                                  </div>
                                  <input
                                    type="text"
                                    className="w-full text-sm bg-transparent rounded px-2 py-1 focus:bg-blue-50 focus:ring-1 focus:ring-blue-300"
                                    placeholder="Add Customer attendee..."
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') {
                                        addCustomerAttendee(row.id, e.currentTarget.value);
                                        e.currentTarget.value = '';
                                        e.preventDefault();
                                      }
                                    }}
                                  />
                                </div>
                              </td>

                              {/* Description */}
                              <td className={excelStyles.cell}>
                                <textarea
                                  className={excelStyles.cellInput}
                                  placeholder="Enter discussion point..."
                                  value={(row.descriptions && row.descriptions[0]) || ''}
                                  onChange={(e) => updateRow(row.id, 'descriptions', [e.target.value])}
                                  rows={2}
                                />
                              </td>

                              {/* Assignee */}
                              <td className={excelStyles.cell}>
                                <select
                                  className={excelStyles.cellSelect}
                                  value={row.assigneeId}
                                  onChange={(e) => updateRow(row.id, 'assigneeId', e.target.value)}
                                >
                                  <option value="">Select Team Lead</option>
                                  {teamLeads?.map((teamLead) => (
                                    <option key={teamLead.id} value={teamLead.id}>
                                      {teamLead.name}
                                    </option>
                                  ))}
                                </select>
                              </td>

                              {/* Due Date */}
                              <td className={excelStyles.cell}>
                                <input
                                  type="date"
                                  className={excelStyles.cellDate}
                                  value={row.dueDate}
                                  onChange={(e) => updateRow(row.id, 'dueDate', e.target.value)}
                                />
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {errors.rows && (
                    <p className="mt-2 text-xs text-red-500">{errors.rows}</p>
                  )}
                </div>
              </form>
            </div>

            <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
              <button
                onClick={() => setShowForm(false)}
                className="btn btn-outline"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                className="btn btn-primary flex items-center"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <CheckCircle2 size={18} className="mr-2" />
                    Create MOM
                  </span>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
      {/* View MOM Modal */}
      {showViewModal && selectedMOM && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <h3 className="text-2xl font-bold text-gray-900 flex items-center">
                <Eye size={24} className="mr-3 text-blue-600" />
                View Minutes of Meeting
              </h3>
              <button
                onClick={closeModals}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Meeting Date</label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    {formatDate(selectedMOM.date)}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Customer</label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    {customers?.find(c => c.id === selectedMOM.customerId)?.name || 'Unknown Customer'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Project</label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    {projects?.find(p => p.id === selectedMOM.projectId)?.name || 'No Project'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Meeting Agenda</label>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    {selectedMOM.agenda || 'No agenda specified'}
                  </div>
                </div>
              </div>

              {/* Attendees */}
              <div className="mb-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Attendees</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Mekhos Technology</label>
                    <div className="flex flex-wrap gap-2">
                      {selectedMOM.attendees?.filter(a => a.company === 'Mekhos Technology').map((attendee, idx) => (
                        <span key={idx} className="inline-flex items-center bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                          {attendee.name}
                        </span>
                      ))}
                      {selectedMOM.attendees?.filter(a => a.company === 'Mekhos Technology').length === 0 && (
                        <span className="text-gray-500 text-sm">No Mekhos attendees</span>
                      )}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Customer</label>
                    <div className="flex flex-wrap gap-2">
                      {selectedMOM.attendees?.filter(a => a.company !== 'Mekhos Technology').map((attendee, idx) => (
                        <span key={idx} className="inline-flex items-center bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full">
                          {attendee.name}
                        </span>
                      ))}
                      {selectedMOM.attendees?.filter(a => a.company !== 'Mekhos Technology').length === 0 && (
                        <span className="text-gray-500 text-sm">No customer attendees</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Items */}
              <div className="mb-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Discussion Points & Action Items</h4>
                {selectedMOM.actionItems && selectedMOM.actionItems.length > 0 ? (
                  <div className="space-y-3">
                    {selectedMOM.actionItems.map((item, idx) => (
                      <div key={idx} className="p-4 bg-gray-50 rounded-lg border">
                        <div className="font-medium text-gray-900 mb-2">{item.description}</div>
                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <div className="flex items-center space-x-4">
                            {item.assigneeId && (
                              <span>
                                <strong>Assigned to:</strong> {teamLeads?.find(tl => tl.id === item.assigneeId)?.name || 'Unknown'}
                              </span>
                            )}
                            {item.dueDate && (
                              <span>
                                <strong>Due:</strong> {formatDate(item.dueDate)}
                              </span>
                            )}
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            item.status === 'COMPLETED' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {item.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No discussion points or action items recorded.
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
              <button
                onClick={() => {
                  setShowViewModal(false);
                  setShowEditModal(true);
                }}
                className="btn btn-primary flex items-center"
              >
                <Edit size={16} className="mr-2" />
                Edit MOM
              </button>
              <button
                onClick={closeModals}
                className="btn btn-outline"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit MOM Modal */}
      {showEditModal && selectedMOM && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
              <h3 className="text-2xl font-bold text-gray-900 flex items-center">
                <Edit size={24} className="mr-3 text-green-600" />
                Edit Minutes of Meeting
              </h3>
              <button
                onClick={closeModals}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              <MOMCard
                mom={selectedMOM}
                customers={customers || []}
                projects={projects || []}
                teamLeads={teamLeads || []}
                engineers={engineers || []}
                onUpdate={async (updatedMOM) => {
                  try {
                    await momsAPI.updateMOM(selectedMOM.id, updatedMOM);
                    await dataService.loadMOMs();
                    showSuccess('MOM Updated', 'Minutes of Meeting updated successfully!');
                    closeModals();
                  } catch (error) {
                    console.error('Error updating MOM:', error);
                    showError('Update Failed', 'Failed to update MOM. Please try again.');
                  }
                }}
                onDelete={() => {
                  handleDeleteMOM(selectedMOM.id);
                  closeModals();
                }}
              />
            </div>

            <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
              <button
                onClick={closeModals}
                className="btn btn-outline"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
      {/* Excel View Modal - All MOMs */}
      {showViewModal && !selectedMOM && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <h3 className="text-2xl font-bold text-gray-900 flex items-center">
                <Eye size={24} className="mr-3 text-blue-600" />
                Excel View - All MOMs
              </h3>
              <button
                onClick={closeModals}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="p-6 overflow-auto max-h-[calc(90vh-120px)]">
              <div className={excelStyles.container}>
                <div className={excelStyles.tableContainer}>
                  <table className={excelStyles.table}>
                    <thead>
                      <tr className={excelStyles.headerRow}>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colDate} sticky top-0 bg-gray-50 z-10`}>Date</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colCustomer} sticky top-0 bg-gray-50 z-10`}>Customer</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colProject} sticky top-0 bg-gray-50 z-10`}>Project</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colAgenda} sticky top-0 bg-gray-50 z-10`}>Agenda</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colMekhosAttendees} sticky top-0 bg-gray-50 z-10`}>Mekhos Attendees</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colDate}`}>Date</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colCustomer}`}>Customer</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colProject}`}>Project</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colAgenda}`}>Agenda</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colMekhosAttendees}`}>Mekhos Attendees</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colCustomerAttendees}`}>Customer Attendees</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colDescription}`}>Description</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colAssignee}`}>Assignee</th>
                        <th className={`${excelStyles.headerCell} ${excelStyles.colDueDate}`}>Due Date</th>
                      </tr>
                    </thead>
                    <tbody>
                      {moms && moms.map((mom) => {
                        const customer = customers?.find(c => c.id === mom.customerId);
                        const project = projects?.find(p => p.id === mom.projectId);
                        const mekhosAttendees = mom.attendees?.filter(a => a.company === 'Mekhos Technology') || [];
                        const customerAttendees = mom.attendees?.filter(a => a.company !== 'Mekhos Technology') || [];

                        return mom.actionItems && mom.actionItems.length > 0 ? (
                          mom.actionItems.map((item, idx) => (
                            <tr key={`${mom.id}-${idx}`} className={excelStyles.row}>
                              {idx === 0 && (
                                <>
                                  <td className={excelStyles.cell} rowSpan={mom.actionItems?.length || 1}>
                                    <div className="p-2 text-base text-gray-700 font-medium">
                                      {formatDate(mom.date)}
                                    </div>
                                  </td>
                                  <td className={excelStyles.cell} rowSpan={mom.actionItems?.length || 1}>
                                    <div className="p-2 text-base">
                                      {customer?.name || 'Unknown Customer'}
                                    </div>
                                  </td>
                                  <td className={excelStyles.cell} rowSpan={mom.actionItems?.length || 1}>
                                    <div className="p-2 text-base">
                                      {project?.name || 'No Project'}
                                    </div>
                                  </td>
                                  <td className={excelStyles.cell} rowSpan={mom.actionItems?.length || 1}>
                                    <div className="p-2 text-base">
                                      {mom.agenda || 'No agenda'}
                                    </div>
                                  </td>
                                  <td className={excelStyles.cell} rowSpan={mom.actionItems?.length || 1}>
                                    <div className="p-2 text-base">
                                      <div className="flex flex-wrap gap-1">
                                        {mekhosAttendees.map((attendee, aIdx) => (
                                          <span key={aIdx} className="inline-flex items-center bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded">
                                            {attendee.name}
                                          </span>
                                        ))}
                                        {mekhosAttendees.length === 0 && (
                                          <span className="text-gray-400 text-sm">No attendees</span>
                                        )}
                                      </div>
                                    </div>
                                  </td>
                                  <td className={excelStyles.cell} rowSpan={mom.actionItems?.length || 1}>
                                    <div className="p-2 text-base">
                                      <div className="flex flex-wrap gap-1">
                                        {customerAttendees.map((attendee, aIdx) => (
                                          <span key={aIdx} className="inline-flex items-center bg-green-100 text-green-800 text-sm px-2 py-1 rounded">
                                            {attendee.name}
                                          </span>
                                        ))}
                                        {customerAttendees.length === 0 && (
                                          <span className="text-gray-400 text-sm">No attendees</span>
                                        )}
                                      </div>
                                    </div>
                                  </td>
                                </>
                              )}
                              <td className={excelStyles.cell}>
                                <div className="p-2 text-base">
                                  {item.description || 'No description'}
                                </div>
                              </td>
                              <td className={excelStyles.cell}>
                                <div className="p-2 text-base">
                                  {teamLeads?.find(tl => tl.id === item.assigneeId)?.name || 'Unassigned'}
                                </div>
                              </td>
                              <td className={excelStyles.cell}>
                                <div className="p-2 text-base">
                                  {item.dueDate ? formatDate(item.dueDate) : 'No due date'}
                                </div>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr key={mom.id} className={excelStyles.row}>
                            <td className={excelStyles.cell}>
                              <div className="p-2 text-base text-gray-700 font-medium">
                                {formatDate(mom.date)}
                              </div>
                            </td>
                            <td className={excelStyles.cell}>
                              <div className="p-2 text-base">
                                {customer?.name || 'Unknown Customer'}
                              </div>
                            </td>
                            <td className={excelStyles.cell}>
                              <div className="p-2 text-base">
                                {project?.name || 'No Project'}
                              </div>
                            </td>
                            <td className={excelStyles.cell}>
                              <div className="p-2 text-base">
                                {mom.agenda || 'No agenda'}
                              </div>
                            </td>
                            <td className={excelStyles.cell}>
                              <div className="p-2 text-base">
                                <div className="flex flex-wrap gap-1">
                                  {mekhosAttendees.map((attendee, aIdx) => (
                                    <span key={aIdx} className="inline-flex items-center bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded">
                                      {attendee.name}
                                    </span>
                                  ))}
                                  {mekhosAttendees.length === 0 && (
                                    <span className="text-gray-400 text-sm">No attendees</span>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className={excelStyles.cell}>
                              <div className="p-2 text-base">
                                <div className="flex flex-wrap gap-1">
                                  {customerAttendees.map((attendee, aIdx) => (
                                    <span key={aIdx} className="inline-flex items-center bg-green-100 text-green-800 text-sm px-2 py-1 rounded">
                                      {attendee.name}
                                    </span>
                                  ))}
                                  {customerAttendees.length === 0 && (
                                    <span className="text-gray-400 text-sm">No attendees</span>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className={excelStyles.cell}>
                              <div className="p-2 text-base text-gray-400">
                                No discussion points
                              </div>
                            </td>
                            <td className={excelStyles.cell}>
                              <div className="p-2 text-base text-gray-400">
                                Unassigned
                              </div>
                            </td>
                            <td className={excelStyles.cell}>
                              <div className="p-2 text-base text-gray-400">
                                No due date
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
              <button
                onClick={closeModals}
                className="btn btn-outline"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}






    </div>
  );
};

export default MOMSimpleTable;
