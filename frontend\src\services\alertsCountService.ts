import { getDefaultStore } from 'jotai';
import { unreadAlertsCountStandaloneAtom } from '../store';
import { alertsAPI } from './api';

/**
 * Lightweight service for managing unread alerts count
 * This service fetches only the count, not the full alerts data
 */
class AlertsCountService {
  private store = getDefaultStore();
  private refreshInterval: NodeJS.Timeout | null = null;
  private isRefreshing = false;

  /**
   * Fetch unread alerts count from API and update store
   */
  async fetchUnreadCount(): Promise<number> {
    if (this.isRefreshing) {
      console.log('🔄 Unread count fetch already in progress, skipping...');
      return this.store.get(unreadAlertsCountStandaloneAtom);
    }

    try {
      this.isRefreshing = true;
      console.log('🔄 Fetching unread alerts count...');
      
      const response = await alertsAPI.getUnreadAlertsCount();
      const count = response.data.count;
      
      // Update the store
      this.store.set(unreadAlertsCountStandaloneAtom, count);
      
      console.log('✅ Unread alerts count updated:', count);
      return count;
    } catch (error) {
      console.error('❌ Error fetching unread alerts count:', error);
      return this.store.get(unreadAlertsCountStandaloneAtom);
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * Start periodic refresh of unread count
   * @param intervalMs - Refresh interval in milliseconds (default: 60 seconds)
   */
  startPeriodicRefresh(intervalMs: number = 60000): void {
    if (this.refreshInterval) {
      console.log('⚠️ Periodic refresh already running, stopping previous one...');
      this.stopPeriodicRefresh();
    }

    console.log(`🚀 Starting periodic unread alerts count refresh (every ${intervalMs / 1000}s)`);
    
    // Initial fetch
    this.fetchUnreadCount();
    
    // Set up periodic refresh
    this.refreshInterval = setInterval(() => {
      console.log('⏰ Periodic unread alerts count refresh...');
      this.fetchUnreadCount();
    }, intervalMs);
  }

  /**
   * Stop periodic refresh
   */
  stopPeriodicRefresh(): void {
    if (this.refreshInterval) {
      console.log('🛑 Stopping periodic unread alerts count refresh');
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  /**
   * Get current unread count from store (synchronous)
   */
  getCurrentCount(): number {
    return this.store.get(unreadAlertsCountStandaloneAtom);
  }

  /**
   * Manually update the count (useful after marking alerts as read)
   */
  updateCount(count: number): void {
    console.log('📊 Manually updating unread alerts count to:', count);
    this.store.set(unreadAlertsCountStandaloneAtom, count);
  }

  /**
   * Decrement count by specified amount (useful after marking alerts as read)
   */
  decrementCount(amount: number = 1): void {
    const currentCount = this.getCurrentCount();
    const newCount = Math.max(0, currentCount - amount);
    console.log(`📉 Decrementing unread alerts count: ${currentCount} -> ${newCount}`);
    this.updateCount(newCount);
  }

  /**
   * Reset count to 0
   */
  resetCount(): void {
    console.log('🔄 Resetting unread alerts count to 0');
    this.updateCount(0);
  }
}

// Export singleton instance
export const alertsCountService = new AlertsCountService();
