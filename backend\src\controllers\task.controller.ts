import { Request, Response } from 'express';
import { prisma } from '../index';
import { v4 as uuidv4 } from 'uuid';
import { AlertService } from '../services/alertService';
import { validateDates, validateTaskName } from '../utils/auth.utils';
import { toCamelCase } from '../utils/textUtils';

// @desc    Get all tasks
// @route   GET /api/tasks
// @access  Private
export const getTasks = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      projectId,
      assigneeId,
      status,
      priority,
      search,
      startDate,
      endDate,
      assigneeName
    } = req.query;

    // Filter conditions
    const where: any = {};

    // Filter by project if provided
    if (projectId) {
      where.projectId = projectId as string;
    }

    // Filter by assignee if provided
    if (assigneeId) {
      where.assigneeId = assigneeId as string;
    }

    // Filter by status if provided
    if (status) {
      where.status = status as string;
    }

    // Filter by priority if provided
    if (priority) {
      where.priority = priority as string;
    }

    // Add date range filtering
    if (startDate || endDate) {
      where.AND = where.AND || [];
      if (startDate) {
        where.AND.push({
          startDate: {
            gte: new Date(startDate as string),
          },
        });
      }
      if (endDate) {
        where.AND.push({
          endDate: {
            lte: new Date(endDate as string),
          },
        });
      }
    }

    // Enhanced search functionality
    if (search) {
      const searchTerm = search as string;
      const searchConditions = [
        {
          name: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        },
        {
          description: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        },
        {
          displayId: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        },
      ];

      // Combine search with existing OR conditions if they exist
      if (where.OR) {
        where.AND = where.AND || [];
        where.AND.push({
          OR: [
            ...where.OR,
            ...searchConditions
          ]
        });
        delete where.OR;
      } else {
        where.OR = searchConditions;
      }
    }

    // Search by assignee name specifically
    if (assigneeName) {
      where.user = {
        name: {
          contains: assigneeName as string,
          mode: 'insensitive',
        },
      };
    }

    // Filter by user role
    if (req.user.role === 'ENGINEER') {
      // Engineers can see tasks assigned to them OR tasks that have subtasks assigned to them
      where.OR = [
        { assigneeId: req.user.id },
        {
          subtask: {
            some: {
              assigneeId: req.user.id
            }
          }
        }
      ];
    } else if (req.user.role === 'TEAM_LEAD') {
      // Team leads can see tasks assigned to them OR tasks assigned to engineers in their department
      const departmentEngineers = await prisma.user.findMany({
        where: {
          department: req.user.department,
          role: 'ENGINEER'
        },
        select: { id: true }
      });
      const engineerIds = departmentEngineers.map(engineer => engineer.id);

      where.OR = [
        { assigneeId: req.user.id }, // Tasks assigned to the team lead
        { assigneeId: { in: engineerIds } } // Tasks assigned to engineers in their department
      ];
    } else if (req.user.role === 'PROJECT_MANAGER') {
      // Project managers can see tasks in projects they manage
      const managedProjects = await prisma.project.findMany({
        where: { projectManagerId: req.user.id },
        select: { id: true }
      });
      const projectIds = managedProjects.map((p: any) => p.id);
      if (projectIds.length > 0) {
        where.projectId = { in: projectIds };
      } else {
        // If no projects managed, return empty result
        where.id = 'non-existent-id';
      }
    }

    const tasks = await prisma.task.findMany({
      where,
      include: {
        subtask: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
          orderBy: {
            sequence: 'asc',
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        project: {
          select: {
            id: true,
            name: true,
            code: true,
            customer: {
              select: {
                id: true,
                name: true,
                code: true,
              },
            },
          },
        },
      },
      orderBy: [
        {
          projectId: 'asc',
        },
        {
          sequence: 'asc',
        },
      ],
    });

    res.status(200).json({
      success: true,
      count: tasks.length,
      data: tasks,
    });
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single task
// @route   GET /api/tasks/:id
// @access  Private
export const getTask = async (req: Request, res: Response): Promise<void> => {
  try {
    const task = await prisma.task.findUnique({
      where: { id: req.params.id },
      include: {
        subtask: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        project: {
          select: {
            name: true,
            code: true,
            customer: true,
            department: true,
          },
        },
      },
    });

    if (!task) {
      res.status(404).json({
        success: false,
        message: 'Task not found',
      });
      return;
    }

    // Check if user has access to this task
    if (req.user.role === 'ENGINEER' && task.assigneeId !== req.user.id) {
      res.status(403).json({
        success: false,
        message: 'Not authorized to access this task',
      });
      return;
    }

    if (req.user.role === 'TEAM_LEAD') {
      // Team leads can access tasks assigned to them or tasks assigned to engineers in their department
      const isAssignedToTeamLead = task.assigneeId === req.user.id;

      if (!isAssignedToTeamLead) {
        // Check if task is assigned to an engineer in the same department
        const assignee = await prisma.user.findUnique({
          where: { id: task.assigneeId },
          select: { role: true, department: true }
        });

        const isAssignedToEngineerInDepartment = assignee &&
          assignee.role === 'ENGINEER' &&
          assignee.department === req.user.department;

        if (!isAssignedToEngineerInDepartment) {
          res.status(403).json({
            success: false,
            message: 'Not authorized to access this task',
          });
          return;
        }
      }
    }

    if (
      req.user.role === 'MANAGER' &&
      task.department !== req.user.department
    ) {
      res.status(403).json({
        success: false,
        message: 'Not authorized to access this task',
      });
      return;
    }

    // Add displayId to task and its subtasks
    const taskWithDisplayId = {
      ...task,
      displayId: `T${task.sequence}`,
      subtask: task.subtask.map(subtask => ({
        ...subtask,
        displayId: `S${task.sequence}${subtask.sequence}`
      }))
    };

    res.status(200).json({
      success: true,
      data: taskWithDisplayId,
    });
  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create task
// @route   POST /api/tasks
// @access  Private
export const createTask = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      projectId,
      sectionId,
      name,
      description,
      assigneeId,
      assigneeType,
      department,
      startDate,
      endDate,
      status,
      priority,
      subtasks,
    } = req.body;

    // Validate task name
    const nameValidation = validateTaskName(name);
    if (!nameValidation.isValid) {
      res.status(400).json({
        success: false,
        message: nameValidation.message
      });
      return;
    }

    // Validate dates
    const dateValidation = validateDates(startDate, endDate);
    if (!dateValidation.isValid) {
      res.status(400).json({
        success: false,
        message: dateValidation.message
      });
      return;
    }

    // Validate required fields
    if (!projectId || !sectionId || !name || !assigneeId) {
      res.status(400).json({
        success: false,
        message: 'Missing required fields: projectId, sectionId, name, and assigneeId are required',
      });
      return;
    }

    // Validate section exists
    const sectionExists = await prisma.section.findUnique({
      where: { id: sectionId }
    });

    if (!sectionExists) {
      res.status(400).json({
        success: false,
        message: 'Invalid section ID. Section does not exist.',
      });
      return;
    }

    // Validate section belongs to the project
    if (sectionExists.projectId !== projectId) {
      res.status(400).json({
        success: false,
        message: 'Section does not belong to the specified project.',
      });
      return;
    }

    // Check if project exists
    const projectExists = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!projectExists) {
      res.status(404).json({
        success: false,
        message: 'Project not found',
      });
      return;
    }

    // Validate task assignee - must be a team lead
    if (assigneeId) {
      const assignee = await prisma.user.findUnique({
        where: { id: assigneeId },
      });

      if (!assignee) {
        res.status(400).json({
          success: false,
          message: 'Assignee not found',
        });
        return;
      }

      if (assignee.role !== 'TEAM_LEAD') {
        res.status(400).json({
          success: false,
          message: 'Task assignee must be a team lead',
        });
        return;
      }
    }

    // Check if user has access to create task for this project
    if (
      req.user.role === 'PROJECT_MANAGER' &&
      projectExists.projectManagerId !== req.user.id
    ) {
      res.status(403).json({
        success: false,
        message: 'Not authorized to create task for this project',
      });
      return;
    }

    // Validate subtask assignees - must be engineers
    if (subtasks && subtasks.length > 0) {
      for (const subtask of subtasks) {
        if (subtask.assigneeId) {
          const subtaskAssignee = await prisma.user.findUnique({
            where: { id: subtask.assigneeId },
          });

          if (!subtaskAssignee) {
            res.status(400).json({
              success: false,
              message: `Subtask assignee not found: ${subtask.assigneeId}`,
            });
            return;
          }

          if (subtaskAssignee.role !== 'ENGINEER') {
            res.status(400).json({
              success: false,
              message: 'Subtask assignees must be engineers',
            });
            return;
          }
        }
      }
    }

    // Get the next sequence number for this project's tasks
    const lastTask = await prisma.task.findFirst({
      where: { projectId },
      orderBy: { sequence: 'desc' }
    });

    const nextSequence = lastTask ? lastTask.sequence + 1 : 1;

    // Create task first
    const taskId = uuidv4();
    const task = await prisma.task.create({
      data: {
        id: taskId,
        projectId,
        sectionId,
        sequence: nextSequence,
        displayId: `T${nextSequence}`,
        name: toCamelCase(name),
        description,
        assigneeId,
        assigneeType: assigneeType || 'ENGINEER',
        department: department || projectExists.department || 'IT',
        startDate: startDate ? new Date(startDate) : new Date(),
        ...(endDate && { endDate: new Date(endDate) }), // Only set endDate if provided
        status: status || 'NOT_STARTED',
        priority: priority || 'Medium',
        createdBy: req.user.id,
      },
    });

    // Create subtasks with global unique numbering
    if (subtasks && subtasks.length > 0) {
      // Get current global subtask count for the project
      const globalSubtaskCount = await prisma.subtask.count({
        where: {
          task: {
            projectId: projectId
          }
        }
      });

      for (let i = 0; i < subtasks.length; i++) {
        const subtask = subtasks[i];
        await prisma.subtask.create({
          data: {
            id: uuidv4(),
            taskId: task.id,
            sequence: i + 1,
            displayId: `T${globalSubtaskCount + i + 1}`,
            name: toCamelCase(subtask.name),
            description: subtask.description,
            assigneeId: subtask.assigneeId,
            assigneeType: subtask.assigneeType || 'ENGINEER',
            startDate: subtask.startDate ? new Date(subtask.startDate) : new Date(),
            ...(subtask.endDate && { endDate: new Date(subtask.endDate) }), // Only set endDate if provided
            status: subtask.status || 'NOT_STARTED',
            priority: subtask.priority || 'Medium',
            createdBy: req.user.id,
          },
        });
      }
    }

    // Get the complete task with subtasks
    const taskWithSubtasks = await prisma.task.findUnique({
      where: { id: task.id },
      include: {
        subtask: true,
      },
    });

    // Create single consolidated alert for task creation/assignment
    try {
      if (assigneeId) {
        // If task is assigned, send assignment alert (which implies creation)
        await AlertService.createTaskAlert(task.id, 'ASSIGNED', req.user.id);
      } else {
        // Only create a "CREATED" alert if no assignee is specified
        await AlertService.createTaskAlert(task.id, 'CREATED', req.user.id);
      }

      // Note: Subtask alerts are disabled to reduce notification noise
      // Subtasks will generate their own alerts when individually created/updated
    } catch (alertError) {
      console.error('Error creating task alert:', alertError);
      // Don't fail the task creation if alert creation fails
    }

    res.status(201).json({
      success: true,
      data: taskWithSubtasks,
    });
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update task
// @route   PUT /api/tasks/:id
// @access  Private
export const updateTask = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      name,
      description,
      assigneeId,
      assigneeType,
      department,
      startDate,
      endDate,
      status,
      priority,
    } = req.body;

    // Validate task name if provided
    if (name) {
      const nameValidation = validateTaskName(name);
      if (!nameValidation.isValid) {
        res.status(400).json({
          success: false,
          message: nameValidation.message
        });
        return;
      }
    }

    // Validate dates if provided
    if (startDate || endDate) {
      const dateValidation = validateDates(startDate, endDate);
      if (!dateValidation.isValid) {
        res.status(400).json({
          success: false,
          message: dateValidation.message
        });
        return;
      }
    }

    // Check if task exists
    const taskExists = await prisma.task.findUnique({
      where: { id: req.params.id },
      include: {
        project: true,
      },
    });

    if (!taskExists) {
      res.status(404).json({
        success: false,
        message: 'Task not found',
      });
      return;
    }

    // Check if user has access to update this task
    if (
      req.user.role === 'ENGINEER' &&
      taskExists.assigneeId !== req.user.id
    ) {
      res.status(403).json({
        success: false,
        message: 'Not authorized to update this task',
      });
      return;
    }

    if (
      req.user.role === 'PROJECT_MANAGER' &&
      taskExists.project.projectManagerId !== req.user.id
    ) {
      res.status(403).json({
        success: false,
        message: 'Not authorized to update this task',
      });
      return;
    }

    // Team leads can only update status and endDate fields
    if (req.user.role === 'TEAM_LEAD') {
      const allowedFields = ['status', 'endDate'];
      const requestedFields = Object.keys(req.body);
      const unauthorizedFields = requestedFields.filter(field => !allowedFields.includes(field));

      if (unauthorizedFields.length > 0) {
        res.status(403).json({
          success: false,
          message: `Team leads can only update status and end date. Unauthorized fields: ${unauthorizedFields.join(', ')}`,
        });
        return;
      }
    }

    // Validate task assignee if being updated - must be a team lead
    if (assigneeId) {
      const assignee = await prisma.user.findUnique({
        where: { id: assigneeId },
      });

      if (!assignee) {
        res.status(400).json({
          success: false,
          message: 'Assignee not found',
        });
        return;
      }

      if (assignee.role !== 'TEAM_LEAD') {
        res.status(400).json({
          success: false,
          message: 'Task assignee must be a team lead',
        });
        return;
      }
    }

    // Check if task has subtasks and if status is being updated
    if (status) {
      const existingSubtasks = await prisma.subtask.findMany({
        where: { taskId: req.params.id },
        select: { id: true }
      });

      if (existingSubtasks.length > 0) {
        // Task has subtasks - status should be calculated automatically
        console.log(`Task ${req.params.id} has ${existingSubtasks.length} subtasks. Status will be calculated automatically based on subtasks.`);

        // Remove status from update data to prevent manual override
        const updateData: any = {
          name: name ? toCamelCase(name) : undefined,
          description,
          assigneeId,
          assigneeType,
          department,
          startDate: startDate ? new Date(startDate) : undefined,
          endDate: endDate ? new Date(endDate) : undefined,
          priority,
        };

        // Update task without status
        const task = await prisma.task.update({
          where: { id: req.params.id },
          data: updateData,
          include: {
            subtask: true,
          },
        });

        // Calculate and set the correct status based on subtasks
        const subtasks = await prisma.subtask.findMany({
          where: { taskId: req.params.id },
          select: { status: true }
        });

        // IMPORTANT: Never update tasks that are manually marked as COMPLETED or ON_HOLD
        // These statuses should be preserved regardless of subtask changes
        if (taskExists.status === 'COMPLETED' || taskExists.status === 'ON_HOLD') {
          console.log(`Task ${req.params.id} status preserved (${taskExists.status}) - manually set status not changed`);

          // Still return the task data but don't change the status
          const taskWithSubtasks = await prisma.task.findUnique({
            where: { id: req.params.id },
            include: {
              subtask: true,
            },
          });

          res.status(200).json({
            success: true,
            data: taskWithSubtasks,
            message: `Task status preserved as ${taskExists.status} (manually set)`
          });
          return;
        }

        let calculatedStatus: string = 'NOT_STARTED';
        if (subtasks.length > 0) {
          const statusCounts = {
            COMPLETED: 0,
            IN_PROGRESS: 0,
            NOT_STARTED: 0,
            DELAYED: 0,
            ON_HOLD: 0
          };

          subtasks.forEach(subtask => {
            statusCounts[subtask.status as keyof typeof statusCounts]++;
          });

          const total = subtasks.length;

          if (statusCounts.COMPLETED === total) {
            calculatedStatus = 'COMPLETED';
          } else if (statusCounts.DELAYED > 0) {
            calculatedStatus = 'DELAYED';
          } else if (statusCounts.IN_PROGRESS > 0) {
            calculatedStatus = 'IN_PROGRESS';
          } else if (statusCounts.ON_HOLD > 0) {
            calculatedStatus = 'ON_HOLD';
          } else {
            calculatedStatus = 'NOT_STARTED';
          }
        }

        // Update task with calculated status
        const updatedTask = await prisma.task.update({
          where: { id: req.params.id },
          data: { status: calculatedStatus as any },
          include: {
            subtask: true,
          },
        });

        // Create alerts for task completion if status changed to completed
        try {
          if (calculatedStatus === 'COMPLETED' && (taskExists.status as string) !== 'COMPLETED') {
            await AlertService.createTaskAlert(req.params.id, 'COMPLETED', req.user.id);
          }
        } catch (alertError) {
          console.error('Error creating task completion alert:', alertError);
        }

        res.status(200).json({
          success: true,
          data: updatedTask,
          message: `Task status automatically set to ${calculatedStatus} based on subtasks`
        });
        return;
      }
    }

    // Update task normally (no subtasks or status not being updated)
    const task = await prisma.task.update({
      where: { id: req.params.id },
      data: {
        name: name ? toCamelCase(name) : undefined,
        description,
        assigneeId,
        assigneeType,
        department,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
        status,
        priority,
      },
      include: {
        subtask: true,
      },
    });

    // Create alerts for task updates (consolidated to prevent duplicates)
    try {
      // Determine the most important alert type to send
      if (status === 'COMPLETED' && taskExists.status !== 'COMPLETED') {
        // Task completion is most important
        await AlertService.createTaskAlert(task.id, 'COMPLETED', req.user.id);
      } else if (assigneeId && assigneeId !== taskExists.assigneeId) {
        // Assignment change is next most important
        await AlertService.createTaskAlert(task.id, 'ASSIGNED', req.user.id);
      } else {
        // General update alert only if no specific action occurred
        await AlertService.createTaskAlert(task.id, 'UPDATED', req.user.id);
      }
    } catch (alertError) {
      console.error('Error creating task update alerts:', alertError);
      // Don't fail the task update if alert creation fails
    }

    res.status(200).json({
      success: true,
      data: task,
    });
  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete task
// @route   DELETE /api/tasks/:id
// @access  Private
export const deleteTask = async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if task exists
    const taskExists = await prisma.task.findUnique({
      where: { id: req.params.id },
      include: {
        project: true,
      },
    });

    if (!taskExists) {
      res.status(404).json({
        success: false,
        message: 'Task not found',
      });
      return;
    }

    // Check if user has access to delete this task
    if (
      req.user.role === 'MANAGER' &&
      taskExists.department !== req.user.department
    ) {
      res.status(403).json({
        success: false,
        message: 'Not authorized to delete this task',
      });
      return;
    }

    // Delete task (subtasks will be deleted automatically due to cascade)
    await prisma.task.delete({
      where: { id: req.params.id },
    });

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
