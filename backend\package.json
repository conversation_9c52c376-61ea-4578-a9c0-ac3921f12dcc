{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node dist/index.js", "start:ts": "ts-node src/index.ts", "dev": "nodemon --exec ts-node src/index.ts", "dev:js": "nodemon src/js/server.js", "build": "tsc", "build:clean": "rm -rf dist && npm run build", "prisma:generate": "prisma generate", "prisma:db-pull": "prisma db pull", "prisma:migrate": "prisma migrate dev", "prisma:migrate:prod": "prisma migrate deploy", "prisma:studio": "prisma studio", "seed": "node prisma/seed.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@prisma/client": "^6.7.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "node-fetch": "^3.3.2", "prisma": "^6.7.0", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.17", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "prisma": {"seed": "node prisma/seed.js"}}