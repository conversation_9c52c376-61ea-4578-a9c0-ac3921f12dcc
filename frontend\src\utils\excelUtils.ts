// Excel utility functions for project import/export
import { Project, Task, Subtask, Section } from '../types';
import { toCamelCase } from './textUtils';

// Excel column mappings - simplified format
export interface ExcelTaskRow {
  'TaskID': string;
  'TaskName': string;
  'ParentTask': string;
  'MilestoneCategory': string;
  'AssignedTo': string;
  'Status': string;
  'StartDate': string;
  'EndDate': string;
  'Priority': string;
}

// Convert project data to Excel format
export const convertProjectToExcelData = (project: Project, users: any[] = []): ExcelTaskRow[] => {
  const excelData: ExcelTaskRow[] = [];

  // Helper function to get user name by ID
  const getUserName = (userId: string): string => {
    if (!userId) return '';
    const user = users.find(u => u.id === userId);
    return user ? user.name : '';
  };

  // Process each section
  project.sections?.forEach(section => {
    section.tasks?.forEach(task => {
      // Add the main task
      excelData.push({
        'TaskID': task.displayId,
        'TaskName': task.name,
        'ParentTask': '', // Empty for main tasks
        'MilestoneCategory': section.name,
        'AssignedTo': getUserName(task.assigneeId) || '', // Always use getUserName
        'Status': task.status,
        'StartDate': formatDateForExcel(task.startDate),
        'EndDate': formatDateForExcel(task.endDate),
        'Priority': task.priority || 'Medium'
      });

      // Add subtasks if they exist
      if (task.subtasks && task.subtasks.length > 0) {
        task.subtasks.forEach(subtask => {
          excelData.push({
            'TaskID': subtask.displayId,
            'TaskName': subtask.name,
            'ParentTask': task.displayId, // Just the parent task ID/name
            'MilestoneCategory': section.name,
            'AssignedTo': getUserName(subtask.assigneeId) || '', // Always use getUserName
            'Status': subtask.status,
            'StartDate': formatDateForExcel(subtask.startDate),
            'EndDate': formatDateForExcel(subtask.endDate),
            'Priority': subtask.priority || 'Medium'
          });
        });
      }
    });
  });

  return excelData;
};

// Format date for Excel
export const formatDateForExcel = (dateString: string): string => {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  } catch {
    return '';
  }
};

// Parse Excel date - handles various formats
export const parseExcelDate = (dateString: string): string => {
  if (!dateString) return '';

  try {
    // Handle various date formats
    let dateValue = dateString.toString().trim();

    // If it's a number (Excel serial date), convert it
    if (!isNaN(Number(dateValue)) && Number(dateValue) > 25000) {
      // Excel serial date (days since 1900-01-01)
      const excelEpoch = new Date(1900, 0, 1);
      const date = new Date(excelEpoch.getTime() + (Number(dateValue) - 1) * 24 * 60 * 60 * 1000);
      return date.toISOString();
    }

    // Handle common date formats
    const formats = [
      dateValue, // Try as-is first
      dateValue.replace(/\//g, '-'), // Convert slashes to dashes
      dateValue.replace(/\./g, '-'), // Convert dots to dashes
      dateValue.replace(/\s+/g, '-'), // Convert spaces to dashes
    ];

    for (const format of formats) {
      const date = new Date(format);
      if (!isNaN(date.getTime())) {
        return date.toISOString();
      }
    }

    // If all else fails, try parsing with manual format detection
    const parts = dateValue.split(/[-\/\.\s]/);
    if (parts.length >= 3) {
      // Try different arrangements: MM/DD/YYYY, DD/MM/YYYY, YYYY/MM/DD
      const arrangements = [
        [parts[2], parts[0], parts[1]], // MM/DD/YYYY -> YYYY/MM/DD
        [parts[2], parts[1], parts[0]], // DD/MM/YYYY -> YYYY/MM/DD
        [parts[0], parts[1], parts[2]], // YYYY/MM/DD
      ];

      for (const [year, month, day] of arrangements) {
        const date = new Date(Number(year), Number(month) - 1, Number(day));
        if (!isNaN(date.getTime()) && date.getFullYear() > 1900) {
          return date.toISOString();
        }
      }
    }

    // Default to current date if parsing fails
    console.warn(`Could not parse date: ${dateString}, using current date`);
    return new Date().toISOString();
  } catch (error) {
    console.warn(`Date parsing error for "${dateString}":`, error);
    return new Date().toISOString();
  }
};

// Validate Excel row data
export const validateExcelRow = (row: any, rowIndex: number): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Only 2 required fields: TaskName and MilestoneCategory
  if (!row['TaskName'] || !row['TaskName'].trim()) {
    errors.push(`Row ${rowIndex + 1}: TaskName is required`);
  }

  if (!row['MilestoneCategory'] || !row['MilestoneCategory'].trim()) {
    errors.push(`Row ${rowIndex + 1}: MilestoneCategory (Section) is required`);
  }

  // All other fields are optional (TaskID, AssignedTo, Status, StartDate, EndDate, Priority, ParentTask)

  // Optional date validation - only validate if both dates are provided
  if (row['StartDate'] && row['EndDate']) {
    try {
      const startDate = new Date(row['StartDate']);
      const endDate = new Date(row['EndDate']);
      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime()) && startDate > endDate) {
        errors.push(`Row ${rowIndex + 1}: End date must be after start date`);
      }
    } catch (dateError) {
      // Ignore date parsing errors - dates are optional
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Convert Excel data to project structure
export const convertExcelDataToProject = (
  excelData: ExcelTaskRow[],
  users: any[],
  existingProject: Project
): { sections: Section[]; errors: string[] } => {
  const errors: string[] = [];
  const sectionsMap = new Map<string, Section>();
  const tasksMap = new Map<string, Task>();

  // Initialize sections from existing project and track existing tasks for duplicate detection
  const existingTasksMap = new Map<string, boolean>();

  existingProject.sections?.forEach(section => {
    sectionsMap.set(section.name, {
      ...section,
      tasks: []  // Start with empty tasks array - we only want to send NEW tasks from Excel
    });

    // Track existing tasks to prevent duplicates
    section.tasks?.forEach(task => {
      const existingTaskKey = `${section.name}-${task.name}`;
      existingTasksMap.set(existingTaskKey, true);
      tasksMap.set(existingTaskKey, task); // Also add to tasksMap for subtask parent lookup

      // Track existing subtasks
      task.subtasks?.forEach(subtask => {
        const existingSubtaskKey = `${task.name}-${subtask.name}`;
        existingTasksMap.set(existingSubtaskKey, true);
      });
    });
  });

  console.log('🔧 Processing Excel data rows:', excelData.length);

  excelData.forEach((row, index) => {
    console.log(`🔧 Row ${index + 1}:`, row);
    console.log(`🔧 Row ${index + 1} TaskName:`, row['TaskName']);
    console.log(`🔧 Row ${index + 1} MilestoneCategory:`, row['MilestoneCategory']);
    console.log(`🔧 Row ${index + 1} ParentTask:`, row['ParentTask']);

    const validation = validateExcelRow(row, index);
    if (!validation.isValid) {
      console.log(`🔧 Row ${index + 1} validation failed:`, validation.errors);
      errors.push(...validation.errors);
      return;
    }

    // Skip empty rows
    if (!row['TaskName'] || !row['TaskName'].trim()) {
      console.log(`🔧 Skipping row ${index + 1} - empty TaskName`);
      return;
    }

    const sectionName = row['MilestoneCategory'];
    if (!sectionName || !sectionName.trim()) {
      console.log(`🔧 Skipping row ${index + 1} - empty MilestoneCategory`);
      return;
    }

    // Get or create section
    if (!sectionsMap.has(sectionName)) {
      // Create new section if it doesn't exist
      const newSection: Section = {
        id: `section-${Date.now()}-${Math.random()}`,
        projectId: existingProject.id || '',
        name: sectionName,
        sequence: sectionsMap.size + 1,
        description: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tasks: []  // This will hold the tasks for backend processing
      };
      sectionsMap.set(sectionName, newSection);
    }

    const section = sectionsMap.get(sectionName)!;

    // Check if this is a task or subtask based on ParentTask field
    const parentTask = row['ParentTask'] || '';

    // Improved logic for detecting tasks vs subtasks:
    // - If ParentTask is empty → main task
    // - If ParentTask contains actual task name → subtask for that task
    // - Ignore common reference/instruction text
    const isReferenceText = parentTask.toLowerCase().includes('no parent') ||
                           parentTask.toLowerCase().includes('this row is task') ||
                           parentTask.toLowerCase().includes('reference') ||
                           parentTask.toLowerCase().includes('main task') ||
                           parentTask.toLowerCase().includes('parent task') ||
                           parentTask.toLowerCase().includes('example') ||
                           parentTask.toLowerCase().includes('sample');

    const isTask = !parentTask || parentTask.trim() === '' || isReferenceText;
    const isSubtask = parentTask && parentTask.trim() !== '' && !isReferenceText;

    console.log(`🔧 Row ${index + 1} parentTask:`, parentTask);
    console.log(`🔧 Row ${index + 1} isTask:`, isTask);
    console.log(`🔧 Row ${index + 1} isSubtask:`, isSubtask);

    if (isTask) {
      console.log(`🔧 Row ${index + 1} - Processing as TASK`);
      // This is a main task
      const taskId = row['TaskID'] || `T${tasksMap.size + 1}`;
      const taskKey = `${sectionName}-${row['TaskName']}`;

      // Check for duplicates - prevent creating tasks with same name in same section
      if (!tasksMap.has(taskKey) && !existingTasksMap.has(taskKey)) {
        // Find user by name (preferred) or ID (fallback)
        const assignee = row['AssignedTo'] ? users.find(u =>
          u.name.toLowerCase() === row['AssignedTo'].toLowerCase() ||
          u.id === row['AssignedTo']
        ) : null;

        console.log(`🔧 Row ${index + 1} AssignedTo: "${row['AssignedTo']}" → Found: ${assignee ? assignee.name : 'Not found'}`);

        const newTask: Task = {
          id: `task-${Date.now()}-${Math.random()}`,
          displayId: taskId,
          projectId: existingProject.id || '',
          sectionId: section.id,
          name: toCamelCase(row['TaskName'].trim()),
          description: row['Description'] || '',
          assigneeId: assignee?.id || '',
          assigneeType: 'ENGINEER',
          department: 'GENERAL',
          startDate: row['StartDate'] ? parseExcelDate(row['StartDate']) : new Date().toISOString(),
          endDate: row['EndDate'] ? parseExcelDate(row['EndDate']) : new Date().toISOString(),
          status: row['Status'] || 'NOT_STARTED',
          priority: row['Priority'] || 'Medium',
          subtasks: [],
          createdBy: '',
          createdAt: new Date().toISOString()
        };

        tasksMap.set(taskKey, newTask);
        section.tasks!.push(newTask);  // Add to tasks array for backend processing
        console.log(`🔧 ✅ Created task: ${newTask.name} in section ${sectionName}`);
      } else {
        const isDuplicateInExcel = tasksMap.has(taskKey);
        const isDuplicateInProject = existingTasksMap.has(taskKey);

        if (isDuplicateInProject) {
          console.log(`🔧 ❌ Duplicate task detected: "${row['TaskName']}" already exists in project section ${sectionName}`);
          errors.push(`Row ${index + 1}: Task "${row['TaskName']}" already exists in project section ${sectionName} - skipping duplicate`);
        } else if (isDuplicateInExcel) {
          console.log(`🔧 ❌ Duplicate task detected: "${row['TaskName']}" appears multiple times in Excel for section ${sectionName}`);
          errors.push(`Row ${index + 1}: Task "${row['TaskName']}" appears multiple times in Excel for section ${sectionName} - skipping duplicate`);
        }
      }
    } else if (isSubtask) {
      console.log(`🔧 Row ${index + 1} - Processing as SUBTASK for parent: ${parentTask}`);

      // Find parent task by the name specified in ParentTask field
      const parentTaskName = parentTask.trim();

      // Find parent task by name, displayId, or TaskID in current section
      let parentTaskObj = null;
      let isExistingTask = false;

      // First, try exact matches
      for (const [key, task] of tasksMap.entries()) {
        if (key.startsWith(`${sectionName}-`) &&
            (task.name === parentTaskName ||
             task.displayId === parentTaskName)) {
          parentTaskObj = task;
          isExistingTask = existingTasksMap.has(key);
          console.log(`🔧 Found parent task (exact match): ${task.name} (${task.displayId}) - Existing: ${isExistingTask}`);
          break;
        }
      }

      // If no exact match, try partial matches
      if (!parentTaskObj) {
        for (const [key, task] of tasksMap.entries()) {
          if (key.startsWith(`${sectionName}-`) &&
              (task.name.toLowerCase().includes(parentTaskName.toLowerCase()) ||
               parentTaskName.toLowerCase().includes(task.name.toLowerCase()))) {
            parentTaskObj = task;
            isExistingTask = existingTasksMap.has(key);
            console.log(`🔧 Found parent task (partial match): ${task.name} (${task.displayId}) - Existing: ${isExistingTask}`);
            break;
          }
        }
      }

      // If still no match, check existing tasks in the project
      if (!parentTaskObj) {
        existingProject.sections?.forEach(existingSection => {
          if (existingSection.name === sectionName) {
            existingSection.tasks?.forEach(existingTask => {
              if (existingTask.name === parentTaskName ||
                  existingTask.displayId === parentTaskName ||
                  existingTask.name.toLowerCase().includes(parentTaskName.toLowerCase()) ||
                  parentTaskName.toLowerCase().includes(existingTask.name.toLowerCase())) {

                // Create a reference to the existing task
                const taskKey = `${sectionName}-${existingTask.name}`;
                if (!tasksMap.has(taskKey)) {
                  tasksMap.set(taskKey, existingTask);
                  existingTasksMap.set(taskKey, true);
                }
                parentTaskObj = existingTask;
                isExistingTask = true;
                console.log(`🔧 Found parent task in existing project: ${existingTask.name} (${existingTask.displayId})`);
              }
            });
          }
        });
      }

      if (!parentTaskObj) {
        console.log(`🔧 ❌ Parent task "${parentTaskName}" not found in section ${sectionName}`);
        console.log(`🔧 Available tasks in ${sectionName}:`, Array.from(tasksMap.entries())
          .filter(([key]) => key.startsWith(`${sectionName}-`))
          .map(([, task]) => `${task.name} (${task.displayId})`));
      }

      if (parentTaskObj) {
        // Check for duplicate subtasks within the same parent task
        const subtaskExists = parentTaskObj.subtasks.some(existingSubtask =>
          existingSubtask.name.toLowerCase() === row['TaskName'].toLowerCase()
        );

        if (subtaskExists) {
          console.log(`🔧 ❌ Duplicate subtask detected: "${row['TaskName']}" already exists under parent ${parentTaskObj.name}`);
          errors.push(`Row ${index + 1}: Subtask "${row['TaskName']}" already exists under parent task "${parentTaskObj.name}" - skipping duplicate`);
        } else {
          // Additional safety check: Look for this subtask in other parent tasks in the existing project
          let subtaskInOtherParent = false;
          existingProject.sections?.forEach(existingSection => {
            existingSection.tasks?.forEach(existingTask => {
              if (existingTask.id !== parentTaskObj.id && existingTask.subtasks) {
                const foundInOtherParent = existingTask.subtasks.some(existingSubtask =>
                  existingSubtask.name.toLowerCase() === row['TaskName'].toLowerCase()
                );
                if (foundInOtherParent) {
                  subtaskInOtherParent = true;
                  console.log(`🔧 ❌ Subtask "${row['TaskName']}" already exists under different parent task "${existingTask.name}"`);
                  errors.push(`Row ${index + 1}: Subtask "${row['TaskName']}" already exists under different parent task "${existingTask.name}" - cannot move existing subtasks`);
                }
              }
            });
          });

          if (!subtaskInOtherParent) {
          const subtaskAssignee = row['AssignedTo'] ? users.find(u =>
            u.name.toLowerCase() === row['AssignedTo'].toLowerCase() ||
            u.id === row['AssignedTo']
          ) : null;

          console.log(`🔧 Row ${index + 1} Subtask AssignedTo: "${row['AssignedTo']}" → Found: ${subtaskAssignee ? subtaskAssignee.name : 'Not found'}`);

          const newSubtask: Subtask = {
          id: `subtask-${Date.now()}-${Math.random()}`,
          displayId: row['TaskID'] || `S${parentTaskObj.subtasks.length + 1}`,
          taskId: parentTaskObj.id,
          name: toCamelCase(row['TaskName'].trim()),
          description: row['Description'] || '',
          assigneeId: subtaskAssignee?.id || '',
          assigneeType: 'ENGINEER',
          startDate: row['StartDate'] ? parseExcelDate(row['StartDate']) : new Date().toISOString(),
          endDate: row['EndDate'] ? parseExcelDate(row['EndDate']) : new Date().toISOString(),
          status: row['Status'] || 'NOT_STARTED',
          priority: row['Priority'] || 'Medium',
          createdBy: '',
          createdAt: new Date().toISOString()
        };

          parentTaskObj.subtasks.push(newSubtask);
          console.log(`🔧 ✅ Created subtask: ${newSubtask.name} for parent ${parentTaskObj.name}`);

          // If this is an existing task that we're adding subtasks to,
          // we need to include it in the section's tasks array for backend processing
          if (isExistingTask && !section.tasks!.some(t => t.id === parentTaskObj.id)) {
            section.tasks!.push(parentTaskObj);
            console.log(`🔧 📤 Added existing parent task ${parentTaskObj.name} to backend payload for subtask processing`);
          }
          } // Close the subtaskInOtherParent check
        }
      }
    } else {
      console.log(`🔧 ❌ Row ${index + 1} - Neither task nor subtask! parentTask: "${parentTask}"`);
      console.log(`🔧 ❌ Row ${index + 1} - isTask: ${isTask}, isSubtask: ${isSubtask}`);
    }
  });

  const finalSections = Array.from(sectionsMap.values());
  console.log('🔧 convertExcelDataToProject result:');
  console.log('🔧 Number of sections:', finalSections.length);
  console.log('🔧 Sections:', finalSections);
  console.log('🔧 Errors:', errors);

  // Debug: Show tasks in each section
  finalSections.forEach((section, index) => {
    console.log(`🔧 Section ${index + 1}: ${section.name} - ${section.tasks?.length || 0} tasks`);
    section.tasks?.forEach((task, taskIndex) => {
      console.log(`  🔧 Task ${taskIndex + 1}: ${task.name} (ID: ${task.id})`);
    });
  });

  return {
    sections: finalSections,
    errors
  };
};

// Generate Excel template data with current milestones
export const generateExcelTemplate = (milestones: any[] = []): ExcelTaskRow[] => {
  // Get current milestone names, fallback to default if none available
  const availableMilestones = milestones.length > 0
    ? milestones.map(m => m.name)
    : ['Design', 'Procurement', 'Assembly', 'Testing', 'MQI'];

  const firstMilestone = availableMilestones[0];
  const secondMilestone = availableMilestones[1] || availableMilestones[0];

  return [
    {
      'TaskID': 'T1',
      'TaskName': 'Design Review',
      'ParentTask': '', // Leave empty for main tasks
      'MilestoneCategory': `${firstMilestone}`,
      'AssignedTo': 'Team Lead Name',
      'Status': 'NOT_STARTED',
      'StartDate': '2024-01-01',
      'EndDate': '2024-01-15',
      'Priority': 'High'
    },
    {
      'TaskID': 'S1',
      'TaskName': 'Create Initial Drawings',
      'ParentTask': 'Design Review', // Use exact parent task name
      'MilestoneCategory': `${firstMilestone}`,
      'AssignedTo': 'Engineer Name',
      'Status': 'NOT_STARTED',
      'StartDate': '2024-01-02',
      'EndDate': '2024-01-10',
      'Priority': 'Medium'
    },
    {
      'TaskID': 'S2',
      'TaskName': 'Review Specifications',
      'ParentTask': 'Design Review', // Use exact parent task name
      'MilestoneCategory': `${firstMilestone}`,
      'AssignedTo': 'Engineer Name',
      'Status': 'NOT_STARTED',
      'StartDate': '2024-01-05',
      'EndDate': '2024-01-12',
      'Priority': 'Medium'
    },
    {
      'TaskID': 'T2',
      'TaskName': 'Material Selection',
      'ParentTask': '', // Leave empty for main tasks
      'MilestoneCategory': `${secondMilestone}`,
      'AssignedTo': 'Team Lead Name',
      'Status': 'NOT_STARTED',
      'StartDate': '2024-01-16',
      'EndDate': '2024-01-30',
      'Priority': 'High'
    },
    {
      'TaskID': 'S3',
      'TaskName': 'Research Suppliers',
      'ParentTask': 'Material Selection', // Use exact parent task name
      'MilestoneCategory': `${secondMilestone}`,
      'AssignedTo': 'Engineer Name',
      'Status': 'NOT_STARTED',
      'StartDate': '2024-01-17',
      'EndDate': '2024-01-25',
      'Priority': 'Medium'
    }
  ];
};

// Preview Excel data before import
export interface ExcelPreviewRow {
  rowNumber: number;
  action: 'CREATE_TASK' | 'UPDATE_TASK' | 'CREATE_SUBTASK' | 'UPDATE_SUBTASK' | 'SKIP' | 'ERROR';
  taskName: string;
  sectionName: string;
  assignedTo: string;
  status: string;
  startDate: string;
  endDate: string;
  priority: string;
  parentTask?: string;
  warnings: string[];
  errors: string[];
  willBeAssigned: {
    taskId: string;
    assigneeId: string;
    assigneeName: string;
    sectionId: string;
    dates: {
      start: string;
      end: string;
    };
  };
}

export const previewExcelData = (
  excelData: ExcelTaskRow[],
  users: any[],
  existingProject: Project
): ExcelPreviewRow[] => {
  const preview: ExcelPreviewRow[] = [];
  const existingTasks = new Map<string, any>();
  const sectionsMap = new Map<string, any>();

  // Build existing tasks map for matching
  existingProject.sections?.forEach(section => {
    sectionsMap.set(section.name, section);
    section.tasks?.forEach(task => {
      const key = `${section.name}-${task.name}`;
      existingTasks.set(key, task);
      console.log(`🔍 Preview: Added existing task to map: "${key}" -> ${task.displayId}`);
    });
  });

  console.log(`🔍 Preview: Built existing tasks map with ${existingTasks.size} tasks`);

  let taskCounter = 1;

  excelData.forEach((row, index) => {
    const rowNumber = index + 1;
    const warnings: string[] = [];
    const errors: string[] = [];

    // Validate required fields
    if (!row['TaskName'] || !row['TaskName'].trim()) {
      preview.push({
        rowNumber,
        action: 'ERROR',
        taskName: row['TaskName'] || '',
        sectionName: row['MilestoneCategory'] || '',
        assignedTo: row['AssignedTo'] || '',
        status: row['Status'] || '',
        startDate: row['StartDate'] || '',
        endDate: row['EndDate'] || '',
        priority: row['Priority'] || '',
        warnings: [],
        errors: ['TaskName is required'],
        willBeAssigned: {
          taskId: '',
          assigneeId: '',
          assigneeName: '',
          sectionId: '',
          dates: { start: '', end: '' }
        }
      });
      return;
    }

    if (!row['MilestoneCategory'] || !row['MilestoneCategory'].trim()) {
      preview.push({
        rowNumber,
        action: 'ERROR',
        taskName: row['TaskName'] || '',
        sectionName: row['MilestoneCategory'] || '',
        assignedTo: row['AssignedTo'] || '',
        status: row['Status'] || '',
        startDate: row['StartDate'] || '',
        endDate: row['EndDate'] || '',
        priority: row['Priority'] || '',
        warnings: [],
        errors: ['MilestoneCategory (Section) is required'],
        willBeAssigned: {
          taskId: '',
          assigneeId: '',
          assigneeName: '',
          sectionId: '',
          dates: { start: '', end: '' }
        }
      });
      return;
    }

    const taskName = row['TaskName'].trim();
    const sectionName = row['MilestoneCategory'].trim();
    const parentTask = row['ParentTask'] || '';

    // Use same logic as main conversion function
    const isReferenceText = parentTask.toLowerCase().includes('no parent') ||
                           parentTask.toLowerCase().includes('this row is task') ||
                           parentTask.toLowerCase().includes('reference') ||
                           parentTask.toLowerCase().includes('main task') ||
                           parentTask.toLowerCase().includes('parent task') ||
                           parentTask.toLowerCase().includes('example') ||
                           parentTask.toLowerCase().includes('sample');

    const isTask = !parentTask || parentTask.trim() === '' || isReferenceText;
    const isSubtask = parentTask && parentTask.trim() !== '' && !isReferenceText;

    // Find assignee
    let assignee = null;
    let assigneeName = 'Unassigned';
    if (row['AssignedTo'] && row['AssignedTo'].trim()) {
      assignee = users.find(u =>
        u.name === row['AssignedTo'] || u.id === row['AssignedTo']
      );
      if (assignee) {
        assigneeName = assignee.name;
      } else {
        warnings.push(`User "${row['AssignedTo']}" not found - will be left unassigned`);
        assigneeName = `"${row['AssignedTo']}" (Not Found)`;
      }
    }

    // Handle dates
    const currentDate = new Date();
    let startDate = row['StartDate'] ? row['StartDate'] : currentDate.toISOString().split('T')[0];
    let endDate = row['EndDate'] ? row['EndDate'] : new Date(currentDate.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    if (!row['StartDate']) {
      warnings.push('Start date not provided - will use today\'s date');
    }
    if (!row['EndDate']) {
      warnings.push('End date not provided - will use 7 days from start date');
    }

    // Handle status
    const status = row['Status'] || 'NOT_STARTED';
    if (!row['Status']) {
      warnings.push('Status not provided - will use NOT_STARTED');
    }

    // Handle priority
    const priority = row['Priority'] || 'Medium';
    if (!row['Priority']) {
      warnings.push('Priority not provided - will use Medium');
    }

    // Check if section exists
    let sectionId = '';
    if (sectionsMap.has(sectionName)) {
      sectionId = sectionsMap.get(sectionName).id;
    } else {
      warnings.push(`Section "${sectionName}" doesn't exist - will be created`);
      sectionId = `new-section-${sectionName}`;
    }

    console.log(`🔍 Preview Row ${rowNumber}: ParentTask="${parentTask}", isTask=${isTask}, isSubtask=${isSubtask}`);

    if (isTask) {
      // This is a main task
      const taskKey = `${sectionName}-${taskName}`;
      const existingTask = existingTasks.get(taskKey);
      console.log(`🔍 Preview Row ${rowNumber}: Looking for existing task with key "${taskKey}" -> ${existingTask ? 'FOUND' : 'NOT FOUND'}`);

      const taskId = row['TaskID'] || `T${taskCounter++}`;
      if (!row['TaskID']) {
        warnings.push(`TaskID not provided - will auto-generate as ${taskId}`);
      }

      if (existingTask) {
        // Will update existing task
        preview.push({
          rowNumber,
          action: 'UPDATE_TASK',
          taskName,
          sectionName,
          assignedTo: row['AssignedTo'] || '',
          status,
          startDate: row['StartDate'] || '',
          endDate: row['EndDate'] || '',
          priority,
          warnings,
          errors,
          willBeAssigned: {
            taskId: existingTask.displayId,
            assigneeId: assignee?.id || '',
            assigneeName,
            sectionId,
            dates: { start: startDate, end: endDate }
          }
        });
      } else {
        // Will create new task
        preview.push({
          rowNumber,
          action: 'CREATE_TASK',
          taskName,
          sectionName,
          assignedTo: row['AssignedTo'] || '',
          status,
          startDate: row['StartDate'] || '',
          endDate: row['EndDate'] || '',
          priority,
          warnings,
          errors,
          willBeAssigned: {
            taskId,
            assigneeId: assignee?.id || '',
            assigneeName,
            sectionId,
            dates: { start: startDate, end: endDate }
          }
        });
      }
    } else if (isSubtask) {
      // This is a subtask
      const parentTaskName = parentTask.trim();
      console.log(`🔍 Preview Row ${rowNumber}: Processing as SUBTASK, looking for parent "${parentTaskName}"`);

      if (!parentTaskName) {
        errors.push('Parent task name is required for subtasks');
      } else {
        // Find parent task in existing tasks
        let parentTaskObj = null;
        for (const [key, task] of existingTasks.entries()) {
          if (key.startsWith(`${sectionName}-`) &&
              (task.name === parentTaskName ||
               task.displayId === parentTaskName ||
               task.name.toLowerCase().includes(parentTaskName.toLowerCase()) ||
               parentTaskName.toLowerCase().includes(task.name.toLowerCase()))) {
            parentTaskObj = task;
            console.log(`🔍 Preview Row ${rowNumber}: Found parent task "${task.name}" (${task.displayId})`);
            break;
          }
        }

        if (!parentTaskObj) {
          warnings.push(`Parent task "${parentTaskName}" not found in section "${sectionName}" - subtask may not be created properly`);
        }

        const subtaskId = row['TaskID'] || `S${taskCounter++}`;
        if (!row['TaskID']) {
          warnings.push(`TaskID not provided - will auto-generate as ${subtaskId}`);
        }

        // Check if subtask exists
        let existingSubtask = null;
        if (parentTaskObj) {
          existingSubtask = parentTaskObj.subtasks?.find((st: any) => st.name === taskName);
        }

        if (existingSubtask) {
          // Will update existing subtask
          preview.push({
            rowNumber,
            action: 'UPDATE_SUBTASK',
            taskName,
            sectionName,
            assignedTo: row['AssignedTo'] || '',
            status,
            startDate: row['StartDate'] || '',
            endDate: row['EndDate'] || '',
            priority,
            parentTask: parentTaskName,
            warnings,
            errors,
            willBeAssigned: {
              taskId: existingSubtask.displayId,
              assigneeId: assignee?.id || '',
              assigneeName,
              sectionId,
              dates: { start: startDate, end: endDate }
            }
          });
        } else {
          // Will create new subtask
          preview.push({
            rowNumber,
            action: 'CREATE_SUBTASK',
            taskName,
            sectionName,
            assignedTo: row['AssignedTo'] || '',
            status,
            startDate: row['StartDate'] || '',
            endDate: row['EndDate'] || '',
            priority,
            parentTask: parentTaskName,
            warnings,
            errors,
            willBeAssigned: {
              taskId: subtaskId,
              assigneeId: assignee?.id || '',
              assigneeName,
              sectionId,
              dates: { start: startDate, end: endDate }
            }
          });
        }
      }
    } else {
      // Unknown row type
      warnings.push('ParentTask field format not recognized - assuming this is a main task');

      const taskKey = `${sectionName}-${taskName}`;
      const existingTask = existingTasks.get(taskKey);
      const taskId = row['TaskID'] || `T${taskCounter++}`;

      if (existingTask) {
        preview.push({
          rowNumber,
          action: 'UPDATE_TASK',
          taskName,
          sectionName,
          assignedTo: row['AssignedTo'] || '',
          status,
          startDate: row['StartDate'] || '',
          endDate: row['EndDate'] || '',
          priority,
          warnings,
          errors,
          willBeAssigned: {
            taskId: existingTask.displayId,
            assigneeId: assignee?.id || '',
            assigneeName,
            sectionId,
            dates: { start: startDate, end: endDate }
          }
        });
      } else {
        preview.push({
          rowNumber,
          action: 'CREATE_TASK',
          taskName,
          sectionName,
          assignedTo: row['AssignedTo'] || '',
          status,
          startDate: row['StartDate'] || '',
          endDate: row['EndDate'] || '',
          priority,
          warnings,
          errors,
          willBeAssigned: {
            taskId,
            assigneeId: assignee?.id || '',
            assigneeName,
            sectionId,
            dates: { start: startDate, end: endDate }
          }
        });
      }
    }
  });

  return preview;
};
