-- CreateTable
CREATE TABLE `milestone_templates` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `sequence` INTEGER NOT NULL DEFAULT 1,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `milestone_templates_name_key`(`name`),
    UNIQUE INDEX `milestone_templates_sequence_key`(`sequence`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Insert default milestone templates
INSERT INTO `milestone_templates` (`id`, `name`, `description`, `sequence`, `isActive`, `createdAt`, `updatedAt`) VALUES
('mt_001', 'Design', 'Design and planning phase', 1, true, NOW(), NOW()),
('mt_002', 'Procurement', 'Procurement and sourcing phase', 2, true, NOW(), NOW()),
('mt_003', 'Assembly', 'Assembly and manufacturing phase', 3, true, NOW(), NOW()),
('mt_004', 'Testing', 'Testing and quality assurance phase', 4, true, NOW(), NOW()),
('mt_005', 'MQ1', 'First milestone and quality checkpoint', 5, true, NOW(), NOW()),
('mt_006', 'MQ2', 'Second milestone and quality checkpoint', 6, true, NOW(), NOW());
