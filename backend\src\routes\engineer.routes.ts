import express, { Request, Response } from 'express';
import {
  getEngineers,
  getEngineer,
  createEngineer,
  updateEngineer,
  deleteEngineer,
} from '../controllers/engineer.controller';
import { protect, authorize } from '../middleware/auth.middleware';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(getEngineers)
  .post(
    authorize('DIRECTOR', 'GENERAL_MANAGER', 'MANAGER'),
    createEngineer
  );

router.route('/:id')
  .get(getEngineer)
  .put(
    authorize('DIRECTOR', 'GENERAL_MANAGER', 'MANAGER'),
    updateEngineer
  )
  .delete(
    authorize('DIRECTOR', 'GENERAL_MANAGER'),
    deleteEngineer
  );

export default router;
