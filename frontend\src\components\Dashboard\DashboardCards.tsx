import React, { useEffect, useState } from 'react';
import { useAtom } from 'jotai';
import { useNavigate } from 'react-router-dom';
import {
  projectsAtom,
  currentUserAtom,
  alertsAtom,
  userSubtasksAtom
} from '../../store';
import { TaskStatus, Project, Task, Subtask } from '../../types';
import { paymentsAPI } from '../../services/api';
import {
  ClipboardList,
  CheckSquare,
  Clock,
  Bell,
  TrendingUp,
  ListTodo,
  DollarSign,
  CreditCard,
  AlertCircle
} from 'lucide-react';

const DashboardCards: React.FC = () => {
  const navigate = useNavigate();
  const [projects] = useAtom(projectsAtom);
  const [currentUser] = useAtom(currentUserAtom);
  const [alerts] = useAtom(alertsAtom);
  const [userSubtasks] = useAtom(userSubtasksAtom);

  // Payment summary state
  const [paymentSummary, setPaymentSummary] = useState({
    totalProjects: 0,
    totalPoValue: 0,
    totalPaid: 0,
    totalPending: 0,
    paidPercentage: 0,
    pendingPercentage: 0
  });
  const [paymentLoading, setPaymentLoading] = useState(false);

  // Fetch payment summary for Directors
  useEffect(() => {
    const fetchPaymentSummary = async () => {
      if (currentUser?.role !== 'DIRECTOR') return;

      setPaymentLoading(true);
      try {
        const response = await paymentsAPI.getPaymentSummary();
        if (response.success) {
          setPaymentSummary(response.data);
        }
      } catch (error) {
        console.error('Error fetching payment summary:', error);
      } finally {
        setPaymentLoading(false);
      }
    };

    fetchPaymentSummary();
  }, [currentUser?.role]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Filter projects and tasks based on user role and assignment
  const userProjects = projects.filter(project => {
    if (currentUser?.role === 'DIRECTOR') {
      return true; // Directors can see all projects
    } else if (currentUser?.role === 'PROJECT_MANAGER') {
      // Project managers can see projects they manage
      return project.projectManagerId === currentUser.id;
    } else if (currentUser?.role === 'TEAM_LEAD') {
      // Team leads can see projects where they are assigned tasks or projects in their department
      return project.department === currentUser.department ||
             project.tasks?.some(task => task.assigneeId === currentUser.id) || false;
    } else if (currentUser?.role === 'ENGINEER') {
      // Engineers only see projects where they are assigned tasks
      return project.tasks?.some(task => task.assigneeId === currentUser.id) || false;
    }
    return false;
  });

  // Get all tasks from user projects
  const userTasks: Task[] = userProjects.flatMap(project => project.tasks || []);

  // Get all subtasks from user tasks
  const userSubtasksFromTasks: Subtask[] = userTasks.flatMap(task => task.subtasks || []);

  // Combine userSubtasks from atom with subtasks from tasks
  const allSubtasks: Subtask[] = [...userSubtasks, ...userSubtasksFromTasks].filter(
    (subtask, index, self) => self.findIndex(s => s.id === subtask.id) === index
  );

  // Calculate unread alerts count
  const unreadAlertsCount = alerts.filter(alert => !alert.read).length;

  // Calculate stats
  const totalProjects = userProjects.length;
  const completedProjects = userProjects.filter(project =>
    project.status === TaskStatus.COMPLETED
  ).length;

  const totalTasks = userTasks.length;
  const completedTasks = userTasks.filter(task =>
    task.status === TaskStatus.COMPLETED
  ).length;

  const totalSubtasks = allSubtasks.length;
  const completedSubtasks = allSubtasks.filter(subtask =>
    subtask.status === TaskStatus.COMPLETED
  ).length;

  const delayedTasks = userTasks.filter(task =>
    task.status === TaskStatus.DELAYED
  ).length;

  const tasksInProgress = userTasks.filter(task =>
    task.status === TaskStatus.IN_PROGRESS
  ).length;

  // Calculate progress percentages
  const projectProgress = totalProjects > 0
    ? Math.round((completedProjects / totalProjects) * 100)
    : 0;

  const taskProgress = totalTasks > 0
    ? Math.round((completedTasks / totalTasks) * 100)
    : 0;

  const subtaskProgress = totalSubtasks > 0
    ? Math.round((completedSubtasks / totalSubtasks) * 100)
    : 0;

  // Handle navigation - all roles can access their respective paths
  const getNavigationPath = (defaultPath: string) => {
    return defaultPath;
  };

  const handleNavigate = (path: string, filter?: string) => {
    const finalPath = getNavigationPath(path);
    if (filter) {
      navigate(`${finalPath}?status=${filter}`);
    } else {
      navigate(finalPath);
    }
  };

  // Calculate additional subtask-specific metrics for engineers
  const subtasksInProgress = allSubtasks.filter(subtask =>
    subtask.status === TaskStatus.IN_PROGRESS
  ).length;

  const delayedSubtasks = allSubtasks.filter(subtask =>
    subtask.status === TaskStatus.DELAYED
  ).length;

  const pendingSubtasks = allSubtasks.filter(subtask =>
    subtask.status === TaskStatus.NOT_STARTED
  ).length;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8 mb-10 perspective-1000">
      <Card
        title="Total Projects"
        value={totalProjects.toString()}
        description={`${projectProgress}% Complete`}
        icon={<ClipboardList size={24} />}
        color="primary"
        onClick={() => handleNavigate('/projects')}
      />

      {/* Show different cards based on user role */}
      {currentUser?.role === 'ENGINEER' ? (
        // Subtask cards for engineers
        <>
          <Card
            title="Completed Subtasks"
            value={completedSubtasks.toString()}
            description={`of ${totalSubtasks} Subtasks (${subtaskProgress}%)`}
            icon={<CheckSquare size={24} />}
            color="success"
            onClick={() => handleNavigate('/subtasks', 'COMPLETED')}
          />

          <Card
            title="Subtasks In Progress"
            value={subtasksInProgress.toString()}
            description={`${Math.round((subtasksInProgress / totalSubtasks) * 100) || 0}% of all subtasks`}
            icon={<Clock size={24} />}
            color="info"
            onClick={() => handleNavigate('/subtasks', 'IN_PROGRESS')}
          />

          <Card
            title="Delayed Subtasks"
            value={delayedSubtasks.toString()}
            description="Require attention"
            icon={<Bell size={24} />}
            color="error"
            onClick={() => handleNavigate('/subtasks', 'DELAYED')}
          />
        </>
      ) : (
        // Task cards for other roles
        <>
          <Card
            title="Completed Tasks"
            value={completedTasks.toString()}
            description={`of ${totalTasks} Tasks (${taskProgress}%)`}
            icon={<CheckSquare size={24} />}
            color="success"
            onClick={() => handleNavigate('/tasks', 'COMPLETED')}
          />

          <Card
            title="Tasks In Progress"
            value={tasksInProgress.toString()}
            description={`${Math.round((tasksInProgress / totalTasks) * 100) || 0}% of all tasks`}
            icon={<Clock size={24} />}
            color="info"
            onClick={() => handleNavigate('/tasks', 'IN_PROGRESS')}
          />

          <Card
            title="Delayed Tasks"
            value={delayedTasks.toString()}
            description="Require attention"
            icon={<Bell size={24} />}
            color="error"
            onClick={() => handleNavigate('/tasks', 'DELAYED')}
          />
        </>
      )}

      {currentUser?.role === 'ENGINEER' ? (
        <Card
          title="Pending Subtasks"
          value={pendingSubtasks.toString()}
          description="Ready to start"
          icon={<ListTodo size={24} />}
          color="warning"
          onClick={() => handleNavigate('/subtasks', 'NOT_STARTED')}
        />
      ) : (
        <Card
          title="Total Subtasks"
          value={totalSubtasks.toString()}
          description={`${subtaskProgress}% Complete`}
          icon={<ListTodo size={24} />}
          color="warning"
          onClick={() => handleNavigate('/subtasks')}
        />
      )}

      {(currentUser?.role === 'DIRECTOR' || currentUser?.role === 'PROJECT_MANAGER') && (
        <Card
          title="Completion Rate"
          value={`${taskProgress}%`}
          description="Tasks completed"
          icon={<TrendingUp size={24} />}
          color="accent"
          onClick={() => handleNavigate('/tasks')}
        />
      )}

      {currentUser?.role === 'ENGINEER' && (
        <Card
          title="Subtask Completion Rate"
          value={`${subtaskProgress}%`}
          description="Subtasks completed"
          icon={<TrendingUp size={24} />}
          color="accent"
          onClick={() => handleNavigate('/subtasks')}
        />
      )}

      {/* Payment Cards for Directors */}
      {currentUser?.role === 'DIRECTOR' && (
        <>
          <Card
            title="Total PO Value"
            value={paymentLoading ? "..." : formatCurrency(paymentSummary.totalPoValue)}
            description={`${paymentSummary.totalProjects} Projects`}
            icon={<DollarSign size={24} />}
            color="primary"
            onClick={() => handleNavigate('/po-details')}
          />

          <Card
            title="Total Paid"
            value={paymentLoading ? "..." : formatCurrency(paymentSummary.totalPaid)}
            description={`${paymentSummary.paidPercentage.toFixed(1)}% of total`}
            icon={<CheckSquare size={24} />}
            color="success"
            onClick={() => handleNavigate('/transaction-history')}
          />

          <Card
            title="Total Pending"
            value={paymentLoading ? "..." : formatCurrency(paymentSummary.totalPending)}
            description={`${paymentSummary.pendingPercentage.toFixed(1)}% remaining`}
            icon={<CreditCard size={24} />}
            color="warning"
            onClick={() => handleNavigate('/pending-payments')}
          />
        </>
      )}
    </div>
  );
};

interface CardProps {
  title: string;
  value: string;
  description: string;
  icon: React.ReactNode;
  color: 'primary' | 'success' | 'info' | 'warning' | 'error' | 'accent';
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({ title, value, description, icon, color, onClick }) => {
  const getColorClasses = () => {
    switch (color) {
      case 'primary':
        return {
          bg: 'bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700',
          shadow: 'shadow-blue-500/30',
          hoverShadow: 'hover:shadow-blue-500/50',
          iconBg: 'bg-blue-400/30',
          hoverIconBg: 'group-hover:bg-blue-300/40',
          border: 'border-blue-400/20'
        };
      case 'success':
        return {
          bg: 'bg-gradient-to-br from-green-500 via-green-600 to-green-700',
          shadow: 'shadow-green-500/30',
          hoverShadow: 'hover:shadow-green-500/50',
          iconBg: 'bg-green-400/30',
          hoverIconBg: 'group-hover:bg-green-300/40',
          border: 'border-green-400/20'
        };
      case 'info':
        return {
          bg: 'bg-gradient-to-br from-teal-500 via-teal-600 to-teal-700',
          shadow: 'shadow-teal-500/30',
          hoverShadow: 'hover:shadow-teal-500/50',
          iconBg: 'bg-teal-400/30',
          hoverIconBg: 'group-hover:bg-teal-300/40',
          border: 'border-teal-400/20'
        };
      case 'warning':
        return {
          bg: 'bg-gradient-to-br from-amber-500 via-amber-600 to-amber-700',
          shadow: 'shadow-amber-500/30',
          hoverShadow: 'hover:shadow-amber-500/50',
          iconBg: 'bg-amber-400/30',
          hoverIconBg: 'group-hover:bg-amber-300/40',
          border: 'border-amber-400/20'
        };
      case 'error':
        return {
          bg: 'bg-gradient-to-br from-red-500 via-red-600 to-red-700',
          shadow: 'shadow-red-500/30',
          hoverShadow: 'hover:shadow-red-500/50',
          iconBg: 'bg-red-400/30',
          hoverIconBg: 'group-hover:bg-red-300/40',
          border: 'border-red-400/20'
        };
      case 'accent':
        return {
          bg: 'bg-gradient-to-br from-purple-500 via-purple-600 to-purple-700',
          shadow: 'shadow-purple-500/30',
          hoverShadow: 'hover:shadow-purple-500/50',
          iconBg: 'bg-purple-400/30',
          hoverIconBg: 'group-hover:bg-purple-300/40',
          border: 'border-purple-400/20'
        };
      default:
        return {
          bg: 'bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700',
          shadow: 'shadow-blue-500/30',
          hoverShadow: 'hover:shadow-blue-500/50',
          iconBg: 'bg-blue-400/30',
          hoverIconBg: 'group-hover:bg-blue-300/40',
          border: 'border-blue-400/20'
        };
    }
  };

  const colors = getColorClasses();

  return (
    <div
      className={`
        relative group cursor-pointer
        ${colors.bg}
        border ${colors.border}
        rounded-2xl
        shadow-2xl ${colors.shadow} ${colors.hoverShadow}
        transform-gpu transition-all duration-500 ease-out
        hover:scale-105 hover:-translate-y-2
        hover:shadow-3xl
        active:scale-95 active:translate-y-0
        before:absolute before:inset-0 before:rounded-2xl
        before:bg-gradient-to-t before:from-black/5 before:to-white/10
        before:opacity-0 before:transition-opacity before:duration-300
        hover:before:opacity-100
        backdrop-blur-sm
      `}
      onClick={onClick}
      style={{
        transformStyle: 'preserve-3d',
      }}
    >
      {/* Top highlight for 3D effect */}
      <div className="absolute top-0 left-4 right-4 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>

      {/* Left edge highlight */}
      <div className="absolute top-4 bottom-4 left-0 w-px bg-gradient-to-b from-transparent via-white/20 to-transparent"></div>

      {/* Main content */}
      <div className="relative p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold tracking-tight drop-shadow-sm">{title}</h3>
          <div className={`
            p-3 rounded-xl ${colors.iconBg} ${colors.hoverIconBg}
            transition-all duration-300
            shadow-lg shadow-black/20
            group-hover:shadow-xl group-hover:scale-110
            backdrop-blur-sm
            border border-white/10
          `}>
            <div className="drop-shadow-sm">
              {icon}
            </div>
          </div>
        </div>

        <div className="flex flex-col">
          <span className="text-4xl font-black mb-2 drop-shadow-lg tracking-tight">
            {value}
          </span>
          <span className="text-sm font-medium opacity-90 leading-tight drop-shadow-sm">
            {description}
          </span>
        </div>
      </div>

      {/* Bottom shadow for depth */}
      <div className="absolute -bottom-2 left-2 right-2 h-4 bg-black/10 rounded-full blur-md transform scale-95 group-hover:scale-100 transition-transform duration-300"></div>

      {/* Glossy overlay */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/10 via-transparent to-black/5 pointer-events-none"></div>

      {/* Shimmer effect on hover */}
      <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 overflow-hidden">
        <div className="absolute -inset-10 bg-gradient-to-r from-transparent via-white/10 to-transparent rotate-12 transform translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-1000"></div>
      </div>
    </div>
  );
};

export default DashboardCards;