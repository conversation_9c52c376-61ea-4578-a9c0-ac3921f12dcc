import React from 'react';
import { use<PERSON>tom } from 'jotai';
import { alertsAtom } from '../../store';
import { formatDistanceToNow } from 'date-fns';
import {
  Bell,
  InfoIcon,
  AlertCircle,
  XCircle,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const RecentAlerts: React.FC = () => {
  const [alerts] = useAtom(alertsAtom);
  const navigate = useNavigate();

  // Get recent alerts (limit to 3)
  const recentAlerts = [...alerts]
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 3);

  // Get alert icon based on type
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'INFO':
        return <InfoIcon size={16} className="text-blue-600 drop-shadow-sm" />;
      case 'WARNING':
        return <AlertCircle size={16} className="text-amber-600 drop-shadow-sm" />;
      case 'ERROR':
        return <XCircle size={16} className="text-red-600 drop-shadow-sm" />;
      case 'SUCCESS':
        return <CheckCircle size={16} className="text-green-600 drop-shadow-sm" />;
      default:
        return <InfoIcon size={16} className="text-blue-600 drop-shadow-sm" />;
    }
  };

  // Get alert background with 3D gradient effect
  const getAlertBgStyle = (type: string) => {
    switch (type) {
      case 'INFO':
        return 'bg-gradient-to-br from-blue-50 via-blue-25 to-white border-blue-200/60 shadow-blue-100/50';
      case 'WARNING':
        return 'bg-gradient-to-br from-amber-50 via-amber-25 to-white border-amber-200/60 shadow-amber-100/50';
      case 'ERROR':
        return 'bg-gradient-to-br from-red-50 via-red-25 to-white border-red-200/60 shadow-red-100/50';
      case 'SUCCESS':
        return 'bg-gradient-to-br from-green-50 via-green-25 to-white border-green-200/60 shadow-green-100/50';
      default:
        return 'bg-gradient-to-br from-blue-50 via-blue-25 to-white border-blue-200/60 shadow-blue-100/50';
    }
  };

  return (
    <div className="relative">
      {/* Main card with enhanced 3D effect */}
      <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-2xl shadow-2xl border border-gray-200/50 p-8 relative overflow-hidden transform hover:scale-[1.02] transition-all duration-300 hover:shadow-3xl">
        {/* Subtle background pattern for depth */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-gray-100/20 pointer-events-none"></div>

        {/* Top highlight for 3D effect */}
        <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/60 to-transparent"></div>

        {/* Header section */}
        <div className="flex justify-between items-center mb-8 relative z-10">
          <h3 className="text-xl font-bold text-gray-800 flex items-center">
            <div className="p-2 bg-gradient-to-br from-primary/10 to-primary/20 rounded-xl mr-3 shadow-inner">
              <Bell size={20} className="text-primary drop-shadow-sm" />
            </div>
            Recent Alerts
          </h3>
          <button
            onClick={() => navigate('/alerts')}
            className="group px-4 py-2 bg-gradient-to-r from-primary/90 to-primary text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 flex items-center font-medium"
          >
            <span className="text-sm">View All</span>
            <ArrowRight size={16} className="ml-2 transition-transform group-hover:translate-x-1" />
          </button>
        </div>

        {/* Alerts list */}
        <div className="space-y-4 relative z-10">
          {recentAlerts.map((alert, index) => (
            <div
              key={alert.id}
              className={`group relative p-5 rounded-xl border-2 ${getAlertBgStyle(alert.type)}
                         shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1
                         backdrop-blur-sm overflow-hidden`}
              style={{
                boxShadow: `
                  0 4px 6px -1px rgba(0, 0, 0, 0.1),
                  0 2px 4px -1px rgba(0, 0, 0, 0.06),
                  inset 0 1px 0 rgba(255, 255, 255, 0.1)
                `
              }}
            >
              {/* Inner glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-transparent to-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              {/* Top edge highlight */}
              <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/40 to-transparent"></div>

              <div className="flex items-start relative z-10">
                {/* Icon container with 3D effect */}
                <div className="mr-4 mt-1">
                  <div className="p-2 bg-white/60 rounded-lg shadow-inner backdrop-blur-sm border border-white/20">
                    {getAlertIcon(alert.type)}
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-semibold text-gray-900 mb-1 leading-relaxed">
                    {alert.title}
                  </h4>
                  <p className="text-xs text-gray-700 mb-2 leading-relaxed">
                    {alert.message}
                  </p>
                  <div className="flex items-center justify-between">
                    <p className="text-xs text-gray-600 font-medium">
                      {formatDistanceToNow(new Date(alert.createdAt), { addSuffix: true })}
                    </p>
                    {!alert.read && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold
                                     bg-gradient-to-r from-primary to-primary/80 text-white shadow-lg
                                     transform hover:scale-105 transition-transform duration-200">
                        New
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}

          {recentAlerts.length === 0 && (
            <div className="text-center py-12 relative">
              <div className="p-6 bg-gradient-to-br from-gray-50 to-white rounded-2xl shadow-inner border border-gray-100 inline-block">
                <Bell size={32} className="mx-auto text-gray-400 mb-3 drop-shadow-sm" />
                <p className="text-gray-500 font-medium">No alerts found</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Bottom shadow for elevation effect */}
      <div className="absolute -bottom-2 left-4 right-4 h-4 bg-gray-900/10 rounded-2xl blur-lg -z-10"></div>
    </div>
  );
};

export default RecentAlerts;