import express from 'express';
import {
  getUserSettings,
  updateUserSettings,
  resetUserSettings,
  getNotificationSettings,
  updateNotificationSettings,
  getUserProfile,
  updateUserProfile,
} from '../controllers/userSettings.controller';
import { protect } from '../middleware/auth.middleware';

const router = express.Router();

// Apply protection to all routes
router.use(protect);

// Routes
router.route('/')
  .get(getUserSettings)
  .put(updateUserSettings);

router.route('/profile')
  .get(getUserProfile)
  .put(updateUserProfile);

router.route('/notifications')
  .get(getNotificationSettings)
  .put(updateNotificationSettings);

router.route('/reset')
  .post(resetUserSettings);

export default router;
