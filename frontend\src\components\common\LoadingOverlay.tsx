import React from 'react';
import { useAtom } from 'jotai';
import { isDataLoadingAtom } from '../../store';
import { Loader2 } from 'lucide-react';

const LoadingOverlay: React.FC = () => {
  const [isDataLoading] = useAtom(isDataLoadingAtom);

  if (!isDataLoading) return null;

  return (
    <div className="fixed inset-0 bg-black/30 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="text-center">
        {/* Simple loading spinner */}
        <div className="w-12 h-12 mx-auto mb-4">
          <Loader2 className="w-12 h-12 text-white animate-spin" />
        </div>

        {/* Simple loading text */}
        <p className="text-white text-lg font-medium">
          Loading...
        </p>
      </div>
    </div>
  );
};

export default LoadingOverlay;
