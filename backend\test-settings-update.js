const axios = require('axios');

const BASE_URL = 'http://localhost:5002';

async function testSettingsUpdate() {
  try {
    console.log('🧪 Testing Settings Update Functionality...\n');

    // Step 1: Login to get a token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'password'
    });

    if (!loginResponse.data.success) {
      throw new Error('Login failed: ' + loginResponse.data.message);
    }

    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');

    // Step 2: Get current user profile
    console.log('\n2. Getting current user profile...');
    const profileResponse = await axios.get(`${BASE_URL}/api/settings/profile`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (!profileResponse.data.success) {
      throw new Error('Get profile failed: ' + profileResponse.data.message);
    }

    const currentProfile = profileResponse.data.data;
    console.log('✅ Current profile retrieved');
    console.log('📊 Current profile:', currentProfile);

    // Step 3: Update the profile
    console.log('\n3. Updating profile...');
    const updateData = {
      name: currentProfile.name + ' (Settings Updated)',
      email: currentProfile.email
    };

    const updateResponse = await axios.put(`${BASE_URL}/api/settings/profile`, updateData, {
      headers: { 
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!updateResponse.data.success) {
      throw new Error('Profile update failed: ' + updateResponse.data.message);
    }

    console.log('✅ Profile updated successfully!');
    console.log('📊 Updated profile:', updateResponse.data.data);

    // Step 4: Verify the update
    console.log('\n4. Verifying profile update...');
    const verifyResponse = await axios.get(`${BASE_URL}/api/settings/profile`, {
      headers: { Authorization: `Bearer ${token}` }
    });

    const updatedProfile = verifyResponse.data.data;
    if (updatedProfile && updatedProfile.name === updateData.name) {
      console.log('✅ Profile update verified successfully!');
      console.log(`📝 Name changed from "${currentProfile.name}" to "${updatedProfile.name}"`);
    } else {
      console.log('❌ Profile update verification failed');
    }

    console.log('\n🎉 Settings update functionality is working correctly!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('📊 Response data:', error.response.data);
      console.error('📊 Response status:', error.response.status);
    }
    process.exit(1);
  }
}

// Run the test
testSettingsUpdate();
