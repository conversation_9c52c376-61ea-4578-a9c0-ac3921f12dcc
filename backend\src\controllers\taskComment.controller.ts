import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

// @desc    Get comments for a task
// @route   GET /api/tasks/:taskId/comments
// @access  Private
export const getTaskComments = async (req: Request, res: Response): Promise<void> => {
  try {
    const { taskId } = req.params;

    // Check if this is a MOM task (virtual task)
    if (taskId.startsWith('MOM-')) {
      const momId = taskId.replace('MOM-', '');

      // Check if M<PERSON> exists and user has access
      const mom = await prisma.mom.findUnique({
        where: { id: momId },
        include: {
          project: {
            select: {
              id: true,
              department: true
            }
          }
        }
      });

      if (!mom) {
        res.status(404).json({
          success: false,
          message: 'MOM not found',
        });
        return;
      }

      // For MOM tasks, return empty comments array since MOMs don't have comments yet
      // In the future, you could implement MOM-specific comments
      res.status(200).json({
        success: true,
        data: [],
      });
      return;
    }

    // Check if task exists and user has access
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        project: {
          select: {
            id: true,
            department: true
          }
        },
        user: {
          select: {
            id: true,
            department: true
          }
        }
      }
    });

    if (!task) {
      res.status(404).json({
        success: false,
        message: 'Task not found',
      });
      return;
    }

    // Check user permissions
    const user = req.user;
    const hasAccess =
      user.role === 'DIRECTOR' ||
      user.role === 'PROJECT_MANAGER' ||
      (user.role === 'TEAM_LEAD' && (
        task.project.department === user.department || // Project in same department
        task.assigneeId === user.id || // Assigned to task
        task.user.department === user.department // Task assignee in same department
      )) ||
      (user.role === 'ENGINEER' && task.assigneeId === user.id);

    if (!hasAccess) {
      res.status(403).json({
        success: false,
        message: 'Access denied',
      });
      return;
    }

    const comments = await prisma.taskcomment.findMany({
      where: { taskId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    res.status(200).json({
      success: true,
      count: comments.length,
      data: comments,
    });
  } catch (error) {
    console.error('Get task comments error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create a comment for a task
// @route   POST /api/tasks/:taskId/comments
// @access  Private
export const createTaskComment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { taskId } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    if (!content || !content.trim()) {
      res.status(400).json({
        success: false,
        message: 'Comment content is required',
      });
      return;
    }

    // Check if this is a MOM task (virtual task)
    if (taskId.startsWith('MOM-')) {
      res.status(400).json({
        success: false,
        message: 'Comments are not supported for MOM tasks',
      });
      return;
    }

    // Check if task exists and user has access
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        project: {
          select: {
            id: true,
            department: true
          }
        },
        user: {
          select: {
            id: true,
            department: true
          }
        }
      }
    });

    if (!task) {
      res.status(404).json({
        success: false,
        message: 'Task not found',
      });
      return;
    }

    // Check user permissions
    const user = req.user;
    const hasAccess =
      user.role === 'DIRECTOR' ||
      user.role === 'PROJECT_MANAGER' ||
      (user.role === 'TEAM_LEAD' && (
        task.project.department === user.department || // Project in same department
        task.assigneeId === user.id || // Assigned to task
        task.user.department === user.department // Task assignee in same department
      )) ||
      (user.role === 'ENGINEER' && task.assigneeId === user.id);

    if (!hasAccess) {
      res.status(403).json({
        success: false,
        message: 'Access denied',
      });
      return;
    }

    const comment = await prisma.taskcomment.create({
      data: {
        id: uuidv4(),
        taskId,
        userId,
        content: content.trim(),
        updatedAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: comment,
    });
  } catch (error) {
    console.error('Create task comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update a task comment
// @route   PUT /api/tasks/:taskId/comments/:commentId
// @access  Private
export const updateTaskComment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { taskId, commentId } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    if (!content || !content.trim()) {
      res.status(400).json({
        success: false,
        message: 'Comment content is required',
      });
      return;
    }

    // Check if this is a MOM task (virtual task)
    if (taskId.startsWith('MOM-')) {
      res.status(400).json({
        success: false,
        message: 'Comments are not supported for MOM tasks',
      });
      return;
    }

    // Check if comment exists and belongs to user
    const comment = await prisma.taskcomment.findUnique({
      where: { id: commentId },
      include: {
        task: true
      }
    });

    if (!comment) {
      res.status(404).json({
        success: false,
        message: 'Comment not found',
      });
      return;
    }

    if (comment.taskId !== taskId) {
      res.status(400).json({
        success: false,
        message: 'Comment does not belong to this task',
      });
      return;
    }

    if (comment.userId !== userId) {
      res.status(403).json({
        success: false,
        message: 'You can only edit your own comments',
      });
      return;
    }

    const updatedComment = await prisma.taskcomment.update({
      where: { id: commentId },
      data: {
        content: content.trim(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    res.status(200).json({
      success: true,
      data: updatedComment,
    });
  } catch (error) {
    console.error('Update task comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete a task comment
// @route   DELETE /api/tasks/:taskId/comments/:commentId
// @access  Private
export const deleteTaskComment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { taskId, commentId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Check if this is a MOM task (virtual task)
    if (taskId.startsWith('MOM-')) {
      res.status(400).json({
        success: false,
        message: 'Comments are not supported for MOM tasks',
      });
      return;
    }

    // Check if comment exists
    const comment = await prisma.taskcomment.findUnique({
      where: { id: commentId },
      include: {
        task: true
      }
    });

    if (!comment) {
      res.status(404).json({
        success: false,
        message: 'Comment not found',
      });
      return;
    }

    if (comment.taskId !== taskId) {
      res.status(400).json({
        success: false,
        message: 'Comment does not belong to this task',
      });
      return;
    }

    // Check permissions - user can delete their own comments, or admins can delete any
    const canDelete =
      comment.userId === userId ||
      userRole === 'DIRECTOR' ||
      userRole === 'PROJECT_MANAGER';

    if (!canDelete) {
      res.status(403).json({
        success: false,
        message: 'You can only delete your own comments',
      });
      return;
    }

    await prisma.taskcomment.delete({
      where: { id: commentId }
    });

    res.status(200).json({
      success: true,
      message: 'Comment deleted successfully',
    });
  } catch (error) {
    console.error('Delete task comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
