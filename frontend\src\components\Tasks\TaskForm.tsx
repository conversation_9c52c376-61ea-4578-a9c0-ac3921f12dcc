import React, { useState } from 'react';
import { useAtom } from 'jotai';
import { useForm, useFieldArray, SubmitHandler } from 'react-hook-form';
import {
  customersAtom,
  departmentsAtom,
  engineersAtom,
  projectsAtom,
  currentUserAtom
} from '../../store';
import { v4 as uuidv4 } from 'uuid';
import { Project, Task, Subtask, TaskStatus, TaskAssigneeType, Customer } from '../../types';
import { Plus, Trash2, Save, XCircle, PlusCircle } from 'lucide-react';
import CustomerDialog from '../Customers/CustomerDialog';

type FormValues = {
  projectName: string;
  projectCode: string;
  customer: string;
  poNumber: string;
  poDate: string;
  startDate: string;
  endDate: string;
  department: string;
  tasks: {
    name: string;
    description: string;
    assigneeId: string;
    startDate: string;
    endDate: string;
    status: TaskStatus;
    subtasks: {
      name: string;
      description: string;
      assigneeId: string;
      assigneeType: TaskAssigneeType;
      startDate: string;
      endDate: string;
      status: TaskStatus;
    }[];
  }[];
};

const TaskForm: React.FC = () => {
  const [customers, setCustomers] = useAtom(customersAtom);
  const [departments] = useAtom(departmentsAtom);
  const [engineers] = useAtom(engineersAtom);
  const [, setProjects] = useAtom(projectsAtom);
  const [currentUser] = useAtom(currentUserAtom);

  // Filter users for task assignment - only team leads
  const teamLeads = engineers.filter(user => user.role === 'TEAM_LEAD');
  // Filter users for subtask assignment - only engineers
  const engineersOnly = engineers.filter(user => user.role === 'ENGINEER');

  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState(false);
  const [newCustomerName, setNewCustomerName] = useState('');

  const {
    register,
    handleSubmit,
    control,
    watch,
    reset,
    formState: { errors }
  } = useForm<FormValues>({
    defaultValues: {
      tasks: [
        {
          name: '',
          description: '',
          assigneeId: '',
          startDate: '',
          endDate: '',
          status: TaskStatus.NOT_STARTED,
          subtasks: []
        }
      ]
    }
  });

  const { fields: taskFields, append: appendTask, remove: removeTask } = useFieldArray({
    control,
    name: "tasks"
  });

  const watchedTasks = watch("tasks");

  const onSubmit: SubmitHandler<FormValues> = (data) => {
    setIsSubmitting(true);

    // Simulate API call delay
    setTimeout(() => {
      try {
        // Create a new project with tasks and subtasks
        const newProject: Project = {
          id: uuidv4(),
          name: data.projectName,
          code: data.projectCode,
          customerId: data.customer, // This is now the customer ID
          poNumber: data.poNumber,
          poDate: data.poDate,
          startDate: data.startDate,
          endDate: data.endDate,
          department: data.department,
          tasks: [],
          status: TaskStatus.NOT_STARTED,
          createdBy: currentUser?.id || '',
          createdAt: new Date().toISOString()
        };

        // Process tasks and subtasks
        data.tasks.forEach((taskData) => {
          const newTask: Task = {
            id: uuidv4(),
            projectId: newProject.id,
            name: taskData.name,
            description: taskData.description,
            assigneeId: taskData.assigneeId,
            assigneeType: TaskAssigneeType.ENGINEER,
            department: newProject.department,
            startDate: taskData.startDate,
            endDate: taskData.endDate,
            status: taskData.status,
            subtasks: [],
            createdBy: currentUser?.id || '',
            createdAt: new Date().toISOString()
          };

          // Process subtasks
          if (taskData.subtasks && taskData.subtasks.length > 0) {
            taskData.subtasks.forEach((subtaskData) => {
              const newSubtask: Subtask = {
                id: uuidv4(),
                taskId: newTask.id,
                name: subtaskData.name,
                description: subtaskData.description,
                assigneeId: subtaskData.assigneeId,
                assigneeType: subtaskData.assigneeType,
                startDate: subtaskData.startDate,
                endDate: subtaskData.endDate,
                status: subtaskData.status,
                totalTime: calculateTotalHours(subtaskData.startDate, subtaskData.endDate),
                createdBy: currentUser?.id || '',
                createdAt: new Date().toISOString()
              };

              newTask.subtasks.push(newSubtask);
            });
          }

          newProject.tasks.push(newTask);
        });

        // Add the new project to the state
        setProjects((prevProjects) => [...prevProjects, newProject]);

        // Show success message
        setSuccess('Project and tasks created successfully!');

        // Reset the form after submission
        reset();

        // Clear success message after 5 seconds
        setTimeout(() => {
          setSuccess(null);
        }, 5002);
      } catch (error) {
        console.error('Error creating project and tasks:', error);
      } finally {
        setIsSubmitting(false);
      }
    }, 1000);
  };

  // Handler for adding a new customer
  const handleAddCustomer = (newCustomer: Customer) => {
    setCustomers((prevCustomers) => [...prevCustomers, newCustomer]);

    // Auto-select the newly created customer in the form
    const customerField = document.getElementById('customer') as HTMLSelectElement;
    if (customerField) {
      customerField.value = newCustomer.id;
      // Trigger change event to update react-hook-form
      const event = new Event('change', { bubbles: true });
      customerField.dispatchEvent(event);
    }
  };

  // Handler for opening the customer dialog
  const handleOpenCustomerDialog = () => {
    setIsCustomerDialogOpen(true);
  };

  // Helper function to calculate hours between dates
  const calculateTotalHours = (startDate: string, endDate: string): number => {
    if (!startDate || !endDate) return 0;

    const start = new Date(startDate);
    const end = new Date(endDate);

    // Calculate difference in hours
    const diffInMs = end.getTime() - start.getTime();
    const diffInHours = diffInMs / (1000 * 60 * 60);

    // Return an estimated number of working hours (8 hours per day)
    const workingDays = Math.ceil(diffInHours / 24);
    return workingDays * 8;
  };

  return (
    <div className="card p-6">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">Create New Project & Tasks</h2>

      {success && (
        <div className="bg-success/10 text-success-dark p-4 rounded-md flex items-center justify-between mb-6 fade-in">
          <div className="flex items-center">
            <CheckCircle size={18} className="mr-2" />
            <span>{success}</span>
          </div>
          <button
            onClick={() => setSuccess(null)}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle size={18} />
          </button>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Project Details</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label htmlFor="projectName" className="form-label">Project Name</label>
              <input
                id="projectName"
                type="text"
                className={`form-input ${errors.projectName ? 'border-error' : ''}`}
                placeholder="Enter project name"
                {...register("projectName", { required: "Project name is required" })}
              />
              {errors.projectName && (
                <p className="mt-1 text-xs text-error">{errors.projectName.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="projectCode" className="form-label">Project Code</label>
              <input
                id="projectCode"
                type="text"
                className={`form-input ${errors.projectCode ? 'border-error' : ''}`}
                placeholder="Enter project code"
                {...register("projectCode", { required: "Project code is required" })}
              />
              {errors.projectCode && (
                <p className="mt-1 text-xs text-error">{errors.projectCode.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="customer" className="form-label">Customer</label>
              <div className="flex space-x-2">
                <div className="flex-1">
                  <select
                    id="customer"
                    className={`form-select ${errors.customer ? 'border-error' : ''}`}
                    {...register("customer", { required: "Customer is required" })}
                  >
                    <option value="">Select Customer</option>
                    {customers.map((customer) => (
                      <option key={customer.id} value={customer.id}>
                        {customer.name}
                      </option>
                    ))}
                  </select>
                </div>
                <button
                  type="button"
                  className="btn btn-outline flex items-center py-2"
                  onClick={handleOpenCustomerDialog}
                >
                  <PlusCircle size={16} className="mr-1" />
                  New
                </button>
              </div>
              {errors.customer && (
                <p className="mt-1 text-xs text-error">{errors.customer.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="poNumber" className="form-label">PO Number</label>
              <input
                id="poNumber"
                type="text"
                className={`form-input ${errors.poNumber ? 'border-error' : ''}`}
                placeholder="Enter PO number"
                {...register("poNumber", { required: "PO number is required" })}
              />
              {errors.poNumber && (
                <p className="mt-1 text-xs text-error">{errors.poNumber.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="poDate" className="form-label">PO Date</label>
              <input
                id="poDate"
                type="date"
                className={`form-input ${errors.poDate ? 'border-error' : ''}`}
                {...register("poDate", { required: "PO date is required" })}
              />
              {errors.poDate && (
                <p className="mt-1 text-xs text-error">{errors.poDate.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="department" className="form-label">Department</label>
              <select
                id="department"
                className={`form-select ${errors.department ? 'border-error' : ''}`}
                {...register("department", { required: "Department is required" })}
              >
                <option value="">Select Department</option>
                {departments.map((department) => (
                  <option key={department.id} value={department.name}>
                    {department.name}
                  </option>
                ))}
              </select>
              {errors.department && (
                <p className="mt-1 text-xs text-error">{errors.department.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="startDate" className="form-label">Project Start Date</label>
              <input
                id="startDate"
                type="date"
                className={`form-input ${errors.startDate ? 'border-error' : ''}`}
                {...register("startDate", { required: "Start date is required" })}
              />
              {errors.startDate && (
                <p className="mt-1 text-xs text-error">{errors.startDate.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="endDate" className="form-label">Project End Date</label>
              <input
                id="endDate"
                type="date"
                className={`form-input ${errors.endDate ? 'border-error' : ''}`}
                {...register("endDate", { required: "End date is required" })}
              />
              {errors.endDate && (
                <p className="mt-1 text-xs text-error">{errors.endDate.message}</p>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-800">Tasks</h3>
            <button
              type="button"
              className="btn btn-outline flex items-center"
              onClick={() => appendTask({
                name: '',
                description: '',
                assigneeId: '',
                startDate: '',
                endDate: '',
                status: TaskStatus.NOT_STARTED,
                subtasks: []
              })}
            >
              <Plus size={18} className="mr-1" />
              Add Task
            </button>
          </div>

          {taskFields.map((taskField, taskIndex) => {
            // Get the subtasks for this task from the useFieldArray
            const subtasksFieldArray = useFieldArray({
              control,
              name: `tasks.${taskIndex}.subtasks`
            });

            return (
              <div
                key={taskField.id}
                className="border border-gray-200 rounded-md p-4 space-y-4 relative"
              >
                <div className="absolute top-4 right-4">
                  {taskFields.length > 1 && (
                    <button
                      type="button"
                      className="text-gray-400 hover:text-error transition-colors"
                      onClick={() => removeTask(taskIndex)}
                    >
                      <Trash2 size={18} />
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="form-label">Task Name</label>
                    <input
                      type="text"
                      className={`form-input ${errors.tasks?.[taskIndex]?.name ? 'border-error' : ''}`}
                      placeholder="Enter task name"
                      {...register(`tasks.${taskIndex}.name`, { required: "Task name is required" })}
                    />
                    {errors.tasks?.[taskIndex]?.name && (
                      <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.name?.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="form-label">Assignee</label>
                    <select
                      className={`form-select ${errors.tasks?.[taskIndex]?.assigneeId ? 'border-error' : ''}`}
                      {...register(`tasks.${taskIndex}.assigneeId`, { required: "Assignee is required" })}
                    >
                      <option value="">Select Team Lead</option>
                      {teamLeads.map((teamLead) => (
                        <option key={teamLead.id} value={teamLead.id}>
                          {teamLead.name} ({teamLead.department})
                        </option>
                      ))}
                    </select>
                    {errors.tasks?.[taskIndex]?.assigneeId && (
                      <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.assigneeId?.message}</p>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="form-label">Description</label>
                    <textarea
                      className={`form-input ${errors.tasks?.[taskIndex]?.description ? 'border-error' : ''}`}
                      rows={2}
                      placeholder="Enter task description"
                      {...register(`tasks.${taskIndex}.description`, { required: "Description is required" })}
                    />
                    {errors.tasks?.[taskIndex]?.description && (
                      <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.description?.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="form-label">Start Date</label>
                    <input
                      type="date"
                      className={`form-input ${errors.tasks?.[taskIndex]?.startDate ? 'border-error' : ''}`}
                      {...register(`tasks.${taskIndex}.startDate`, { required: "Start date is required" })}
                    />
                    {errors.tasks?.[taskIndex]?.startDate && (
                      <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.startDate?.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="form-label">End Date</label>
                    <input
                      type="date"
                      className={`form-input ${errors.tasks?.[taskIndex]?.endDate ? 'border-error' : ''}`}
                      {...register(`tasks.${taskIndex}.endDate`, { required: "End date is required" })}
                    />
                    {errors.tasks?.[taskIndex]?.endDate && (
                      <p className="mt-1 text-xs text-error">{errors.tasks?.[taskIndex]?.endDate?.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="form-label">Status</label>
                    <select
                      className="form-select"
                      {...register(`tasks.${taskIndex}.status`)}
                    >
                      {Object.values(TaskStatus).map((status) => (
                        <option key={status} value={status}>
                          {status.replace(/_/g, ' ')}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Subtasks Section */}
                <div className="mt-6 bg-gray-50 p-4 rounded border border-gray-100">
                  <div className="flex justify-between items-center mb-3">
                    <h4 className="text-md font-medium text-gray-700">Subtasks</h4>
                    <button
                      type="button"
                      className="btn btn-outline py-1 px-2 text-sm flex items-center"
                      onClick={() => subtasksFieldArray.append({
                        name: '',
                        description: '',
                        assigneeId: '',
                        assigneeType: TaskAssigneeType.ENGINEER,
                        startDate: '',
                        endDate: '',
                        status: TaskStatus.NOT_STARTED
                      })}
                    >
                      <Plus size={16} className="mr-1" />
                      Add Subtask
                    </button>
                  </div>

                  {subtasksFieldArray.fields.length === 0 && (
                    <p className="text-sm text-gray-500 italic">No subtasks added yet</p>
                  )}

                  {subtasksFieldArray.fields.map((subtaskField, subtaskIndex) => (
                    <div
                      key={subtaskField.id}
                      className="border border-gray-200 rounded p-3 mb-3 bg-white relative"
                    >
                      <button
                        type="button"
                        className="absolute top-3 right-3 text-gray-400 hover:text-error transition-colors"
                        onClick={() => subtasksFieldArray.remove(subtaskIndex)}
                      >
                        <Trash2 size={16} />
                      </button>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label className="form-label text-sm">Subtask Name</label>
                          <input
                            type="text"
                            className="form-input text-sm"
                            placeholder="Enter subtask name"
                            {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.name`, { required: true })}
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <label className="form-label text-sm">Assignee Type</label>
                            <select
                              className="form-select text-sm"
                              {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.assigneeType`)}
                            >
                              {Object.values(TaskAssigneeType).map((type) => (
                                <option key={type} value={type}>
                                  {type.replace(/_/g, ' ')}
                                </option>
                              ))}
                            </select>
                          </div>

                          <div>
                            <label className="form-label text-sm">Assignee</label>
                            <select
                              className="form-select text-sm"
                              {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.assigneeId`, { required: true })}
                            >
                              <option value="">Select Engineer</option>
                              {engineersOnly.map((engineer) => (
                                <option key={engineer.id} value={engineer.id}>
                                  {engineer.name} ({engineer.department})
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>

                        <div className="md:col-span-2">
                          <label className="form-label text-sm">Description</label>
                          <textarea
                            className="form-input text-sm"
                            rows={2}
                            placeholder="Enter subtask description"
                            {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.description`, { required: true })}
                          />
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <label className="form-label text-sm">Start Date</label>
                            <input
                              type="date"
                              className="form-input text-sm"
                              {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.startDate`, { required: true })}
                            />
                          </div>

                          <div>
                            <label className="form-label text-sm">End Date</label>
                            <input
                              type="date"
                              className="form-input text-sm"
                              {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.endDate`, { required: true })}
                            />
                          </div>
                        </div>

                        <div>
                          <label className="form-label text-sm">Status</label>
                          <select
                            className="form-select text-sm"
                            {...register(`tasks.${taskIndex}.subtasks.${subtaskIndex}.status`)}
                          >
                            {Object.values(TaskStatus).map((status) => (
                              <option key={status} value={status}>
                                {status.replace(/_/g, ' ')}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>

        <div className="flex justify-end mt-6">
          <button
            type="submit"
            className="btn btn-primary flex items-center"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </span>
            ) : (
              <span className="flex items-center">
                <Save size={18} className="mr-2" />
                Save Project
              </span>
            )}
          </button>
        </div>
      </form>

      {/* Customer Dialog */}
      <CustomerDialog
        isOpen={isCustomerDialogOpen}
        onClose={() => setIsCustomerDialogOpen(false)}
        onSave={handleAddCustomer}
        initialData={{ name: newCustomerName }}
      />
    </div>
  );
};

export default TaskForm;