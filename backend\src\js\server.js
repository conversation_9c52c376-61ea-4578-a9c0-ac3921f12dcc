const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { PrismaClient } = require('@prisma/client');
const { v4: uuidv4 } = require('uuid');

// Load environment variables
const path = require('path');
dotenv.config({ path: path.join(__dirname, '../../.env') });

// Debug: Log environment variables
console.log('Environment variables loaded:');
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Not set');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? 'Set' : 'Not set');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 5002;

// Initialize Prisma client
const prisma = new PrismaClient();

// Log startup information
console.log('Server starting up');

// Middleware
app.use(cors({
  origin: (origin, callback) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    return callback(null, true); // Accept all origins
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());

// Middleware to handle date format conversion
app.use((req, res, next) => {
  // Convert ISO date strings to YYYY-MM-DD format for any request body
  if (req.body) {
    const convertDates = (obj) => {
      if (!obj || typeof obj !== 'object') return;

      Object.keys(obj).forEach(key => {
        const value = obj[key];

        // Check if the value is a date string in ISO format
        if (typeof value === 'string' &&
            /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/.test(value)) {
          // Convert to YYYY-MM-DD format
          obj[key] = value.split('T')[0];
        } else if (typeof value === 'object') {
          // Recursively process nested objects and arrays
          convertDates(value);
        }
      });
    };

    convertDates(req.body);
  }
  next();
});

// Add a simple auth middleware for development
const simpleAuth = async (req, res, next) => {
  // Get the token from the authorization header
  const authHeader = req.headers.authorization;

  // For development, we'll be more lenient with auth
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    console.warn('Auth header missing or invalid');
    return res.status(401).json({
      success: false,
      message: 'Not authorized to access this route',
    });
  }

  // Get the token
  const token = authHeader.split(' ')[1];

  try {
    // Verify the token
    const jwt = require('jsonwebtoken');
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    console.error('Token verification failed:', error);
    return res.status(401).json({
      success: false,
      message: 'Invalid token',
    });
  }
};

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();

  // Log incoming requests immediately
  console.log(`🌐 INCOMING REQUEST: ${req.method} ${req.originalUrl}`);
  if (req.method === 'POST') {
    console.log(`🌐 POST Body:`, req.body);
  }

  // Log the response when it's sent
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.originalUrl} ${res.statusCode} ${duration}ms`);
  });

  next();
});

// Root route
app.get('/', (req, res) => {
  res.send('Project Management API is running');
});

// API health check endpoint
app.get('/api', (req, res) => {
  console.log('🔍 API health check called');
  res.status(200).json({
    success: true,
    message: 'API is running',
    timestamp: new Date().toISOString()
  });
});

// Test endpoint to check if requests are reaching the backend
app.post('/api/test', (req, res) => {
  console.log('🧪 TEST ENDPOINT CALLED!');
  console.log('🧪 Request body:', req.body);
  res.status(200).json({
    success: true,
    message: 'Test endpoint working',
    receivedData: req.body
  });
});

// Database test endpoint
app.get('/api/db-test', async (req, res) => {
  console.log('🗄️ DATABASE TEST CALLED!');
  try {
    // Test database connection
    const customerCount = await prisma.customer.count();
    const userCount = await prisma.user.count();

    console.log('🗄️ Database connection successful');
    console.log('🗄️ Customer count:', customerCount);
    console.log('🗄️ User count:', userCount);

    res.status(200).json({
      success: true,
      message: 'Database connection working',
      data: {
        customerCount,
        userCount
      }
    });
  } catch (error) {
    console.error('🗄️ Database connection failed:', error);
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: error.message
    });
  }
});

// Auth routes
app.post('/api/auth/register', async (req, res) => {
  try {
    const { email, password, name, role, department } = req.body;

    // Check if user already exists
    const userExists = await prisma.user.findUnique({
      where: { email },
    });

    if (userExists) {
      return res.status(400).json({
        success: false,
        message: 'User already exists',
      });
    }

    // Create user (in a real app, you would hash the password)
    const user = await prisma.user.create({
      data: {
        email,
        password, // In a real app, this would be hashed
        name,
        role,
        department,
      },
    });

    res.status(201).json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department,
      },
    });
  } catch (error) {
    console.error('AUTH', 'Register error', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
      });
    }

    // For simplicity in development, we'll accept plain text password comparison
    // In production, you should use bcrypt.compare(password, user.password)
    if (password !== 'password') { // Hardcoded for testing
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
      });
    }

    // Generate a JWT token
    const jwt = require('jsonwebtoken');
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
    const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '30d';

    const token = jwt.sign(
      { id: user.id, role: user.role },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    // Check if user has passwordChanged field
    let passwordChanged = "Y"; // Default to Y to avoid repeated password change prompts
    try {
      // Try to access the passwordChanged field
      if (user.passwordChanged) {
        passwordChanged = user.passwordChanged;
      }

      console.info('AUTH', `User login: ${user.email}`, {
        passwordChanged: passwordChanged,
        skipDuplicateCheck: true
      });

    } catch (error) {
      console.warn('Could not access passwordChanged field:', error.message);
      // Default to "Y" if the field doesn't exist to avoid repeated password change prompts
      passwordChanged = "Y";
    }

    res.status(200).json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department,
        profileImage: user.profileImage,
        token: token,
        passwordChanged: passwordChanged, // Include passwordChanged status
      },
    });
  } catch (error) {
    console.error('AUTH', 'Login error', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Auth - Change password
app.post('/api/auth/change-password', async (req, res) => {
  try {
    // Get the token from the authorization header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized',
      });
    }

    // Get the token
    const token = authHeader.split(' ')[1];

    // Verify the token
    const jwt = require('jsonwebtoken');
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

    let userId;
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      userId = decoded.id;
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token',
      });
    }

    // Get the current and new passwords from the request body
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Please provide both current and new passwords',
      });
    }

    // Validate new password strength
    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'New password must be at least 6 characters long',
      });
    }

    // Get the user
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // For simplicity in development, we'll accept plain text password comparison
    // In production, you should use bcrypt.compare(currentPassword, user.password)
    if (currentPassword !== 'password') { // Hardcoded for testing
      return res.status(401).json({
        success: false,
        message: 'Current password is incorrect',
      });
    }

    // Hash the new password
    const bcrypt = require('bcryptjs');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update the user's password and set passwordChanged to "Y"
    try {
      await prisma.user.update({
        where: { id: userId },
        data: {
          password: hashedPassword,
          passwordChanged: "Y",
        },
      });
    } catch (error) {
      // If there's an error with the passwordChanged field, try without it
      if (error.message && error.message.includes('passwordChanged')) {
        console.warn('Updating user without passwordChanged field');
        await prisma.user.update({
          where: { id: userId },
          data: {
            password: hashedPassword,
          },
        });
      } else {
        // If it's a different error, rethrow it
        throw error;
      }
    }

    res.status(200).json({
      success: true,
      message: 'Password changed successfully',
    });
  } catch (error) {
    console.error('AUTH', 'Change password error', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Auth - Get current user
app.get('/api/auth/me', async (req, res) => {
  try {
    // Get the token from the authorization header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.warn('Auth header missing or invalid');
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route',
      });
    }

    // Get the token
    const token = authHeader.split(' ')[1];

    // Verify the token
    const jwt = require('jsonwebtoken');
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

    let userId;

    try {
      // Verify the token
      const decoded = jwt.verify(token, JWT_SECRET);
      userId = decoded.id;
    } catch (error) {
      console.error('Token verification failed:', error);
      return res.status(401).json({
        success: false,
        message: 'Invalid token',
      });
    }

    // Try to fetch user with passwordChanged field
    let user;
    try {
      user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          department: true,
          profileImage: true,
          passwordChanged: true, // Include passwordChanged status
        },
      });

      // Log the user data for debugging
      console.info('AUTH', `User data fetched: ${user.id}`, {
        passwordChanged: user.passwordChanged || 'N',
        skipDuplicateCheck: true
      });

    } catch (error) {
      console.error('Error fetching user with passwordChanged field:', error);

      // If there's an error (likely because passwordChanged doesn't exist), fetch without it
      user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          department: true,
          profileImage: true,
        },
      });

      // Add default passwordChanged value
      if (user) {
        user.passwordChanged = "Y"; // Set to Y to avoid repeated password change prompts
        console.info('AUTH', `Setting default passwordChanged to Y for user: ${user.id}`);
      }
    }

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error('Get current user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// User routes
app.get('/api/users', simpleAuth, async (req, res) => {
  try {
    // Try to fetch users with passwordChanged field
    let users;
    try {
      users = await prisma.user.findMany({
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          department: true,
          profileImage: true,
          code: true,
          skills: true,
          joinDate: true,
          createdAt: true,
          passwordChanged: true, // Include passwordChanged status
        },
      });
    } catch (error) {
      console.error('Error fetching users with passwordChanged field:', error);

      // If there's an error (likely because passwordChanged doesn't exist), fetch without it
      users = await prisma.user.findMany({
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          department: true,
          profileImage: true,
          code: true,
          skills: true,
          joinDate: true,
          createdAt: true,
        },
      });

      // Add default passwordChanged value to each user
      users = users.map(user => ({
        ...user,
        passwordChanged: "N"
      }));
    }

    res.status(200).json({
      success: true,
      count: users.length,
      data: users,
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.post('/api/users', simpleAuth, async (req, res) => {
  try {
    const {
      email,
      password,
      name,
      role,
      department,
      profileImage,
      code,
      skills,
      joinDate
    } = req.body;

    // Validate required fields
    if (!email || !password || !name || !role || !department) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields: email, password, name, role, department',
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a valid email address',
      });
    }

    // Validate password strength
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 6 characters long',
      });
    }

    // Check if user already exists
    const userExists = await prisma.user.findUnique({
      where: { email },
    });

    if (userExists) {
      return res.status(400).json({
        success: false,
        message: 'User with this email already exists',
      });
    }

    // Check if code is unique if provided
    if (code) {
      const codeExists = await prisma.user.findUnique({
        where: { code },
      });

      if (codeExists) {
        return res.status(400).json({
          success: false,
          message: 'Employee code already exists',
        });
      }
    }

    // Hash password
    const bcrypt = require('bcryptjs');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    console.log('Creating user with data:', {
      email,
      name,
      role,
      department,
      profileImage: profileImage ? 'Provided' : 'Not provided',
      code: code || 'Not provided',
      skills: skills || 'Not provided',
      joinDate: joinDate ? 'Provided' : 'Not provided',
      password: 'Hashed (not shown)'
    });

    // Create user
    let user;
    try {
      // Generate a unique ID for the user
      const userId = uuidv4();
      const now = new Date();

      // Try to create user with passwordChanged field
      user = await prisma.user.create({
        data: {
          id: userId,
          email,
          password: hashedPassword,
          name,
          role,
          department,
          profileImage,
          code,
          skills,
          joinDate: joinDate ? new Date(joinDate) : null,
          passwordChanged: "N", // Set initial passwordChanged status to "N"
          updatedAt: now, // Add required updatedAt field
        },
      });
    } catch (error) {
      // If there's an error with the passwordChanged field, try without it
      if (error.message && error.message.includes('passwordChanged')) {
        console.warn('Creating user without passwordChanged field');
        const userId = uuidv4();
        const now = new Date();
        user = await prisma.user.create({
          data: {
            id: userId,
            email,
            password: hashedPassword,
            name,
            role,
            department,
            profileImage,
            code,
            skills,
            joinDate: joinDate ? new Date(joinDate) : null,
            updatedAt: now, // Add required updatedAt field
          },
        });

        // Add passwordChanged property manually
        user.passwordChanged = "N";
      } else {
        // If it's a different error, rethrow it
        throw error;
      }
    }

    console.log('User created successfully:', user.id);

    res.status(201).json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department,
        profileImage: user.profileImage,
        code: user.code,
        skills: user.skills,
        joinDate: user.joinDate,
        passwordChanged: user.passwordChanged || "N", // Include passwordChanged status
      },
    });
  } catch (error) {
    console.error('Create user error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: `A user with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during user creation',
    });
  }
});

app.put('/api/users/:id', simpleAuth, async (req, res) => {
  try {
    const {
      email,
      name,
      role,
      department,
      profileImage,
      password,
      code,
      skills,
      joinDate
    } = req.body;

    console.log('Updating user with ID:', req.params.id);
    console.log('Update data:', {
      email: email || 'Not changed',
      name: name || 'Not changed',
      role: role || 'Not changed',
      department: department || 'Not changed',
      profileImage: profileImage ? 'Provided' : 'Not changed',
      code: code || 'Not changed',
      skills: skills || 'Not changed',
      joinDate: joinDate ? 'Provided' : 'Not changed',
      password: password ? 'Provided (hashed)' : 'Not changed'
    });

    // Check if user exists
    const userExists = await prisma.user.findUnique({
      where: { id: req.params.id },
    });

    if (!userExists) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Validate email format if provided
    if (email && email !== userExists.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          success: false,
          message: 'Please provide a valid email address',
        });
      }

      // Check if email is already in use by another user
      const emailExists = await prisma.user.findFirst({
        where: {
          email,
          NOT: { id: req.params.id }
        },
      });

      if (emailExists) {
        return res.status(400).json({
          success: false,
          message: 'Email is already in use by another user',
        });
      }
    }

    // Validate password strength if provided
    if (password && password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 6 characters long',
      });
    }

    // Check if code is unique if provided and changed
    if (code && code !== userExists.code) {
      const codeExists = await prisma.user.findUnique({
        where: { code },
      });

      if (codeExists) {
        return res.status(400).json({
          success: false,
          message: 'Employee code already exists',
        });
      }
    }

    // Prepare update data - only include fields that are provided
    const updateData = {};

    if (email !== undefined) updateData.email = email;
    if (name !== undefined) updateData.name = name;
    if (role !== undefined) updateData.role = role;
    if (department !== undefined) updateData.department = department;
    if (profileImage !== undefined) updateData.profileImage = profileImage;
    if (code !== undefined) updateData.code = code;
    if (skills !== undefined) updateData.skills = skills;
    if (joinDate !== undefined) updateData.joinDate = joinDate ? new Date(joinDate) : null;

    // If password is provided, hash it and set passwordChanged to "Y"
    if (password) {
      const bcrypt = require('bcryptjs');
      const salt = await bcrypt.genSalt(10);
      updateData.password = await bcrypt.hash(password, salt);

      // Try to set passwordChanged field, but don't fail if it doesn't exist
      try {
        updateData.passwordChanged = "Y"; // Set passwordChanged to "Y" when password is updated
      } catch (error) {
        console.warn('Could not set passwordChanged field:', error.message);
      }
    }

    // Update user
    let user;
    try {
      // Try to update user with all fields including passwordChanged if present
      user = await prisma.user.update({
        where: { id: req.params.id },
        data: updateData,
      });
    } catch (error) {
      // If there's an error with the passwordChanged field, try without it
      if (error.message && error.message.includes('passwordChanged')) {
        console.warn('Updating user without passwordChanged field');

        // Remove passwordChanged from updateData
        const { passwordChanged, ...updateDataWithoutPasswordChanged } = updateData;

        user = await prisma.user.update({
          where: { id: req.params.id },
          data: updateDataWithoutPasswordChanged,
        });

        // Add passwordChanged property manually
        if (password) {
          user.passwordChanged = "Y";
        } else {
          user.passwordChanged = "N";
        }
      } else {
        // If it's a different error, rethrow it
        throw error;
      }
    }

    console.log('User updated successfully:', user.id);

    res.status(200).json({
      success: true,
      data: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department,
        profileImage: user.profileImage,
        code: user.code,
        skills: user.skills,
        joinDate: user.joinDate,
        passwordChanged: user.passwordChanged || "N", // Include passwordChanged status
      },
    });
  } catch (error) {
    console.error('Update user error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: `A user with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during user update',
    });
  }
});

app.delete('/api/users/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if user exists
    const userExists = await prisma.user.findUnique({
      where: { id },
    });

    if (!userExists) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Delete user
    await prisma.user.delete({
      where: { id },
    });

    console.log('User deleted successfully:', id);

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});


// Department routes
app.get('/api/departments', simpleAuth, async (req, res) => {
  try {
    const departments = await prisma.department.findMany();

    res.status(200).json({
      success: true,
      count: departments.length,
      data: departments,
    });
  } catch (error) {
    console.error('Get departments error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.post('/api/departments', simpleAuth, async (req, res) => {
  try {
    const { name, code, description } = req.body;

    // Validate required fields
    if (!name || !code) {
      return res.status(400).json({
        success: false,
        message: 'Please provide department name and code'
      });
    }

    console.log('Creating department with data:', {
      name,
      code,
      description: description || 'Not provided'
    });

    // Check if department already exists
    const departmentExists = await prisma.department.findFirst({
      where: {
        OR: [
          { name },
          { code }
        ]
      }
    });

    if (departmentExists) {
      return res.status(400).json({
        success: false,
        message: 'Department with this name or code already exists'
      });
    }

    // Generate a unique ID for the department
    const departmentId = uuidv4();

    // Create department
    const department = await prisma.department.create({
      data: {
        id: departmentId,
        name,
        code,
        description: description || null
      }
    });

    console.log('Department created successfully:', department.id);

    res.status(201).json({
      success: true,
      data: department
    });
  } catch (error) {
    console.error('Create department error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: `A department with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during department creation'
    });
  }
});

app.put('/api/departments/:id', simpleAuth, async (req, res) => {
  try {
    const { name, code, description, managerId } = req.body;
    const { id } = req.params;

    console.log('Updating department with ID:', id);
    console.log('Update data:', {
      name: name || 'Not changed',
      code: code || 'Not changed',
      description: description || 'Not changed',
      managerId: managerId || 'Not changed',
    });

    // Check if department exists
    const departmentExists = await prisma.department.findUnique({
      where: { id },
    });

    if (!departmentExists) {
      return res.status(404).json({
        success: false,
        message: 'Department not found',
      });
    }

    // Check if name or code is already in use by another department
    if ((name && name !== departmentExists.name) || (code && code !== departmentExists.code)) {
      const existingDepartment = await prisma.department.findFirst({
        where: {
          OR: [
            { name },
            { code },
          ],
          NOT: {
            id,
          },
        },
      });

      if (existingDepartment) {
        return res.status(400).json({
          success: false,
          message: 'Department with this name or code already exists',
        });
      }
    }

    // Prepare update data - only include fields that are provided
    const updateData = {};

    if (name !== undefined) updateData.name = name;
    if (code !== undefined) updateData.code = code;
    if (description !== undefined) updateData.description = description;
    if (managerId !== undefined) updateData.managerId = managerId;

    // Update department
    const department = await prisma.department.update({
      where: { id },
      data: updateData,
    });

    console.log('Department updated successfully:', department.id);

    res.status(200).json({
      success: true,
      data: department,
    });
  } catch (error) {
    console.error('Update department error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: `A department with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during department update',
    });
  }
});

app.delete('/api/departments/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if department exists
    const departmentExists = await prisma.department.findUnique({
      where: { id },
    });

    if (!departmentExists) {
      return res.status(404).json({
        success: false,
        message: 'Department not found',
      });
    }

    // Delete department
    await prisma.department.delete({
      where: { id },
    });

    console.log('Department deleted successfully:', id);

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete department error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Engineers routes (now using User model with any role)
app.get('/api/engineers', simpleAuth, async (req, res) => {
  try {
    // Get all users with their engineer-specific fields
    const engineers = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        department: true,
        role: true,
        code: true,
        skills: true,
        joinDate: true,
        profileImage: true,
      },
    });

    // Process skills field to convert from string to array
    const processedEngineers = engineers.map(engineer => ({
      ...engineer,
      // Convert skills string to array if it exists
      skills: engineer.skills ? engineer.skills.split(',') : [],
      // Format joinDate if needed
      joinDate: engineer.joinDate ? engineer.joinDate.toISOString().split('T')[0] : null
    }));

    res.status(200).json({
      success: true,
      count: processedEngineers.length,
      data: processedEngineers,
    });
  } catch (error) {
    console.error('Get engineers error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Project routes
app.get('/api/projects', simpleAuth, async (req, res) => {
  try {
    const projects = await prisma.project.findMany({
      include: {
        task: {
          include: {
            subtask: true,
          },
        },
        customer: true,
        section: {
          include: {
            task: {
              include: {
                subtask: true,
              },
              orderBy: {
                sequence: 'asc',
              },
            },
          },
          orderBy: {
            sequence: 'asc',
          },
        },
      },
    });

    // Normalize the response and add displayId to tasks and subtasks
    const normalizedProjects = projects.map(project => {
      const normalizedProject = { ...project };

      // Handle backward compatibility: if projectType exists but projectCategory doesn't, copy it
      if (project.projectType && !project.projectCategory) {
        normalizedProject.projectCategory = project.projectType;
      }

      // Map tasks to include 'subtasks' property and displayId
      normalizedProject.tasks = (project.task || []).map(task => ({
        ...task,
        displayId: `T${task.sequence}`,
        subtasks: (task.subtask || []).map(subtask => ({
          ...subtask,
          displayId: `S${task.sequence}${subtask.sequence}`
        }))
      }));

      // Map sections to include tasks with displayId
      normalizedProject.sections = (project.section || []).map(section => ({
        ...section,
        tasks: (section.task || []).map(task => ({
          ...task,
          displayId: `T${task.sequence}`,
          subtasks: (task.subtask || []).map(subtask => ({
            ...subtask,
            displayId: `S${task.sequence}${subtask.sequence}`
          }))
        }))
      }));



      return normalizedProject;
    });

    res.status(200).json({
      success: true,
      count: normalizedProjects.length,
      data: normalizedProjects,
    });
  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.get('/api/projects/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const project = await prisma.project.findUnique({
      where: { id },
      include: {
        task: {
          include: {
            subtask: true,
          },
        },
        customer: true,
        section: {
          include: {
            task: {
              include: {
                subtask: true,
              },
            },
          },
          orderBy: {
            sequence: 'asc',
          },
        },
      },
    });

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found',
      });
    }

    // Add displayId to tasks and subtasks
    const normalizedProject = {
      ...project,
      tasks: (project.task || []).map(task => ({
        ...task,
        displayId: `T${task.sequence}`,
        subtasks: (task.subtask || []).map(subtask => ({
          ...subtask,
          displayId: `S${task.sequence}${subtask.sequence}`
        }))
      })),
      sections: (project.section || []).map(section => ({
        ...section,
        tasks: (section.task || []).map(task => ({
          ...task,
          displayId: `T${task.sequence}`,
          subtasks: (task.subtask || []).map(subtask => ({
            ...subtask,
            displayId: `S${task.sequence}${subtask.sequence}`
          }))
        }))
      }))
    };



    // Handle backward compatibility: if projectType exists but projectCategory doesn't, copy it
    if (project.projectType && !normalizedProject.projectCategory) {
      normalizedProject.projectCategory = project.projectType;
    }

    res.status(200).json({
      success: true,
      data: normalizedProject,
    });
  } catch (error) {
    console.error('Get project error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Create a new project
app.post('/api/projects', simpleAuth, async (req, res) => {
  console.log('🚀 PROJECT CREATION REQUEST RECEIVED!');
  console.log('🚀 Request body:', JSON.stringify(req.body, null, 2));

  try {
    const {
      name,
      code,
      projectCategory,
      customerId,
      poNumber,
      poDate,
      startDate,
      endDate,
      projectManagerId,
      projectManagerIds, // New field for multiple managers
      status,
      createdBy,
      createdAt,
      updatedAt
    } = req.body;

    console.info('API', `Creating new project: ${name}`, { skipDuplicateCheck: true });
    console.log('Create project request body:', req.body);

    // Handle both single and multiple manager formats
    const managerIds = projectManagerIds || (projectManagerId ? [projectManagerId] : []);
    console.log('Manager IDs to assign:', managerIds);

    // Validate required fields
    if (!name || !code || !projectCategory || !customerId || managerIds.length === 0 || !status) {
      console.log('Missing required fields:', { name, code, projectCategory, customerId, managerIds, status });
      return res.status(400).json({
        success: false,
        message: 'Missing required fields. At least one project manager is required.'
      });
    }

    try {
      // Get the count of existing projects to generate sequential ID
      const projectCount = await prisma.project.count();
      const nextProjectNumber = projectCount + 1;
      const projectId = `P${nextProjectNumber}`;

      console.info('API', `Generating project ID: ${projectId}`, { skipDuplicateCheck: true });

      // Get current timestamp for both createdAt and updatedAt if not provided
      const now = new Date().toISOString();

      // Create the project with proper data validation
      console.log('Creating project with data:', {
        id: projectId,
        name,
        code,
        customerId,
        poNumber: poNumber || '',
        poDate: poDate ? new Date(poDate).toISOString() : now,
        startDate: startDate ? new Date(startDate).toISOString() : now,
        endDate: endDate ? new Date(endDate).toISOString() : null,
        projectManagerId: managerIds[0], // Use first manager as primary
        status: status || 'NOT_STARTED',
        createdBy: createdBy || 'system',
      });

      // Use the first manager as the creator if no createdBy is provided
      const projectCreator = createdBy || managerIds[0];

      console.log('Using project creator:', projectCreator);

      const project = await prisma.project.create({
        data: {
          id: projectId,
          name,
          code,
          projectCategory,
          customerId,
          poNumber: poNumber || '',
          poDate: poDate ? new Date(poDate).toISOString() : now,
          startDate: startDate ? new Date(startDate).toISOString() : now,
          endDate: endDate ? new Date(endDate).toISOString() : null,
          department: 'Engineering', // Temporary fix - use default department value
          projectManagerId: managerIds[0], // Use first manager as primary
          status: status || 'NOT_STARTED',
          createdBy: projectCreator,
          createdAt: now,
          updatedAt: now
        },
      });

      console.log('Project created successfully:', project.id);

      // Create default sections
      const defaultSections = [
        { name: 'Design', description: 'Design and planning phase' },
        { name: 'Procurement', description: 'Procurement and sourcing phase' },
        { name: 'Assembly', description: 'Assembly and manufacturing phase' },
        { name: 'Testing', description: 'Testing and quality assurance phase' },
        { name: 'MQ1', description: 'First milestone and quality checkpoint' },
        { name: 'MQ2', description: 'Second milestone and quality checkpoint' }
      ];

      console.log('Creating default sections for project:', project.id);

      for (let i = 0; i < defaultSections.length; i++) {
        const section = defaultSections[i];
        await prisma.section.create({
          data: {
            id: uuidv4(),
            projectId: project.id,
            name: section.name,
            sequence: i + 1,
            description: section.description,
          },
        });
      }

      console.log('Default sections created successfully');

      console.info('API', `Project created successfully: ${name}`, {
        entityId: project.id,
        entityName: project.name,
        skipDuplicateCheck: true
      });

      return res.status(201).json({
        success: true,
        data: project,
      });
    } catch (innerError) {
      console.error('API', `Error creating project: ${innerError.message}`, {
        details: innerError.message,
        skipDuplicateCheck: true
      });
      return res.status(500).json({
        success: false,
        message: 'Error creating project',
        error: innerError.message
      });
    }
  } catch (error) {
    console.error('🚨 PROJECT CREATION ERROR:', error);
    console.error('🚨 Error message:', error.message);
    console.error('🚨 Error stack:', error.stack);
    console.error('API', 'Create project error', {
      details: error.message,
      skipDuplicateCheck: true
    });
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Task routes
app.get('/api/tasks', simpleAuth, async (req, res) => {
  try {
    console.info('API', 'Fetching all tasks');

    const tasks = await prisma.task.findMany({
      include: {
        subtask: {
          orderBy: {
            sequence: 'asc'
          }
        },
        project: {
          select: {
            id: true,
            name: true,
            code: true,
            customerId: true,
            customer: {
              select: {
                id: true,
                name: true
              }
            }
          },
        },
      },
      orderBy: {
        sequence: 'asc'
      }
    });

    // Add displayId to each task and its subtasks
    const tasksWithDisplayId = tasks.map(task => ({
      ...task,
      displayId: `T${task.sequence}`,
      subtask: task.subtask.map(subtask => ({
        ...subtask,
        displayId: `S${task.sequence}${subtask.sequence}`
      }))
    }));

    console.info('API', `Retrieved ${tasksWithDisplayId.length} tasks from database`);

    res.status(200).json({
      success: true,
      count: tasksWithDisplayId.length,
      data: tasksWithDisplayId,
    });
  } catch (error) {
    console.error('API', 'Get tasks error', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

app.get('/api/tasks/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const task = await prisma.task.findUnique({
      where: { id },
      include: {
        subtask: true,
        project: {
          select: {
            name: true,
            code: true,
            customer: true,
          },
        },
      },
    });

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found',
      });
    }

    res.status(200).json({
      success: true,
      data: task,
    });
  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Update task
app.put('/api/tasks/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`Updating task ${id}:`, JSON.stringify(req.body, null, 2));

    // Check if task exists
    const taskExists = await prisma.task.findUnique({
      where: { id },
    });

    if (!taskExists) {
      return res.status(404).json({
        success: false,
        message: 'Task not found',
      });
    }

    // Format dates properly for Prisma
    const formatDate = (dateStr) => {
      if (!dateStr) return null;
      // Check if it's already an ISO string
      if (typeof dateStr === 'string' && dateStr.includes('T')) return dateStr;

      // Convert YYYY-MM-DD to ISO string
      try {
        const date = new Date(dateStr);
        return date.toISOString();
      } catch (error) {
        console.warn(`Invalid date format: ${dateStr}`);
        return null;
      }
    };

    // Prepare update data - only include fields that are provided
    const updateData = {
      // Always update the updatedAt field with the current timestamp
      updatedAt: new Date().toISOString()
    };

    // Extract all possible field names from the request body
    const {
      name, title, // Support both name and title
      description,
      status,
      priority,
      startDate,
      endDate,
      projectId,
      assigneeId, assignedToId, // Support both assigneeId and assignedToId
      department,
      ...otherFields
    } = req.body;

    // Handle field name variations
    if (name !== undefined) updateData.name = name;
    else if (title !== undefined) updateData.name = title; // Map title to name

    if (description !== undefined) updateData.description = description;
    if (status !== undefined) updateData.status = status;
    if (priority !== undefined) updateData.priority = priority;
    if (startDate !== undefined) updateData.startDate = formatDate(startDate);
    if (endDate !== undefined) updateData.endDate = formatDate(endDate);
    if (projectId !== undefined) updateData.projectId = projectId;

    if (assigneeId !== undefined) updateData.assigneeId = assigneeId;
    else if (assignedToId !== undefined) updateData.assigneeId = assignedToId; // Map assignedToId to assigneeId

    if (department !== undefined) updateData.department = department;

    // Log any other fields that might be important
    if (Object.keys(otherFields).length > 0) {
      console.log('Other fields in request:', otherFields);
    }

    console.log('Update data:', JSON.stringify(updateData, null, 2));

    // If no valid fields were provided, return an error
    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No valid fields provided for update'
      });
    }

    console.log('Attempting to update task in database with:', {
      taskId: id,
      updateData: JSON.stringify(updateData, null, 2)
    });

    try {
      // Update task
      const task = await prisma.task.update({
        where: { id },
        data: updateData,
        include: {
          subtask: true,
          project: {
            select: {
              id: true,
              name: true,
              code: true,
              customer: true,
            },
          },
        },
      });

      console.log(`Task ${id} updated successfully:`, {
        taskId: id,
        updatedTask: JSON.stringify(task, null, 2)
      });

      // After successfully updating a task, recalculate and update the parent project's status and progress
      await updateProjectStatusAndProgress(task.projectId);

      return res.status(200).json({
        success: true,
        data: task,
      });
    } catch (updateError) {
      console.error('Error updating task in database:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Database error during task update',
        error: updateError.message
      });
    }
  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Delete task
app.delete('/api/tasks/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if task exists
    const taskExists = await prisma.task.findUnique({
      where: { id },
    });

    if (!taskExists) {
      return res.status(404).json({
        success: false,
        message: 'Task not found',
      });
    }

    // Delete task
    await prisma.task.delete({
      where: { id },
    });

    console.log(`Task ${id} deleted successfully`);

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Helper function to generate sequential IDs
const generateSequentialId = async (prisma, type, projectId = null, taskId = null) => {
  if (type === 'task') {
    // Get the count of existing tasks for this project
    const taskCount = await prisma.task.count({
      where: { projectId }
    });
    return `T${taskCount + 1}`;
  } else if (type === 'subtask') {
    // Get count of existing subtasks for this task
    const subtaskCount = await prisma.subtask.count({
      where: { taskId }
    });
    // Generate sequential subtask ID (e.g., S1, S2, etc.)
    return `S${subtaskCount + 1}`;
  }
  throw new Error('Invalid type for ID generation');
};

// Create a new task
app.post('/api/tasks', simpleAuth, async (req, res) => {
  try {
    const {
      projectId,
      name,
      description,
      assigneeId,
      assigneeType,
      department,
      startDate,
      endDate,
      status,
      createdBy,
      isSubtask,
      parentTaskId,
      displayId
    } = req.body;

    console.log('Creating new task/subtask:', {
      name,
      isSubtask,
      parentTaskId,
      displayId
    });

    // Check if this is actually a subtask being created
    if (isSubtask && parentTaskId) {
      console.log('This is a subtask for task:', parentTaskId);

      try {
        // Get the parent task to ensure it exists
        const parentTask = await prisma.task.findUnique({
          where: { id: parentTaskId }
        });

        if (!parentTask) {
          return res.status(404).json({
            success: false,
            message: 'Parent task not found'
          });
        }

        // Get the next sequence number for this task's subtasks
        const lastSubtask = await prisma.subtask.findFirst({
          where: { taskId: parentTaskId },
          orderBy: { sequence: 'desc' }
        });
        const nextSequence = (lastSubtask?.sequence || 0) + 1;

        // Validate subtask assignee - must be an engineer
        if (assigneeId) {
          const assignee = await prisma.user.findUnique({
            where: { id: assigneeId },
          });

          if (!assignee) {
            return res.status(400).json({
              success: false,
              message: 'Subtask assignee not found',
            });
          }

          if (assignee.role !== 'ENGINEER') {
            return res.status(400).json({
              success: false,
              message: 'Subtask assignee must be an engineer',
            });
          }
        }

        // Get current timestamp for both createdAt and updatedAt
        const now = new Date().toISOString();

        // Create the subtask
        const subtask = await prisma.subtask.create({
          data: {
            id: req.body.id || `${projectId}T${parentTask.sequence}S${nextSequence}`,
            taskId: parentTaskId,
            sequence: nextSequence,
            displayId: displayId || `S${nextSequence}`,
            name,
            description: description || '',
            assigneeId,
            assigneeType: assigneeType || 'ENGINEER',
            startDate: startDate ? new Date(startDate).toISOString() : now,
            ...(endDate && { endDate: new Date(endDate).toISOString() }), // Only set endDate if provided
            status: status || 'NOT_STARTED',
            totalTime: req.body.totalTime || 0,
            createdBy,
            createdAt: now,
            updatedAt: now
          }
        });

        console.log('Subtask created successfully:', subtask.id);

        // Return the created subtask
        return res.status(201).json({
          success: true,
          data: {
            ...subtask,
            displayId: displayId || `S${nextSequence}`
          }
        });
      } catch (subtaskError) {
        console.error('Error creating subtask:', subtaskError);
        return res.status(500).json({
          success: false,
          message: 'Error creating subtask',
          error: subtaskError.message
        });
      }
    }

    // Regular task creation logic continues here...
    // ... rest of the existing task creation code ...

    // Validate required fields for regular task creation
    if (!projectId || !name || !assigneeId || !status || !createdBy) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields for task creation'
      });
    }

    try {
      // Get the project to ensure it exists
      const project = await prisma.project.findUnique({
        where: { id: projectId }
      });

      if (!project) {
        return res.status(404).json({
          success: false,
          message: 'Project not found'
        });
      }

      // Get the next sequence number for this project's tasks
      const lastTask = await prisma.task.findFirst({
        where: { projectId },
        orderBy: { sequence: 'desc' }
      });
      const nextSequence = (lastTask?.sequence || 0) + 1;

      // Generate the task ID
      const taskId = `${projectId}T${nextSequence}`;

      // Get current timestamp for both createdAt and updatedAt
      const now = new Date().toISOString();

      // Create the task with the generated ID and sequence
      const task = await prisma.task.create({
        data: {
          id: taskId,
          projectId,
          sequence: nextSequence,
          name,
          description: description || '',
          assigneeId,
          assigneeType: assigneeType || 'ENGINEER',
          department: department || '',
          startDate: startDate ? new Date(startDate).toISOString() : now,
          endDate: endDate ? new Date(endDate).toISOString() : now,
          status,
          createdBy,
          createdAt: now,
          updatedAt: now
        },
      });

      // Create subtasks if provided
      if (subtasks && subtasks.length > 0) {
        try {
          for (const subtask of subtasks) {
            // Get the next sequence number for this task's subtasks
            const lastSubtask = await prisma.subtask.findFirst({
              where: { taskId: task.id },
              orderBy: { sequence: 'desc' }
            });
            const nextSubtaskSequence = (lastSubtask?.sequence || 0) + 1;

            // Generate the subtask ID
            const subtaskId = `${projectId}T${task.sequence}S${nextSubtaskSequence}`;

            // Create the subtask with the generated ID and sequence
            await prisma.subtask.create({
              data: {
                id: subtaskId,
                taskId: task.id,
                sequence: nextSubtaskSequence,
                name: subtask.name,
                description: subtask.description || '',
                assigneeId: subtask.assigneeId || assigneeId,
                assigneeType: subtask.assigneeType || 'ENGINEER',
                startDate: subtask.startDate ? new Date(subtask.startDate).toISOString() : now,
                endDate: subtask.endDate ? new Date(subtask.endDate).toISOString() : now,
                status: subtask.status || 'NOT_STARTED',
                totalTime: subtask.totalTime || 0,
                createdBy: subtask.createdBy || createdBy,
                createdAt: now,
                updatedAt: now
              }
            });
          }
        } catch (subtaskError) {
          console.error('Error creating subtasks:', subtaskError);
          // Continue with task creation even if subtasks fail
        }
      }

      // Add displayId to the response
      const responseTask = {
        ...task,
        displayId: `T${nextSequence}`
      };

      console.log('Task created successfully:', task.id);

      res.status(201).json({
        success: true,
        data: responseTask,
      });
    } catch (innerError) {
      console.error('Error creating task:', innerError);
      return res.status(500).json({
        success: false,
        message: 'Error creating task',
        error: innerError.message
      });
    }
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Customer routes
app.get('/api/customers', simpleAuth, async (req, res) => {
  try {
    const customers = await prisma.customer.findMany({
      include: {
        _count: {
          select: {
            project: true,
          },
        },
      },
    });

    res.status(200).json({
      success: true,
      count: customers.length,
      data: customers,
    });
  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.get('/api/customers/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        project: true,
      },
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    res.status(200).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('Get customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.post('/api/customers', simpleAuth, async (req, res) => {
  try {
    const { name, code, contactName, email, phone, address } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Please provide a customer name',
      });
    }

    console.log('Creating customer with data:', {
      name,
      code: code || 'Not provided',
      contactName: contactName || 'Not provided',
      email: email || 'Not provided',
      phone: phone || 'Not provided',
      address: address || 'Not provided',
    });

    // Check if customer with same name or code already exists
    if (code) {
      const existingCustomer = await prisma.customer.findFirst({
        where: {
          OR: [
            { name },
            { code },
          ],
        },
      });

      if (existingCustomer) {
        return res.status(400).json({
          success: false,
          message: 'Customer with this name or code already exists',
        });
      }
    }

    // Generate a unique ID for the customer
    const customerId = uuidv4();

    const customer = await prisma.customer.create({
      data: {
        id: customerId,
        name,
        code,
        contactName,
        email,
        phone,
        address,
      },
    });

    console.log('Customer created successfully:', customer.id);

    res.status(201).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('Create customer error:', error);

    // Check for Prisma-specific errors
    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: `A customer with this ${error.meta?.target?.[0] || 'field'} already exists`,
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || 'Server error during customer creation',
    });
  }
});

app.put('/api/customers/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, contactName, email, phone, address } = req.body;

    const customer = await prisma.customer.update({
      where: { id },
      data: {
        name,
        code,
        contactName,
        email,
        phone,
        address,
      },
    });

    res.status(200).json({
      success: true,
      data: customer,
    });
  } catch (error) {
    console.error('Update customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

app.delete('/api/customers/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if customer has related projects or MOMs
    const customerWithRelations = await prisma.customer.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            project: true,
          },
        },
      },
    });

    if (customerWithRelations._count.project > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete customer with associated projects',
      });
    }

    await prisma.customer.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: 'Customer deleted successfully',
    });
  } catch (error) {
    console.error('Delete customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// MOM routes
app.get('/api/moms', simpleAuth, async (req, res) => {
  try {
    console.log('MOM List - Fetching all MOMs');

    const moms = await prisma.mom.findMany({
      include: {
        project: {
          include: {
            customer: true,
          },
        },
        user_mom_createdByTouser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        mompoint: {
          orderBy: {
            slNo: 'asc',
          },
        },
        momactionitem: true,
        momattendee: true,
      },
      orderBy: {
        date: 'desc',
      },
    });

    console.log(`MOM List - Found ${moms.length} MOMs`);

    // Transform MOMs to match frontend expectations
    const processedMoms = moms.map(mom => {
      const actionItems = [];

      // Check for new action items first (current structure)
      if (mom.momactionitem && mom.momactionitem.length > 0) {
        mom.momactionitem.forEach(item => {
          actionItems.push({
            id: item.id,
            description: item.description,
            assigneeId: item.assigneeId || '',
            dueDate: item.dueDate ? item.dueDate.toISOString() : '',
            status: item.status || 'PENDING'
          });
        });
      } else {
        // Fallback to old mompoints structure (legacy support)
        const momPoints = mom.mompoint || [];
        momPoints.forEach(point => {
          if (point.discussion) {
            actionItems.push({
              id: point.id,
              description: point.discussion,
              assigneeId: point.responsibility || '',
              dueDate: point.plannedDate ? point.plannedDate.toISOString() : '',
              status: point.status || 'PENDING'
            });
          }
        });
      }

      // Process attendees
      const attendees = mom.momattendee?.map(attendee => ({
        id: attendee.id,
        name: attendee.name,
        company: attendee.company,
        designation: attendee.designation || '',
        email: attendee.email || ''
      })) || [];

      return {
        id: mom.id,
        date: mom.date.toISOString(),
        projectId: mom.projectId,
        customerId: mom.project?.customer?.id || '',
        agenda: mom.agenda || 'Meeting discussion',
        attendees: attendees,
        actionItems: actionItems,
        points: actionItems.map(item => item.description).filter(Boolean),
        createdBy: mom.createdBy,
        createdAt: mom.createdAt.toISOString(),
        updatedAt: mom.updatedAt.toISOString(),
        project: mom.project,
        customer: mom.project?.customer,
        user_mom_createdByTouser: mom.user_mom_createdByTouser
      };
    });

    console.log('MOM List - Sending response with processed MOMs');

    // Debug: Log the first processed MOM to see the structure
    if (processedMoms.length > 0) {
      console.log('MOM List - First processed MOM structure:', {
        id: processedMoms[0].id,
        date: processedMoms[0].date,
        customerId: processedMoms[0].customerId,
        projectId: processedMoms[0].projectId,
        agenda: processedMoms[0].agenda,
        attendeesCount: processedMoms[0].attendees?.length || 0,
        actionItemsCount: processedMoms[0].actionItems?.length || 0,
        customer: processedMoms[0].customer,
        project: processedMoms[0].project
      });

      // Log the full MOM data for debugging
      console.log('MOM List - Full first MOM data:', JSON.stringify(processedMoms[0], null, 2));
    }

    res.status(200).json({
      success: true,
      count: processedMoms.length,
      data: processedMoms,
    });
  } catch (error) {
    console.error('Get MOMs error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Get a specific MOM
app.get('/api/moms/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`MOM Detail - Fetching MOM with ID: ${id}`);

    const mom = await prisma.mom.findUnique({
      where: { id },
      include: {
        project: {
          include: {
            customer: true,
          },
        },
        user_mom_createdByTouser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        mompoint: {
          orderBy: {
            slNo: 'asc',
          },
        },
        momactionitem: true,
        momattendee: true,
      },
    });

    if (!mom) {
      console.log(`MOM Detail - MOM with ID ${id} not found`);
      return res.status(404).json({
        success: false,
        message: 'MOM not found'
      });
    }

    console.log('MOM Detail - MOM found, raw data:', {
      id: mom.id,
      date: mom.date,
      projectId: mom.projectId,
      momPointsCount: mom.mompoint?.length || 0,
      actionItemsCount: mom.momactionitem?.length || 0,
      attendeesCount: mom.momattendee?.length || 0
    });

    // Transform MOM to match frontend expectations
    const actionItems = [];

    // Check for new action items first (current structure)
    if (mom.momactionitem && mom.momactionitem.length > 0) {
      mom.momactionitem.forEach(item => {
        actionItems.push({
          id: item.id,
          description: item.description,
          assigneeId: item.assigneeId || '',
          dueDate: item.dueDate ? item.dueDate.toISOString() : '',
          status: item.status || 'PENDING'
        });
      });
    } else {
      // Fallback to old mompoints structure (legacy support)
      const momPoints = mom.mompoint || [];
      momPoints.forEach(point => {
        if (point.discussion) {
          actionItems.push({
            id: point.id,
            description: point.discussion,
            assigneeId: point.responsibility || '',
            dueDate: point.plannedDate ? point.plannedDate.toISOString() : '',
            status: point.status || 'PENDING'
          });
        }
      });
    }

    // Process attendees
    const attendees = mom.momattendee?.map(attendee => ({
      id: attendee.id,
      name: attendee.name,
      company: attendee.company,
      designation: attendee.designation || '',
      email: attendee.email || ''
    })) || [];

    const processedMom = {
      id: mom.id,
      date: mom.date.toISOString(),
      projectId: mom.projectId,
      customerId: mom.project?.customer?.id || '',
      agenda: mom.agenda || 'Meeting discussion',
      attendees: attendees,
      actionItems: actionItems,
      points: actionItems.map(item => item.description).filter(Boolean),
      createdBy: mom.createdBy,
      createdAt: mom.createdAt.toISOString(),
      updatedAt: mom.updatedAt.toISOString(),
      project: mom.project,
      customer: mom.project?.customer,
      user_mom_createdByTouser: mom.user_mom_createdByTouser
    };

    console.log('MOM Detail - Sending response with processed MOM');
    res.status(200).json({
      success: true,
      data: processedMom
    });
  } catch (error) {
    console.error('Get MOM error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Create a new MOM
app.post('/api/moms', simpleAuth, async (req, res) => {
  try {
    console.log('MOM Creation - Request body:', JSON.stringify(req.body, null, 2));

    const {
      id,
      date,
      customerId,
      engineerId,
      agenda,
      attendees,
      points,
      actionItems,
      createdBy,
      createdAt,
      projectId
    } = req.body;

    console.log('MOM Creation - Extracted data:', {
      id: id || 'Will be generated',
      date,
      customerId,
      engineerId,
      agenda,
      attendeesCount: attendees?.length || 0,
      pointsType: Array.isArray(points) ? 'array' : typeof points,
      pointsValue: points,
      actionItemsCount: actionItems?.length || 0,
      projectId: projectId || null
    });

    // Format the date properly for Prisma
    const formattedDate = new Date(date);
    console.log('MOM Creation - Formatted date:', formattedDate.toISOString());

    // Create the MOM
    const mom = await prisma.mom.create({
      data: {
        id: id || uuidv4(),
        date: formattedDate,
        customerId,
        engineerId,
        agenda,
        // Convert points array to string for storage
        points: Array.isArray(points) ? JSON.stringify(points) : (points || '[]'),
        projectId: projectId || null,
        createdBy: createdBy || req.user?.id,
        createdAt: new Date(createdAt || new Date().toISOString()),
        updatedAt: new Date().toISOString()
      }
    });

    console.log('MOM Creation - MOM created successfully:', mom.id);

    // Create attendees
    if (attendees && attendees.length > 0) {
      console.log(`MOM Creation - Creating ${attendees.length} attendees`);
      for (const attendee of attendees) {
        try {
          console.log('MOM Creation - Creating attendee:', {
            name: attendee.name,
            company: attendee.company
          });

          await prisma.momattendee.create({
            data: {
              id: attendee.id || uuidv4(),
              momId: mom.id,
              name: attendee.name,
              company: attendee.company,
              designation: attendee.designation,
              email: attendee.email
            }
          });
        } catch (error) {
          console.error('MOM Creation - Error creating attendee:', error.message);
        }
      }
      console.log('MOM Creation - All attendees created successfully');
    }

    // Create action items
    if (actionItems && actionItems.length > 0) {
      console.log(`MOM Creation - Creating ${actionItems.length} action items`);
      for (const item of actionItems) {
        try {
          console.log('MOM Creation - Creating action item:', {
            description: item.description,
            assigneeId: item.assigneeId,
            dueDate: item.dueDate
          });

          // Format the due date properly for Prisma
          const formattedDueDate = new Date(item.dueDate);
          console.log('MOM Creation - Formatted due date:', formattedDueDate.toISOString());

          await prisma.momactionitem.create({
            data: {
              id: item.id || uuidv4(),
              momId: mom.id,
              description: item.description,
              assigneeId: item.assigneeId,
              dueDate: formattedDueDate,
              status: item.status || 'PENDING'
            }
          });
          console.log('MOM Creation - Action item created successfully');
        } catch (error) {
          console.error('MOM Creation - Error creating action item:', error.message);
        }
      }
      console.log('MOM Creation - All action items created successfully');
    }

    // Process the MOM data for response
    let processedMom;
    try {
      // Try to parse points as JSON
      const pointsArray = JSON.parse(mom.points);
      processedMom = {
        ...mom,
        points: Array.isArray(pointsArray) ? pointsArray : [mom.points]
      };
    } catch (e) {
      // If parsing fails, treat points as a single string
      processedMom = {
        ...mom,
        points: [mom.points]
      };
    }

    // Create MOM alert with proper title
    try {
      // Get MOM with related data for alert
      const momWithData = await prisma.mom.findUnique({
        where: { id: mom.id },
        include: {
          customer: true,
          project: true,
          momactionitem: true
        }
      });

      if (momWithData) {
        const meetingDate = new Date(momWithData.date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });

        const alertTitle = `Minutes of Meeting created for ${momWithData.customer.name}${momWithData.project ? ` - ${momWithData.project.name}` : ''}`;
        const alertMessage = `Meeting held on ${meetingDate}${momWithData.agenda ? ` regarding "${momWithData.agenda}"` : ''}`;

        // Create alert for project manager if project exists
        if (momWithData.project) {
          await prisma.alert.create({
            data: {
              id: uuidv4(),
              type: 'MOM',
              priority: 'MEDIUM',
              title: alertTitle,
              message: alertMessage,
              relatedTo: mom.id,
              relatedType: 'MOM',
              assigneeId: momWithData.project.projectManagerId,
              department: momWithData.project.department,
              createdBy: req.user?.id
            }
          });
        }

        console.log('MOM Creation - Alert created successfully');
      }
    } catch (alertError) {
      console.error('MOM Creation - Error creating alert:', alertError);
      // Don't fail the MOM creation if alert fails
    }

    console.log('MOM Creation - Sending successful response');
    res.status(201).json({
      success: true,
      data: processedMom
    });
  } catch (error) {
    console.error('MOM Creation - ERROR:', error);
    console.error('MOM Creation - Error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    });

    res.status(500).json({
      success: false,
      message: 'Server error during MOM creation',
      error: error.message
    });
  }
});

// Update a MOM
app.put('/api/moms/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`MOM Update - Updating MOM with ID: ${id}`);

    const {
      date,
      customerId,
      engineerId,
      agenda,
      points,
      projectId,
      actionItems
    } = req.body;

    console.log('MOM Update - Request data:', {
      date,
      customerId,
      engineerId,
      agenda,
      pointsType: Array.isArray(points) ? 'array' : typeof points,
      pointsValue: points,
      projectId,
      actionItemsCount: actionItems ? actionItems.length : 0
    });

    // Check if MOM exists
    const momExists = await prisma.mom.findUnique({
      where: { id }
    });

    if (!momExists) {
      console.log(`MOM Update - MOM with ID ${id} not found`);
      return res.status(404).json({
        success: false,
        message: 'MOM not found'
      });
    }

    // Format the date properly for Prisma if provided
    const formattedDate = date ? new Date(date) : undefined;
    if (formattedDate) {
      console.log('MOM Update - Formatted date:', formattedDate.toISOString());
    }

    // Update the MOM
    const mom = await prisma.mom.update({
      where: { id },
      data: {
        date: formattedDate,
        customerId: customerId || undefined,
        engineerId: engineerId || undefined,
        agenda: agenda || undefined,
        points: points ? (Array.isArray(points) ? JSON.stringify(points) : points) : undefined,
        projectId: projectId || undefined,
        updatedAt: new Date().toISOString()
      },
      include: {
        momattendee: true,
        momactionitem: true,
        customer: true,
        project: true
      }
    });

    console.log('MOM Update - MOM updated successfully');

    // Update action items if provided
    if (actionItems && Array.isArray(actionItems)) {
      console.log(`MOM Update - Updating ${actionItems.length} action items`);

      // First, delete all existing action items for this MOM
      await prisma.momactionitem.deleteMany({
        where: { momId: id }
      });

      // Then create new action items
      for (const item of actionItems) {
        try {
          console.log('MOM Update - Creating action item:', {
            description: item.description,
            assigneeId: item.assigneeId,
            dueDate: item.dueDate
          });

          // Format the due date properly for Prisma
          const formattedDueDate = new Date(item.dueDate);
          console.log('MOM Update - Formatted due date:', formattedDueDate.toISOString());

          await prisma.momactionitem.create({
            data: {
              id: item.id || uuidv4(),
              momId: mom.id,
              description: item.description,
              assigneeId: item.assigneeId,
              dueDate: formattedDueDate,
              status: item.status || 'PENDING'
            }
          });
          console.log('MOM Update - Action item created successfully');
        } catch (error) {
          console.error('MOM Update - Error creating action item:', error.message);
        }
      }
      console.log('MOM Update - All action items updated successfully');
    }

    // Create processed MOM object
    let processedMom = {
      ...mom,
      points: []
    };

    // Get updated action items
    const updatedActionItems = await prisma.momactionitem.findMany({
      where: { momId: id }
    });

    // Create a new object with the action items
    processedMom = {
      ...processedMom,
      actionItems: updatedActionItems
    };

    console.log('MOM Update - Sending response with processed MOM');
    res.status(200).json({
      success: true,
      data: processedMom
    });
  } catch (error) {
    console.error('MOM Update - ERROR:', error);
    console.error('MOM Update - Error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    });

    res.status(500).json({
      success: false,
      message: 'Server error during MOM update',
      error: error.message
    });
  }
});

// Delete a MOM (Director only)
app.delete('/api/moms/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`MOM Delete - Deleting MOM with ID: ${id}`);

    // Check if user is Director
    if (req.user?.role !== 'DIRECTOR') {
      console.log(`MOM Delete - Access denied. User role: ${req.user?.role}`);
      return res.status(403).json({
        success: false,
        message: 'Access denied. Only Directors can delete MOMs.'
      });
    }

    // Check if MOM exists
    const momExists = await prisma.mom.findUnique({
      where: { id }
    });

    if (!momExists) {
      console.log(`MOM Delete - MOM with ID ${id} not found`);
      return res.status(404).json({
        success: false,
        message: 'MOM not found'
      });
    }

    // Delete related records first
    console.log(`MOM Delete - Deleting related attendees and action items`);

    // Delete attendees
    await prisma.momattendee.deleteMany({
      where: { momId: id }
    });

    // Delete action items
    await prisma.momactionitem.deleteMany({
      where: { momId: id }
    });

    // Delete the MOM
    await prisma.mom.delete({
      where: { id }
    });

    console.log(`MOM Delete - MOM with ID ${id} deleted successfully`);

    res.status(200).json({
      success: true,
      message: 'MOM deleted successfully'
    });
  } catch (error) {
    console.error('MOM Delete - ERROR:', error);
    console.error('MOM Delete - Error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    });

    res.status(500).json({
      success: false,
      message: 'Server error during MOM deletion',
      error: error.message
    });
  }
});

// Settings routes
app.get('/api/settings/profile', simpleAuth, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user profile
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        department: true,
        role: true
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('API', 'Error fetching user profile', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

app.put('/api/settings/profile', simpleAuth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { name, email } = req.body;

    // Update user profile
    const user = await prisma.user.update({
      where: { id: userId },
      data: {
        name,
        email
      },
      select: {
        id: true,
        name: true,
        email: true,
        department: true,
        role: true
      }
    });

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('API', 'Error updating user profile', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

app.put('/api/settings/password', simpleAuth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { currentPassword, newPassword } = req.body;

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if current password is correct
    const isMatch = await bcrypt.compare(currentPassword, user.password);

    if (!isMatch) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
        passwordChanged: 'Y'
      }
    });

    res.status(200).json({
      success: true,
      message: 'Password updated successfully'
    });
  } catch (error) {
    console.error('API', 'Error changing password', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

app.get('/api/settings/notifications', simpleAuth, async (req, res) => {
  try {
    // For demo purposes, return mock data
    const mockNotificationSettings = {
      emailNotifications: true,
      taskReminders: true,
      projectUpdates: true,
      securityAlerts: true
    };

    res.status(200).json({
      success: true,
      data: mockNotificationSettings
    });
  } catch (error) {
    console.error('API', 'Error fetching notification settings', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

app.put('/api/settings/notifications', simpleAuth, async (req, res) => {
  try {
    const userId = req.user.id;
    const notificationSettings = req.body;

    // For demo purposes, return the same data
    res.status(200).json({
      success: true,
      data: notificationSettings
    });
  } catch (error) {
    console.error('API', 'Error updating notification settings', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});



// Alerts routes
app.get('/api/alerts', simpleAuth, async (req, res) => {
  try {
    const { priority, status, type, search } = req.query;

    // Build the where clause
    let where = {};

    if (search) {
      where.OR = [
        { message: { contains: search } },
        { relatedTo: { contains: search } }
      ];
    }

    if (type && type !== 'ALL') {
      where.type = type;
    }

    // Get alerts from the database
    const alertsUnsorted = await prisma.alert.findMany({
      where
    });

    // Sort alerts manually with proper priority ordering
    const priorityOrder = { 'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };

    const alerts = alertsUnsorted.sort((a, b) => {
      // First sort by read status (unread first)
      if (a.read !== b.read) {
        return a.read ? 1 : -1;
      }

      // Then by priority (CRITICAL > HIGH > MEDIUM > LOW)
      const aPriority = priorityOrder[a.priority] || 1;
      const bPriority = priorityOrder[b.priority] || 1;

      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }

      // Finally by creation date (newest first)
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    console.info('API', `Retrieved ${alerts.length} alerts from database`);

    res.status(200).json({
      success: true,
      count: alerts.length,
      data: alerts,
    });
  } catch (error) {
    console.error('API', 'Error fetching alerts', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

app.get('/api/alerts/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Get alert from database
    const alert = await prisma.alert.findUnique({
      where: { id }
    });

    if (!alert) {
      return res.status(404).json({
        success: false,
        message: 'Alert not found'
      });
    }

    res.status(200).json({
      success: true,
      data: alert
    });
  } catch (error) {
    console.error('API', 'Error fetching alert', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

app.put('/api/alerts/:id/read', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if alert exists
    const alert = await prisma.alert.findUnique({
      where: { id }
    });

    if (!alert) {
      return res.status(404).json({
        success: false,
        message: 'Alert not found'
      });
    }

    // Mark the alert as read
    const updatedAlert = await prisma.alert.update({
      where: { id },
      data: { read: true }
    });

    res.status(200).json({
      success: true,
      data: updatedAlert
    });
  } catch (error) {
    console.error('API', 'Error marking alert as read', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

app.put('/api/alerts/:id/resolve', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if alert exists
    const alert = await prisma.alert.findUnique({
      where: { id }
    });

    if (!alert) {
      return res.status(404).json({
        success: false,
        message: 'Alert not found'
      });
    }

    // Since the alert schema doesn't have a status field, we'll just mark it as read
    // In a real application, you would update the status field
    const updatedAlert = await prisma.alert.update({
      where: { id },
      data: {
        read: true
      }
    });

    res.status(200).json({
      success: true,
      data: updatedAlert
    });
  } catch (error) {
    console.error('API', 'Error resolving alert', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});



// Helper function to update project status and progress
async function updateProjectStatusAndProgress(projectId) {
  // Fetch all tasks for the project
  const tasks = await prisma.task.findMany({
    where: { projectId },
    include: { subtask: true },
  });
  // Calculate status and progress
  let completed = 0, inProgress = 0, delayed = 0, onHold = 0, total = tasks.length;
  tasks.forEach(task => {
    if (task.status === 'COMPLETED') completed++;
    else if (task.status === 'IN_PROGRESS') inProgress++;
    else if (task.status === 'DELAYED') delayed++;
    else if (task.status === 'ON_HOLD') onHold++;
  });
  // Example: set project status to COMPLETED if all tasks are completed
  let projectStatus = 'NOT_STARTED';
  if (completed === total && total > 0) projectStatus = 'COMPLETED';
  else if (inProgress > 0) projectStatus = 'IN_PROGRESS';
  else if (delayed > 0) projectStatus = 'DELAYED';
  else if (onHold > 0) projectStatus = 'ON_HOLD';

  // Update the project with just the status and updatedAt
  await prisma.project.update({
    where: { id: projectId },
    data: {
      status: projectStatus,
      updatedAt: new Date().toISOString(),
    },
  });
}

// Get all subtasks
app.get('/api/subtasks', simpleAuth, async (req, res) => {
  try {
    console.info('API', 'Fetching all subtasks');

    const subtasks = await prisma.subtask.findMany({
      include: {
        task: {
          select: {
            id: true,
            name: true,
            projectId: true,
            project: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Add displayId to each subtask
    const subtasksWithDisplayId = subtasks.map(subtask => ({
      ...subtask,
      displayId: `S${subtask.sequence}`
    }));

    console.info('API', `Found ${subtasksWithDisplayId.length} subtasks`);

    res.status(200).json({
      success: true,
      count: subtasksWithDisplayId.length,
      data: subtasksWithDisplayId,
    });
  } catch (error) {
    console.error('Get subtasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Get single subtask
app.get('/api/subtasks/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const subtask = await prisma.subtask.findUnique({
      where: { id },
      include: {
        task: {
          select: {
            id: true,
            name: true,
            projectId: true,
            project: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    if (!subtask) {
      return res.status(404).json({
        success: false,
        message: 'Subtask not found',
      });
    }

    // Add displayId
    const subtaskWithDisplayId = {
      ...subtask,
      displayId: `S${subtask.sequence}`
    };

    res.status(200).json({
      success: true,
      data: subtaskWithDisplayId,
    });
  } catch (error) {
    console.error('Get subtask error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Create a new subtask
app.post('/api/subtasks', simpleAuth, async (req, res) => {
  try {
    const {
      taskId,
      name,
      description,
      assigneeId,
      assigneeType,
      startDate,
      endDate,
      status,
      priority,
      createdBy,
      totalTime
    } = req.body;

    console.log('Creating new subtask:', {
      name,
      taskId,
      assigneeId
    });

    // Validate required fields
    if (!taskId || !name || !assigneeId || !status || !createdBy) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: taskId, name, assigneeId, status, createdBy'
      });
    }

    // Check if parent task exists
    const parentTask = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        project: {
          select: {
            id: true,
            code: true
          }
        }
      }
    });

    if (!parentTask) {
      return res.status(404).json({
        success: false,
        message: 'Parent task not found'
      });
    }

    // Check if assignee exists and is an engineer
    const assignee = await prisma.user.findUnique({
      where: { id: assigneeId }
    });

    if (!assignee) {
      return res.status(404).json({
        success: false,
        message: 'Assignee not found'
      });
    }

    if (assignee.role !== 'ENGINEER') {
      return res.status(400).json({
        success: false,
        message: 'Subtask assignees must be engineers'
      });
    }

    // Get the next sequence number for this task's subtasks
    const lastSubtask = await prisma.subtask.findFirst({
      where: { taskId },
      orderBy: { sequence: 'desc' }
    });

    const nextSequence = lastSubtask ? lastSubtask.sequence + 1 : 1;

    // Generate subtask ID
    const subtaskId = `${parentTask.project.code}T${parentTask.sequence}S${nextSequence}`;

    // Get current timestamp
    const now = new Date().toISOString();

    // Create the subtask
    const subtask = await prisma.subtask.create({
      data: {
        id: subtaskId,
        taskId,
        sequence: nextSequence,
        displayId: `S${nextSequence}`,
        name,
        description: description || '',
        assigneeId,
        assigneeType: assigneeType || 'ENGINEER',
        startDate: startDate ? new Date(startDate).toISOString() : now,
        endDate: endDate ? new Date(endDate).toISOString() : now,
        status: status || 'NOT_STARTED',
        priority: priority || 'Medium',
        totalTime: totalTime || 0,
        createdBy,
        createdAt: now,
        updatedAt: now
      },
      include: {
        task: {
          select: {
            id: true,
            name: true,
            projectId: true,
            project: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    console.log('Subtask created successfully:', subtask.id);

    // Return the created subtask with displayId
    res.status(201).json({
      success: true,
      data: {
        ...subtask,
        displayId: `S${nextSequence}`
      }
    });
  } catch (error) {
    console.error('Error creating subtask:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating subtask',
      error: error.message
    });
  }
});

// Update subtask
app.put('/api/subtasks/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      status,
      assigneeId,
      assigneeType,
      startDate,
      endDate,
      department,
      priority,
      totalTime
    } = req.body;

    // Check if subtask exists
    const subtaskExists = await prisma.subtask.findUnique({ where: { id } });
    if (!subtaskExists) {
      return res.status(404).json({ success: false, message: 'Subtask not found' });
    }

    // Validate subtask assignee if being updated - must be an engineer
    if (assigneeId) {
      const assignee = await prisma.user.findUnique({
        where: { id: assigneeId },
      });

      if (!assignee) {
        return res.status(400).json({
          success: false,
          message: 'Subtask assignee not found',
        });
      }

      if (assignee.role !== 'ENGINEER') {
        return res.status(400).json({
          success: false,
          message: 'Subtask assignee must be an engineer',
        });
      }
    }

    // Prepare update data
    const updateData = {
      updatedAt: new Date().toISOString()
    };
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (status !== undefined) updateData.status = status;
    if (assigneeId !== undefined) updateData.assigneeId = assigneeId;
    if (assigneeType !== undefined) updateData.assigneeType = assigneeType;
    if (startDate !== undefined) updateData.startDate = new Date(startDate).toISOString();
    if (endDate !== undefined) updateData.endDate = new Date(endDate).toISOString();
    if (department !== undefined) updateData.department = department;
    if (priority !== undefined) updateData.priority = priority;
    if (totalTime !== undefined) updateData.totalTime = totalTime;

    const subtask = await prisma.subtask.update({
      where: { id },
      data: updateData
    });

    // Update parent project status/progress
    const parentTask = await prisma.task.findUnique({ where: { id: subtask.taskId } });
    if (parentTask) {
      await updateProjectStatusAndProgress(parentTask.projectId);
    }

    res.status(200).json({ success: true, data: subtask });
  } catch (error) {
    console.error('Update subtask error:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// Delete subtask
app.delete('/api/subtasks/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if subtask exists
    const subtaskExists = await prisma.subtask.findUnique({
      where: { id },
      include: {
        task: {
          select: {
            id: true,
            projectId: true
          }
        }
      }
    });

    if (!subtaskExists) {
      return res.status(404).json({
        success: false,
        message: 'Subtask not found',
      });
    }

    // Delete subtask
    await prisma.subtask.delete({
      where: { id },
    });

    console.log(`Subtask ${id} deleted successfully`);

    // Update parent project status/progress
    if (subtaskExists.task) {
      await updateProjectStatusAndProgress(subtaskExists.task.projectId);
    }

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete subtask error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Section routes
// Get sections for a project
app.get('/api/projects/:projectId/sections', simpleAuth, async (req, res) => {
  try {
    const { projectId } = req.params;

    // Check if project exists
    const projectExists = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!projectExists) {
      return res.status(404).json({
        success: false,
        message: 'Project not found',
      });
    }

    // Get sections with their tasks
    const sections = await prisma.section.findMany({
      where: { projectId },
      orderBy: { sequence: 'asc' },
      include: {
        task: {
          include: {
            subtask: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: { sequence: 'asc' },
        },
      },
    });

    res.status(200).json({
      success: true,
      data: sections,
    });
  } catch (error) {
    console.error('Error fetching sections:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Create a new section
app.post('/api/projects/:projectId/sections', simpleAuth, async (req, res) => {
  try {
    const { projectId } = req.params;
    const { name, description } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Section name is required',
      });
    }

    // Check if project exists
    const projectExists = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!projectExists) {
      return res.status(404).json({
        success: false,
        message: 'Project not found',
      });
    }

    // Check if section name already exists in this project
    const existingSection = await prisma.section.findFirst({
      where: {
        projectId,
        name,
      },
    });

    if (existingSection) {
      return res.status(400).json({
        success: false,
        message: 'Section with this name already exists in the project',
      });
    }

    // Get next sequence number
    const lastSection = await prisma.section.findFirst({
      where: { projectId },
      orderBy: { sequence: 'desc' },
    });

    const nextSequence = lastSection ? lastSection.sequence + 1 : 1;

    // Create section
    const section = await prisma.section.create({
      data: {
        id: uuidv4(),
        projectId,
        name,
        description: description || '',
        sequence: nextSequence,
      },
    });

    res.status(201).json({
      success: true,
      data: section,
    });
  } catch (error) {
    console.error('Error creating section:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Project Categories routes
app.get('/api/project-categories', simpleAuth, async (req, res) => {
  try {
    const categories = await prisma.project_category_item.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Error fetching project categories:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch project categories',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get single project category
app.get('/api/project-categories/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    const category = await prisma.project_category_item.findUnique({
      where: { id }
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: 'Project category not found'
      });
    }

    res.status(200).json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error('Error fetching project category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch project category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Create project category
app.post('/api/project-categories', simpleAuth, async (req, res) => {
  try {
    const { name, code, description } = req.body;

    // Validate required fields
    if (!name || !code) {
      return res.status(400).json({
        success: false,
        message: 'Name and code are required'
      });
    }

    // Check if category with same name or code already exists
    const existingCategory = await prisma.project_category_item.findFirst({
      where: {
        OR: [
          { name: name.trim() },
          { code: code.trim().toUpperCase() }
        ]
      }
    });

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: existingCategory.name === name.trim()
          ? 'Project category with this name already exists'
          : 'Project category with this code already exists'
      });
    }

    const category = await prisma.project_category_item.create({
      data: {
        id: uuidv4(),
        name: name.trim(),
        code: code.trim().toUpperCase(),
        description: description?.trim() || null
      }
    });

    res.status(201).json({
      success: true,
      data: category,
      message: 'Project category created successfully'
    });
  } catch (error) {
    console.error('Error creating project category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create project category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Update project category
app.put('/api/project-categories/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, description } = req.body;

    // Validate required fields
    if (!name || !code) {
      return res.status(400).json({
        success: false,
        message: 'Name and code are required'
      });
    }

    // Check if category exists
    const existingCategory = await prisma.project_category_item.findUnique({
      where: { id }
    });

    if (!existingCategory) {
      return res.status(404).json({
        success: false,
        message: 'Project category not found'
      });
    }

    // Check if another category with same name or code already exists
    const duplicateCategory = await prisma.project_category_item.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          {
            OR: [
              { name: name.trim() },
              { code: code.trim().toUpperCase() }
            ]
          }
        ]
      }
    });

    if (duplicateCategory) {
      return res.status(400).json({
        success: false,
        message: duplicateCategory.name === name.trim()
          ? 'Project category with this name already exists'
          : 'Project category with this code already exists'
      });
    }

    const category = await prisma.project_category_item.update({
      where: { id },
      data: {
        name: name.trim(),
        code: code.trim().toUpperCase(),
        description: description?.trim() || null
      }
    });

    res.status(200).json({
      success: true,
      data: category,
      message: 'Project category updated successfully'
    });
  } catch (error) {
    console.error('Error updating project category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update project category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Delete project category
app.delete('/api/project-categories/:id', simpleAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if category exists
    const existingCategory = await prisma.project_category_item.findUnique({
      where: { id }
    });

    if (!existingCategory) {
      return res.status(404).json({
        success: false,
        message: 'Project category not found'
      });
    }

    // Check if category is being used by any projects
    const projectsUsingCategory = await prisma.project.findFirst({
      where: { projectCategory: existingCategory.name }
    });

    if (projectsUsingCategory) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete project category as it is being used by existing projects'
      });
    }

    await prisma.project_category_item.delete({
      where: { id }
    });

    res.status(200).json({
      success: true,
      message: 'Project category deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting project category:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete project category',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Excel Import/Export Routes
// @desc    Import Excel data for project
// @route   POST /api/projects/:id/excel-import
// @access  Private
app.post('/api/projects/:id/excel-import', simpleAuth, async (req, res) => {
  try {
    const projectId = req.params.id;
    const { sections } = req.body;

    console.log('🔄 Excel import request for project:', projectId);
    console.log('🔄 Sections to import:', sections?.length);
    console.log('🔄 Sections data:', JSON.stringify(sections, null, 2));

    // Validate project exists
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        section: {
          include: {
            task: {
              include: {
                subtask: true
              }
            }
          }
        }
      }
    });

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Check if user has access to this project
    if (
      req.user.role === 'PROJECT_MANAGER' &&
      project.projectManagerId !== req.user.id
    ) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to import data for this project'
      });
    }

    const importResults = {
      sectionsProcessed: 0,
      tasksCreated: 0,
      tasksUpdated: 0,
      subtasksCreated: 0,
      subtasksUpdated: 0,
      errors: []
    };

    // Helper function to get next task sequence
    const getNextTaskSequence = async (projectId) => {
      const lastTask = await prisma.task.findFirst({
        where: { projectId },
        orderBy: { sequence: 'desc' }
      });
      return (lastTask?.sequence || 0) + 1;
    };

    // Helper function to get next subtask sequence
    const getNextSubtaskSequence = async (taskId) => {
      const lastSubtask = await prisma.subtask.findFirst({
        where: { taskId },
        orderBy: { sequence: 'desc' }
      });
      return (lastSubtask?.sequence || 0) + 1;
    };

    // Process each section
    for (const sectionData of sections) {
      try {
        console.log('🔧 Processing section:', sectionData.name);
        console.log('🔧 Section tasks:', sectionData.tasks);
        console.log('🔧 Section tasks length:', sectionData.tasks?.length || 0);
        // Find or create section
        let section = await prisma.section.findFirst({
          where: {
            projectId: projectId,
            name: sectionData.name
          }
        });

        if (!section) {
          // Create new section
          section = await prisma.section.create({
            data: {
              id: uuidv4(),
              projectId: projectId,
              name: sectionData.name,
              sequence: sectionData.sequence || 1,
              description: sectionData.description || ''
            }
          });
        }

        importResults.sectionsProcessed++;

        // Process tasks in this section
        for (const taskData of sectionData.tasks || []) {
          try {
            let task = null;

            // Check if task exists by name and section (ignore displayId)
            task = await prisma.task.findFirst({
              where: {
                projectId: projectId,
                sectionId: section.id,
                name: taskData.name
              },
              include: {
                subtask: true
              }
            });

            if (task) {
              // Update existing task (only update fields that are provided)
              const updateData = {
                name: taskData.name
              };

              // Only update optional fields if they are provided
              if (taskData.description !== undefined) updateData.description = taskData.description || '';
              if (taskData.assigneeId) updateData.assigneeId = taskData.assigneeId;
              if (taskData.assigneeType) updateData.assigneeType = taskData.assigneeType;
              if (taskData.department) updateData.department = taskData.department;
              if (taskData.startDate) updateData.startDate = new Date(taskData.startDate);
              if (taskData.endDate) updateData.endDate = new Date(taskData.endDate);
              if (taskData.status) updateData.status = taskData.status;
              if (taskData.priority) updateData.priority = taskData.priority;

              task = await prisma.task.update({
                where: { id: task.id },
                data: updateData,
                include: {
                  subtask: true
                }
              });
              importResults.tasksUpdated++;
            } else {
              // Create new task with default values for missing fields
              const taskSequence = await getNextTaskSequence(projectId);
              const currentDate = new Date();

              task = await prisma.task.create({
                data: {
                  id: uuidv4(),
                  projectId: projectId,
                  sectionId: section.id,
                  sequence: taskSequence,
                  displayId: taskData.displayId || `T${taskSequence}`,
                  name: taskData.name,
                  description: taskData.description || '',
                  assigneeId: taskData.assigneeId || '',
                  assigneeType: taskData.assigneeType || 'ENGINEER',
                  department: taskData.department || 'GENERAL',
                  startDate: taskData.startDate ? new Date(taskData.startDate) : currentDate,
                  endDate: taskData.endDate ? new Date(taskData.endDate) : new Date(currentDate.getTime() + 7 * 24 * 60 * 60 * 1000), // Default to 7 days later
                  status: taskData.status || 'NOT_STARTED',
                  priority: taskData.priority || 'Medium',
                  createdBy: req.user.id
                },
                include: {
                  subtask: true
                }
              });
              importResults.tasksCreated++;
            }

            // Process subtasks
            for (const subtaskData of taskData.subtasks || []) {
              try {
                // Check if subtask exists by name and parent task (ignore displayId)
                let existingSubtask = await prisma.subtask.findFirst({
                  where: {
                    taskId: task.id,
                    name: subtaskData.name
                  }
                });

                if (existingSubtask) {
                  // Update existing subtask (only update fields that are provided)
                  const updateData = {
                    name: subtaskData.name
                  };

                  // Only update optional fields if they are provided
                  if (subtaskData.description !== undefined) updateData.description = subtaskData.description || '';
                  if (subtaskData.assigneeId) updateData.assigneeId = subtaskData.assigneeId;
                  if (subtaskData.assigneeType) updateData.assigneeType = subtaskData.assigneeType;
                  if (subtaskData.startDate) updateData.startDate = new Date(subtaskData.startDate);
                  if (subtaskData.endDate) updateData.endDate = new Date(subtaskData.endDate);
                  if (subtaskData.status) updateData.status = subtaskData.status;
                  if (subtaskData.priority) updateData.priority = subtaskData.priority;

                  await prisma.subtask.update({
                    where: { id: existingSubtask.id },
                    data: updateData
                  });
                  importResults.subtasksUpdated++;
                } else {
                  // Create new subtask with default values for missing fields
                  const subtaskSequence = await getNextSubtaskSequence(task.id);
                  const currentDate = new Date();

                  await prisma.subtask.create({
                    data: {
                      id: uuidv4(),
                      taskId: task.id,
                      sequence: subtaskSequence,
                      displayId: subtaskData.displayId || `S${task.sequence}${subtaskSequence}`,
                      name: subtaskData.name,
                      description: subtaskData.description || '',
                      assigneeId: subtaskData.assigneeId || '',
                      assigneeType: subtaskData.assigneeType || 'ENGINEER',
                      startDate: subtaskData.startDate ? new Date(subtaskData.startDate) : currentDate,
                      endDate: subtaskData.endDate ? new Date(subtaskData.endDate) : new Date(currentDate.getTime() + 7 * 24 * 60 * 60 * 1000), // Default to 7 days later
                      status: subtaskData.status || 'NOT_STARTED',
                      priority: subtaskData.priority || 'Medium',
                      createdBy: req.user.id
                    }
                  });
                  importResults.subtasksCreated++;
                }
              } catch (subtaskError) {
                console.error('Error processing subtask:', subtaskError);
                importResults.errors.push(`Failed to process subtask: ${subtaskData.name}`);
              }
            }
          } catch (taskError) {
            console.error('Error processing task:', taskError);
            importResults.errors.push(`Failed to process task: ${taskData.name}`);
          }
        }
      } catch (sectionError) {
        console.error('Error processing section:', sectionError);
        importResults.errors.push(`Failed to process section: ${sectionData.name}`);
      }
    }

    console.log('✅ Import completed with results:', importResults);

    res.status(200).json({
      success: true,
      message: 'Excel data imported successfully',
      data: importResults
    });

  } catch (error) {
    console.error('🔄 Excel import error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to import Excel data: ' + error.message
    });
  }
});

// @desc    Get project data for Excel export
// @route   GET /api/projects/:id/excel-export
// @access  Private
app.get('/api/projects/:id/excel-export', simpleAuth, async (req, res) => {
  try {
    const projectId = req.params.id;

    console.log('📊 Excel export request for project:', projectId);

    // Get project with all related data
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        customer: true,
        user_project_projectManagerIdTouser: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        section: {
          include: {
            task: {
              include: {
                subtask: {
                  include: {
                    user: {
                      select: {
                        id: true,
                        name: true,
                        email: true,
                        role: true
                      }
                    }
                  }
                },
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                    role: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!project) {
      return res.status(404).json({
        success: false,
        message: 'Project not found'
      });
    }

    // Check if user has access to this project
    if (
      req.user.role === 'PROJECT_MANAGER' &&
      project.projectManagerId !== req.user.id
    ) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to export this project'
      });
    }

    // Format data for Excel export
    const formattedProject = {
      ...project,
      sections: project.section.map(section => ({
        ...section,
        tasks: section.task.map(task => ({
          ...task,
          displayId: `T${task.sequence}`,
          subtasks: task.subtask.map(subtask => ({
            ...subtask,
            displayId: `S${task.sequence}${subtask.sequence}`
          }))
        }))
      }))
    };

    res.status(200).json({
      success: true,
      data: formattedProject
    });

  } catch (error) {
    console.error('📊 Excel export error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export project data: ' + error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.info('SYSTEM', `Server is running on port ${PORT}`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('SYSTEM', 'Unhandled Rejection', err);
  // Don't exit the process in development, just log the error
  console.error('SYSTEM', 'Server continuing despite unhandled rejection...');
});
