import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAtom } from 'jotai';
import { currentUserAtom } from '../store';
import { paymentsAPI } from '../services/api';
import {
  ArrowLeft,
  DollarSign,
  CreditCard,
  Calendar,
  Building2,
  User,
  FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  Search,
  Filter,
  Download,
  Eye
} from 'lucide-react';

interface PaymentRecord {
  id: string;
  projectId: string;
  projectName: string;
  projectCode: string;
  customerName: string;
  amount: number;
  paymentDate: string;
  paymentMethod: string;
  referenceNumber?: string;
  description?: string;
  createdBy: string;
  createdAt: string;
  user: {
    name: string;
    email: string;
  };
}

interface PaymentSummary {
  totalProjects: number;
  totalPoValue: number;
  totalPaid: number;
  totalPending: number;
  paidPercentage: number;
  pendingPercentage: number;
}

const PaymentListPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentUser] = useAtom(currentUserAtom);
  const [payments, setPayments] = useState<PaymentRecord[]>([]);
  const [summary, setSummary] = useState<PaymentSummary>({
    totalProjects: 0,
    totalPoValue: 0,
    totalPaid: 0,
    totalPending: 0,
    paidPercentage: 0,
    pendingPercentage: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMethod, setSelectedMethod] = useState('');
  const [selectedProject, setSelectedProject] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState('');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [amountRange, setAmountRange] = useState({ min: '', max: '' });
  const [selectedCreatedBy, setSelectedCreatedBy] = useState('');

  // Derived data for filter options
  const [projects, setProjects] = useState<Array<{id: string, name: string, code: string}>>([]);
  const [customers, setCustomers] = useState<Array<{name: string}>>([]);
  const [creators, setCreators] = useState<Array<{id: string, name: string}>>([]);

  // Check if user is Director
  const isDirector = currentUser?.role === 'DIRECTOR';

  useEffect(() => {
    if (!isDirector) {
      navigate('/dashboard');
      return;
    }

    fetchPaymentData();
  }, [isDirector, navigate]);

  const fetchPaymentData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch payment records and summary
      const [recordsResponse, summaryResponse] = await Promise.all([
        paymentsAPI.getPaymentRecords(),
        paymentsAPI.getPaymentSummary()
      ]);

      if (recordsResponse.success && summaryResponse.success) {
        const paymentData = recordsResponse.data.payments;
        setPayments(paymentData);
        setSummary(summaryResponse.data);

        // Extract unique values for filter options
        const uniqueProjects = Array.from(
          new Map(paymentData.map(p => [p.projectId, { id: p.projectId, name: p.projectName, code: p.projectCode }]))
            .values()
        );

        const uniqueCustomers = Array.from(
          new Set(paymentData.map(p => p.customerName))
        ).map(name => ({ name }));

        const uniqueCreators = Array.from(
          new Map(paymentData.map(p => [p.createdBy, { id: p.createdBy, name: p.user.name }]))
            .values()
        );

        setProjects(uniqueProjects);
        setCustomers(uniqueCustomers);
        setCreators(uniqueCreators);
      } else {
        setError('Failed to fetch payment data');
      }
    } catch (err) {
      console.error('Error fetching payment data:', err);
      setError('Failed to load payment data');
    } finally {
      setLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get payment method display
  const getPaymentMethodDisplay = (method: string) => {
    const methods: Record<string, string> = {
      'BANK_TRANSFER': 'Bank Transfer',
      'UPI': 'UPI',
      'CASH': 'Cash',
      'CHEQUE': 'Cheque',
      'CREDIT_CARD': 'Credit Card',
      'NEFT': 'NEFT',
      'RTGS': 'RTGS',
      'OTHER': 'Other'
    };
    return methods[method] || method;
  };

  // Filter payments
  const filteredPayments = payments.filter(payment => {
    // Search filter
    const matchesSearch = !searchTerm ||
      payment.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.projectCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.referenceNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.user.name.toLowerCase().includes(searchTerm.toLowerCase());

    // Payment method filter
    const matchesMethod = !selectedMethod || payment.paymentMethod === selectedMethod;

    // Project filter
    const matchesProject = !selectedProject || payment.projectId === selectedProject;

    // Customer filter
    const matchesCustomer = !selectedCustomer || payment.customerName === selectedCustomer;

    // Date range filter
    let matchesDateRange = true;
    if (dateRange.start || dateRange.end) {
      const paymentDate = new Date(payment.paymentDate);
      if (dateRange.start) {
        matchesDateRange = matchesDateRange && paymentDate >= new Date(dateRange.start);
      }
      if (dateRange.end) {
        matchesDateRange = matchesDateRange && paymentDate <= new Date(dateRange.end);
      }
    }

    // Amount range filter
    let matchesAmountRange = true;
    if (amountRange.min || amountRange.max) {
      const amount = payment.amount;
      if (amountRange.min) {
        matchesAmountRange = matchesAmountRange && amount >= parseFloat(amountRange.min);
      }
      if (amountRange.max) {
        matchesAmountRange = matchesAmountRange && amount <= parseFloat(amountRange.max);
      }
    }

    // Created by filter
    const matchesCreatedBy = !selectedCreatedBy || payment.createdBy === selectedCreatedBy;

    return matchesSearch && matchesMethod && matchesProject && matchesCustomer &&
           matchesDateRange && matchesAmountRange && matchesCreatedBy;
  });

  if (!isDirector) {
    return null; // Will redirect in useEffect
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-500 border-t-transparent mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Loading Payment Data</h2>
          <p className="text-gray-600">Please wait while we fetch the payment information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Data</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-gray-50 to-blue-50 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/dashboard')}
              className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Dashboard
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Payment Records</h1>
              <p className="text-gray-600">Detailed view of all payment transactions</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => navigate('/payments')}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Eye className="w-4 h-4 mr-2" />
              Payment Dashboard
            </button>
            <button
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total PO Value</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(summary.totalPoValue)}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <DollarSign className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Paid</p>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(summary.totalPaid)}</p>
              <p className="text-xs text-gray-500">{summary.paidPercentage.toFixed(1)}% of total</p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Pending</p>
              <p className="text-2xl font-bold text-orange-600">{formatCurrency(summary.totalPending)}</p>
              <p className="text-xs text-gray-500">{summary.pendingPercentage.toFixed(1)}% remaining</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Transactions</p>
              <p className="text-2xl font-bold text-purple-600">{filteredPayments.length}</p>
              <p className="text-xs text-gray-500">Payment records</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <CreditCard className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Payment Records Table */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Payment Records</h3>
            <span className="text-sm text-gray-500">
              Showing {filteredPayments.length} of {payments.length} payments
            </span>
          </div>

          {/* Enhanced Filters */}
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Filter className="w-4 h-4 text-gray-600 mr-2" />
                <h4 className="text-sm font-medium text-gray-900">Advanced Filters</h4>
              </div>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedMethod('');
                  setSelectedProject('');
                  setSelectedCustomer('');
                  setDateRange({ start: '', end: '' });
                  setAmountRange({ min: '', max: '' });
                  setSelectedCreatedBy('');
                }}
                className="text-xs text-blue-600 hover:text-blue-800 font-medium"
              >
                Clear All Filters
              </button>
            </div>

            {/* First Row - Search and Basic Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-4">
              {/* Search */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Projects, customers, references..."
                    className="w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Project Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Project</label>
                <select
                  value={selectedProject}
                  onChange={(e) => setSelectedProject(e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Projects</option>
                  {projects.map(project => (
                    <option key={project.id} value={project.id}>
                      {project.name} ({project.code})
                    </option>
                  ))}
                </select>
              </div>

              {/* Customer Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Customer</label>
                <select
                  value={selectedCustomer}
                  onChange={(e) => setSelectedCustomer(e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Customers</option>
                  {customers.map(customer => (
                    <option key={customer.name} value={customer.name}>
                      {customer.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Payment Method Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Payment Method</label>
                <select
                  value={selectedMethod}
                  onChange={(e) => setSelectedMethod(e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Methods</option>
                  <option value="BANK_TRANSFER">Bank Transfer</option>
                  <option value="UPI">UPI</option>
                  <option value="CASH">Cash</option>
                  <option value="CHEQUE">Cheque</option>
                  <option value="CREDIT_CARD">Credit Card</option>
                  <option value="NEFT">NEFT</option>
                  <option value="RTGS">RTGS</option>
                  <option value="OTHER">Other</option>
                </select>
              </div>
            </div>

            {/* Second Row - Date Range, Amount Range, Created By */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {/* Date Range */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Payment Date Range</label>
                <div className="flex space-x-2">
                  <input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                    className="flex-1 px-2 py-2 text-xs border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="From"
                  />
                  <input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                    className="flex-1 px-2 py-2 text-xs border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="To"
                  />
                </div>
              </div>

              {/* Amount Range */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Amount Range (₹)</label>
                <div className="flex space-x-2">
                  <input
                    type="number"
                    value={amountRange.min}
                    onChange={(e) => setAmountRange(prev => ({ ...prev, min: e.target.value }))}
                    placeholder="Min amount"
                    className="flex-1 px-2 py-2 text-xs border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <input
                    type="number"
                    value={amountRange.max}
                    onChange={(e) => setAmountRange(prev => ({ ...prev, max: e.target.value }))}
                    placeholder="Max amount"
                    className="flex-1 px-2 py-2 text-xs border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Created By Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Created By</label>
                <select
                  value={selectedCreatedBy}
                  onChange={(e) => setSelectedCreatedBy(e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Users</option>
                  {creators.map(creator => (
                    <option key={creator.id} value={creator.id}>
                      {creator.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Filter Summary */}
            {(searchTerm || selectedMethod || selectedProject || selectedCustomer ||
              dateRange.start || dateRange.end || amountRange.min || amountRange.max || selectedCreatedBy) && (
              <div className="mt-4 pt-3 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-600">
                    Active filters: {[
                      searchTerm && 'Search',
                      selectedMethod && 'Payment Method',
                      selectedProject && 'Project',
                      selectedCustomer && 'Customer',
                      (dateRange.start || dateRange.end) && 'Date Range',
                      (amountRange.min || amountRange.max) && 'Amount Range',
                      selectedCreatedBy && 'Created By'
                    ].filter(Boolean).join(', ')}
                  </span>
                  <span className="text-xs font-medium text-blue-600">
                    {filteredPayments.length} of {payments.length} records
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Project Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Method
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reference
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created By
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPayments.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                    No payment records found.
                  </td>
                </tr>
              ) : (
                filteredPayments.map((payment) => (
                  <tr key={payment.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{payment.projectName}</div>
                        <div className="text-sm text-gray-500">{payment.projectCode}</div>
                        <div className="text-xs text-gray-400">{payment.customerName}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-bold text-green-600">{formatCurrency(payment.amount)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {getPaymentMethodDisplay(payment.paymentMethod)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(payment.paymentDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {payment.referenceNumber || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{payment.user.name}</div>
                      <div className="text-xs text-gray-500">{payment.user.email}</div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default PaymentListPage;

// This component can be reused for Transaction History by passing a prop
export const TransactionHistoryPage: React.FC = () => {
  return <PaymentListPage />;
};
