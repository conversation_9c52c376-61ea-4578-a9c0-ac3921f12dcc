import { Request, Response } from 'express';
import { prisma } from '../index';
import { v4 as uuidv4 } from 'uuid';
import { AlertService } from '../services/alertService';

// @desc    Get all MOMs
// @route   GET /api/moms
// @access  Private
export const getMOMs = async (req: Request, res: Response): Promise<void> => {
  try {
    const moms = await prisma.mom.findMany({
      include: {
        project: {
          include: {
            customer: true,
          },
        },
        user_mom_createdByTouser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        user_mom_updatedByTouser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        mompoint: {
          orderBy: {
            slNo: 'asc',
          },
        },
      },
      orderBy: {
        date: 'desc',
      },
    });

    console.log(`Found ${moms.length} MOMs`);

    // Debug: Log the raw MOM data
    if (moms.length > 0) {
      console.log('Raw MOM mompoint data:', JSON.stringify(moms[0].mompoint, null, 2));
    }

    // Transform MOMs to match frontend expectations
    const transformedMoms = moms.map(mom => {
      // Convert mompoints to action items format
      const actionItems = mom.mompoint?.map(point => ({
        id: point.id,
        description: point.discussion || '',
        assigneeId: point.responsibility || '',
        dueDate: point.plannedDate ? point.plannedDate.toISOString() : '',
        status: point.status || 'PENDING'
      })) || [];

      return {
        id: mom.id,
        date: mom.date.toISOString(),
        projectId: mom.projectId,
        customerId: mom.project?.customer?.id || '',
        agenda: mom.agenda || 'Meeting discussion',
        attendees: [], // No attendees in current schema
        actionItems: actionItems,
        points: mom.mompoint?.map(p => p.discussion).filter(Boolean) || [],
        createdBy: mom.createdBy,
        createdAt: mom.createdAt.toISOString(),
        updatedAt: mom.updatedAt.toISOString(),
        project: mom.project,
        customer: mom.project?.customer,
        user_mom_createdByTouser: mom.user_mom_createdByTouser,
        user_mom_updatedByTouser: mom.user_mom_updatedByTouser
        // Removed mompoint to avoid double counting in statistics
      };
    });

    console.log('Transformed MOMs for frontend:', transformedMoms.length);
    if (transformedMoms.length > 0) {
      console.log('First transformed MOM:', {
        id: transformedMoms[0].id,
        customerId: transformedMoms[0].customerId,
        customer: transformedMoms[0].customer?.name,
        actionItemsCount: transformedMoms[0].actionItems.length,
        date: transformedMoms[0].date,
        agenda: transformedMoms[0].agenda
      });

      // Log the full transformed MOM for debugging
      console.log('Full transformed MOM data:', JSON.stringify(transformedMoms[0], null, 2));
    }

    res.status(200).json({
      success: true,
      count: transformedMoms.length,
      data: transformedMoms,
    });
  } catch (error) {
    console.error('Get MOMs error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single MOM by project ID
// @route   GET /api/moms/project/:projectId
// @access  Private
export const getMOMByProject = async (req: Request, res: Response): Promise<void> => {
  try {
    const mom = await prisma.mom.findUnique({
      where: { projectId: req.params.projectId },
      include: {
        project: {
          include: {
            customer: true,
          },
        },
        user_mom_createdByTouser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        user_mom_updatedByTouser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        mompoint: {
          orderBy: {
            slNo: 'asc',
          },
        },
      },
    });

    if (!mom) {
      res.status(404).json({
        success: false,
        message: 'MOM not found for this project',
      });
      return;
    }

    // Transform MOM to match frontend expectations
    const actionItems = mom.mompoint?.map(point => ({
      id: point.id,
      description: point.discussion || '',
      assigneeId: point.responsibility || '',
      dueDate: point.plannedDate ? point.plannedDate.toISOString() : '',
      status: point.status || 'PENDING'
    })) || [];

    const transformedMom = {
      id: mom.id,
      date: mom.date.toISOString(),
      projectId: mom.projectId,
      customerId: mom.project?.customer?.id || '',
      agenda: mom.agenda || 'Meeting discussion',
      attendees: [], // No attendees in current schema
      actionItems: actionItems,
      points: mom.mompoint?.map(p => p.discussion).filter(Boolean) || [],
      createdBy: mom.createdBy,
      createdAt: mom.createdAt.toISOString(),
      updatedAt: mom.updatedAt.toISOString(),
      project: mom.project,
      customer: mom.project?.customer,
      user_mom_createdByTouser: mom.user_mom_createdByTouser,
      user_mom_updatedByTouser: mom.user_mom_updatedByTouser
      // Removed mompoint to avoid double counting in statistics
    };

    res.status(200).json({
      success: true,
      data: transformedMom,
    });
  } catch (error) {
    console.error('Get MOM by project error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single MOM
// @route   GET /api/moms/:id
// @access  Private
export const getMOM = async (req: Request, res: Response): Promise<void> => {
  try {
    const mom = await prisma.mom.findUnique({
      where: { id: req.params.id },
      include: {
        project: {
          include: {
            customer: true,
          },
        },
        user_mom_createdByTouser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        user_mom_updatedByTouser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        mompoint: {
          orderBy: {
            slNo: 'asc',
          },
        },
      },
    });

    if (!mom) {
      res.status(404).json({
        success: false,
        message: 'MOM not found',
      });
      return;
    }

    // Transform MOM to match frontend expectations
    const actionItems = mom.mompoint?.map(point => ({
      id: point.id,
      description: point.discussion || '',
      assigneeId: point.responsibility || '',
      dueDate: point.plannedDate ? point.plannedDate.toISOString() : '',
      status: point.status || 'PENDING'
    })) || [];

    const transformedMom = {
      id: mom.id,
      date: mom.date.toISOString(),
      projectId: mom.projectId,
      customerId: mom.project?.customer?.id || '',
      agenda: mom.agenda || 'Meeting discussion',
      attendees: [], // No attendees in current schema
      actionItems: actionItems,
      points: mom.mompoint?.map(p => p.discussion).filter(Boolean) || [],
      createdBy: mom.createdBy,
      createdAt: mom.createdAt.toISOString(),
      updatedAt: mom.updatedAt.toISOString(),
      project: mom.project,
      customer: mom.project?.customer,
      user_mom_createdByTouser: mom.user_mom_createdByTouser,
      user_mom_updatedByTouser: mom.user_mom_updatedByTouser
      // Removed mompoint to avoid double counting in statistics
    };

    res.status(200).json({
      success: true,
      data: transformedMom,
    });
  } catch (error) {
    console.error('Get MOM error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create MOM for project
// @route   POST /api/moms
// @access  Private
export const createMOM = async (req: Request, res: Response) => {
  try {
    const { projectId, date, agenda } = req.body;

    console.log('Creating MOM for project:', projectId, 'with agenda:', agenda);

    // Validate required fields
    if (!projectId) {
      res.status(400).json({
        success: false,
        message: 'Project ID is required',
      });
      return;
    }

    // Check if project exists
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!project) {
      res.status(404).json({
        success: false,
        message: 'Project not found',
      });
      return;
    }

    // Check if MOM already exists for this project
    const existingMOM = await prisma.mom.findUnique({
      where: { projectId },
    });

    if (existingMOM) {
      res.status(400).json({
        success: false,
        message: 'MOM already exists for this project',
      });
      return;
    }

    // Create MOM
    const momId = uuidv4();
    const mom = await prisma.mom.create({
      data: {
        id: momId,
        projectId,
        date: date ? new Date(date) : new Date(),
        agenda: agenda || null,
        createdBy: req.user.id,
      },
      include: {
        project: {
          include: {
            customer: true,
          },
        },
        user_mom_createdByTouser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        mompoint: true,
      },
    });

    console.log('MOM created successfully:', mom.id);

    // Create MOM alert
    await AlertService.createMOMAlert(mom.id, 'CREATED', req.user.id);

    res.status(201).json({
      success: true,
      data: mom,
    });
  } catch (error) {
    console.error('Create MOM error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update MOM
// @route   PUT /api/moms/:id
// @access  Private
export const updateMOM = async (req: Request, res: Response) => {
  try {
    const { date, agenda } = req.body;

    // Check if MOM exists
    const momExists = await prisma.mom.findUnique({
      where: { id: req.params.id },
    });

    if (!momExists) {
      res.status(404).json({
        success: false,
        message: 'MOM not found',
      });
      return;
    }

    // Update MOM
    const mom = await prisma.mom.update({
      where: { id: req.params.id },
      data: {
        date: date ? new Date(date) : undefined,
        agenda: agenda !== undefined ? agenda : undefined,
        updatedBy: req.user.id,
      },
      include: {
        project: {
          include: {
            customer: true,
          },
        },
        user_mom_createdByTouser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        user_mom_updatedByTouser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        mompoint: {
          orderBy: {
            slNo: 'asc',
          },
        },
      },
    });

    res.status(200).json({
      success: true,
      data: mom,
    });
  } catch (error) {
    console.error('Update MOM error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete MOM
// @route   DELETE /api/moms/:id
// @access  Private
export const deleteMOM = async (req: Request, res: Response) => {
  try {
    // Check if MOM exists
    const momExists = await prisma.mom.findUnique({
      where: { id: req.params.id },
    });

    if (!momExists) {
      res.status(404).json({
        success: false,
        message: 'MOM not found',
      });
      return;
    }

    // Delete MOM (points will be deleted automatically due to cascade)
    await prisma.mom.delete({
      where: { id: req.params.id },
    });

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete MOM error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Add MOM point
// @route   POST /api/moms/:id/points
// @access  Private
export const addMOMPoint = async (req: Request, res: Response) => {
  try {
    const {
      date,
      discussionType,
      station,
      discussion,
      actionPlan,
      responsibility,
      plannedDate,
      completionDate,
      status,
      imageAttachment,
      remarks,
    } = req.body;

    // Check if MOM exists
    const mom = await prisma.mom.findUnique({
      where: { id: req.params.id },
      include: {
        mompoint: {
          orderBy: {
            slNo: 'desc',
          },
          take: 1,
        },
      },
    });

    if (!mom) {
      res.status(404).json({
        success: false,
        message: 'MOM not found',
      });
      return;
    }

    // Calculate next serial number
    const nextSlNo = mom.mompoint.length > 0 ? mom.mompoint[0].slNo + 1 : 1;

    // Create MOM point
    const momPoint = await prisma.mompoint.create({
      data: {
        id: uuidv4(),
        momId: req.params.id,
        slNo: nextSlNo,
        date: date ? new Date(date) : null,
        discussionType,
        station,
        discussion,
        actionPlan,
        responsibility,
        plannedDate: plannedDate ? new Date(plannedDate) : null,
        completionDate: completionDate ? new Date(completionDate) : null,
        status: status || 'PENDING',
        imageAttachment,
        remarks,
      },
    });

    // Update MOM's updatedBy
    await prisma.mom.update({
      where: { id: req.params.id },
      data: {
        updatedBy: req.user.id,
      },
    });

    res.status(201).json({
      success: true,
      data: momPoint,
    });
  } catch (error) {
    console.error('Add MOM point error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update MOM point
// @route   PUT /api/moms/:id/points/:pointId
// @access  Private
export const updateMOMPoint = async (req: Request, res: Response) => {
  try {
    const {
      date,
      discussionType,
      station,
      discussion,
      actionPlan,
      responsibility,
      plannedDate,
      completionDate,
      status,
      imageAttachment,
      remarks,
    } = req.body;

    // Check if MOM point exists
    const momPointExists = await prisma.mompoint.findUnique({
      where: { id: req.params.pointId },
    });

    if (!momPointExists) {
      res.status(404).json({
        success: false,
        message: 'MOM point not found',
      });
      return;
    }

    // Update MOM point
    const momPoint = await prisma.mompoint.update({
      where: { id: req.params.pointId },
      data: {
        date: date ? new Date(date) : undefined,
        discussionType,
        station,
        discussion,
        actionPlan,
        responsibility,
        plannedDate: plannedDate ? new Date(plannedDate) : undefined,
        completionDate: completionDate ? new Date(completionDate) : undefined,
        status,
        imageAttachment,
        remarks,
      },
    });

    // Update MOM's updatedBy
    await prisma.mom.update({
      where: { id: req.params.id },
      data: {
        updatedBy: req.user.id,
      },
    });

    res.status(200).json({
      success: true,
      data: momPoint,
    });
  } catch (error) {
    console.error('Update MOM point error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete MOM point
// @route   DELETE /api/moms/:id/points/:pointId
// @access  Private
export const deleteMOMPoint = async (req: Request, res: Response) => {
  try {
    // Check if MOM point exists
    const momPointExists = await prisma.mompoint.findUnique({
      where: { id: req.params.pointId },
    });

    if (!momPointExists) {
      res.status(404).json({
        success: false,
        message: 'MOM point not found',
      });
      return;
    }

    // Delete MOM point
    await prisma.mompoint.delete({
      where: { id: req.params.pointId },
    });

    // Update MOM's updatedBy
    await prisma.mom.update({
      where: { id: req.params.id },
      data: {
        updatedBy: req.user.id,
      },
    });

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    console.error('Delete MOM point error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};
