import { useCallback, useEffect } from 'react';
import { useAtom } from 'jotai';
import { projectsAtom } from '../store';
import { updateProjectHierarchyStatus, isStatusUpdateNeeded } from '../utils/statusCalculator';
import { projectsAPI, tasksAPI, subtasksAPI } from '../services/api';

/**
 * Hook to automatically update statuses based on dates and completion
 */
export const useStatusUpdater = () => {
  const [projects, setProjects] = useAtom(projectsAtom);

  /**
   * Update a single project's status hierarchy
   */
  const updateProjectStatus = useCallback(async (projectId: string) => {
    try {
      const project = projects.find(p => p.id === projectId);
      if (!project) return;

      // Calculate new statuses
      const updatedProject = updateProjectHierarchyStatus(project);

      // Check if project status needs update
      if (isStatusUpdateNeeded(project.status, updatedProject.status)) {
        console.log(`Updating project ${project.name} status: ${project.status} → ${updatedProject.status}`);

        // Update project status in backend
        await projectsAPI.updateProject(projectId, {
          status: updatedProject.status
        });
      }

      // Update task statuses if needed
      if (updatedProject.tasks) {
        for (const task of updatedProject.tasks) {
          const originalTask = project.tasks?.find(t => t.id === task.id);

          if (originalTask && isStatusUpdateNeeded(originalTask.status, task.status)) {
            console.log(`Updating task ${task.name} status: ${originalTask.status} → ${task.status}`);

            // DISABLED: Backend now handles task status updates automatically when subtasks change
            // await tasksAPI.updateTask(task.id, {
            //   status: task.status
            // });
          }

          // Update subtask statuses if needed
          if (task.subtasks) {
            for (const subtask of task.subtasks) {
              const originalSubtask = originalTask?.subtasks?.find(s => s.id === subtask.id);

              if (originalSubtask && isStatusUpdateNeeded(originalSubtask.status, subtask.status)) {
                console.log(`Updating subtask ${subtask.name} status: ${originalSubtask.status} → ${subtask.status}`);

                // Update subtask status in backend
                await subtasksAPI.updateSubtask(subtask.id, {
                  status: subtask.status
                });
              }
            }
          }
        }
      }

      // IMPORTANT: Don't update local state with calculated status
      // The calculated status might override manually set COMPLETED/ON_HOLD statuses
      // Instead, let the components fetch fresh data from the database
      console.log('✅ Status calculation completed, components will fetch fresh data');

    } catch (error) {
      console.error(`Error updating status for project ${projectId}:`, error);
    }
  }, [projects, setProjects]);

  /**
   * Update all projects' statuses
   */
  const updateAllProjectStatuses = useCallback(async () => {
    console.log('🔄 Running automatic status updates...');

    for (const project of projects) {
      await updateProjectStatus(project.id);
    }

    console.log('✅ Automatic status updates completed');
  }, [projects, updateProjectStatus]);

  /**
   * Update statuses for a specific task and its subtasks
   */
  const updateTaskStatus = useCallback(async (taskId: string) => {
    try {
      // Find the project and task
      const project = projects.find(p =>
        p.tasks?.some(t => t.id === taskId)
      );

      if (!project) return;

      const task = project.tasks?.find(t => t.id === taskId);
      if (!task) return;

      // Update the entire project hierarchy
      await updateProjectStatus(project.id);

    } catch (error) {
      console.error(`Error updating status for task ${taskId}:`, error);
    }
  }, [projects, updateProjectStatus]);

  /**
   * Update statuses for a specific subtask
   */
  const updateSubtaskStatus = useCallback(async (subtaskId: string) => {
    try {
      // Find the project and task containing this subtask
      const project = projects.find(p =>
        p.tasks?.some(t =>
          t.subtasks?.some(s => s.id === subtaskId)
        )
      );

      if (!project) return;

      // Update the entire project hierarchy
      await updateProjectStatus(project.id);

    } catch (error) {
      console.error(`Error updating status for subtask ${subtaskId}:`, error);
    }
  }, [projects, updateProjectStatus]);

  /**
   * Schedule automatic status updates - optimized to reduce API calls
   */
  useEffect(() => {
    // Only run if we have projects and not already running
    if (projects.length === 0) return;

    // Run status updates every 10 minutes (reduced frequency)
    const interval = setInterval(() => {
      console.log('🔄 Scheduled status update check...');
      updateAllProjectStatuses();
    }, 10 * 60 * 1000); // 10 minutes instead of 5

    // Run initial update after 30 seconds (increased delay)
    const timeout = setTimeout(() => {
      console.log('🔄 Initial status update check...');
      updateAllProjectStatuses();
    }, 30000); // 30 seconds instead of 10

    return () => {
      clearInterval(interval);
      clearTimeout(timeout);
    };
  }, [projects.length]); // Remove updateAllProjectStatuses from dependencies to prevent recreation

  return {
    updateProjectStatus,
    updateAllProjectStatuses,
    updateTaskStatus,
    updateSubtaskStatus
  };
};

/**
 * Hook for manual status updates (for immediate use)
 */
export const useManualStatusUpdate = () => {
  const { updateProjectStatus, updateTaskStatus, updateSubtaskStatus } = useStatusUpdater();

  return {
    /**
     * Trigger immediate status update for a project
     */
    triggerProjectUpdate: updateProjectStatus,

    /**
     * Trigger immediate status update for a task
     */
    triggerTaskUpdate: updateTaskStatus,

    /**
     * Trigger immediate status update for a subtask
     */
    triggerSubtaskUpdate: updateSubtaskStatus
  };
};
