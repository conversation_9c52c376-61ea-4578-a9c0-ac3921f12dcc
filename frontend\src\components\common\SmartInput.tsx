import React, { useState, useRef, useEffect } from 'react';
import { AlertCircle, CheckCircle, Eye, EyeOff, Info } from 'lucide-react';

interface SmartInputProps {
  label: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'date' | 'textarea' | 'select';
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  onBlur?: () => void;
  error?: string | null;
  isValid?: boolean;
  isDirty?: boolean;
  isTouched?: boolean;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  helpText?: string;
  options?: { value: string; label: string }[];
  rows?: number;
  showValidationIcon?: boolean;
  showCharacterCount?: boolean;
  maxLength?: number;
  min?: string;
  max?: string;
  step?: string;
  autoComplete?: string;
  pattern?: string;
  inputMode?: 'text' | 'numeric' | 'email' | 'tel' | 'url' | 'search';
}

export const SmartInput: React.FC<SmartInputProps> = ({
  label,
  type = 'text',
  value,
  onChange,
  onBlur,
  error,
  isValid = true,
  isDirty = false,
  isTouched = false,
  placeholder,
  required = false,
  disabled = false,
  className = '',
  helpText,
  options = [],
  rows = 3,
  showValidationIcon = true,
  showCharacterCount = false,
  maxLength,
  min,
  max,
  step,
  autoComplete,
  pattern,
  inputMode
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>(null);

  // Determine validation state - Only show errors when user makes mistakes
  const hasError = error && isTouched;
  const hasSuccess = false; // Never show success messages
  const showRealTimeError = false; // Never show real-time errors

  // Get border color based on state
  const getBorderColor = () => {
    if (hasError) return 'border-red-500 focus:border-red-500 focus:ring-red-500/20';
    if (hasSuccess) return 'border-green-500 focus:border-green-500 focus:ring-green-500/20';
    if (isFocused) return 'border-blue-500 focus:border-blue-500 focus:ring-blue-500/20';
    return 'border-gray-300 focus:border-blue-500 focus:ring-blue-500/20';
  };

  // Get background color based on state
  const getBackgroundColor = () => {
    if (hasError) return 'bg-red-50';
    if (hasSuccess) return 'bg-green-50';
    return 'bg-white';
  };

  // Handle focus
  const handleFocus = (e: React.FocusEvent) => {
    setIsFocused(true);
  };

  // Handle blur
  const handleBlur = (e: React.FocusEvent) => {
    setIsFocused(false);
    onBlur?.();
  };

  // Get input props
  const getInputProps = () => {
    const baseProps = {
      ref: inputRef as any,
      value,
      onChange,
      onFocus: handleFocus,
      onBlur: handleBlur,
      placeholder,
      required,
      disabled,
      className: `
        w-full px-4 py-3 rounded-lg border-2 transition-all duration-200
        ${getBorderColor()} ${getBackgroundColor()}
        focus:outline-none focus:ring-2
        disabled:opacity-50 disabled:cursor-not-allowed
        ${className}
      `,
      maxLength,
      min,
      max,
      step,
      autoComplete,
      pattern,
      inputMode
    };

    if (type === 'password') {
      return {
        ...baseProps,
        type: showPassword ? 'text' : 'password'
      };
    }

    return {
      ...baseProps,
      type: type === 'textarea' || type === 'select' ? undefined : type
    };
  };

  // Render input element
  const renderInput = () => {
    const inputProps = getInputProps();

    if (type === 'textarea') {
      return (
        <textarea
          {...inputProps}
          rows={rows}
        />
      );
    }

    if (type === 'select') {
      return (
        <select {...inputProps}>
          <option value="">{placeholder || `Select ${label}`}</option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      );
    }

    return <input {...inputProps} />;
  };

  return (
    <div className="space-y-2">
      {/* Label */}
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      {/* Input Container */}
      <div className="relative">
        {renderInput()}

        {/* Password Toggle */}
        {type === 'password' && (
          <button
            type="button"
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        )}

        {/* Validation Icon - Only show error icon when there's an actual error */}
        {showValidationIcon && type !== 'password' && hasError && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <AlertCircle className="text-red-500" size={20} />
          </div>
        )}

        {/* Character Count */}
        {showCharacterCount && maxLength && (
          <div className="absolute right-3 bottom-2 text-xs text-gray-400">
            {value.length}/{maxLength}
          </div>
        )}
      </div>

      {/* Error Message - Only show when user makes an actual error */}
      {hasError && (
        <div className="flex items-start space-x-2 text-red-600 text-sm">
          <AlertCircle size={16} className="mt-0.5 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      {/* Character Count (bottom) */}
      {showCharacterCount && maxLength && type === 'textarea' && (
        <div className="text-right text-xs text-gray-400">
          {value.length}/{maxLength}
        </div>
      )}
    </div>
  );
};

export default SmartInput;
